# When you add config variables, you MUST think about how they affect our various runtime environments:
#   - local dev environment
#   - AWS dev environment
#   - AWS qa environment
#   - AWS prod environment
#
# IMPORTANT - do not put secrets in <env>.yml if it is version controlled
#
# Also be cognizant about whether the variable should be a secret (stored in secrets manager)
# and if the value should be injected dynamically (i.e. database hostname is injected by Terraform)
#
# If a variable is added here, it also needs to be added to one or more of the following places
# depending on the decisions made above
#   - config/<env>.yml
#   - config/custom-environment-variables.yml
#   - Terraform:
#     - aws/<env>/api/env_vars.tf
#     - aws/<env>/api/secrets_manager.tf
#
# To test tenant DBs on local
#   1. start up a mysql docker container (if you want DB persistence, create ~/Desktop/mysql folder)
#      docker run -d -v ~/Desktop/mysql:/var/lib/mysql -e MYSQL_ALLOW_EMPTY_PASSWORD=true -p 33306:3306 mysql:8.0.22 --default-authentication-plugin=mysql_native_password --sql-mode=NO_ENGINE_SUBSTITUTION
#   2. add the following to the db section in localdev.yml
#      tenants:
#          - host: 'localhost'
#            port: 33306
#
accountMigration:
    exportDir: '/tmp/account-migration/export'
    importDir: '/tmp/account-migration/import'
    kmsKeyId: 'alias/dt-dev-account-migration-kms-key'
    region: 'us-west-2'
    s3:
        bucketName: 'dt-dev-00-tenant-migration-us-west-2'
    dynamoDb:
        ap2TableName: 'ap-schema-v1.0.1'

accountSnapshot:
    dir: '/tmp/account-snapshot'
    s3:
        bucketName: 'dt-dev-00-tenant-migration-us-west-2'

customConnectionsAndTests:
    allotment: 1000
    idHashAlgorithm: 'sha256'
    db:
        hashIdLength: 64

agent:
    defaultInterval: ******** # 6 hours in milliseconds
    maxSecondsBeforePasswordRequired: 900 # 15 minutes in seconds

airtable:
    apTests: 'AP Tests'
    baseDCF: 'appB8wjosFIc5rs1b'
    baseCAC: 'app8gXzOkfcuhVElB'
    categories: 'Load Categories'
    ccpaRequirementPage: 'CPRA Statute'
    ccpaRequirementView: 'Product View'
    ccpaOldRequirementPage: 'CCPA Statute'
    ccpaOldRequirementView: 'CCPA (Statute) Reqs'
    ccpaRequirementIndexPage: 'CPRA Statute'
    ccpaRequirementIndexView: 'Product View'
    ccpaRequirementName: 'Requirement Code'
    ccpaRequirementDescription: 'Requirement Name'
    ccpaRequirementLongDescription: 'Requirement Description'
    ccpaRequirementAdditionalInfo: ''
    ccpaRequirementAdditionalInfo2: ''
    ccpaRequirementAdditionalInfo3: ''
    ccpaRequirementIndexName: 'Requirement Code'
    ccpaCategory: 'Category-Unique copy'
    ccpaTopic: ''
    ccpaSubCategory: 'Subcategory-Unique'
    ccpaLevels: ''
    ccpa&RegRequirementPage: 'CPRA Regulations'
    ccpa&RegRequirementView: 'Product View'
    ccpa&RegOldRequirementPage: 'CCPA Regulations'
    ccpa&RegOldRequirementView: 'Product View'
    ccpa&RegRequirementIndexPage: 'CCPA Regulations'
    ccpa&RegRequirementIndexView: 'Product View'
    ccpa&RegRequirementName: 'Requirement Code'
    ccpa&RegRequirementDescription: 'Requirement Name'
    ccpa&RegRequirementLongDescription: 'Requirement Description'
    ccpa&RegRequirementAdditionalInfo: 'Previous OAG Regulation'
    ccpa&RegRequirementAdditionalInfo2: ''
    ccpa&RegRequirementAdditionalInfo3: ''
    ccpa&RegRequirementIndexName: 'Requirement Code'
    ccpa&RegCategory: 'Category - Unique'
    ccpa&RegTopic: ''
    ccpa&RegSubCategory: ''
    ccpa&RegLevels: ''
    ccmRequirementPage: 'CSA CCM v4.0.6'
    ccmRequirementView: 'Product View'
    ccmRequirementIndexPage: ''
    ccmRequirementIndexView: ''
    ccmRequirementName: 'Control ID'
    ccmRequirementDescription: 'Control Title'
    ccmRequirementLongDescription: 'Control Specification'
    ccmRequirementAdditionalInfo: 'Consensus Assessments Question (CAIQ)'
    ccmRequirementAdditionalInfo2: 'Implementation Guidelines'
    ccmRequirementAdditionalInfo3: 'Auditing Guidelines'
    ccmRequirementIndexName: 'Control ID'
    ccmCategory: 'Category-Unique'
    ccmTopic: ''
    ccmSubCategory: ''
    ccmLevels: ''
    cyberEssentialsRequirementPage: 'Cyber Essentials'
    cyberEssentialsRequirementView: 'Product View'
    cyberEssentialsRequirementIndexPage: 'Cyber Essentials'
    cyberEssentialsRequirementIndexView: 'Product View'
    cyberEssentialsRequirementName: 'ID'
    cyberEssentialsRequirementDescription: 'Name'
    cyberEssentialsRequirementLongDescription: 'Description'
    cyberEssentialsRequirementAdditionalInfo: 'Status'
    cyberEssentialsRequirementAdditionalInfo2: 'Applicability'
    cyberEssentialsRequirementAdditionalInfo3: ''
    cyberEssentialsRequirementIndexName: 'ID'
    cyberEssentialsCategory: 'Theme'
    cyberEssentialsTopic: ''
    cyberEssentialsSubCategory: ''
    cyberEssentialsLevels: ''
    cmmcRequirementPage: 'CMMC 2.0'
    cmmcRequirementView: 'Product View'
    cmmcRequirementIndexPage: 'CMMC 2.0'
    cmmcRequirementIndexView: 'Product View'
    cmmcRequirementName: 'PracticeCode'
    cmmcRequirementDisplayName: 'PracticeCode'
    cmmcRequirementDescription: 'Practice Name'
    cmmcRequirementLongDescription: 'Practice Description'
    cmmcRequirementAdditionalInfo: 'Assessment Objectives'
    cmmcRequirementAdditionalInfo2: ''
    cmmcRequirementAdditionalInfo3: ''
    cmmcRequirementIndexName: 'PracticeCode'
    cmmcCategory: 'Category-Unique'
    cmmcTopic: ''
    cmmcSubCategory: 'Subcategory-Unique'
    cmmcLevels: 'Baselines'
    cmmcRequirementProfiles: 'Framework'
    cobitRequirementPage: ''
    cobitRequirementView: ''
    cobitRequirementIndexPage: 'COBIT 2019'
    cobitRequirementIndexView: 'Requirements View (PROD)'
    cobitRequirementName: 'Practice ID'
    cobitRequirementDescription: 'Practice Name'
    cobitRequirementLongDescription: 'Practice Description'
    cobitRequirementAdditionalInfo: 'Objective Purpose Statement'
    cobitRequirementIndexName: 'Practice ID'
    cobitCategory: '(F) Domain'
    cobitTopic: ''
    cobitSubCategory: 'Area'
    cobitLevels: ''
    rbacExtendedRoles: 'appvMqc0cKRSgWlaB'
    controlRisks: 'Risk Library'
    controlTBD: 'Status'
    controlCode: 'Code'
    controlName: 'Name'
    controlDescription: 'Description'
    controlActivity: 'Activities'
    controlQuestion: 'Question'
    controlDomain: 'Control Section [REPLACE with DOMAIN]'
    controlCategory: 'Control Subsection [REPLACE with CATEGORY]'
    controlRequirementssoc2: 'SOC 2'
    controlRequirementshipaa: 'HIPAA (All)'
    controlRequirementsiso27001: 'ISO27001 Requirements'
    controlRequirementsiso27001-annex-a: 'ISO27001 Annex A Controls'
    controlRequirementsiso27001-2022: 'ISO 27001:2022'
    controlRequirementsiso27001-2022-annex-a: 'ISO 27002:2022'
    controlRequirementspci: 'PCI DSS v3.2.1'
    controlRequirementspci4: 'PCI DSS v4.0'
    controlRequirementsgdpr: 'GDPR'
    controlRequirementsccpa: 'CPRA (Statute)'
    controlRequirementsccpa&Reg: 'CPRA Regulations'
    controlRequirementsmssspa: ''
    controlRequirementsnistai: 'NIST AI RMF'
    controlRequirementsnistcsf: 'NIST CSF 1.1'
    controlRequirementsnistcsf2: 'NIST CSF 2.0'
    controlRequirementsnist80053: 'NIST SP 800-53r5'
    controlRequirementscmmc: ''
    controlRequirementsnist800171: 'NIST SP 800-171r2'
    controlRequirementsiso27701: 'ISO 27701:2019'
    controlRequirementscobit: 'COBIT 2019'
    controlRequirementsffiec: ''
    controlRequirementssoxitgc: 'SOX ITGC'
    controlRequirementsccm: 'CSA CCM v4.0.6'
    controlRequirementscyberEssentials: 'Cyber Essentials'
    controlRequirementsiso27017-2015: 'ISO 27017:2015'
    controlRequirementsiso27018-2019: 'ISO 27018:2019'
    controlRequirementsnis2: ''
    drataControls: 'Product View - DO NOT FILTER'

    22RequirementPage: >-
        FedRAMP r5
    22RequirementView: 'Product View'
    22RequirementName: 'ID'
    22SortOrder: 'Sort Order'
    22RequirementDescription: 'Control Name'
    22RequirementLongDescription: ''
    22RequirementAdditionalInfo: ''
    22RequirementAdditionalInfo2: ''
    22RequirementAdditionalInfo3: ''
    22RequirementIndexName: ''
    22Category: 'Category-Unique'
    22Topic: ''
    22SubCategory: 'Subcategory-Unique'
    22Levels: ''

    ffiecRequirementPage: ''
    ffiecRequirementView: ''
    ffiecRequirementIndexPage: 'FFIEC'
    ffiecRequirementIndexView: 'Requirements View (PROD)'
    ffiecRequirementName: 'Code'
    ffiecRequirementDescription: 'Declarative Statement'
    ffiecRequirementLongDescription: 'Declarative Statement'
    ffiecRequirementAdditionalInfo: 'Component'
    ffiecRequirementIndexName: 'Code'
    ffiecCategory: 'Category-Unique'
    ffiecTopic: ''
    ffiecSubCategory: 'Subcategory-Unique'
    ffiecLevels: '(F1) Maturity Level'
    frameworks: 'app8Y1TNgHJEFEss7'
    frameworksPage: 'Frameworks'
    frameworksView: 'Product View'
    frameworksName: 'Product Name'
    frameworksPill: 'Pill Name'
    frameworksTag: 'Tag'
    frameworksPolicies: 'DCF Policies'
    gdprRequirementPage: 'GDPR'
    gdprRequirementView: 'GDPR Reqs'
    gdprRequirementIndexPage: 'GDPR'
    gdprRequirementIndexView: 'Requirements View (PROD)'
    gdprRequirementName: 'Article'
    gdprRequirementDescription: 'Article Title'
    gdprRequirementLongDescription: 'Description'
    gdprRequirementAdditionalInfo: ''
    gdprRequirementIndexName: 'Article'
    gdprCategory: '(F) Chapter'
    gdprTopic: ''
    gdprSubCategory: 'Section'
    gdprLevels: ''
    grid: 'Grid view'
    hipaaRequirementPage: 'HIPAA'
    hipaaRequirementView: 'HIPAA Reqs'
    hipaaRequirementIndexPage: 'HIPAA'
    hipaaRequirementIndexView: 'Requirements View (PROD)'
    hipaaRequirementName: 'Section'
    hipaaRequirementDescription: 'Standard'
    hipaaRequirementLongDescription: 'Implementation'
    hipaaRequirementAdditionalInfo: 'Audit Inquiry'
    hipaaRequirementIndexName: 'Section'
    hipaaCategory: '(F) Rules'
    hipaaTopic: '(sF) Category'
    hipaaSubCategory: ''
    hipaaLevels: ''
    isoRequirementPage: 'ISO27001:2013'
    isoRequirementView: 'Product View'
    isoRequirementIndexPage: 'ISO 27001:2013'
    isoRequirementIndexView: 'Requirements View (PROD)'
    isoRequirementName: 'Code'
    isoRequirementDescription: 'Description'
    isoRequirementLongDescription: 'Actions'
    isoRequirementAdditionalInfo: ''
    isoRequirementIndexName: 'Code'
    iso27001Category: '(F) Section Name'
    iso27001Topic: ''
    iso27001SubCategory: ''
    iso27001Levels: ''
    iso27001-annex-aRequirementPage: 'ISO27002:2013'
    iso27001-annex-aRequirementView: 'ISO27001 Annex A'
    iso27001-annex-aRequirementIndexPage: 'ISO 27002:2013'
    iso27001-annex-aRequirementIndexView: 'Requirements View (PROD)'
    iso27001-annex-aRequirementName: 'Code'
    iso27001-annex-aRequirementDescription: 'Name'
    iso27001-annex-aRequirementLongDescription: 'Description'
    iso27001-annex-aRequirementAdditionalInfo: 'Section Objective'
    iso27001-annex-aRequirementIndexName: 'Control ID'
    iso27001-annex-aCategory: '(F) Area'
    iso27001-annex-aTopic: ''
    iso27001-annex-aSubCategory: 'Section'
    iso27001-annex-aLevels: ''
    iso27001-2022RequirementPage: 'ISO 27001:2022'
    iso27001-2022RequirementView: 'Product View'
    iso27001-2022RequirementName: 'Code'
    iso27001-2022RequirementDisplayName: 'Code'
    iso27001-2022RequirementDescription: 'Description'
    iso27001-2022RequirementLongDescription: 'Actions'
    iso27001-2022RequirementAdditionalInfo: ''
    iso27001-2022RequirementAdditionalInfo2: ''
    iso27001-2022RequirementAdditionalInfo3: ''
    iso27001-2022RequirementIndexName: ''
    iso27001-2022Category: 'Category-Unique'
    iso27001-2022Topic: ''
    iso27001-2022SubCategory: ''
    iso27001-2022Levels: ''
    iso27001-2022-annex-aRequirementPage: 'ISO 27002:2022'
    iso27001-2022-annex-aRequirementView: 'Product View'
    iso27001-2022-annex-aRequirementName: 'Control ID'
    iso27001-2022-annex-aRequirementDisplayName: 'Control ID'
    iso27001-2022-annex-aRequirementDescription: 'Name'
    iso27001-2022-annex-aRequirementLongDescription: 'Control'
    iso27001-2022-annex-aRequirementAdditionalInfo: 'Properties'
    iso27001-2022-annex-aRequirementAdditionalInfo2: ''
    iso27001-2022-annex-aRequirementAdditionalInfo3: ''
    iso27001-2022-annex-aRequirementIndexName: 'Control ID'
    iso27001-2022-annex-aCategory: 'Category-Unique'
    iso27001-2022-annex-aTopic: ''
    iso27001-2022-annex-aSubCategory: ''
    iso27001-2022-annex-aLevels: ''
    iso27701RequirementPage: 'ISO 27701:2019'
    iso27701RequirementView: 'Product View'
    iso27701RequirementIndexPage: 'ISO 27701'
    iso27701RequirementIndexView: 'Requirements View (PROD)'
    iso27701RequirementName: 'Req/Control ID'
    iso27701RequirementDisplayName: 'Req/Control ID'
    iso27701RequirementDescription: 'Clause/Control'
    iso27701RequirementLongDescription: 'Description'
    iso27701RequirementAdditionalInfo: 'Additional Info (Section)'
    iso27701RequirementAdditionalInfo2: ''
    iso27701RequirementAdditionalInfo3: ''
    iso27701RequirementIndexName: 'Control ID'
    iso27701Category: 'Category-Unique'
    iso27701Topic: ''
    iso27701SubCategory: ''
    iso27701Levels: ''
    iso27701RequirementProfiles: 'Framework'
    iso27017-2015RequirementPage: 'ISO 27017:2015'
    iso27017-2015RequirementView: 'Product View'
    iso27017-2015RequirementIndexPage: 'ISO 27017:2015'
    iso27017-2015RequirementIndexView: 'Product View'
    iso27017-2015RequirementName: 'Clause'
    iso27017-2015RequirementDescription: 'Name'
    iso27017-2015RequirementLongDescription: 'Description'
    iso27017-2015RequirementAdditionalInfo: 'ISO 27001 (2013/2022)'
    iso27017-2015RequirementAdditionalInfo2: 'Cloud - Implementation guidance for CSC'
    iso27017-2015RequirementAdditionalInfo3: 'Cloud - Implementation guidance for CSP'
    iso27017-2015RequirementIndexName: 'Clause'
    iso27017-2015Category: 'Area Name'
    iso27017-2015Topic: ''
    iso27017-2015SubCategory: 'Section Name'
    iso27017-2015Levels: ''
    iso27018-2019RequirementPage: 'ISO 27018:2019'
    iso27018-2019RequirementView: 'Product View'
    iso27018-2019RequirementIndexPage: 'ISO 27018:2019'
    iso27018-2019RequirementIndexView: 'Product View'
    iso27018-2019RequirementName: 'Clause'
    iso27018-2019RequirementDescription: 'Name'
    iso27018-2019RequirementLongDescription: 'Description'
    iso27018-2019RequirementAdditionalInfo: 'ISO 27001 (2013/2022)'
    iso27018-2019RequirementAdditionalInfo2: 'PII - Implementation Guidance for CSP'
    iso27018-2019RequirementAdditionalInfo3: ''
    iso27018-2019RequirementIndexName: 'Clause'
    iso27018-2019Category: 'Area Name'
    iso27018-2019Topic: ''
    iso27018-2019SubCategory: 'Section Name'
    iso27018-2019Levels: ''
    masterControls: 'DCF Controls'
    masterTests: 'AP Tests'
    mssspaRequirementPage: 'Microsoft SSPA'
    mssspaRequirementView: ''
    mssspaRequirementIndexPage: 'MS SSPA'
    mssspaRequirementIndexView: 'Grid view'
    mssspaRequirementName: 'Requirement #'
    mssspaRequirementDescription: 'Requirement Description'
    mssspaRequirementLongDescription: 'Requirement Description'
    mssspaRequirementAdditionalInfo: 'Evidence of Compliance'
    mssspaRequirementIndexName: 'Requirement #'
    mssspaCategory: 'Category-Unique'
    mssspaTopic: ''
    mssspaSubCategory: ''
    mssspaLevels: ''
    nis2RequirementPage: 'NIS 2 Cybersecurity Core'
    nis2RequirementView: 'Product View'
    nis2RequirementIndexPage: 'NIS 2 Cybersecurity Core'
    nis2RequirementIndexView: 'Product View'
    nis2RequirementName: 'Article ID'
    nis2RequirementDisplayName: 'Article ID'
    nis2RequirementDescription: 'Article Title'
    nis2RequirementLongDescription: 'Article'
    nis2RequirementAdditionalInfo: ''
    nis2RequirementAdditionalInfo2: ''
    nis2RequirementAdditionalInfo3: ''
    nis2RequirementIndexName: 'Article ID'
    nis2Category: 'Section'
    nis2Topic: ''
    nis2SubCategory: ''
    nis2Levels: ''
    nist800171RequirementPage: 'NIST SP 800-171r2'
    nist800171RequirementView: 'Product View'
    nist800171RequirementIndexPage: 'NIST SP 800-171r2'
    nist800171RequirementIndexView: 'Product View'
    nist800171RequirementName: 'Code'
    nist800171RequirementDisplayName: 'Code'
    nist800171RequirementDescription: 'Requirement Description'
    nist800171RequirementLongDescription: 'Requirement Description'
    nist800171RequirementAdditionalInfo: 'Discussion'
    nist800171RequirementAdditionalInfo2: 'Assessment Objectives'
    nist800171RequirementAdditionalInfo3: 'Basic/Derived Security Requirement'
    nist800171RequirementIndexName: 'Control ID'
    nist800171Category: 'Category-Unique copy'
    nist800171Topic: 'Basic/Derived Security Requirement'
    nist800171SubCategory: 'Subcategory-Unique'
    nist800171Levels: ''
    nist80053RequirementPage: 'NIST SP 800-53r5'
    nist80053RequirementView: 'Grid view'
    nist80053RequirementIndexPage: 'NIST SP 800-53r5'
    nist80053RequirementIndexView: 'Requirements View (PROD)'
    nist80053RequirementName: 'Control ID'
    nist80053RequirementDescription: 'Control Name'
    nist80053RequirementLongDescription: 'Control Text'
    nist80053RequirementAdditionalInfo: 'Control Discussion'
    nist80053RequirementAdditionalInfo2: 'Assessment Objectives (800-53A)'
    nist80053RequirementIndexName: 'Control ID'
    nist80053Category: 'Category-Unique'
    nist80053Topic: 'Privacy-Unique'
    nist80053SubCategory: 'Subcategory-Unique'
    nist80053Levels: '(F1) Control Baseline (DEC2020)'
    nistaiRequirementPage: 'NIST AI RMF'
    nistaiRequirementView: 'Product View'
    nistaiRequirementIndexPage: ''
    nistaiRequirementIndexView: ''
    nistaiRequirementName: 'Title'
    nistaiRequirementDescription: 'Description'
    nistaiRequirementLongDescription: 'Description'
    nistaiRequirementAdditionalInfo: 'Actions'
    nistaiRequirementAdditionalInfo2: 'Documentation'
    nistaiRequirementAdditionalInfo3: 'AI Actors'
    nistaiRequirementIndexName: 'Title'
    nistaiCategory: 'Category-Unique'
    nistaiTopic: ''
    nistaiSubCategory: ''
    nistaiLevels: ''
    nistcsfRequirementPage: 'NIST CSF 1.1'
    nistcsfRequirementView: 'Product View'
    nistcsfRequirementIndexPage: 'NIST CSF 1.1'
    nistcsfRequirementIndexView: 'NIST CSF 1.1'
    nistcsfRequirementName: 'Code'
    nistcsfRequirementDescription: 'Activity'
    nistcsfRequirementLongDescription: 'Activity'
    nistcsfRequirementAdditionalInfo: ''
    nistcsfRequirementAdditionalInfo2: ''
    nistcsfRequirementAdditionalInfo3: ''
    nistcsfRequirementIndexName: 'Subcategory ID'
    nistcsfCategory: 'Category-Unique'
    nistcsfTopic: ''
    nistcsfSubCategory: 'Subcategory-Unique'
    nistcsfLevels: ''
    nistcsf2RequirementPage: 'NIST CSF 2.0'
    nistcsf2RequirementView: 'Product View'
    nistcsf2RequirementIndexPage: 'NIST CSF 2.0'
    nistcsf2RequirementIndexView: 'Product View'
    nistcsf2RequirementName: 'Subcategory ID'
    nistcsf2RequirementDisplayName: 'Subcategory ID'
    nistcsf2RequirementDescription: 'Subcategory Description'
    nistcsf2RequirementLongDescription: 'Implementation Examples'
    nistcsf2RequirementAdditionalInfo: ''
    nistcsf2RequirementAdditionalInfo2: ''
    nistcsf2RequirementAdditionalInfo3: ''
    nistcsf2RequirementIndexName: 'Subcategory ID'
    nistcsf2Category: 'Category-Unique'
    nistcsf2Topic: ''
    nistcsf2SubCategory: 'Subcategory-Unique'
    nistcsf2Levels: ''
    pciRequirementPage: 'PCI DSS v3.2.1'
    pciRequirementView: 'PCI Reqs'
    pciRequirementIndexPage: 'PCI DSS v3.2.1'
    pciRequirementIndexView: 'Requirements View (PROD)'
    pciCategory: '(F) Requirement (Internal)'
    pciRequirementName: 'Code'
    pciRequirementDescription: 'Name'
    pciRequirementLongDescription: 'Description'
    pciRequirementAdditionalInfo: 'Guidance'
    pciRequirementIndexName: 'Code'
    pciTopic: ''
    pciSubCategory: ''
    pciLevels: ''
    pci4RequirementPage: 'PCI DSS v4.0'
    pci4RequirementView: 'Product View'
    pci4RequirementIndexPage: ''
    pci4RequirementIndexView: ''
    pci4Category: 'Category-Unique'
    pci4RequirementName: 'OSCAL ID'
    pci4RequirementDisplayName: 'PCI DSS 4.0 ID'
    pci4RequirementDescription: 'Defined Approach Requirements'
    pci4RequirementLongDescription: ''
    pci4RequirementAdditionalInfo: 'Customized Approach Objective'
    pci4RequirementAdditionalInfo2: 'Applicability Notes'
    pci4RequirementAdditionalInfo3: 'Purpose'
    pci4RequirementIndexName: ''
    pci4Topic: ''
    pci4SubCategory: ''
    pci4Levels: ''
    pci4RequirementProfiles: 'SAQ'
    soc2RequirementPage: 'SOC 2'
    soc2RequirementView: 'Product View'
    soc2RequirementIndexPage: 'SOC 2'
    soc2RequirementIndexView: 'SOC2 Criteria'
    soc2RequirementName: 'Ref. #'
    soc2RequirementDisplayName: 'Ref. #'
    soc2RequirementDescription: 'Criteria'
    soc2RequirementLongDescription: 'Criteria'
    soc2RequirementAdditionalInfo: 'Points of Focus (POF) 2022'
    soc2RequirementAdditionalInfo2: ''
    soc2RequirementAdditionalInfo3: ''
    soc2RequirementIndexName: 'Requirement'
    soc2Category: 'Control Area Name'
    soc2Topic: 'TSC'
    soc2SubCategory: ''
    riskLibrary: 'Risk Library'
    riskLibraryId: 'ID'
    riskLibraryTitle: 'Title'
    riskLibraryDescription: 'Description'
    riskLibraryWizardCore: 'Wizard Core'
    riskLibraryWizardGroup: 'Wizard Group'
    riskLibraryCategory: 'Category'
    riskManagementBaseTableId: 'tblSqBHcWOQbivygL'
    riskManagmentViewName: 'Risk Library (PROD)'
    controlsBaseTableId: 'tblKKMmKxbm33T5Bd'
    controlsViewId: 'viwtZy8hQPWJQTa6X'
    rbacRolesTableId: 'tblCaV6KGAFDMHFus'
    rbacRolesViewId: 'viwPOLYd3xP8rfRen'
    rbacSubjectTableId: 'tbl5MKDcaG8pmzfLQ'
    rbacRolesSubjectViewId: 'viw3DXxUJNIStYP6e'
    soc2Levels: ''
    soxitgcRequirementPage: ''
    soxitgcRequirementView: ''
    soxitgcRequirementIndexPage: 'SOX ITGC'
    soxitgcRequirementIndexView: 'Requirements View (Prod)'
    soxitgcRequirementName: 'Requirement ID'
    soxitgcRequirementDescription: 'Requirement Objective'
    soxitgcRequirementLongDescription: 'Activity Test'
    soxitgcRequirementAdditionalInfo: 'Control Objective Description'
    soxitgcRequirementIndexName: 'Requirement ID'
    soxitgcCategory: 'Calculation'
    soxitgcTopic: ''
    soxitgcSubCategory: 'COSO Component'
    soxitgcLevels: ''
    policyPage: 'DCF Policies'
    policyView: 'Product View'
    policyName: 'Policy'
    policyControls: 'Related DCF Controls'
    evidenceTemplates: 'Readiness Evidence Guidance DFH [DO NOT EDIT]'
    # change evidence template name once airtable is done
    evidenceTemplateView: 'Grid view'
    evidenceTemplateCode: 'Template Code'
    evidenceStatus: 'Status'
    evidenceName: 'Short Name'
    evidenceDescription: 'Request Description'
    evidenceImplementationGuidance: 'Additional Guidance'
    evidenceControls: 'DCF Control [DCF 2.0 RECORDS]'

amply:
    key: 'CHANGEME'
    ipUuid1: 'adfdcf9d-7d86-4be4-8ab7-fa2afb8e7e40'
    ipUuid2: '7c8c1365-edd8-434f-b6cc-be5ca92aae7c'
    timeout: 15000
    template:
        main: 'd18804c1-5715-4e50-b206-e01bebcd82d6'
        mainV2: '21315cc4-199b-45ca-8f0d-b647aed544d5'
        mainV3: 'mainV3'
        accessReview: 'accessReview'
        controlStatus: 'd1a1b079-5dd1-4cdc-93ba-faba98aaf058'
        annualComplianceRenewalTasks: '283edf93-8239-4cd8-bd59-7c987ceaf1f7'
        trustCenter: 'f3a80ab8-607e-4c0f-945c-************'
        controlTestError: 'b844c600-8d60-47d5-94a5-5a5b8e31d67e'
        vendorScheduledQuestionnairesMonthlyDigest: 'vendorScheduledQuestionnairesMonthlyDigest'
        questionnaireSend: 'questionnaireSend'

api:
    name: '[LOCAL] Drata'
    version: '0.0.1'
    defaultStrategy: 'key'
    trustCenterPartnerStrategy: 'tc-partner-key'
    auditorApiStrategy: 'auditor-api-strategy'
    type: 'internal'
    httpTimeout: 240000 # 4 minutes in milliseconds
    jsonLogging: true
    autopilotEnabled: false
    autopilotTenantBatchSize: 5
    autopilotAuroraTenantBatchSize: 18
    autopilotGroupedTaskWait: 3000 # 3 seconds
    autopilotTestMaxNumberOfHoursInTesting: 24
    eventTrackingDefaultUsername: 'Autopilot'
    eventTrackingPublicApiUsername: 'Evidence API'
    eventTrackingVendorQuestionnaireUsername: 'Vendor Questionnaire Response'
    eventTrackingScheduledUsername: 'Scheduled'
    eventTrackingDrataPolicyUsername: 'Drata Policy Automation'
    synchronizationsBatchSize: 1
    synchronizationsWaitTime: 1000 # in milliseconds
    autoLogging: true # set to false to disable the automatic `request completed` and `request errored` logging
    autoLoggingReq: true # set to false to suppress `req` logging
    autoLoggingContext: true # set to false to suppress `context` logging
    connectionMetricsLogging: false
    requestLogging: false # set to true to enable simple HTTP Request logging of METHOD /path (EG: GET /connections)
    reqCustomProps: true # set to false to suppress `loggingContext` logging: { domain, organizationalName, traceId }
    pinoLogLevel: 'info' # info is the same level as log
    logLevel: 'log'
    logRedactions: # list of redacted log values
        - 'req.headers.authorization'
        - 'req.headers.cookie'
        - 'req.headers["x-api-proxy-allow"]'
        - 'res.headers["set-cookie"]'
        - 'privateKey'
        - 'secret'
        - 'client.privateKey'
        - 'client.secret'
        - 'identifier.client.privateKey'
        - 'identifier.client.secret'
    polloRedactions: # list of redacted pollo values
        - 'metadata'
        - 'connection.metadata'
        - 'privateKey'
        - 'client.privateKey'
        - 'client.secret'
        - 'identifier.client.privateKey'
        - 'identifier.client.secret'
    tempDirectory: '/tmp'
    # this will add a prefix to all routes, i.e. 'v1' will yield '/v1/health/ping'
    routePrefix: ''
    codePrefix: 'dcf'
    userAgent: 'Dratabot (+https://dratabot.com)'
    simulateProdIdentitySync: false
    firstAdmin: 1
    testDisabledMessage: 'Backfilled tests set to Disabled by default'
    policyArchiveMessage: 'Associated Policy Archived'
    policyTestDisabledMessage: 'All Drata policies were disabled'
    publicApiKey:
        expiration: 30 # number of days
        user:
            lastName: '- Public API Key'
        email:
            prefix: 'public_api_key_'
            domain: 'drata-public-api.com'
    enableMtls: false
    pathPrefix: 'dist'
    promiseTaskBatchLimit: 500
    invalidDomains:
        domains:
            - 'localhost'
            - '127.0.0.1'
        startsWith:
            - '10'
    policy:
        legalDisclaimerLimit: 1000
    supportUser:
        firstName: 'Drata'
        lastName: 'Support'
        email: '<EMAIL>'
        avatar: 'https://cdn.drata.com/icon/icon_fwhite_bblue_72-circle.png'
    # NOTE: If updating the usaRestrictedTenantImpersonation list, ensure this list is
    # also updated in the `accountIsUSRestrictedTenant` method in `accounts.helpers.js`
    # within the Site Admin app. This keeps the UI in sync.
    usaRestrictedTenantImpersonation: ['ursamajor.com']

appUri:
    download: 'download/%s/%d'

apideck:
    enabled: true
    api:
        baseUrl: 'https://unify.apideck.com'
        vault:
            url: 'https://unify.apideck.com/vault'
            consumers: '/consumers'
            revoke: '/revoke'
            sessions: '/sessions'
            connections: '/connections'
            fileStorage: '/connections/file-storage'
        fileStorage:
            files: '/file-storage/files'
            drives: '/file-storage/drives'
        proxy: '/proxy'
        key: 'CHANGEME'
        downstreamUrlForFileLabels:
            googleDrive: 'https://www.googleapis.com/drive/v3/files'
        downstreamUrlForLabelsListByProvider:
            googleDrive: 'https://drivelabels.googleapis.com/v2/labels?view=LABEL_VIEW_FULL'
    oauth:
        redirect: 'http://localhost:5000/callback/cloud'
    app:
        redirect:
            uri: 'https://integration-oauth.drata.com'
        id: 'CHANGEME'

archive:
    contentType: 'application/zip'
    format: 'zip'
    subFolder: 'archives'

asana:
    api:
        url: 'https://app.asana.com/api/1.0'
        limit: 50
        endpoints:
            attachments: '/attachments'
            stories: '/tasks/%s/stories' #GET /tasks/%s/stories
            tags: '/tags'
            tasksByTag: '/tags/%s/tasks' # GET /tags/{tag_gid}/tasks
            tasks: '/tasks'
            task: '/tasks/%s' # GET /tasks/{task_gid}
            workspaces: '/workspaces'
            users: '/users'
            subtasks: '/tasks/%s/subtasks' # POST /tasks/{task_gid}/subtasks
            projects: '/projects'
            project: '/projects/%s'
            customFields: '/projects/%s/custom_field_settings'
    oauth2:
        key: '1200007995506594' # NON-PROD Asana App
        secret: 'CHANGEME'
        tokenUrl: 'https://app.asana.com/-/oauth_token'
        redirect: 'http://localhost:5000/callback/asana'
        accessToken:
            grantType: 'authorization_code'
        refreshToken:
            grantType: 'refresh_token'

asset:
    defaultWorkstationName: 'Workstation'

mongoDbAtlas:
    api:
        url: 'https://cloud.mongodb.com/api/atlas/v1.0'
        endpoints:
            orgs: 'orgs'
            users: 'orgs/%s/users'
            groups: 'groups'
            clusters: 'groups/%s/clusters'
            accessList: 'groups/%s/accessList'
            backups: 'groups/%s/clusters/%s/backup/schedule'
    url: 'https://cloud.mongodb.com'

auth0:
    api:
        endpoints:
            accounts: '/api/v2/tenants/settings'
            users: '/api/v2/users'
            userPermissions: '/api/v2/users/%s/permissions' #/api/v2/users/{user_id}}/permissions
            userRoles: '/api/v2/users/%s/roles'
    oauth2:
        key: CHANGEME
        secret: 'CHANGEME'
        redirect: 'http://localhost:5000/callback/auth0'
        tokenUrl: '/oauth/token'
        accessToken:
            grantType: 'authorization_code'
        refreshToken:
            grantType: 'refresh_token'

pagerDuty:
    api:
        url: 'https://api.pagerduty.com'
        endpoints:
            accounts: '/abilities'
            users: '/users'
            userPermissions: ''

autopilot1:
    db:
        connectionLimit: 10 # TBD, change later

autopilot2:
    api:
        enabled: true
    schedule:
        defaultGlobalTime: '18:00'
    customTestBaseId: 90000
    pagination:
        max: 50

atlassian:
    api:
        jiraUrl: 'https://api.atlassian.com/ex/jira/%s/rest/api/3'
        user: '/user?accountId='
        users: '/user/search'
        expandRoles: '&expand=groups,applicationRoles'
        serviceInfo: '/serverInfo'
    oauth2:
        key: CHANGEME
        secret: CHANGEME
        redirect: 'http://localhost:5000/callback/atlassian'
        tokenUrl: 'https://auth.atlassian.com/oauth/token'
        resourcesUrl: 'https://api.atlassian.com/oauth/token/accessible-resources'
        accessToken:
            grantType: 'authorization_code'
        refreshToken:
            grantType: 'refresh_token'

aws:
    accountId: '************'
    region:
        default: 'us-east-1'
        sts: 'us-east-1'
    localAws:
        enabled: false
        url: http://localhost:4567
    eventbridge:
        enabled: false
        region: 'us-west-2'
    inspector:
        roleSessionName: 'DrataAutopilotAccountInspector'
        maxUpdateRecords: 200
        maxParallelizedResultsToUpdate: 10 # AWS Inspector only allows 10 findings ARNS per request
    s3:
        expires: 60
        extendedExpires: 600
        appBucket: 'dt-dev-app-us-west-2'
        cdnBucket: 'dt-dev-cdn-us-west-2'
        eventBucket: 'dt-dev-event-us-west-2'
        autopilotBucket: 'dt-dev-autopilot-data-us-west-2'
        region: 'us-west-2'
        uploadPartSize: 5242880
        requestHandler:
            timeout: 120000
            connectionTimeout: 40000
        eventMaxSockets: 75
    autopilot:
        arnTemplate: 'arn:aws:iam::%s:role/DrataAutopilotRole'
        roleName: 'DrataAutopilotRole'
        roleSessionName: 'DrataAutopilotAccountProtection'
        arnPrincipalTemplate: 'arn:aws:iam::%s:root' # PROD=************
    quickLaunch:
        roleArn: 'arn:aws:iam::************:role/dt-bd-api-green-assumable-role'
        roleSessionName: 'DrataQuickLaunchRole'
        deploymentApiUrl: 'https://deployment-marketplace.%s.amazonaws.com/catalogs/AWSMarketplace/products/%s/deployment-parameters'
        deploymentSecretsTimeToLive: 30 # days
    codecommit:
        roleName: 'DrataCodeCommitRole'
        roleSessionName: 'DrataAutopilotAccountCodeCommit'
        defaultRegion: 'us-west-2'
        adminActions:
            - 'codecommit:UpdatePullRequestApprovalRuleContent'
            - 'codecommit:DeletePullRequestApprovalRule'
            - 'codecommit:UpdatePullRequestApprovalState'
            - 'codecommit:UpdateRepositoryName'
            - 'codecommit:AssociateApprovalRuleTemplateWithRepository'
            - 'codecommit:BatchAssociateApprovalRuleTemplateWithRepositories'
            - 'codecommit:CreateRepository'
            - 'codecommit:OverridePullRequestApprovalRules'
            - 'codecommit:CreatePullRequestApprovalRule'
            - 'codecommit:UpdateDefaultBranch'
            - 'codecommit:PutRepositoryTriggers'
            - 'codecommit:DeleteRepository'
            - 'codecommit:BatchDisassociateApprovalRuleTemplateFromRepositories'
            - 'codecommit:DisassociateApprovalRuleTemplateFromRepository'
            - 'codecommit:CreateApprovalRuleTemplate'
            - 'codecommit:UpdateApprovalRuleTemplateName'
            - 'codecommit:DeleteApprovalRuleTemplate'
            - 'codecommit:UpdateApprovalRuleTemplateContent'
            - 'codecommit:GitPush'
            - 'codecommit:PutFile'
            - 'codecommit:CreateCommit'
            - 'codecommit:MergePullRequestByThreeWay'
            - 'codecommit:UpdateRepositoryDescription'
            - 'codecommit:CreatePullRequest'
            - 'codecommit:MergePullRequestBySquash'
            - 'codecommit:UpdatePullRequestStatus'
            - 'codecommit:UploadArchive'
            - 'codecommit:MergeBranchesByFastForward'
            - 'codecommit:MergePullRequestByFastForward'
            - 'codecommit:MergeBranchesBySquash'
            - 'codecommit:MergeBranchesByThreeWay'
            - 'codecommit:DeleteFile'
            - 'codecommit:DeleteBranch'
        writeActions:
            - 'codecommit:GitPush'
            - 'codecommit:PutFile'
            - 'codecommit:CreateCommit'
            - 'codecommit:MergePullRequestByThreeWay'
            - 'codecommit:UpdateRepositoryDescription'
            - 'codecommit:MergePullRequestBySquash'
            - 'codecommit:UploadArchive'
            - 'codecommit:MergeBranchesByFastForward'
            - 'codecommit:MergePullRequestByFastForward'
            - 'codecommit:CreateUnreferencedMergeCommit'
            - 'codecommit:MergeBranchesBySquash'
            - 'codecommit:MergeBranchesByThreeWay'
            - 'codecommit:DeleteFile'
            - 'codecommit:DeleteBranch'
    url:
        commercial: 'https://console.aws.amazon.com'
        government: 'https://console.amazonaws-us-gov.com'
    externalId: 'drata'
    iamDataReport: 'iam-data.json'
    runner:
        enableExecuteCommand: false
        launchType: 'FARGATE'
        subnets: ['CHANGEME']
        securityGroups: 'CHANGEME'
        assignPublicIp: 'DISABLED'
        commands:
            autopilot:
                [
                    './bin/drata-cli',
                    'autopilot',
                    '-d %s',
                    '-t %s',
                    '-p %s',
                    '--run-id %s',
                    '--async',
                    '--tenant-db %s',
                    '--tenant-batch-size %s',
                    '--aurora-tenant-batch-size %s',
                ]
            synchronizations: ['./bin/drata-cli', 'syncs', '-d %s']
            companyNotifications: ['./bin/drata-cli', 'notifications:company', '-d %s']

        name: 'CHANGEME'
        region: 'CHANGEME'
    cloudFrontDistributionsMaxResults: 1000
    ec2InstanceMaxResults: 1000
    ecrRepositoryMaxResults: 1000
    eksClusterMaxResults: 100
    rdsInstanceMaxResults: 100
    ecsClusterMaxResults: 100
    ecsTaskMaxResults: 100
    wafMaxResults: 100
    buckets:
        maxResults: 100
    orgUnits:
        listAccountsPageSize: 20
        subAccountsBatchSize: 5 # the amount of sub accounts to run in parallel
        subAccountsBatchSizeForAssetSync: 5 # the amount of sub accounts to run in parallel for asset sync
        regionsSubAccountsBatchSize: 5 # the amount of regions to run in parallel on subaccounts
        addSubAccountsBatchSize: 5 # the amount of sub accounts to add in parallel
        maxSubAccountDefaultLimit: 350 # the default max amount of subAccounts Drata will process
        listAccountParentBatchSize: 8 # the amount of parent accounts to fetch at the same time
        describeOrganizationalUnitBatchSize: 3 # the amount of org units description to fetch at the same time
        batchThrottleDelayMs: 1000
    acm:
        region: us-west-2
    govcloud:
        key: 'CHANGEME'
        secret: 'CHANGEME'
        partition: 'aws-us-gov'
        region:
            default: us-gov-west-1
        intermediate:
            role:
                arn: 'CHANGEME'
                name: drata-intermediate-govcloud-api
        role:
            name: drata-govcloud-api
    concurrency:
        usersBatchSize: 10 # the amount of users to fetch at the same time

azureGCCHigh:
    oauth2:
        redirect: 'callback/azure'
        authRedirect: 'signin/microsoft/callback'
        tokenUrl: 'https://login.microsoftonline.us'
        scopes:
            - 'https://management.usgovcloudapi.net/.default'
    url: 'https://portal.azure.us'
    api:
        url: 'https://management.usgovcloudapi.net'
        endpoints:
            resources: 'subscriptions/%s/resources'
    config:
        assetResults: 250
        photoSize: 48
        subscriptionsDefaultLimit: 500
    azureManagementId: 'de18dc9b-667f-45e2-9937-2068b5edc0df'

azure:
    oauth2:
        redirect: 'callback/azure'
        authRedirect: 'signin/microsoft/callback'
        tokenUrl: 'https://login.microsoftonline.com'
        scopes:
            - 'https://management.azure.com/.default'
    url: 'https://portal.azure.com'
    api:
        url: 'https://management.azure.com'
        endpoints:
            resources: 'subscriptions/%s/resources'
    config:
        assetResults: 250
        photoSize: 48
        subscriptionsDefaultLimit: 500 # the amount of subscriptions Drata will support

azureDevops:
    boards:
        oauth2:
            read:
                secret: 'CHANGEME'
            write:
                secret: 'CHANGEME'
            tokenUrl: 'https://app.vssps.visualstudio.com/oauth2/token'
            redirect: 'https://localhost:5000/callback/azuredevops-boards'
            accessToken:
                grantType: 'urn:ietf:params:oauth:grant-type:jwt-bearer'
            refreshToken:
                grantType: 'refresh_token'
    repos:
        codeReviewsEnforcedPolicyTypeId: fa4e907d-c16b-4a4c-9dfa-4906e5d171dd
        oauth2:
            secret: 'CHANGEME'
            tokenUrl: 'https://app.vssps.visualstudio.com/oauth2/token'
            redirect: 'https://localhost:5000/callback/azuredevops-repos'
            accessToken:
                grantType: 'urn:ietf:params:oauth:grant-type:jwt-bearer'
            refreshToken:
                grantType: 'refresh_token'
    devops:
        oauth2:
            secret: 'CHANGEME'
            tokenUrl: 'https://app.vssps.visualstudio.com/oauth2/token'
            redirect: 'https://localhost:5000/callback/azuredevops'
            accessToken:
                grantType: 'urn:ietf:params:oauth:grant-type:jwt-bearer'
            refreshToken:
                grantType: 'refresh_token'
    api:
        url:
            app: 'https://app.vssps.visualstudio.com/_apis'
            dev: 'https://vssps.dev.azure.com/%s/_apis'
            codebase: 'https://dev.azure.com'
            baseUrl: 'https://%s.azure.com'
            defaultSubdomain: 'vsaex.dev'
            worksItemBordsDetail: 'https://dev.azure.com/%s/%s/_workitems/edit/%s'
        apiVersion: '6.1-preview.1'
        workItemListMaxResults: 200
        endpoints:
            accessControlLists: '%s/_apis/accesscontrollists/%s?api-version=%s'
            accessControlListsByToken: '%s/_apis/accesscontrollists/%s?token=%s&api-version=%s'
            accountList: 'accounts?memberId=%s&api-version=%s'
            createWorkItem: '%s/%s/_apis/wit/workitems/$%s?api-version=7.1'
            paginatedProjectList: '%s/_apis/projects?$top=%s&$skip=%s&api-version=%s'
            projectList: '%s/_apis/projects?api-version=%s'
            repoList: '%s/%s/_apis/git/repositories?api-version=%s'
            repoPoliciesList: '/%s/%s/_apis/git/policy/configurations?repositoryId=%s&refName=%s&api-version=%s'
            selfProfile: 'profile/profiles/me?api-version=%s'
            tagList: '%s/%s/_apis/wit/tags?api-version=7.1-preview.1'
            teamList: '%s/_apis/projects/%s/teams?api-version=%s'
            userEntitlementsList: '%s/_apis/userentitlements?api-version=%s&top=%s&skip=%s'
            userList: 'graph/users?api-version=%s'
            usersSearch: 'graph/subjectquery?api-version=7.1-preview.1'
            workItem: '%s/%s/_apis/wit/workitems/%s?$expand=relations&api-version=7.1'
            workItemClassifications: '%s/%s/_apis/wit/classificationnodes/%s?$depth=6&api-version=7.1'
            workItemComments: '%s/%s/_apis/wit/workItems/%s/comments?api-version=7.1-preview.4'
            workitemDetailsList: '%s/%s/_apis/wit/workitems?ids=%s&expand=all&api-version=5.1'
            workItemList: '%s/%s/_apis/wit/workitems?ids=%s&api-version=6.0'
            workItemReferences: '%s/_apis/wit/wiql?api-version=6.1-preview.2'
            workItemStates: '%s/%s/_apis/wit/workitemtypes/%s/states?api-version=7.1'
            workItemTypeFields: '%s/%s/_apis/wit/workitemtypes/%s/fields?$expand=All&api-version=7.1'
            workItemTypes: '%s/%s/_apis/wit/workitemtypes?api-version=6.1-preview.2'

bitbucket:
    api:
        url: 'https://api.bitbucket.org'
        endpoints:
            workspaces: '/2.0/workspaces'
            members: '/2.0/workspaces/%s/members'
            collaborators: '/2.0/workspaces/%s/permissions/repositories/%s'
            repositories: '/2.0/repositories/%s/'
            pullrequests: '/2.0/repositories/%s/pullrequests'
            branchRestrictions: '/2.0/repositories/%s/%s/branch-restrictions'
            groups: '/1.0/groups/%s/%s/members' # This endpoint has been deprecated by BitBucket but has not yet been replaced - https://support.atlassian.com/bitbucket-cloud/docs/groups-endpoint/
            workspacePermissions: '/2.0/user/permissions/workspaces'
            repositoryPermissions: '/2.0/user/permissions/repositories'
            branches: '/2.0/repositories/%s/%s/refs/branches'
            branch: '/2.0/repositories/%s/%s/refs/branches/%s'
    oauth2:
        key: 'PbX96nuThu9MQUTfcz'
        secret: 'CHANGEME'
        redirect: 'http://localhost:5000/callback/bitbucket'
        tokenUrl: 'https://bitbucket.org/site/oauth2/access_token'
        accessToken:
            grantType: 'authorization_code'
        refreshToken:
            grantType: 'refresh_token'
    code:
        oauth2:
            key: 'at2z7tvuGGRVJU3TqV'
            secret: 'CHANGEME'
            redirect: 'http://localhost:5000/callback/bitbucket-code'
            tokenUrl: 'https://bitbucket.org/site/oauth2/access_token'
            accessToken:
                grantType: 'authorization_code'
            refreshToken:
                grantType: 'refresh_token'

cache:
    enabled: true
    globalPrefix: 'globalstore'
    ttl:
        min: 60
        10min: 600
        15min: 900
        hour: 3600
        day: 86400
        week: 604800
    superBusterChunkSize: 3
    entryBusterChunkSize: 300
    log:
        enabled: false

certn:
    api:
        url: 'https://demo-api.certn.co'
        endpoints:
            teams: api/v2/teams
            hrApplicant: hr/v1/applications/invite/
            viewApplicant: hr/v1/applicants/%s/
            listApplicant: hr/v1/applicants/
            questionare: api/v2/teams/%s/employer/reference_templates
        webhookHeader: 'certn-signature'
    display:
        url: 'https://whitelabel.certn.co/hr/applications/'

checkr:
    api:
        url: 'https://api.checkr-staging.com/v1'
        webhookHeader: 'x-checkr-signature'
        nodeEndPointLimit: 99
    display:
        url: 'https://dashboard.checkrhq-staging.net/candidates'
    # staging account
    oauth2:
        key: '575a7a76c4e81b13df4ff885'
        secret: 'CHANGEME'
        redirect: '/callback/checkr'
        tokenUrl: 'https://api.checkr-staging.com/oauth/tokens'
        accessToken:
            grantType: 'authorization_code'
    userAccessReview:
        baseUrl: dashboard.checkrhq-staging.net

logoDev:
    logoUrl: 'https://img.logo.dev/'
    format: 'png'
    size: '256'
    retina: 'true'
    token: 'pk_FSzVxLECQMiGB7EWsXo74w' # This key is safe to be shared publicly and used only with our image APIs.

clickup:
    api:
        url: 'https://api.clickup.com/api/v2/'
        endpoints:
            team: 'team'
            task: 'task/%s/' # task_id
            search: 'team/%s/task' # team/team_id/task
            user: 'team/%s/user/%s' # team_id, user_id
            space: 'team/%s/space' #team_id
            folderlessList: 'space/%s/list' #space_id
            folder: 'space/%s/folder' #space_id
            folderList: 'folder/%s/list' #folder_id
            createTask: 'list/%s/task' #list_id
            getTasks: 'list/%s/task' #list_id
            taskTypes: '/team/%s/custom_item' #team_id
            listMembers: 'list/%s/member' #list_id
            customFields: 'list/%s/field' #list_id
            setCustomField: 'task/%s/field/%s' #task_id, #field_id
            getTaskComments: 'task/%s/comment' #task_id
    oauth2:
        key: 'BEI475CV9Z4B9SKJ8Y62L0UA8OAU2QXX'
        secret: 'CHANGEME'
        redirect: 'http://localhost:5000/callback/clickup'
        tokenUrl: 'https://api.clickup.com/api/v2/oauth/token'
        accessToken:
            grantType: 'authorization_code'
            # expiresIn: ********* # seconds in 20 years

cloudflare:
    api:
        url: 'https://api.cloudflare.com/client/v4'
        endpoints:
            accounts: 'accounts'
            members: 'accounts/%s/members'
            zones: 'zones?status=active&account.id=%s&page=%s&per_page=50'
            zoneWaf: 'zones/%s/settings/waf'
            zoneEntrypointRuleset: 'zones/%s/rulesets/phases/http_request_firewall_managed/entrypoint'
    url: 'https://dash.cloudflare.com/login/'

confluence:
    oauth2:
        key: MWM9DOSyl4ZZtKpmv2R1Ed7bmqZsqLIA
        secret: CHANGEME
        tokenUrl: https://auth.atlassian.com/oauth/token
        redirect: http://localhost:5000/callback/confluence
        accessToken:
            grantType: authorization_code
        refreshToken:
            grantType: refresh_token
    api:
        baseUrl: https://api.atlassian.com
        cloudIdUri: /oauth/token/accessible-resources
        basePagesUri: /ex/confluence/%s
        pagesUri: /wiki/api/v2/pages?limit=%s
        parentUri: /wiki/api/v2/pages?id=%s
        searchUri: /wiki/rest/api/search?&expand=content.history,content.version,content.ancestors,content.space&cql=(type="page" and space.type!="personal" and (title~"%s*" OR title~"%s" OR title~"%s*"))&limit=%s
        searchUriSynchronTenant: /wiki/rest/api/search?&expand=content.history,content.version,content.ancestors,content.space&cql=(space.type!="personal" and (title~"%s*" OR title~"%s" OR title~"%s*"))&limit=%s
        searchUriNonPageIssuesTenants: ['syncron.com', 'globalvision.co']
        pageUri: /ex/confluence/%s/wiki/api/v2/pages/%s?body-format=styled_view
        externalFiles: /ex/confluence/%s/wiki/api/v2/pages?id=%s
        spacesUri: /ex/confluence/%s/wiki/api/v2/spaces?ids=%s
        pageVersion: /ex/confluence/%s/wiki/api/v2/pages/%s?version=%s&body-format=styled_view
        diffBase: /pages/diffpagesbyversion.action?pageId=%s&selectedPageVersions=%s&selectedPageVersions=%s

contentTokens:
    companyName: '\[COMPANY_NAME\]'

controls:
    maxOwners: 50
    maxRequirements: 5

convertApi:
    enabled: true
    secret: 'CHANGEME'
    ownerPassword: 'CHANGEME'

courier:
    api:
        key: 'CHANGEME'
    template:
        controls: '4M6ZGGA88XMTCGG9M9F3TVRPK3QY'
        personnel: 'F3X24HQ1N04GRAG53ZR0XE815WYC'
        personnelDirectMessage: 'F34HJQETQJ4TT9HSTQBZJGK7894P'
        tests: '8MSNP7P8F2MXM5K9EWV5WA6HMTKH'
        controlApprovals: '5EXGAK8SRW4HTQMPSADP6FJPF4JA'
        controlEvidenceUpdates: 'MZ5KRXQNK449TEKQ5P36QY8DTE3F'
        exceptionManagementSubmission: '6VJ6SBY3D64SPRNZFJEHWJ0RD2MF'
        workflowNotifications: '4FE38SMHT8M2C1QE2F7ZJ5NMZ58S'
        workflowUpdatesAndErrorsNotifications: 'AXXEC08FSMM6PVMFBF9PM85HYSDP'
        workflowDeleteNotifications: '2WD1EQEMJV480VQW1B9V4D9KACHQ'

crowdStrike:
    config:
        paginationLimit: 100
        sortBy: 'device_id.desc'
    api:
        endpoints:
            devicesList: 'devices/queries/devices/v1'
            devicesListByToken: 'devices/queries/devices-scroll/v1'
            deviceDetails: 'devices/entities/devices/v2'
            deviceStates: 'devices/entities/online-state/v1'

    oauth2:
        key: 'CHANGEME'
        endpoint: oauth2/token
        accessToken:
            grantType: 'authorization_code'
        refreshToken:
            grantType: 'refresh_token'

cspm:
    api:
        maxRiskTypeResults: 200

curricula:
    api:
        url: https://dev.curricula.com/api/v1
        endpoints:
            learners: 'learners'
            assignments: 'accounts/%s/assignments'
            accounts: 'accounts'
            learner-activity: 'assignments/%s/learner-activity'
            certificate: 'assignments/%s/completion-certificate'
    oauth2:
        key: 'oKJzwmRWB1q8P20r5MA94V6jDbyvxGrd'
        secret: 'CHANGEME'
        redirect: 'http://localhost:5000/callback/curricula'
        tokenUrl: 'https://dev.curricula.com/oauth/token'
        accessToken:
            grantType: 'authorization_code'
        refreshToken:
            grantType: 'refresh_token'
        scopes: 'assignments:read learners:read assignments:learner-activity'

customConnections:
    fileName: 'Custom_Evidence'
    fileExtension: '.json'
    maxCustomDataRecordsNumber: 500000 # 500k records overall

jamf:
    api:
        pageSize: 50

coverdash:
    api:
        audience: 'https://staging.coverdash.com'
        authBaseUri: 'https://auth.staging.coverdash.com'
        baseUri: 'https://staging-5.coverdash.com'
        clientId: 'pupZrzhFfYUyHCl8Qqgh7u6yaFpI6hTj'
        clientSecret: 'CHANGEME'

cube:
    api:
        url: 'https://tart-nightingale.aws-us-west-2-t-18712.cubecloudapp.dev/cubejs-api/v1'
        secret: 'CHANGEME'
        jwtExpiration: '30d'

datadog:
    env: 'CHANGEME'
    service: 'CHANGEME'
    region: 'CHANGEME'
    mode: 'CHANGEME'
    enabled: false
    api:
        endpoints:
            notificationProfiles: '%s/api/v2/security_monitoring/configuration/notification_profiles'
        pageSize: 30

db:
    type: 'mysql'
    host: 'CHANGEME'
    username: 'CHANGEME'
    password: 'CHANGEME'
    port: 'CHANGEME'
    databaseName: 'CHANGEME'
    connectionLimit: 10
    charset: 'utf8mb4_unicode_ci'
    booleanLength: 1
    dateTimePrecision: 6
    colorHexCodeLength: 8
    uuidLength: 36
    varcharLength: 191 # 191 * 4 = 764 (IF innodb_large_prefix is OFF the MAX is 767)
    varcharLongLength: 768 # 768 * 4 = 3072 (IF innodb_large_prefix is ON the MAX is 3072)
    dateStringLength: 10
    cliConfigFileName: 'ormconfig.json'
    cliTenantConfigFileName: 'ormconfig-tenant.json'
    cliAllConfigFileName: 'ormconfig-all.json'
    cliDataSourceFileName: 'ormconfig.ts'
    cliGlobalDataSourceFileName: 'ds-global.ts'
    cliTenantDataSourceFileName: 'ds-tenant.ts'
    tenantSuffix: '_tenant'
    tenants: []
    entityManager: 'ENTITY_MANAGER'
    # options: true, false; one or more of ['query', 'error', 'schema', 'warn', 'info', 'log']
    logging: ['error']
    # options: advanced-console, simple-console, file, debug, pollo
    loggingType: 'pollo'
    ssl: null
    maxLongTextAzureId: 1024
    baseAppMigration: '/database/migrations/app/Rebase.sql'

decodable:
    lambda:
        host: 'decodable.int.prod.drata.net'
        version: 'v1'
        method: 'POST'
    enabled: false

digitalocean:
    oauth2:
        key: '****************************************************************'
        secret: 'CHANGEME'
        redirect: 'http://localhost:5000/callback/digitalocean'
        tokenUrl: 'https://cloud.digitalocean.com/v1/oauth/token'
        accessToken:
            grantType: 'authorization_code'
        refreshToken:
            grantType: 'refresh_token'
    publicBucketAclGrantee:
        Grantee:
            Type: 'Group'
            URI: 'http://acs.amazonaws.com/groups/global/AllUsers'
        Permission: 'READ'
    url: 'https://cloud.digitalocean.com'

docraptor:
    domain: 'https://docraptor.com/docs'
    testMode: true
    apiKey: 'CHANGEME'
    ownerPassword: 'CHANGEME'
    status: 'https://docraptor.com/status/'
    asyncPdfTimeout: 600000 # relative to our current vendor docraptor

docs:
    gitbook:
        expiresIn: '8h'
        path: https://docs.drata.com/drata/
        secret: CHANGEME

docusign:
    api:
        clientId: b06fcb28-d7d6-4f65-8575-a924cd49df33
        privateKey: CHANGEME
        server: https://account-d.docusign.com
        secret: CHANGEME
        scopes:
            - signature
            - impersonation
        webhookBaseUrl: https://localhost:3000
        ndaBaseUrl: https://appdemo.docusign.com/documents/details
    userAccessReview:
        baseUrl: admindemo.docusign.com

ecs:
    pollInterval: 30000 # milliseconds
    pollMaxCount: 60 # count

email:
    noReplyEmail: '<EMAIL>'
    supportEmail: '<EMAIL>'
    testingEmail: '<EMAIL>'
    adminEmail: '<EMAIL>'
    trustCenterName: 'Trust Center'
    dailyHours: 14
    maxControls: 3
    maxControlTasks: 3
    failedTestUrl: '/compliance/monitoring?checkResultStatus=FAILED&page=1'
    noEvidenceControlsUrl: '/compliance/controls/inscope?page=1&hasEvidence=false'
    controlTasksViewAllUrl: '/governance/personnel?employmentStatuses%5B0%5D=ALL_FORMER_PERSONNEL&sort=SEPARATION_DATE&sortDir=DESC&page=1'
    controlExpiredViewAllUrl: '/compliance/controls/inscope?isReady=false&page=1&hasEvidence=true&userIds%5B0%5D='
    notificationsUrl: '/admin/settings/notifications'
    urlTimeToLive: 86400 # 1 day in seconds
    maxTargetDisplayNames: 10 # compliance check exclusions email
    denyDomainList:
        - 'dratapartners.com'
        - 'dratademo.com'

fibery:
    api:
        url: https://%s.fibery.io/api/commands
    limit: 100

fixtures:
    frameworkTemplates: 'database/seeds/global/fixtures/framework-templates.yml'
    policyTemplates: 'database/seeds/global/fixtures/policy-templates.yml'
    profileDetailsTemplates: 'database/seeds/global/fixtures/profile-details-templates.yml'
    auditorFrameworkTypeTemplates: 'database/seeds/global/fixtures/auditor-framework-type-templates.yml'
    controlTests: 'database/seeds/global/fixtures/control-tests.yml'
    controlTestPolicies: 'database/seeds/global/fixtures/drata-policies-controls.yml'
    frameworkPolicies: 'database/seeds/global/fixtures/drata-policies-frameworks.yml'
    drataControls: 'database/seeds/global/fixtures/drata-controls.yml'
    soc2Requirements: 'database/seeds/global/fixtures/soc2-requirements.yml'
    soc2RequirementIndexes: 'database/seeds/global/fixtures/soc2-requirement-indexes.yml'
    soc2CatalogJSON: 'database/seeds/global/fixtures/soc2-profiles/soc2Catalog.json'
    soc2AllProfile: 'database/seeds/global/fixtures/soc2-profiles/soc2_All.json'
    isoRequirements: 'database/seeds/global/fixtures/iso27001-requirements.yml'
    isoRequirementIndexes: 'database/seeds/global/fixtures/iso27001-requirement-indexes.yml'
    iso2022RequirementIndexes: 'database/seeds/global/fixtures/iso27001-2022-requirement-indexes.yml'
    iso2022Requirements: 'database/seeds/global/fixtures/iso27001-2022-requirements.yml'
    iso420012023Requirements: 'database/seeds/global/fixtures/iso420012023-requirements.yml'
    iso420012023RequirementIndexes: 'database/seeds/global/fixtures/iso420012023-requirement-indexes.yml'
    hipaaRequirements: 'database/seeds/global/fixtures/hipaa-requirements.yml'
    hipaaRequirementIndexes: 'database/seeds/global/fixtures/hipaa-requirement-indexes.yml'
    hipaaCatalogJSON: 'database/seeds/global/fixtures/hipaa-profiles/hipaaCatalog.json'
    hipaaAllProfile: 'database/seeds/global/fixtures/hipaa-profiles/hipaa_All.json'
    pciRequirements: 'database/seeds/global/fixtures/pci-requirements.yml'
    pciRequirementIndexes: 'database/seeds/global/fixtures/pci-requirement-indexes.yml'
    pci4Requirements: 'database/seeds/global/fixtures/pci4-requirements.yml'
    pci4RequirementIndexes: 'database/seeds/global/fixtures/pci4-requirement-indexes.yml'
    pci4CatalogJSON: 'database/seeds/global/fixtures/pci4-profiles/pci4Catalog.json'
    pci4AepProfile: 'database/seeds/global/fixtures/pci4-profiles/PCI_V4_A-EP.json'
    pci4AProfile: 'database/seeds/global/fixtures/pci4-profiles/PCI_V4_A.json'
    pci4BipProfile: 'database/seeds/global/fixtures/pci4-profiles/PCI_V4_B-IP.json'
    pci4BProfile: 'database/seeds/global/fixtures/pci4-profiles/PCI_V4_B.json'
    pci4CvtProfile: 'database/seeds/global/fixtures/pci4-profiles/PCI_V4_C-VT.json'
    pci4CProfile: 'database/seeds/global/fixtures/pci4-profiles/PCI_V4_C.json'
    pci4DmProfile: 'database/seeds/global/fixtures/pci4-profiles/PCI_V4_D-M.json'
    pci4DspProfile: 'database/seeds/global/fixtures/pci4-profiles/PCI_V4_D-SP.json'
    pci4P2peProfile: 'database/seeds/global/fixtures/pci4-profiles/PCI_V4_P2PE.json'
    pci4SpocProfile: 'database/seeds/global/fixtures/pci4-profiles/PCI_V4_SPoC.json'
    pci4NoneProfile: 'database/seeds/global/fixtures/pci4-profiles/PCI_V4_None.json'
    gdprRequirements: 'database/seeds/global/fixtures/gdpr-requirements.yml'
    gdprRequirementIndexes: 'database/seeds/global/fixtures/gdpr-requirement-indexes.yml'
    ccmRequirements: 'database/seeds/global/fixtures/ccm-requirements.yml'
    ccmRequirementIndexes: 'database/seeds/global/fixtures/ccm-requirement-indexes.yml'
    ccpaRequirements: 'database/seeds/global/fixtures/ccpa-requirements.yml'
    ccpaRequirementIndexes: 'database/seeds/global/fixtures/ccpa-requirement-indexes.yml'
    nistcsfRequirements: 'database/seeds/global/fixtures/nistcsf-requirements.yml'
    nistcsfRequirementIndexes: 'database/seeds/global/fixtures/nistcsf-requirement-indexes.yml'
    nistcsf2Requirements: 'database/seeds/global/fixtures/nistcsf2-requirements.yml'
    nistcsf2RequirementIndexes: 'database/seeds/global/fixtures/nistcsf2-requirement-indexes.yml'
    nist800171Requirements: 'database/seeds/global/fixtures/nist800171-requirements.yml'
    nist800171RequirementIndexes: 'database/seeds/global/fixtures/nist800171-requirement-indexes.yml'
    nist800171r3Requirements: 'database/seeds/global/fixtures/nist800171r3-requirements.yml'
    nist800171r3RequirementIndexes: 'database/seeds/global/fixtures/nist800171r3-requirement-indexes.yml'
    nist800171r3CatalogJSON: 'database/seeds/global/fixtures/nist800171r3-profiles/nist800171r3Catalog.json'
    nist800171r3AllProfile: 'database/seeds/global/fixtures/nist800171r3-profiles/nist800171r3_All.json'
    nist80053Requirements: 'database/seeds/global/fixtures/nist80053-requirements.yml'
    nist80053RequirementIndexes: 'database/seeds/global/fixtures/nist80053-requirement-indexes.yml'
    mssspaRequirements: 'database/seeds/global/fixtures/mssspa-requirements.yml'
    mssspaRequirementIndexes: 'database/seeds/global/fixtures/mssspa-requirement-indexes.yml'
    mssspav11Requirements: 'database/seeds/global/fixtures/mssspav11-requirements.yml'
    mssspav11RequirementIndexes: 'database/seeds/global/fixtures/mssspav11-requirement-indexes.yml'
    trustCenterControlTemplates: 'database/seeds/global/fixtures/trust-pages-control-templates.yml'
    path: 'database/seeds/global/fixtures/'
    iso27701Requirements: 'database/seeds/global/fixtures/iso27701-requirements.yml'
    iso27701RequirementIndexes: 'database/seeds/global/fixtures/iso27701-requirement-indexes.yml'
    iso27701CatalogJSON: 'database/seeds/global/fixtures/iso27701-profiles/iso27701Catalog.json'
    iso27701AllProfile: 'database/seeds/global/fixtures/iso27701-profiles/iso27701_All.json'
    riskManagement: 'database/seeds/global/fixtures/risk-management.yml'
    cmmcRequirements: 'database/seeds/global/fixtures/cmmc-requirements.yml'
    cmmcRequirementIndexes: 'database/seeds/global/fixtures/cmmc-requirement-indexes.yml'
    ffiecRequirements: 'database/seeds/global/fixtures/ffiec-requirements.yml'
    ffiecRequirementIndexes: 'database/seeds/global/fixtures/ffiec-requirement-indexes.yml'
    cobitRequirements: 'database/seeds/global/fixtures/cobit-requirements.yml'
    cobitRequirementIndexes: 'database/seeds/global/fixtures/cobit-requirement-indexes.yml'
    soxitgcRequirements: 'database/seeds/global/fixtures/soxitgc-requirements.yml'
    soxitgcRequirementIndexes: 'database/seeds/global/fixtures/soxitgc-requirement-indexes.yml'
    responsesTypeForm: 'database/seeds/global/fixtures/responses-typeform.json'
    extendedRoles: 'database/seeds/global/fixtures/extended-roles.yml'
    cyberEssentialsRequirements: 'database/seeds/global/fixtures/cyberEssentials-requirements.yml'
    cyberEssentialsRequirementIndexes: 'database/seeds/global/fixtures/cyberEssentials-requirement-indexes.yml'
    iso270172015Requirements: 'database/seeds/global/fixtures/iso27017-2015-requirements.yml'
    iso270172015RequirementIndexes: 'database/seeds/global/fixtures/iso27017-2015-requirement-indexes.yml'
    iso270182019Requirements: 'database/seeds/global/fixtures/iso27018-2019-requirements.yml'
    iso270182019RequirementIndexes: 'database/seeds/global/fixtures/iso27018-2019-requirement-indexes.yml'
    fedrampRequirements: 'database/seeds/global/fixtures/fedramp-requirements.yml'
    fedrampCatalogJSON: 'database/seeds/global/fixtures/fedramp-profiles/Catalog.json'
    fedrampRequirementIndexes: 'database/seeds/global/fixtures/fedramp-requirement-indexes.yml'
    fedrampLow: 'database/seeds/global/fixtures/fedramp-profiles/FedRAMP_rev5_LOW.json'
    fedrampModerate: 'database/seeds/global/fixtures/fedramp-profiles/FedRAMP_rev5_MODERATE.json'
    fedrampHigh: 'database/seeds/global/fixtures/fedramp-profiles/FedRAMP_rev5_HIGH.json'
    fedrampLiSaaS: 'database/seeds/global/fixtures/fedramp-profiles/FedRAMP_rev5_Li-SaaS.json'
    nistaiRequirements: 'database/seeds/global/fixtures/nistai-requirements.yml'
    drataEvidenceTemplates: 'database/seeds/global/fixtures/drata-evidence-template.yml'
    nistaiRequirementIndexes: 'database/seeds/global/fixtures/nistai-requirement-indexes.yml'
    helperPath: 'scripts/recurring/framework/helpers/constants.ts'
    nis2Requirements: 'database/seeds/global/fixtures/nis2-requirements.yml'
    nis2RequirementIndexes: 'database/seeds/global/fixtures/nis2-requirement-indexes.yml'
    doraRequirements: 'database/seeds/global/fixtures/dora-requirements.yml'
    doraRequirementIndexes: 'database/seeds/global/fixtures/dora-requirement-indexes.yml'
    drataEssentialsRequirements: 'database/seeds/global/fixtures/drataEssentials-requirements.yml'
    drataEssentialsRequirementIndexes: 'database/seeds/global/fixtures/drataEssentials-requirement-indexes.yml'
    cis81Requirements: 'database/seeds/global/fixtures/cis81-requirements.yml'
    cis81RequirementIndexes: 'database/seeds/global/fixtures/cis81-requirement-indexes.yml'
    cis81CatalogJSON: 'database/seeds/global/fixtures/cis81-profiles/cis81Catalog.json'
    cis81IG1Profile: 'database/seeds/global/fixtures/cis81-profiles/cis81_IG1.json'
    cis81IG2Profile: 'database/seeds/global/fixtures/cis81-profiles/cis81_IG2.json'
    cis81IG3Profile: 'database/seeds/global/fixtures/cis81-profiles/cis81_IG3.json'
    cyberEssentials32Requirements: 'database/seeds/global/fixtures/cyberEssentials32-requirements.yml'
    cyberEssentials32RequirementIndexes: 'database/seeds/global/fixtures/cyberEssentials32-requirement-indexes.yml'
    fr20xRequirements: 'database/seeds/global/fixtures/fr20x-requirements.yml'
    fr20xRequirementIndexes: 'database/seeds/global/fixtures/fr20x-requirement-indexes.yml'
    hitrustRequirements: 'database/seeds/global/fixtures/hitrust-requirements.yml'
    hitrustRequirementIndexes: 'database/seeds/global/fixtures/hitrust-requirement-indexes.yml'
    cyberEssentials32CatalogJSON: 'database/seeds/global/fixtures/cyberEssentials32-profiles/cyberEssentials32Catalog.json'
    cyberEssentials32AllProfile: 'database/seeds/global/fixtures/cyberEssentials32-profiles/cyberEssentials32_All.json'
    controlUpdatesSync: 'scripts/airtable/control_updates.csv'
    controlAdditionsSync: 'scripts/airtable/control_additions.csv'
    policyUpdatesSync: 'scripts/airtable/policy_updates.csv'
    policyXMLPath: 'scripts/recurring/policy/policy.xml'
    isoCatalogJSON: 'database/seeds/global/fixtures/iso-profiles/isoCatalog.json'
    isoAllProfile: 'database/seeds/global/fixtures/iso-profiles/iso_All.json'
    ccpaCatalogJSON: 'database/seeds/global/fixtures/ccpa-profiles/ccpaCatalog.json'
    ccpaAllProfile: 'database/seeds/global/fixtures/ccpa-profiles/ccpa_All.json'
    gdprCatalogJSON: 'database/seeds/global/fixtures/gdpr-profiles/gdprCatalog.json'
    gdprAllProfile: 'database/seeds/global/fixtures/gdpr-profiles/gdpr_All.json'
    pciCatalogJSON: 'database/seeds/global/fixtures/pci-profiles/pciCatalog.json'
    pciAllProfile: 'database/seeds/global/fixtures/pci-profiles/pci_All.json'
    nistcsfCatalogJSON: 'database/seeds/global/fixtures/nistcsf-profiles/nistcsfCatalog.json'
    nistcsfAllProfile: 'database/seeds/global/fixtures/nistcsf-profiles/nistcsf_All.json'
    cmmcCatalogJSON: 'database/seeds/global/fixtures/cmmc-profiles/cmmcCatalog.json'
    cmmcAllProfile: 'database/seeds/global/fixtures/cmmc-profiles/cmmc_All.json'
    nist800171CatalogJSON: 'database/seeds/global/fixtures/nist800171-profiles/nist800171Catalog.json'
    nist800171AllProfile: 'database/seeds/global/fixtures/nist800171-profiles/nist800171_All.json'
    mssspaCatalogJSON: 'database/seeds/global/fixtures/mssspa-profiles/mssspaCatalog.json'
    mssspaAllProfile: 'database/seeds/global/fixtures/mssspa-profiles/mssspa_All.json'
    ffiecCatalogJSON: 'database/seeds/global/fixtures/ffiec-profiles/ffiecCatalog.json'
    ffiecAllProfile: 'database/seeds/global/fixtures/ffiec-profiles/ffiec_All.json'
    cobitCatalogJSON: 'database/seeds/global/fixtures/cobit-profiles/cobitCatalog.json'
    cobitAllProfile: 'database/seeds/global/fixtures/cobit-profiles/cobit_All.json'
    soxitgcCatalogJSON: 'database/seeds/global/fixtures/soxitgc-profiles/soxitgcCatalog.json'
    soxitgcAllProfile: 'database/seeds/global/fixtures/soxitgc-profiles/soxitgc_All.json'
    iso2022CatalogJSON: 'database/seeds/global/fixtures/iso2022-profiles/iso2022Catalog.json'
    iso2022AllProfile: 'database/seeds/global/fixtures/iso2022-profiles/iso2022_All.json'
    ccmCatalogJSON: 'database/seeds/global/fixtures/ccm-profiles/ccmCatalog.json'
    ccmAllProfile: 'database/seeds/global/fixtures/ccm-profiles/ccm_All.json'
    cyberEssentialsCatalogJSON: 'database/seeds/global/fixtures/cyberEssentials-profiles/cyberEssentialsCatalog.json'
    cyberEssentialsAllProfile: 'database/seeds/global/fixtures/cyberEssentials-profiles/cyberEssentials_All.json'
    iso270172015CatalogJSON: 'database/seeds/global/fixtures/iso270172015-profiles/iso270172015Catalog.json'
    iso270172015AllProfile: 'database/seeds/global/fixtures/iso270172015-profiles/iso270172015_All.json'
    iso270182019CatalogJSON: 'database/seeds/global/fixtures/iso270182019-profiles/iso270182019Catalog.json'
    iso270182019AllProfile: 'database/seeds/global/fixtures/iso270182019-profiles/iso270182019_All.json'
    nistaiCatalogJSON: 'database/seeds/global/fixtures/nistai-profiles/nistaiCatalog.json'
    nistaiAllProfile: 'database/seeds/global/fixtures/nistai-profiles/nistai_All.json'
    nistcsf2CatalogJSON: 'database/seeds/global/fixtures/nistcsf2-profiles/nistcsf2Catalog.json'
    nistcsf2AllProfile: 'database/seeds/global/fixtures/nistcsf2-profiles/nistcsf2_All.json'
    nis2CatalogJSON: 'database/seeds/global/fixtures/nis2-profiles/nis2Catalog.json'
    nis2AllProfile: 'database/seeds/global/fixtures/nis2-profiles/nis2_All.json'
    doraCatalogJSON: 'database/seeds/global/fixtures/dora-profiles/doraCatalog.json'
    doraAllProfile: 'database/seeds/global/fixtures/dora-profiles/dora_All.json'
    drataEssentialsCatalogJSON: 'database/seeds/global/fixtures/drataEssentials-profiles/drataEssentialsCatalog.json'
    drataEssentialsAllProfile: 'database/seeds/global/fixtures/drataEssentials-profiles/drataEssentials_All.json'
    iso420012023CatalogJSON: 'database/seeds/global/fixtures/iso420012023-profiles/iso420012023Catalog.json'
    iso420012023AllProfile: 'database/seeds/global/fixtures/iso420012023-profiles/iso420012023_All.json'
    fr20xCatalogJSON: 'database/seeds/global/fixtures/fr20x-profiles/fr20xCatalog.json'
    fr20xAllProfile: 'database/seeds/global/fixtures/fr20x-profiles/fr20x_All.json'
    hitrustCatalogJSON: 'database/seeds/global/fixtures/hitrust-profiles/hitrustCatalog.json'
    hitrusti1Profile: 'database/seeds/global/fixtures/hitrust-profiles/hitrust_i1.json'
    hitrustr2Profile: 'database/seeds/global/fixtures/hitrust-profiles/hitrust_r2.json'
    hitruste1Profile: 'database/seeds/global/fixtures/hitrust-profiles/hitrust_e1.json'
    mssspav11CatalogJSON: 'database/seeds/global/fixtures/mssspav11-profiles/mssspav11Catalog.json'
    mssspav11AllProfile: 'database/seeds/global/fixtures/mssspav11-profiles/mssspav11_All.json'

fr20xReport:
    schema: 'app/fedramp-20x-ksi-validation-report/schemas/Schema.json'
    schemaWithComments: 'app/fedramp-20x-ksi-validation-report/schemas/Schema.jsonc'

frameworks:
    default:
        color: '045ca6'
        bgColor: 'eaf0f6'
        activeLogo: 'frameworks/soc2-active-logo.png'
        inactiveLogo: 'frameworks/soc2-inactive-logo.png'
    rationale: 'This requirement was marked out of scope automatically by Drata when the requirements baseline level was selected for:'
    pciV4Rationale: 'This requirement was marked out of scope automatically by Drata when the current SAQ type selection was made.'
    soc2Rationale: 'All SOC 2 requirements were marked out of scope automatically by Drata when this framework was enabled, except for those that fall under the Trusted Service Criteria category of ‘Security.’'
    controlReadinessDate: '2025-01-28'

gcp:
    url: 'https://console.cloud.google.com'
    scopes:
        - 'https://www.googleapis.com/auth/cloud-platform'
    groupsScopes:
        - 'https://www.googleapis.com/auth/admin.directory.group.readonly'
    cloudIdentityGroupScopes:
        - 'https://www.googleapis.com/auth/cloud-identity.groups.readonly'
    membersScopes:
        - 'https://www.googleapis.com/auth/admin.directory.group.member.readonly'
    regionsBatchSize: 10 # the amount of projects to fetch at the same time
    assetsPageSize: 500
    resourceToAssetBatchSize: 10 # the amount of resources to transform to asset at the same time

intune:
    oauth2:
        authRedirect: 'signin/microsoft/callback'
        tokenUrl: 'https://login.microsoftonline.com/%s/oauth2/v2.0/token'
        scopes:
            - 'https://graph.microsoft.com/.default'
    device:
        pageSize: 100
        batchLimit: 20

intuneGccHigh:
    oauth2:
        authRedirect: 'signin/microsoft-gcc-high/callback'
        tokenUrl: 'https://login.microsoftonline.us/%s/oauth2/v2.0/token'
        scopes:
            - 'https://graph.microsoft.us/.default'
    graphEndpoint: 'https://graph.microsoft.us'

github:
    versionControl:
        applicationId: 95814 # Drata [ACME] Free Org [DEV/TESTING]
        # applicationId: 117127 # Drata [ACME] Paid Org [DEV/TESTING]
        # applicationId: 1037094 # Drata [ACME] Enterprise Org [DEV/TESTING]
        privateKey: 'CHANGEME'
    issues:
        applicationId: 95568 # Drata [ACME] Free Org [DEV/TESTING]
        # applicationId: 117131 # Drata [ACME] Paid Org [DEV/TESTING]
        privateKey: 'CHANGEME'
    code:
        applicationId: 827563 # Drata [ACME] Free Org [DEV/TESTING]
        privateKey: 'CHANGEME'
    webhook:
        secret: 'CHANGEME'
    api:
        graphql:
            url: 'https://api.github.com/graphql'

gitlab:
    api:
        url: 'https://gitlab.com/api'
        graphql:
            url: 'https://gitlab.com/api/graphql'
        endpoints:
            group: v4/groups/%s
            groups: 'v4/groups'
            groupMembers: 'v4/groups/%s/members'
            groupMembersAll: 'v4/groups/%s/members/all'
            projectMembers: 'v4/projects/%s/members/all'
            user: 'v4/users/%s'
            project: 'v4/projects/%s'
            projects: 'v4/groups/%s/projects'
            projectConfig: 'v4/projects/%s/approvals'
            projectRules: 'v4/projects/%s/approval_rules'
            protectedBranches: 'v4/projects/%s/protected_branches/%s'
            selfToken: 'v4/personal_access_tokens/self'
            search: 'v4/projects/%s/issues'
            labels: 'v4/projects/%s/labels'
            notes: 'v4/projects/%s/issues/%s/notes'
            projectsList: 'v4/projects'
            issue: 'v4/projects/%s/issues/%s'
            issues: 'v4/issues'
            newIssue: 'v4/projects/%s/issues'
            epics: 'v4/groups/%s/epics'
            milestones: 'v4/projects/%s/milestones'
            namespace: 'v4/namespaces/%s'
            emailUsers: 'v4/users/%s'
            metadata: 'v4/metadata'
            applicationSettings: 'v4/application/settings'
            users: 'v4/users'
    oauth2:
        key: '11cfcf45df8daa570f7fb0589a9f3a777f563b074473197056fdca7d64f8cdef'
        secret: 'CHANGEME'
        redirect: 'http://localhost:5000/callback/gitlab'
        tokenUrl: 'https://gitlab.com/oauth/token'
        accessToken:
            grantType: 'authorization_code'
            expiresIn: '7200'
        refreshToken:
            grantType: 'refresh_token'
        scope: 'read_user+read_api'

gitlabIssues:
    oauth2:
        key: '46a844ae33fbc98c37343c86b475bed04a53dfbd723b7969fc4ebb7bae363433'
        secret: 'CHANGEME'
        redirect: 'http://localhost:5000/callback/gitlab-issues'
        tokenUrl: 'https://gitlab.com/oauth/token'
        accessToken:
            grantType: 'authorization_code'
            expiresIn: '7200'
        refreshToken:
            grantType: 'refresh_token'
        scope: 'api'

google:
    privateKey: 'CHANGEME'
    clientEmail: '<EMAIL>'
    scopes:
        - 'https://www.googleapis.com/auth/admin.directory.user.readonly'
    groupsScopes:
        - 'https://www.googleapis.com/auth/admin.directory.group.readonly'
        - 'https://www.googleapis.com/auth/admin.directory.orgunit.readonly'
    domainsScope:
        - 'https://www.googleapis.com/auth/admin.directory.domain.readonly'
    rolesScopes:
        - 'https://www.googleapis.com/auth/admin.directory.rolemanagement.readonly'
    oauth:
        key: '134278802507-0r5i28p1dg4q761c32el5m6l2pclotht.apps.googleusercontent.com'
        secret: 'CHANGEME'
        redirectUri: 'signin/google/callback'
        auditorRedirectUri: 'signin/auditor/google/callback'
        serviceUserRedirectUri: 'signin/service-user/google/callback'
        connectionIdpRedirectUri: 'connections/idp/google/callback'
        connectionUarRedirectUri: 'connections/uar/google/callback'
        connectionIdpUpdateRedirectUri: 'connections/idp/google/update/callback'
        connectionUarUpdateRedirectUri: 'connections/uar/google/update/callback'

gusto:
    api:
        url: 'https://api.gusto-demo.com'
        token: 'CHANGEME'
        createCompanyUri: 'v1/provision'
        version: '2024-04-01'
    oauth2:
        key: 'ea39c20a34235550629d95ff0761994ca4520ee0de9de651579a7eb5cdf0062b'
        secret: 'CHANGEME'
        redirect: 'callback/gusto'
        tokenUrl: 'https://api.gusto-demo.com/oauth/token'
        accessToken:
            grantType: 'authorization_code'
        refreshToken:
            grantType: 'refresh_token'

handleBars:
    eventDefaultRequest: 'The requests Drata sends to internal and external systems generate a response that determines the result of the control test.'

heroku:
    api:
        url: 'https://api.heroku.com'
        endpoints:
            apps: 'apps'
            teams: 'teams'
            enterpriseAccounts: 'enterprise-accounts'
    oauth2:
        key: 'fab5d609-8ffc-4bab-a83b-19040fc0a332'
        secret: 'CHANGEME'
        redirect: 'callback/heroku'
        tokenUrl: 'https://id.heroku.com/oauth/token'
        accessToken:
            grantType: 'authorization_code'
        refreshToken:
            grantType: 'refresh_token'
    url: 'https://dashboard.heroku.com'

hrisBackgoundChecks: []

hubspot:
    api:
        url: https://api.hubapi.com
        endpoints:
            accounts: 'account-info/v3/details'
            users: 'settings/v3/users'
            roles: 'settings/v3/users/roles'
            teams: 'settings/v3/users/teams'
    oauth2:
        key: fc2c514b-9a8a-4097-8318-da48d34a92ea
        secret: CHANGEME
        tokenUrl: 'https://api.hubapi.com/oauth/v1/token'
        redirect: 'callback/hubspot'
        accessToken:
            grantType: 'authorization_code'
        refreshToken:
            grantType: 'refresh_token'
        scopes: settings.users.read settings.users.teams.read

hud:
    enabled: false
    apiKey: 'CHANGEME'

miro:
    api:
        url: https://api.miro.com/
        endpoints:
            users: v2/orgs/%s/members
            token: v1/oauth-token
            teams: v2/orgs/%s/teams
            teamMembers: v2/orgs/%s/teams/%s/members
    oauth2:
        redirect: 'callback/miro'
        tokenUrl: 'https://api.miro.com/v1/oauth/token'
        requiredScopes:
            - 'organizations:read'
            - 'organizations:teams:read'
        accessToken:
            grantType: 'authorization_code'
        refreshToken:
            grantType: 'refresh_token'
intercom:
    identityVerificationSecret: 'CHANGEME'

iso:
    isms: 'ISO ISMS'
    annex: 'ISO Annex A'
    isms2022: 'ISO ISMS (2022)'
    annex2022: 'ISO Annex A (2022)'

jira:
    api:
        url: 'https://api.atlassian.com/ex/jira/%s/rest/api/3'
        urlv2: 'https://api.atlassian.com/ex/jira/%s/rest/api/2'
        browseUrl: '%s/browse/%s'
        search: '/search/jql?jql=%s'
        projects: '/project/search'
        issueTypes: '/issuetype/project?projectId=:projectId&level=0'
        subtaskIssueTypes: '/issuetype/project?projectId=:projectId&level=-1'
        users: '/user/search'
        labels: '/label'
        suggestions: '/jql/autocompletedata/suggestions'
        assignable: '/user/assignable/search?project=:projectId'
        createIssue: '/issue'
        serviceInfo: '/serverInfo'
        addIssueAttachment: '/issue/:issueId/attachments'
        fields: '/field'
        ticketStructure: '/issue/createmeta/:projectId/issuetypes/:issueTypeId'
        projectHierarchy: '/project/:projectId/hierarchy'
        parentIssuesFetchLimit: 50
        attachment:
            errorTitle: 'File size limit exceeded'
            errorMessage: 'The findings list could not be attached to the ticket. Download the CSV from the test findings in Drata to review the details.'
            maxSize: 104857600 # 100MB
    oauth2:
        key: 'iSxhy92fGxoa3nD8MgagKauIlB4pR44d'
        secret: 'CHANGEME'
        redirect: 'http://localhost:5000/callback/jira'
        tokenUrl: 'https://auth.atlassian.com/oauth/token'
        resourcesUrl: 'https://api.atlassian.com/oauth/token/accessible-resources'
        accessToken:
            grantType: 'authorization_code'
        refreshToken:
            grantType: 'refresh_token'

jumpcloud:
    api:
        url: https://console.jumpcloud.com
        euUrl: https://console.eu.jumpcloud.com
        systemUsersMaxResults: 100
        userAssociatedSystemsMaxResults: 100
        systemsMaxResults: 20
        filterValueMax: 50
        systemInfoMaxResults: 1000
        commandResultsMaxResults: 100
        systemAppsMaxResults: 1000
        policiesListMaxResults: 50
        browserPluginsMaxResults: 1000
        groupMembersMaxResults: 100
        commandNames:
            - 'DrataCollectorWindows'
            - 'DrataCollectorMac'
            - 'DrataCollectorLinux'
        errors:
            maxRetries: 3
            retryDelayMilliseconds: 3000

        endpoints:
            systemUsers: api/systemusers
            systems: api/systems
            systemInfo: api/v2/systeminsights/system_info
            organizations: api/organizations
            userAssociatedSystems: api/v2/users/%s/systems
            commands: api/commands
            commandResults: api/commands/%s/results
            systemApps: api/v2/systeminsights/apps
            systemPrograms: api/v2/systeminsights/programs
            policiesList: api/v2/policies
            systemsBoundToAPolicy: api/v2/policies/%s/systems
            systemGroupMembers: api/v2/systemgroups/%s/members
            policyStatuses: api/v2/policies/%s/policystatuses
            policyConfig: api/v2/policies/%s
            browserPlugin: api/v2/systeminsights/browser_plugins
            chromeExtensions: api/v2/systeminsights/chrome_extensions
            firefoxAddons: api/v2/systeminsights/firefox_addons
            ieExtensions: api/v2/systeminsights/ie_extensions
            safariExtensions: api/v2/systeminsights/safari_extensions
            services: api/v2/systeminsights/services

jwt:
    secret: 'CHANGEME'
    defaultStrategy: 'jwt'
    siteAdminStrategy: 'site-admin'
    auditorsStrategy: 'auditors'
    serviceUserStrategy: 'service-user'
    consultTenantStrategy: 'consult-tenant'
    expiresIn: 300 # seconds in 5 minutes
    expiresInRefreshToken: 28800 # seconds in 8 hours
    expiresInAuditorClient: 3600 # seconds in 60 minutes
    expiresInRefreshAgentToken: ********* # seconds in 20 years
    cookie:
        domain: ''
        webRefreshTokenKey: 'web-refresh-token-local'
        siteAdminRefreshTokenKey: 'admin-refresh-token-local'

kandji:
    api:
        ping: 'devices?limit=1'
        getDevices: 'devices/?platform=Mac'
        getDetails: 'devices/:deviceId/details'
        getApps: 'devices/:deviceId/apps'
        getLibraryItems: 'devices/:deviceId/library-items'

knock:
    api:
        key: 'CHANGEME'
    channels:
        slack: 'e5259039-27e2-4ab8-818b-7705de54a2bd'
        ms_teams: 'c4419afd-d587-4240-bc61-831fd007048f'
    workflows:
        controlEvidenceUpdate: control-evidence-update
        controls: controls-notification
        exceptionManagementSubmission: exception-management-submission
        nonCompliantPersonnel: non-compliant-personnel
        nonCompliantPersonnelDirect: non-compliant-personnel-direct-message
        requiredApprovals: required-approvals
        testWithConnectionIssue: test-with-connection-issue
        workflowNotifications: workflow-notifications
        workflowNotificationsDelete: workflow-delete-notifications
        workflowUpdatesAndErrors: workflow-updates-and-errors
        welcomeMessage: welcome-message

hexnode:
    api:
        allowedOS:
            - macos
            - windows
        version: '/api/v1'
        devicesMaxResults: 100
        ping: 'devices/?&per_page=1'
        getDevices: 'devices/'
        getDetails: 'devices/:deviceId/'
        getPolicies: 'devices/:deviceId/policies/'
        getApps: 'devices/:deviceId/applications/'
        getLocations: 'devices/:deviceId/locations/'

karmacheck:
    api:
        url: 'https://api-stage.karmacheck.io'
        key: 'CHANGEME'
        webhookDomain: 'https://drata-karmacheck-1.ngrok.io' # localhost proxy via Ngrok
        endpoints:
            createCase: case/create
            getCase: case/id/%s
            getCases: case/list

    display:
        url: 'https://app-stage.karmacheck.com/background_check/'

knowbe4:
    api:
        urls:
            us: 'https://us.api.knowbe4.com'
            eu: 'https://eu.api.knowbe4.com'
            ca: 'https://ca.api.knowbe4.com'
            uk: 'https://uk.api.knowbe4.com'
            de: 'https://de.api.knowbe4.com'
        endpoints:
            campaigns: 'v1/training/campaigns'
            accounts: 'v1/account'
            users: 'v1/users'
            training-enrollments: 'v1/training/enrollments'

launchdarkly:
    enabled: true
    localServer:
        enabled: false
    useLocalDefinition: false
    eventCapacity: 20000 # event capacity for LD support console error/warnings increase
    secret: 'CHANGEME'
    redis:
        enabled: false
        cacheTTL: 15 # TTL in seconds for in-memory cache
    log:
        output: 'log'
        level: 'info'

leen:
    syncGracePeriodMinutes: 60 # 60 minutes = 1 hour
    api:
        key: 'CHANGEME'
        maxResults: 400
        maxResultsToUpdate: 100
        maxParallelizedResultsToUpdate: 100
        syncWaitTimeLimitInMinutes: 10
        syncSleepTimeMilliseconds: 10000
        syncJobsResults: 500
        issuesUrl: 'https://api.leen.dev/v1/appsec/issues'
        issueUrl: 'https://api.leen.dev/v1/appsec/issues?ids=:issueId&enableCursor=true'
        vulnerabilitiesUrl: 'https://api.leen.dev/v1/vms/vulnerabilities'
        vulnerabilityUrl: 'https://api.leen.dev/v1/vms/vulnerabilities?ids=:vulnerabilityIds&enableCursor=true'
        organizationsUrl: 'https://api.leen.dev/v1/provisioning/organizations'
        connectionsUrl: 'https://api.leen.dev/v1/provisioning/organizations/:organizationId/connections'
        connectionUrl: 'https://api.leen.dev/v1/provisioning/organizations/:organizationId/connections/:connectionId'
        testConnectionUrl: 'https://api.leen.dev/v1/provisioning/organizations/:organizationId/connections/:connectionId/test'
        jobsConnectionUrl: 'https://api.leen.dev/v1/provisioning/organizations/:organizationId/connections/:connectionId/jobs'
        inviteToken: 'https://api.leen.dev/v1/provisioning/organizations/:organizationId/connection-invite-tokens'

linear:
    api:
        url: 'https://api.linear.app/graphql'
    oauth2:
        key: '271331031be6ccf1ed50da0d1df5d587'
        secret: 'CHANGEME'
        redirect: 'http://localhost:5000/callback/linear'
        tokenUrl: 'https://api.linear.app/oauth/token'
        accessToken:
            grantType: 'authorization_code'
        refreshToken:
            grantType: 'refresh_token'

lightrun:
    enabled: false
    secret: 'CHANGEME'

mergedev:
    syncGracePeriodMinutes: 60 # 60 minutes = 1 hour
    api:
        url: 'https://api.merge.dev/api/hris/v1'
        token: 'CHANGEME'
        pageSize: 100 # use their limit to reduce API calls
        apiCallRetryLimit: 5
        syncWaitTimeLimitInMinutes: 10
        syncSleepTimeMilliseconds: 10000

microsoft365GccHigh:
    oauth2:
        key: '7957f339-6a8f-45c0-b336-c624556a1c7d'
        secret: 'CHANGEME'
        redirect: 'callback/microsoft-gcc-high'
        authRedirect: 'signin/microsoft-gcc-high/callback'
        tokenUrl: 'https://login.microsoftonline.us'
        scopes:
            - 'https://graph.microsoft.us/.default'
    connectionEstablishedWaitInSeconds: 10
    graphEndpoint: 'https://graph.microsoft.us'

microsoftTeams:
    api:
        baseUrl: 'https://graph.microsoft.com/v1.0'
        serviceUrl: 'https://smba.trafficmanager.net/amer'
    oauth2:
        key: '560e7d09-e8e3-4253-bf1f-d8034e2ffa22'
        secret: 'CHANGEME'
        baseUrl: 'https://login.microsoftonline.com/common/oauth2/v2.0'
        redirect: 'callback/teams'
        type: 'MultiTenant'
        accessToken:
            grantType: 'authorization_code'
    commands:
        welcome: |
            Hi 👋🏻! Thanks for turning on Drata notifications in Microsoft Teams. You can now configure Control and Personnel notifications from within [Drata](https://app.drata.com/account-settings/notifications).
            <br/><br/>
            Some tips to get you started:
            - This is a notification-only bot, it will not respond to commands or messages
            - You'll need an active Drata subscription to utilize this app
            - You can visit the  [Drata Help Center](https://help.drata.com/)  to learn more
            - If you have any issues along the way, start a chat with our support team from the Drata app

mtls:
    clientCert: 'CHANGEME'
    clientKey: 'CHANGEME'
    ca: 'CHANGEME'
    allowedSans: []
    commonName: api-ap.int.localdev.drata.net
    certValidityDays: 7
    certRotationBuffer: 2 # buffer (days) to rotate containers before the MTLS certs expires, max cannot exceed certValidityDays
    certRotationJitter: 24 # jitter (hours) to randomize rotation, max cannot exceed certRotationBuffer

newLabel:
    amountOfDaysForControlTestNewStatus: 45

noteFile:
    maxNoteFilesPerNote: 10

evidenceLibrary:
    evidenceNumberSeed: 100
    maxEvidenceNumberSeed: 1000
    evidenceGuidanceTemplates: true

offboarding:
    ticketTemplate: 'views/ticket.hbs'

office365:
    oauth2:
        key: 'd2ba9083-4183-49c6-b935-96296bf1c308' # NON-PROD on dratadev
        # key: 'd4071e08-ce33-43d6-ba09-e6873d6fba49' # NON-PROD on admindrata
        secret: 'CHANGEME'
        redirect: 'callback/microsoft'
        authRedirect: 'signin/microsoft/callback'
        authAuditorRedirect: 'signin/auditor/microsoft/callback'
        authServiceUserRedirect: 'signin/service-user/microsoft/callback'
        tokenUrl: 'https://login.microsoftonline.com'
        scopes:
            - 'https://graph.microsoft.com/.default'
    connectionEstablishedWaitInSeconds: 10

opensearch:
    enable: false
    connectionType: provisioned
    monitoring:
        index: '%s.monitoring.results' # OpenSearch index for monitoring results | %s is formatted with NODE_CONFIG_ENV
        indexerEnabled: false # enable the indexer which will automatically update the monitoring results index based upon monitoring related drata platform actions
    vendors:
        index: '%s.vendors' # OpenSearch index for vendors | %s is formatted with NODE_CONFIG_ENV
    vendorsRisks:
        index: '%s.vendors.risks' # OpenSearch index for vendor risks | %s is formatted with NODE_CONFIG_ENV
    risks:
        index: '%s.risks' # OpenSearch index for risks | %s is formatted with NODE_CONFIG_ENV
    controls:
        index: '%s.controls'
    libraryTestTemplates:
        index: '%s.library.test.templates' # OpenSearch index for test library templates
    pagination:
        maxFacetSize: 10000
    client:
        keepAlive: true
        keepAliveMsecs: 1000
        maxFreeSockets: 256
        maxRetries: 2
        maxSockets: 256
        pingTimeout: 3000
        requestTimeout: 30000
        resurrectStrategy: 'ping'

productboard:
    private:
        user-key: 'CHANGEME'
        auditor-key: 'CHANGEME'
        service-user-key: 'CHANGEME'

pagination:
    page: 1
    limit: 20
    max: 50
    fakeCount: 9999999
    fakeMaxPage: 1000
    token:
        max: 100

auditHub:
    pagination:
        limit: 20
        max: 200

cosmosTable:
    pagination:
        page: 1
        limit: 20
        max: 200

pivotaltracker:
    api:
        url: 'https://pivotaltracker.com/services/v5'
        endpoints:
            me: 'me'
            projects: 'projects'
            search: 'search?query='

pusher:
    appId: '*******'
    key: '8ea841c652b621f2f3d0'
    cluster: 'us3'
    secret: 'CHANGEME'
    encryptionMasterKeyBase64: 'CHANGEME'
    enabled: true

queue:
    assessmentCompleted:
        name: 'ap-dev-assessment-completed-us-west-2-queue'
        url: 'https://localhost.localstack.cloud:4566/000000000000/ap-dev-assessment-completed-us-west-2-queue'
        region: 'us-west-2'
        enabled: false
    salesforce:
        name: 'tenant-creation-us-west-2-queue'
        url: 'http://sqs.us-west-2.localhost.localstack.cloud:4566/000000000000/tenant-creation-us-west-2-queue'
        region: 'us-west-2'
        enabled: false

riskManagement:
    assessmentReport: 'src/app/risk-management/docs/assessmentReportTemplate.docx'
    dashboardReport: 'views/risk-dashboard.hbs'
    dashboardReportMultiverse: 'views/risk-dashboard-multiverse.hbs'

riskAssessment:
    assessmentReport: 'src/app/users/risk-assessment/docs/assessmentReportTemplate.docx'

rippling:
    api:
        url: 'https://api.ripplingsandbox.com/platform/api'
        bgCheckUrl: https://app.rippling.com/dashboard
        bgcheckReportName: background_check_status_report
        inventoryReportName: inventory
        apiCallRetryLimit: 5
        endpoints:
            company: 'companies/current'
            employees: 'employees/include_terminated'
            employee: 'employees/%s'
            me: 'me'
            reportTemplates: 'reports/report_templates'
            reportTemplateInfo: 'reports/%s/report_template_info'
            generateReport: reports/report_data
            reportResults: reports/report_data
    oauth2:
        key: 'MKvZIeRhcDfbK6QRWOtJKdaBJ6oJb5iMH8zZ2ukB'
        secret: 'CHANGEME'
        redirect: 'callback/rippling'
        tokenUrl: 'https://api.ripplingsandbox.com/api/o/token/'
        accessToken:
            grantType: 'authorization_code'
        refreshToken:
            grantType: 'refresh_token'

redis:
    host: 'CHANGEME'
    port: 'CHANGEME'
    enable_ssl: false
    enableAutoPipelining: true

reports:
    accessReviewFileName: 'Access Review'
    vendorReportFileName: 'vendors'
    prospectiveVendorReportFileName: 'Prospective'
    assetReportFileName: 'assets'
    controlTestInstanceEventSummaryFileName: '%s-summary'
    personnelReportFileName: 'Personnel-Compliance-Overview-%s'
    yesOrNo: 'YES | NO'
    infrastructureFile: 'Infrastructure'
    versionControlFile: 'Version Control'
    observabilityFile: 'Observability'
    connectionReportFileName: 'connections'
    requirementsToControls: 'Requirements-to-Controls'
    controlsToRequirements: 'Controls-to-Requirements'
    allFile: 'All'
    companyLinksReportFileName: 'links'
    allPersonnel: 'all current personnel'
    vulnerabilityFile: 'Vulnerability'
    failedTestFile: 'FailedTestReportFileName'

rollout:
    - drata.com
    - dratadev.onmicrosoft.com
    - socpilot.com
    - arkpes.com

safebase:
    sso:
        enabled: false
        jwtIssuer: 'http://localhost:3000'
        jwtAudience: 'safebase'
        endpoint: /sso/drata
        key: 'CHANGEME'
    api:
        sync:
            chunkSize: 100
        headers:
            cfAccessClientSecret: 'CHANGEME'
            cfAccessClientId: 'CHANGEME'
            authorization: 'CHANGEME'
        baseUrl: https://dev-cd.safebase.io
        endpoint:
            migrateAccount: /api/admin-internal/drata/sync
            evidenceSync: /api/admin-internal/trust-library/events
        webhook:
            headers:
                svixIdKey: svix-id
                svixTimestampKey: svix-timestamp
                svixSignatureKey: svix-signature
                svixDrataSecretKey: drata-svix
            secret: 'CHANGEME'
        vrm:
            secret: 'CHANGEME'
            keyId: 'CHANGEME'
            endpoints:
                info: '/api/ext/v1/rest/partners/vrm-pilot/organizations/%s/info'
                items: '/api/ext/v1/rest/partners/vrm-pilot/organizations/%s/items'
                documents: '/api/ext/v1/rest/partners/vrm-pilot/organizations/%s/documents'
                documentsDownload: '/api/ext/v1/rest/partners/vrm-pilot/organizations/%s/documents/download'
                accessRequest: '/api/ext/v1/rest/partners/vrm-pilot/organizations/%s/access-request/request'
                accessRequestRequirements: '/api/ext/v1/rest/partners/vrm-pilot/organizations/%s/access-request/requirements'

salesforce:
    enabled: false
    setAccountIdUrl: 'https://hooks.zapier.com/hooks/catch/********/bwn7p8r/'
    api:
        # NOTE: the clientId is changed in the Salesforce environment on a regular basis
        #       this value should be updated when it changes
        clientId: '3MVG9EJ2FoGDnkgVqtYmb..iNefBQjLO5t_d4h8p3cFeSgMS0ByQPfDhnpdW.nBc9weJFLZSiQV1bHGq9vb.7'
        key: 'CHANGEME'
        audience: 'https://test.salesforce.com'
        username: '<EMAIL>.dratafull1'
        url: 'https://drata--dratafull1.sandbox.lightning.force.com/lightning'
    connection:
        api:
            baseUrl: https://%s.my.salesforce.com
            endpoints:
                userInfo: services/oauth2/userinfo
                query: services/data/v60.0/query
        oauth2:
            key: CHANGEME
            secret: CHANGEME
            redirect: http://localhost:5000/callback/salesforce
            tokenUrl: services/oauth2/token
            tokenIntrospectionUrl: services/oauth2/introspect
            accessToken:
                grantType: authorization_code
            refreshToken:
                grantType: refresh_token
    report:
        type: 'service_account'
        projectId: 'trim-glazing-436123-n4'
        clientEmail: '<EMAIL>'
        clientId: '101860059338462366808'
        googleSheetsScope: 'https://www.googleapis.com/auth/spreadsheets'
        googleDriveScope: 'https://www.googleapis.com/auth/drive'

scannerUploader:
    s3:
        enableCheckMissingScans: false
        region: 'us-west-2'
        scanningBucket: 'CHANGEME'
        scanningQuarantineBucket: 'CHANGEME'
    sqs:
        apiVersion: '2012-11-05'
        enabled: false
        queue2Enabled: false
        region: 'us-west-2'
        url: 'CHANGEME'
        name: 'CHANGEME'
        urlDeprecated: 'CHANGEME'
        nameDeprecated: 'CHANGEME'

sentry:
    api:
        url: https://sentry.io/
        endpoints:
            accounts: 'api/0/organizations/%s/' # GET api/0/organizations/{organization_slug}
            users: 'api/0/organizations/%s/users/' # GET api/0/organizations/{organization_slug}/users

services:
    vendorDirectory:
        # NOTE: This is the domain for the dev environment
        domain: 'https://vendors-service.dratarules.com'
        useMock: false # Set to true to use mock data instead of calling the vendor directory API

vercel:
    api:
        url: https://api.vercel.com/
        endpoints:
            account: 'v2/teams/%s'
            users: 'v2/teams/%s/members'

segment:
    enabled: false
    segmentEnabledDate: 2022-04-21T18:58:00-07:00
    secret: 'CHANGEME'
    analyticsIdentitySalt: '4387568a-96ab-49cb-b188-56f71bd034e8'
    analyticsAutomationDefaultSiteAdminId: '********-9999-9999-9999-************'
    api:
        url: https://api.segmentapis.com
        endpoints:
            account: /
            userList: /users
            userDetails: /users/:userId
            userGroups: /users/:userId/groups

security:
    maskLength: 6
    algorithm: 'aes-256-cbc'
    encoding: 'utf8'
    output: 'hex'
    encryptionKey: 'CHANGEME'
    encryptionKeyRotation: 'CHANGEME'
    contexts:
        defaultContext:
            algorithm: 'aes-256-cbc'
            encoding: 'utf8'
            output: 'hex'
        temporalContext:
            algorithm: 'secretbox'
            encoding: 'utf8'
            output: 'hex'

securityReport:
    checkStatusExcludeList: [1, 2, 4, 5]

seeding:
    personnelAppCount: 2

server:
    port: 3000
    mtlsPort: 3443
    allowedCns:
        - 'autopilot'

siteAdmin:
    email: <EMAIL>
    guid: a1b2c3d4-e5f6-4ac7-8def-123456789abc

slack:
    enabled: false
    # https://api.slack.com/apps/A03V1HDKKNJ/incoming-webhooks?success=1
    api:
        baseUrl: 'https://slack.com/api'
        uninstall: '/apps.uninstall'
        getChannels: '/conversations.list'
        getUsers: '/users.list'
        getGroups: '/usergroups.list?include_users=true'
        channelTypesFilter: ['private_channel', 'public_channel']
        limit: 400
    auth0:
        token: 'CHANGEME'
    hmac:
        secret: 'CHANGEME'
    oauth2:
        key: '3661857653522.3693317275878'
        secret: 'CHANGEME'
        redirect: 'callback/slack'
        tokenUrl: 'https://slack.com/api/oauth.v2.access'
        accessToken:
            grantType: 'authorization_code'
    salesforce:
        testWebhook:
            url: 'CHANGEME'
        webhook:
            url: 'CHANGEME'
    publicHostname: 'CHANGEME'
    platformAlerts:
        readToken: 'CHANGEME'
        writeToken: 'CHANGEME'
        webhook:
            internal-temporal-support: 'CHANGEME'
            prod-alerts: 'CHANGEME'
            qa-alerts: 'CHANGEME'
            team-the-notorious-bugs-alerts: 'CHANGEME'
            team-the-notorious-bugs-alerts-noisy: 'CHANGEME'

snowflake:
    api:
        url: 'https://%s.snowflakecomputing.com'
        endpoints:
            statements: '/api/v2/statements'
    oauth2:
        redirect: '/callback/snowflake'
        tokenUrl: '/oauth/token-request'
        accessToken:
            grantType: 'authorization_code'
        refreshToken:
            grantType: 'refresh_token'

stackOne:
    api:
        key: CHANGEME
        url: https://api.stackone.com
        pagination:
            max: 100
        headers:
            accountId: x-account-id
        endpoints:
            connectSessions: connect_sessions
            account: accounts/%s
            iam:
                users: unified/iam/users
            lms:
                courses: unified/lms/courses
                users: unified/lms/users
                userAssignments: unified/lms/users/%s/assignments

swagger:
    examples:
        date: '2020-07-06'
        previousDate: '2020-07-05'
        dateTime: '2025-07-01T16:45:55.246Z'
        uuid: 'aaaaaaaa-bbbb-0000-cccc-dddddddddddd'
        phone: '************'
        address: '742 Evergreen Terrace, Springfield, OH 45501'
        dbPort: 3306
        url: 'https://acme.com'
        ipv4: '*************'
        ipv6: '684D:1111:222:3333:4444:5555:6:77'
        googleDriveLabels:
            [
                {
                    'type': 'googleDriveLabels',
                    'content':
                        {
                            'labels':
                                [
                                    {
                                        'order': '**************',
                                        'labels':
                                            [
                                                {
                                                    'styles':
                                                        {
                                                            'badgeColors':
                                                                {
                                                                    'soloColor': '#202124',
                                                                    'backgroundColor': '#bdc1c6',
                                                                    'foregroundColor': '#202124',
                                                                },
                                                        },
                                                    'fieldId': 'A4B10104DE',
                                                    'labelName': 'Internal',
                                                    'badgePriority': '16812526190002',
                                                    'labelDescription': 'Business Data that is intended for only Internal consumption and should not be shared publicly. ',
                                                },
                                            ],
                                        'labelId': '9ZdzGeHXNE1hnrMFrlIrM3WQ0VgdeTSvjyHRNNEbbFcb',
                                        'categoryId': '33B392D1F4',
                                        'categoryName': 'File sensitivity',
                                    },
                                ],
                            'cloudFileId': '1YgcSFNzmpCITM2fAXJNMQ9B2KljDHvbM',
                            'cloudProvider': 'google-drive',
                        },
                },
            ]
        evidenceDataContent: '{"cloudProvider":"google-drive","cloudFileId":"1YgcSFNzmpCITM2fAXJNMQ9B2KljDHvbM","labels":[{"labelId":"9ZdzGeHXNE1hnrMFrlIrM3WQ0VgdeTSvjyHRNNEbbFcb","categoryName":"File sensitivity","order":"**************","categoryId":"33B392D1F4","labels":[{"fieldId":"A4B10104DE","labelName":"Internal","labelDescription":"Business Data that is intended for only Internal consumption and should not be shared publicly. ","styles":{"badgeColors":{"backgroundColor":"#bdc1c6","foregroundColor":"#202124","soloColor":"#202124"}},"badgePriority":"16812526190002"}]}]}'
    enabled: false

sync:
    maxResults: 100
    policyDistributionBatchSize: 30
    defaultChunkSize: 70
    infrastructureUserIdentityBatchSize: 1000
    infrastructureConcurrentConnectionsBatchSize: 15

targetprocess:
    api:
        url: 'tpondemand.com/api/v2'
        displayUrl: 'tpondemand.com/RestUI/Board.aspx#page=userstory/'
        endpoints:
            stories: 'UserStories/?select={id,name,tags,project:{project.id},assignedUser}&filter=?(entityState.name is not "Production")'
            projects: 'Program/?select={id,name}'
            tags: 'Tags/?select={id,name}'
            noUsers: 'and AssignedUser.Count == 0)'
            access_token: '&access_token'

tokens:
    magicLinkExpiration: 1 # in hours
    verificationExpiration: 24 # in hours
    selfServiceAccountCreationInviteExpiration: 168 # in hours
    trustCenterReportsDownload: 168 # in hours

trello:
    api:
        url: 'https://api.trello.com/1'
        endpoints:
            search: 'search'
            me: 'members/me'
    auth:
        key: 'af1d6707f3882dad4582a881dcdb9d95'

typeform:
    secret: 'CHANGEME'
    responsesToken: 'CHANGEME'
    url: https://drata.typeform.com/to
    api:
        url: https://api.typeform.com
        maxRequestsPerSecond: 2
        workspaces: /workspaces
        workspaceManagement: /workspaces/%s
        create: /forms
        forms: /forms/%s
        responses: /forms/%s/responses
        webhooks: /forms/%s/webhooks/%s
    webhook:
        tag: drata-webhook
        url: https://drata-typeform-1.ngrok.io
        get: /forms/%s/webhooks
    formIds:
        vendorRiskQuestionnaire: 'eN455zTA'
        riskAssessment:
            ENGINEERING: 'idKhS6yt'
            LEGAL: 'QPfWoqTw'
            HR: 'poCc5NhH'
            SECURITY: 'KpXaNaVK'
            FINANCE: 'g22JnrvQ'
            SALES: 'V4W1r4xN'
        defaultQuestionnaire: # for seeding only
            # DO NOT DELETE these workspace and form in Typeform website as admin
            # or things will break in some environments https://drata.atlassian.net/browse/ENG-22070
            workspaceId: y34bcT # <NAME_EMAIL>
            questionnaireId: eN455zTA
            questionnaireURL: https://jy5xmat3xgn.typeform.com/to/eN455zTA

temporal:
    apiKey: 'CHANGEME'
    clusterAddress: 'localhost:7233'
    namespace: 'localdev'
    taskQueues: # these need to be named under API_TYPE if they are deployed in the API cluster
        temporal-default: tw_queue
        temporal-slow-pool: tw_slow_queue
    autopilot:
        queueName: autopilot_queue_localdev
    enabled: false
    tracingEnabled: true
    log:
        core:
            LEVEL: 'INFO'

trustcenter:
    maxSLADays: 14
    maxCommonQuestionsList: 25
    maxTextAnnouncementBody: 3500
    maxCommonQuestionText: 3500
    maxDefaultAccessLength: 1825
    defaultAccessLength: 365
    maxTextHeadline: 80
    maxHistoryInDaysForFailingControl: 3
    maxAdditionalDetailsLength: 6000
    maxComplianceDetailsLength: 7500
    maxSectionTitleLength: 45
    maxSectionDescriptionLength: 1200
    partnerClientKey: 'CHANGEME'

url:
    publicApi: 'http://localhost:4000'
    trustCenterPartnerApi: 'http://localhost:4000'
    webApp: 'http://localhost:5000'
    drataApp: 'http://localhost:5173'
    drataAppLocalhost: 'http://localhost:5173'
    adminApp: 'http://localhost:8000'
    appCdn: 'https://img-dev.dratacdn.com'
    cdn: 'https://cdn.drata.com'
    vendorHubApp: 'http://localhost:5000'

validation:
    minYear: 1800
    maxInt: 1000000000
    minInt: -1000000000
    uuidLength: 36
    maxColorHexCodeLength: 8
    maxVarcharShortText: 16
    maxVarcharText: 191
    maxVarcharLongText: 768
    maxLongText: 30000
    maxLongTextNoCsv: 60000
    maxTrustCenterSearch: 100
    minPort: 1024
    maxPort: 65535
    maxRowsCsv: 4000
    maxExcludeList: 300
    maxLongExcludeList: 500
    mediumTextMaxLength: 16777215
    maxVarcharKeyLength: 1500
    codeLength: 20
    maxEventHtmlLength: 1900000 # Experimental number, relative to static length of hbs templates
    maxVendorPotentialReviewNoteText: 1000
    maxVendorPotentialReviewObservationText: 2000
    maxVendorPotentialReviewSourceText: 500
    maxVarcharMediumText: 50
    mimSummarizeWords: 450
    maxSummarizeWords: 5000
    maxSocSummarizeCharacters: 650000
    currencyCodeMaxLength: 3
    minPasswordLength: 6
    maxPasswordMinLength: 12
    maxPocExpiresAtDate: '2100-01-01'
    maxTrustCenterSection: 50
    maxTrustCenterSectionTitle: 100
    maxFollowUpReminderDays: 90
    maxSecurityQuestionnairesTextLength: 2000

vellum:
    enabled: true
    apiKey: 'CHANGEME'
    deployment:
        tprmQuestionnaireSummary: 'tprm-questionnaire-deployment-staging'
        controlsRecommendationFromPolicy: 'policy-control-mapper'
        questionnaireAutomationExtraction: 'questionnaire-automation-extraction-development'
        questionnaireAutomationAnswering: 'questionnaire-automation-answering-development'
        vendorSOC2Summary: 'vendor-soc2-summary-development'
        ap2MonitoringEnhancements: 'ap2-monitoring-enhancements-development'
        policyRecommendationForException: 'policy-recommendation-for-exception'
    recipe:
        validator:
            secret: 'CHANGEME'

vulnerability:
    maxFindingsRecordsNumber: 1000000 # 1M records overall
    maxFindingsTableSizeKb: 2000000 # 2GB max table size
    maxFindingsRecordsDailySync: 1000 # 1K records max daily sync
    maxFindingsPerConnection: 100000 # will be deprecated
    lowSeveritySla: 180 # SLA days
    warningPeriod: 7
    snykBaseUrl: 'https://app.snyk.io/org/'
    dbPagination:
        max: 100

wysiwyg:
    imageUploadLimit: 5
    externalUserId: 0
    externalUserFirstName: Drata
    externalUserLasttName: Admin
    ExternalUserAvatar: https://cdn.drata.com/icon/icon_fwhite_bblue_48-circle.png

workos:
    apiKey: 'CHANGEME'
    webhookSecret: 'CHANGEME'
    clientId: 'project_01F23C64HGGPDVME2343G3X3GW'
    siteAdminOrgId: 'org_01HSPVCVDH0H0B8F453RQGQ5EH'
    webhookMaxSecondsSinceIssued: 180 # that's the recommended value from WorkOS
    redirectUri: 'https://drata-api.ngrok.io/auth/sso/callback'
    returnUrlPathname: 'callback/workos'
    apiHostname: 'sso.api.drata.com'
    auditLogs:
        enabled: false

zoho:
    bugtracker:
        api:
            url: https://bugtracker.zoho.com/restapi/
        bugDetailUrl: https://bugtracker.zoho.com/portal/%s#buginfo/%s/%s/
    oauth2:
        key: 1000.******************************
        secret: 'CHANGEME'
        redirect: 'http://localhost:5000/callback/zoho'
        tokenUrl: 'https://accounts.zoho.com/oauth/v2/token'
        accessToken:
            grantType: 'authorization_code'
        refreshToken:
            grantType: 'refresh_token'

okta:
    siteAdmin:
        auth2:
            domain: 'https://drata.okta.com/'
            clientId: '0oab5r66bzyCpJq8s697' # This is fine here, this is NOT a secret. This clientId is the same on the client
            grantType: 'authorization_code'
            redirectUri: '/authenticate'
            tokenUri: 'oauth2/default/v1/token'
            userInfoUri: 'oauth2/default/v1/userinfo'
        clientCredentials:
            issuer: 'https://drata-automation.okta.com/oauth2/default'
            retool:
                clientId: '0oa1w9h0d09Ec5yRo1d8'
    oauth2:
        urlCode: 'https://%s/oauth2/v1/authorize?client_id=%s&response_type=code&scope=%s&redirect_uri=%s&state=%s&nonce=%s'
        urlToken: 'https://%s/oauth2/v1/token'
        urlRevokeToken: 'https://%s/oauth2/v1/revoke'
        urlIntrospectToken: 'https://%s/oauth2/v1/introspect'
        redirect: /callback/okta-identity
        accessToken:
            grantType: 'authorization_code'
        refreshToken:
            grantType: 'refresh_token'
        scopes:
            login:
                - openid
                - okta.users.read.self
            connection:
                - openid
                - offline_access
                - okta.users.read
    api:
        getDomains: 'https://%s/api/v1/domains'
        getUsers: 'https://%s/api/v1/users'
        getUserInfo: 'https://%s/api/v1/users/me'
        getUserRoles: 'https://%s/api/v1/users/%s/roles'
        getUsersWithRoles: 'https://%s/api/v1/iam/assignees/users'
        getFactors: 'https://%s/api/v1/users/%s/factors'
        getGroups: 'https://%s/api/v1/groups'
        getGroupId: 'https://%s/api/v1/groups?q=%s'
        getGroupsForUser: 'https://%s/api/v1/users/%s/groups'
        getUsersByGroup: 'https://%s/api/v1/groups/%s/users'
        getApplications: 'https://%s/api/v1/apps'
        getAppUsers: 'https://%s/api/v1/apps/%s/users'
        getApplicationGroups: 'https://%s/api/v1/apps/%s/groups?expand=group'
rapid7:
    insightvm:
        api:
            endpoints:
                getReportConfigs: api/3/reports
                getReportHistories: api/3/reports/%s/history
                reportDownload: api/3/reports/%s/history/%s/output
                getResources: api/3
sentinelOne:
    api:
        endpoints:
            getUserByToken: web/api/v2.1/user
            getAgents: web/api/v2.1/agents
            getTokenDetails: web/api/v2.1/users/api-token-details
    config:
        paginationLimit: 100
        sortOrder: 'desc'
        sortBy: 'registeredAt'

backfill:
    pageSize: 100

storageDriver:
    aws:
        s3:
            apEvidenceAssessmentBucket: 'ap-dev-assessment-result-us-west-2-bucket'
            apEvidenceBucket: 'ap-dev-evidence-us-west-2-bucket'
            region: 'us-west-2'

accessData:
    linkTemplate:
        OKTA_IDENTITY: https://%s/admin/user/profile/view/%s
        AZURE: https://portal.azure.com/#view/Microsoft_AAD_UsersAndTenants/UserProfileMenuBlade/~/AdministrativeRole/userId/%s
        AWS: https://console.aws.amazon.com/iamv2/home?region=%s#/users/details/%s?section=permissions
        MICROSOFT_365: https://admin.microsoft.com/Adminportal/Home?#/users/:/UserDetails/%s
        AWS_GOV_CLOUD: https://console.aws.amazon.com/iamv2/home?region=%s#/users/details/%s?section=permissions
        GCP: https://console.cloud.google.com/iam-admin/iam?orgonly=true&project=%s&supportedpurview=organizationId # There isn't a query param on the GCP IAM panel to more specifically redirect user.x
        SLACK: https://app.slack.com/admin # There isn't a query param on the Slack panel to more specifically redirect user.x
        HEROKU: https://dashboard.heroku.com/apps # There isn't a query param on the heroku dashboard to redirect to a specific user
        XERO: https://go.xero.com/ # There isn't a query param on the xero dashboard to more specifically redirect to a specific user
        MIRO: https://miro.com/app/settings/company/%s/users # There isn't a query param on the miro dashboard to redirect to a specific user
        SEGMENT: https://app.segment.com # There isn't a query param on the Segment dashboard to redirect to a specific user
        CHECKR: https://%s/account/user/%s
        DATADOG: https://app.datadoghq.com/organization-settings/users?user_id=%s
        AUTH0: https://manage.auth0.com/dashboard/
        HUBSPOT: https://app.hubspot.com/settings/%s/users/user/%s
        SENTRY: https://%s.sentry.io/settings/members/
        ZOOM: https://zoom.us/account/user/
        CLOUDFLARE: https://dash.cloudflare.com/%s/members
        SNOWFLAKE: https://app.snowflake.com/%s/%s/#/account/users/%s
        MERGEDEV_ZENDESK: https://%s.zendesk.com/agent/users/%s/assigned_tickets
        DOCUSIGN: https://%s/organization/%s/user-profile?email=%s
        CERTN: https://demo-app.certn.co/hr/applications
        PAGER_DUTY: https://%s.pagerduty.com/users/%s
        RIPPLING: https://app.rippling.com/org-chart/chart
        STACKONE_SMARTRECRUITERS: https://www.smartrecruiters.com/settings/administration/user-management # There isn't a query param on the SmartRecruiters dashboard to redirect to a specific user
        STACKONE_TEAMTAILOR: https://app.teamtailor.com/companies/%s/employees/%s
        TEAMTAILOR: https://www.teamtailor.com/
        SMARTRECRUITERS: https://www.smartrecruiters.com/
        GITLAB: https://gitlab.com/dashboard/groups
        ATLASSIAN: https://admin.atlassian.com/
        STACKONE_ASHBY: https://app.ashbyhq.com/admin/users/%s
        STACKONE_ATTIO: https://app.attio.com/ # api does not provide us the subdomain https://app.attio.com/{subdomain}/settings/members/{user.id}
        STACKONE_CONTENTFUL: https://app.contentful.com/account/organizations/%s/organization_memberships
        STACKONE_KLAVIYO: https://www.klaviyo.com/settings/account/users
        STACKONE_LASTPASS: https://admin.lastpass.com/users/view/%s
        STACKONE_LEAPSOME: https://www.leapsome.com/app/#/profile/%s
        STACKONE_LEVER: https://hire.lever.co/settings/users # TODO: confirm
        STACKONE_ORACLEHCM: https://www.oraclehcm.com/ # TODO: search real linkTemplate
        STACKONE_PINPOINT: https://%s.pinpointhq.com/admin/user_management/employees
        STACKONE_PIPEDRIVE: https://%s.pipedrive.com/settings/users
        STACKONE_RECRUITEE: https://app.recruitee.com/#/settings/members/all
        STACKONE_WEBEX: https://admin.webex.com/manage-users/users/%s/summary
        STACKONE_WORKABLE: https://%s.workable.com/backend/settings/members
        STACKONE_ZELT: https://go.zelt.app/people/users/%s/personal
        STACKONE_BITWARDEN: https://vault.bitwarden.com/#/organizations
        STACKONE_SALESLOFT: https://app.salesloft.com/app/settings/users/active
        STACKONE_DIXA: https://www.dixa.com/ # TODO: search real linkTemplate
        STACKONE_FRESHSALES: https://%s.myfreshworks.com/crm/sales/settings/users/%s/edit
        STACKONE_CANVA: https://www.canva.com/settings/people
        STACKONE_GREENHOUSE: https://app7.greenhouse.io/account/users/%s/edit
        GITHUB: https://github.com/orgs/%s/people/%s
        MERGEDEV_AHA: https://%s.aha.io/settings/account/users
        MERGEDEV_BITBUCKET: https://bitbucket.org/%s/workspace/members/
        MERGEDEV_FRESHDESK: https://%s.freshdesk.com/a/admin/agents/filter/active
        MERGEDEV_FRESHSERVICE: https://%s.freshservice.com/agents
        MERGEDEV_HIVE: https://app.hive.com/ # Workspace id is not provided by endpoints
        MERGEDEV_WRIKE: https://www.wrike.com/accounts.htm#account
        MERGEDEV_FRONT: https://app.frontapp.com/settings/global/teammates
        STACKONE_AUTODESK: https://forums.autodesk.com
        STACKONE_ELASTIC: https://partners.elastic.co/English/Partner/home.aspx
        STACKONE_RENDER: https://dashboard.render.com/env-groups
        STACKONE_TERRAFORM: https://app.terraform.io/app/stackone_dev/settings/users
        STACKONE_DOMO: https://%s.domo.com/home
        STACKONE_ENVOY: https://dashboard.envoy.com/employees/%s
        STACKONE_SCALEWAY: https://console.scaleway.com/iam/users/%s/overview
        STACKONE_JETBRAINS: https://stackone.jetbrains.space/m/%s
        STACKONE_FIVETRAN: https://fivetran.com/dashboard/account/users-permissions/users
        STACKONE_INTERCOM: https://app.intercom.com/a/apps/v0o3t3qh/users/%s/all-conversations
        SALESFORCE: https://login.salesforce.com/
        SALESFORCE_UAR: https://login.salesforce.com/
        STACKONE_AIRCALL: https://dashboard.aircall.io/teams
        STACKONE_15FIVE: https://my.15five.com/
        STACKONE_ROLLBAR: https://app.rollbar.com/a/%s/projects
        STACKONE_EGNYTE: https://www.egnyte.com
        STACKONE_QLIK: https://www.qlik.com/us
        STACKONE_BULLHORN: https://www.bullhorn.com
        STACKONE_OPENVPN: https://openvpn.net
        STACKONE_SOPHOS: https://central.sophos.com/manage/overview/users-list
        STACKONE_MEISTERTASK: https://accounts.meister.co/team/members
        STACKONE_TALENTLMS: https://www.talentlms.com
        STACKONE_ONEFLOW: https://app.oneflow.com
        STACKONE_RING_CENTRAL: https://app.ringcentral.com
        STACKONE_ARTICULATE: https://account.articulate.com
        STACKONE_DIALPAD: https://www.dialpad.com
        STACKONE_TABLEAU: https://www.tableau.com
        AZURE_DEVOPS: https://dev.azure.com
        VERCEL: https://vercel.com/%s/~/settings/members
        GOOGLE_ADMIN_CONSOLE: https://admin.google.com/ac/users
        GOOGLE_ADMIN_CONSOLE_OAUTH: https://admin.google.com/ac/users

        STACKONE_LATTICE: https://lattice.com/
        STACKONE_MATILLIONETL: https://www.matillion.com/
        STACKONE_WEBFLOW: https://webflow.com/dashboard/login
        STACKONE_BOX: https://www.box.com/
        STACKONE_DROPBOX: https://www.dropbox.com/
        STACKONE_DROPBOX_SIGN: https://www.dropbox.com/sign
        STACKONE_HARVEST: https://www.getharvest.com/
        STACKONE_KAMELEOON: https://www.kameleoon.com/
        STACKONE_MAKE: https://www.make.com/
        STACKONE_RETOOL: https://retool.com/
        STACKONE_TOGGL: https://toggl.com/
        STACKONE_ANTHROPIC: https://console.anthropic.com/login
        STACKONE_SONARCLOUD: https://www.sonarsource.com/products/sonarcloud/
        STACKONE_1PASSWORD: https://1password.com/
        STACKONE_LACEWORK: https://www.lacework.com/
        STACKONE_OPTIMIZELY: https://www.optimizely.com/
        STACKONE_DATABRICKS: https://www.databricks.com/ # TODO: NOT DEPLOYED BY STACKONE YET, update when available
        STACKONE_IFS: https://www.ifs.com/ # TODO: NOT DEPLOYED BY STACKONE YET, update when available
        STACKONE_MIXPANEL: https://mixpanel.com/ # TODO: NOT DEPLOYED BY STACKONE YET, update when available
        STACKONE_TRAVISCI: https://www.travis-ci.com/ # TODO: NOT DEPLOYED BY STACKONE YET, update when available
        STACKONE_DUO: https://duo.com/
        STACKONE_GONG: https://app.gong.io/
        STACKONE_IRONCLAD: https://ironcladapp.com/,
        STACKONE_SCORO: https://www.scoro.com/,
        STACKONE_TEAMVIEWER_REMOTE: https://account.teamviewer.com,
        STACKONE_SPOTDRAFT: https://app.spotdraft.com/settings/access-control/team-members,
        STACKONE_SPENDESK: https://www.spendesk.com/,
        STACKONE_SENDGRID: https://sendgrid.com/en-us,
        STACKONE_SMARTSHEET: https://www.smartsheet.com/,
        STACKONE_CHECKMK: https://checkmk.com/
        STACKONE_ANSIBLE: https://www.ansible.com/
        STACKONE_TWILIO: https://www.twilio.com/
        STACKONE_OPENAI: https://openai.com/
        STACKONE_NETLIFY: https://www.netlify.com/

wiz:
    api:
        url: https://auth.app.wiz.io/oauth/token

customTestDetails:
    passDescription: Drata inspected %s custom connection to determine if the specified conditions were met.
    failedDescription: Drata inspected %s custom connection and determined the specified conditions were not met. Please check the test logic section to view the conditions.

xero:
    api:
        url: https://api.xero.com
        endpoints:
            account: api.xro/2.0/Accounts
            users: api.xro/2.0/Users
    oauth2:
        key: D7C713CD4D7A4AC29EDD4BE777B50CFB
        secret: CHANGEME
        redirect: http://localhost:5000/callback/xero
        tokenUrl: https://identity.xero.com/connect/token
        accessToken:
            grantType: authorization_code
        refreshToken:
            grantType: refresh_token
        scopes: accounting.settings accounting.settings.read

zoom:
    api:
        url: https://api.zoom.us
        endpoints:
            account: v2/users/me
            users: v2/users
            roles: v2/roles
            groups: v2/groups
    oauth2:
        key: AIgxTswBSS6i2aj9F935ig
        secret: CHANGEME
        redirect: http://localhost:5000/callback/zoom
        tokenUrl: https://zoom.us/oauth/token
        accessToken:
            grantType: authorization_code
        refreshToken:
            grantType: refresh_token
        scopes: account:read:admin user:read:admin role:read:admin
    webhook:
        headers:
            signature: x-zm-signature
            timestamp: x-zm-request-timestamp

complianceAsCode:
    s3:
        findingsBucket: ac-dev-finding-us-west-2-bucket
        iacUploadBucket: ac-dev-metamodel-us-west-2-bucket
        maxFindingsTimeoutMilliseconds: 900000
        findingsPollingDelayMilliseconds: 10000
        iacUploadPresignedUrlExpiresInSeconds: 900
        iacUploadCacheBuster: v1
    sqs:
        iacValidationStarted:
            name: 'LaunchValidationQueue'
            url: 'https://sqs.us-west-2.amazonaws.com/************/LaunchValidationQueue'
            region: 'us-west-2'
            enabled: true
notion:
    api:
        url: 'https://api.notion.com/v1'
        version: '2022-06-28'
        endpoints:
            search: 'workspace/search'
            pages: 'pages'
            export: 'export'
    oauth2:
        key: 'CHANGEME'
        secret: 'CHANGEME'
        tokenUrl: 'https://api.notion.com/v1/oauth/token'
        redirect: 'https://app.drata.com/callback/notion'
        accessToken:
            grantType: authorization_code
        refreshToken:
            grantType: refresh_token
        drataPrivateKey: 'CHANGEME'
    config:
        maxRetries: 30
        retryWaitTime: 500
vetty:
    api:
        url: https://stgapi.vetty.co
        endpoints:
            package: /cfapi/v1/packages/{package_id}
            packages: /cfapi/v1/packages
            applicant: /cfapi/v1/applicants
            screening: /cfapi/v1/screenings
            applicants: /cfapi/v1/applicants
        webhookHeader: 'vetty-sign'
    display:
        url: https://stgclient.vetty.co/client/dashboard
cyberark:
    paths:
        users: '/scim/v2/Users'
        auth: '/Security/StartAuthentication'

# This is intended to be used for testing purposes.
# DELETE THIS ONCE ALL THE PROVIDERS HAVE BEEN IMPLEMENTED.
uarEnhancements:
    showErrors: false

bambooHR:
    api:
        key: 'CHANGEME'
        url: 'https://api.bamboohr.com/api/gateway.php/%s/v1'
        endpoints:
            directory: 'employees/directory'
            employee: 'employees/%s'
            employeeFiles: 'employees/%s/files/view'
            employeeFile: 'employees/%s/files/%d'
            companyFiles: 'files/view'
            companyFileById: 'files/%s'
            listFields: 'meta/fields'
            login: 'oidcLogin'
            customReport: 'reports/custom'
            jobInfo: 'employees/%s/tables/jobInfo'
            employmentStatus: 'employees/%s/tables/employmentStatus'
    oauth2:
        key: 'CHANGEME'
        secret: 'CHANGEME'
        tokenUrl: 'https://%s.bamboohr.com/token.php?request=token'
        redirect: 'https://app.drata.com/callback/bambooHr'
        accessToken:
            grantType: authorization_code
        refreshToken:
            grantType: refresh_token
    config:
        maxRetries: 30
        retryWaitTime: 500

serviceUser:
    amountOfBufferHoursForRecentAccounts: 24

vendors:
    defaultReviewPeriod: 30
    defaultEmailContent: "Hi,\n\nWe'd like to conduct a security review and would like some information from you. Use this link to complete the questionnaire.\n\nThank you."
    csvProcessingBatchSize: 20 # Number of records to process in each batch to prevent timeouts

svix:
    api:
        key: 'CHANGEME'
    config:
        maxLimit: 50 # Currently Svix only allows 50 endpoints per application
        maxMessageAttemptsRetries: 3
        attemptsWaitTime: 2000

tenantDeletionWorkflow:
    deleteForms: false
    deleteS3Objects: false
    deleteDBData: false
    hardDeleteRegionalData: false

# Time at which the tenant reminder workflow should run. Temporarily set for 1 am CTZ
remindersSchedule:
    hour: 6
    minute: 0
    timezone: America/Chicago

flatfile:
    apiKey: 'CHANGEME'
    environmentId: 'CHANGEME'
    baseUrl: 'https://platform.flatfile.com/api'
    namespace: 'drata'
    pageSize: 100

aiAgent:
    api:
        baseUrl: 'CHANGEME'
