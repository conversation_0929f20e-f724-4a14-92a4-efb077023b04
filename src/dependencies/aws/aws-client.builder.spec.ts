import { AwsCredentialIdentity } from '@aws-sdk/types';
import { AwsSetupType } from 'app/apis/enums/aws-setup-type.enum';
import { ConfigurationParams } from 'app/apis/types/aws/aws-configuration-params.type';
import { LoggingContext } from 'commons/adapters/types/logging-context.type';
import * as awsHelpers from 'commons/helpers/aws.helper';
import { AwsClientBuilder } from 'dependencies/aws/aws-client.builder';
import { PolloLogger } from 'pollo-logger/pollo.logger';

describe('AwsClientBuilder', () => {
    let builder: AwsClientBuilder;
    let mockLogger: any;
    const params: ConfigurationParams = {
        RoleArn: 'arn:aws:iam::123456789012:role/test-role',
        ExternalId: 'test-external-id',
        RoleSessionName: 'test-session',
    };
    const loggingContext: LoggingContext = { domain: 'test.drata.com', traceId: 'test-trace-id' };

    beforeEach(() => {
        // Mock the logger
        mockLogger = {
            log: jest.fn(),
            warn: jest.fn(),
            error: jest.fn(),
        };
        jest.spyOn(PolloLogger, 'logger').mockReturnValue(mockLogger);

        // Create a new builder instance
        builder = new AwsClientBuilder(params, AwsSetupType.STANDARD, loggingContext);
    });

    afterEach(() => {
        jest.clearAllMocks();
        jest.restoreAllMocks();
    });

    describe('detectClockSkew', () => {
        const setupCredentials = async (expirationDate: Date) => {
            const credentials: AwsCredentialIdentity = {
                accessKeyId: 'test-key-id',
                secretAccessKey: 'test-secret-key',
                sessionToken: 'test-session-token',
                expiration: expirationDate,
            };

            jest.spyOn(awsHelpers, 'awsSetup').mockResolvedValue({ credentials });
            await builder.buildConfiguration();
        };

        it('should NOT log warning when no clock skew exists (normal operation)', async () => {
            // Setup: Credentials fetched now, expire in 60 minutes (normal AWS behavior)
            const now = new Date();
            const expirationDate = new Date(now.getTime() + 60 * 60 * 1000); // 60 minutes from now

            await setupCredentials(expirationDate);

            // Simulate 10 minutes passing
            jest.useFakeTimers();
            jest.setSystemTime(new Date(now.getTime() + 10 * 60 * 1000));

            // Trigger credential check (which calls detectClockSkew internally)
            const credentials: AwsCredentialIdentity = {
                accessKeyId: 'test-key-id',
                secretAccessKey: 'test-secret-key',
                sessionToken: 'test-session-token',
                expiration: expirationDate,
            };
            jest.spyOn(awsHelpers, 'awsSetup').mockResolvedValue({ credentials });

            // Access private method via reflection for testing
            const refreshMethod = (builder as any).refreshAwsCredentialsCheck.bind(builder);
            await refreshMethod();

            // Should NOT log any warnings (skew is ~0 minutes)
            expect(mockLogger.warn).not.toHaveBeenCalled();

            jest.useRealTimers();
        });

        it('should log warning when system time is 8 hours behind (APSE2 scenario)', async () => {
            // Setup: Credentials fetched 30 minutes ago (real time)
            const systemNow = new Date('2025-09-30T05:00:00Z'); // 8 hours behind

            // Credentials expire 60 minutes after real fetch time
            const expirationDate = new Date('2025-09-30T14:00:00Z');

            await setupCredentials(expirationDate);

            // Manually set lastCredentialFetchTime to 30 minutes ago (system time)
            (builder as any).lastCredentialFetchTime = new Date('2025-09-30T04:30:00Z');

            // Mock Date.now() and new Date() to return system time (8 hours behind)
            jest.useFakeTimers();
            jest.setSystemTime(systemNow);

            // Trigger credential check
            const credentials: AwsCredentialIdentity = {
                accessKeyId: 'test-key-id',
                secretAccessKey: 'test-secret-key',
                sessionToken: 'test-session-token',
                expiration: expirationDate,
            };
            jest.spyOn(awsHelpers, 'awsSetup').mockResolvedValue({ credentials });

            const refreshMethod = (builder as any).refreshAwsCredentialsCheck.bind(builder);
            await refreshMethod();

            // Should log warning about clock skew
            expect(mockLogger.warn).toHaveBeenCalled();
            const warningMessage = mockLogger.warn.mock.calls[0][0];
            // PolloAdapter wraps the message in an object with .msg property
            const messageText =
                typeof warningMessage === 'string' ? warningMessage : warningMessage.msg;
            expect(messageText).toContain('CLOCK SKEW DETECTED');
            expect(messageText).toContain('behind');
            expect(messageText).toContain('Please restart ECS tasks');

            jest.useRealTimers();
        });

        it('should log warning when system time is 1 hour behind', async () => {
            // Setup: Credentials fetched 15 minutes ago
            const systemNow = new Date('2025-09-30T13:00:00Z');

            // Credentials expire 60 minutes after real fetch time (14:45)
            const expirationDate = new Date('2025-09-30T14:45:00Z');

            await setupCredentials(expirationDate);

            // Set lastCredentialFetchTime to 15 minutes ago (system time)
            (builder as any).lastCredentialFetchTime = new Date('2025-09-30T12:45:00Z');

            jest.useFakeTimers();
            jest.setSystemTime(systemNow);

            const credentials: AwsCredentialIdentity = {
                accessKeyId: 'test-key-id',
                secretAccessKey: 'test-secret-key',
                sessionToken: 'test-session-token',
                expiration: expirationDate,
            };
            jest.spyOn(awsHelpers, 'awsSetup').mockResolvedValue({ credentials });

            const refreshMethod = (builder as any).refreshAwsCredentialsCheck.bind(builder);
            await refreshMethod();

            // Should log warning
            expect(mockLogger.warn).toHaveBeenCalled();
            const warningMessage = mockLogger.warn.mock.calls[0][0];
            const messageText =
                typeof warningMessage === 'string' ? warningMessage : warningMessage.msg;
            expect(messageText).toContain('CLOCK SKEW DETECTED');
            expect(messageText).toContain('behind');

            jest.useRealTimers();
        });

        it('should NOT log warning when clock skew is less than 5 minutes', async () => {
            // Setup: Small clock skew (3 minutes)
            const now = new Date('2025-09-30T13:00:00Z');

            // Credentials fetched 10 minutes ago
            (builder as any).lastCredentialFetchTime = new Date('2025-09-30T12:50:00Z');

            // Credentials expire in 53 minutes (expected: 50 minutes = 60 - 10)
            // Skew: 3 minutes (below threshold)
            const expirationDate = new Date('2025-09-30T13:53:00Z');

            await setupCredentials(expirationDate);

            jest.useFakeTimers();
            jest.setSystemTime(now);

            const credentials: AwsCredentialIdentity = {
                accessKeyId: 'test-key-id',
                secretAccessKey: 'test-secret-key',
                sessionToken: 'test-session-token',
                expiration: expirationDate,
            };
            jest.spyOn(awsHelpers, 'awsSetup').mockResolvedValue({ credentials });

            const refreshMethod = (builder as any).refreshAwsCredentialsCheck.bind(builder);
            await refreshMethod();

            // Should NOT log warning (skew < 5 minutes)
            expect(mockLogger.warn).not.toHaveBeenCalled();

            jest.useRealTimers();
        });

        it('should NOT log warning when credentials were fetched less than 5 minutes ago', async () => {
            // Setup: Credentials fetched 2 minutes ago
            const now = new Date('2025-09-30T13:00:00Z');

            (builder as any).lastCredentialFetchTime = new Date('2025-09-30T12:58:00Z');

            // Even with large apparent skew, should not log if fetch time < 5 min
            const expirationDate = new Date('2025-09-30T14:30:00Z');

            await setupCredentials(expirationDate);

            jest.useFakeTimers();
            jest.setSystemTime(now);

            const credentials: AwsCredentialIdentity = {
                accessKeyId: 'test-key-id',
                secretAccessKey: 'test-secret-key',
                sessionToken: 'test-session-token',
                expiration: expirationDate,
            };
            jest.spyOn(awsHelpers, 'awsSetup').mockResolvedValue({ credentials });

            const refreshMethod = (builder as any).refreshAwsCredentialsCheck.bind(builder);
            await refreshMethod();

            // Should NOT log warning (too soon after fetch)
            expect(mockLogger.warn).not.toHaveBeenCalled();

            jest.useRealTimers();
        });

        it('should NOT log warning when credentials have no expiration', async () => {
            const credentials: AwsCredentialIdentity = {
                accessKeyId: 'test-key-id',
                secretAccessKey: 'test-secret-key',
                sessionToken: 'test-session-token',
                // No expiration
            };

            jest.spyOn(awsHelpers, 'awsSetup').mockResolvedValue({ credentials });
            await builder.buildConfiguration();

            const refreshMethod = (builder as any).refreshAwsCredentialsCheck.bind(builder);
            await refreshMethod();

            // Should NOT log warning (no expiration to check)
            expect(mockLogger.warn).not.toHaveBeenCalled();
        });

        it('should NOT log warning when lastCredentialFetchTime is not set', async () => {
            const expirationDate = new Date('2025-09-30T14:00:00Z');
            const credentials: AwsCredentialIdentity = {
                accessKeyId: 'test-key-id',
                secretAccessKey: 'test-secret-key',
                sessionToken: 'test-session-token',
                expiration: expirationDate,
            };

            jest.spyOn(awsHelpers, 'awsSetup').mockResolvedValue({ credentials });
            await builder.buildConfiguration();

            // Manually clear lastCredentialFetchTime
            (builder as any).lastCredentialFetchTime = undefined;

            const refreshMethod = (builder as any).refreshAwsCredentialsCheck.bind(builder);
            await refreshMethod();

            // Should NOT log warning (no fetch time to compare)
            expect(mockLogger.warn).not.toHaveBeenCalled();
        });
    });

    describe('refreshAwsCredentialsCheck', () => {
        it('should log when skipping refresh for credentials without expiration', async () => {
            const credentials: AwsCredentialIdentity = {
                accessKeyId: 'test-key-id',
                secretAccessKey: 'test-secret-key',
                sessionToken: 'test-session-token',
                // No expiration
            };

            jest.spyOn(awsHelpers, 'awsSetup').mockResolvedValue({ credentials });
            await builder.buildConfiguration();

            const refreshMethod = (builder as any).refreshAwsCredentialsCheck.bind(builder);
            await refreshMethod();

            // Should log that refresh was skipped
            expect(mockLogger.log).toHaveBeenCalledWith(
                expect.objectContaining({
                    msg: '[AWS Credential Refresh] Skipped. Credentials have no expiration.',
                }),
            );
        });

        it('should refresh credentials when they will expire within 15 minutes', async () => {
            // Setup: Credentials will expire in 10 minutes
            const now = new Date('2025-09-30T13:50:00Z');
            const expirationDate = new Date('2025-09-30T14:00:00Z'); // 10 minutes from now

            const oldCredentials: AwsCredentialIdentity = {
                accessKeyId: 'old-key-id',
                secretAccessKey: 'old-secret-key',
                sessionToken: 'old-session-token',
                expiration: expirationDate,
            };

            const newCredentials: AwsCredentialIdentity = {
                accessKeyId: 'new-key-id',
                secretAccessKey: 'new-secret-key',
                sessionToken: 'new-session-token',
                expiration: new Date('2025-09-30T14:50:00Z'),
            };

            jest.spyOn(awsHelpers, 'awsSetup').mockResolvedValue({ credentials: oldCredentials });
            await builder.buildConfiguration();

            jest.useFakeTimers();
            jest.setSystemTime(now);

            // Mock the refresh to return new credentials
            jest.spyOn(awsHelpers, 'awsSetup').mockResolvedValue({ credentials: newCredentials });

            const refreshMethod = (builder as any).refreshAwsCredentialsCheck.bind(builder);
            await refreshMethod();

            // Should log refresh reason
            expect(mockLogger.log).toHaveBeenCalledWith(
                expect.objectContaining({
                    msg: expect.stringContaining('expiring soon'),
                }),
            );

            jest.useRealTimers();
        });

        it('should refresh credentials when they have expired', async () => {
            // Setup: Credentials expired 5 minutes ago
            const now = new Date('2025-09-30T14:05:00Z');
            const expirationDate = new Date('2025-09-30T14:00:00Z'); // 5 minutes ago

            const oldCredentials: AwsCredentialIdentity = {
                accessKeyId: 'old-key-id',
                secretAccessKey: 'old-secret-key',
                sessionToken: 'old-session-token',
                expiration: expirationDate,
            };

            const newCredentials: AwsCredentialIdentity = {
                accessKeyId: 'new-key-id',
                secretAccessKey: 'new-secret-key',
                sessionToken: 'new-session-token',
                expiration: new Date('2025-09-30T15:05:00Z'),
            };

            jest.spyOn(awsHelpers, 'awsSetup').mockResolvedValue({ credentials: oldCredentials });
            await builder.buildConfiguration();

            jest.useFakeTimers();
            jest.setSystemTime(now);

            // Mock the refresh to return new credentials
            jest.spyOn(awsHelpers, 'awsSetup').mockResolvedValue({ credentials: newCredentials });

            const refreshMethod = (builder as any).refreshAwsCredentialsCheck.bind(builder);
            await refreshMethod();

            // Should log refresh reason
            expect(mockLogger.log).toHaveBeenCalledWith(
                expect.objectContaining({
                    msg: expect.stringContaining('expired'),
                }),
            );

            jest.useRealTimers();
        });

        it('should prevent duplicate checks when refresh is already in progress', async () => {
            // Setup: Expired credentials
            const oldCredentials: AwsCredentialIdentity = {
                accessKeyId: 'old-key-id',
                secretAccessKey: 'old-secret-key',
                sessionToken: 'old-session-token',
                expiration: new Date('2025-09-30T13:00:00Z'), // Already expired
            };

            const newCredentials: AwsCredentialIdentity = {
                accessKeyId: 'new-key-id',
                secretAccessKey: 'new-secret-key',
                sessionToken: 'new-session-token',
                expiration: new Date('2025-09-30T14:30:00Z'),
            };

            jest.spyOn(awsHelpers, 'awsSetup').mockResolvedValue({ credentials: oldCredentials });
            await builder.buildConfiguration();

            const now = new Date('2025-09-30T13:30:00Z');
            jest.useFakeTimers();
            jest.setSystemTime(now);

            // Track how many times awsSetup is called
            let awsSetupCallCount = 0;
            jest.spyOn(awsHelpers, 'awsSetup').mockImplementation(async () => {
                awsSetupCallCount++;
                return { credentials: newCredentials };
            });

            const refreshMethod = (builder as any).refreshAwsCredentialsCheck.bind(builder);

            // Make 3 concurrent calls to refreshAwsCredentialsCheck
            const [result1, result2, result3] = await Promise.all([
                refreshMethod(),
                refreshMethod(),
                refreshMethod(),
            ]);

            // All should return the same builder instance
            expect(result1).toBe(builder);
            expect(result2).toBe(builder);
            expect(result3).toBe(builder);

            // awsSetup should only be called ONCE despite 3 concurrent calls
            expect(awsSetupCallCount).toBe(1);

            // Should log that refresh was already in progress for the 2nd and 3rd calls
            const inProgressLogs = mockLogger.log.mock.calls.filter(call =>
                call[0].msg?.includes('[AWS Credential Refresh] Refresh already in progress'),
            );
            expect(inProgressLogs.length).toBeGreaterThanOrEqual(2);

            jest.useRealTimers();
        });
    });

    describe('refreshCredentials', () => {
        it('should force refresh credentials regardless of expiration', async () => {
            // Setup: Fresh credentials that don't need refresh
            const now = new Date('2025-09-30T13:00:00Z');
            const expirationDate = new Date('2025-09-30T14:00:00Z'); // 60 minutes from now

            const oldCredentials: AwsCredentialIdentity = {
                accessKeyId: 'old-key-id',
                secretAccessKey: 'old-secret-key',
                sessionToken: 'old-session-token',
                expiration: expirationDate,
            };

            const newCredentials: AwsCredentialIdentity = {
                accessKeyId: 'new-key-id',
                secretAccessKey: 'new-secret-key',
                sessionToken: 'new-session-token',
                expiration: new Date('2025-09-30T14:30:00Z'),
            };

            jest.spyOn(awsHelpers, 'awsSetup').mockResolvedValue({ credentials: oldCredentials });
            await builder.buildConfiguration();

            jest.useFakeTimers();
            jest.setSystemTime(now);

            // Mock the refresh to return new credentials
            jest.spyOn(awsHelpers, 'awsSetup').mockResolvedValue({ credentials: newCredentials });

            // Force refresh
            await builder.refreshCredentials();

            // Should log force refresh initiated
            expect(mockLogger.log).toHaveBeenCalledWith(
                expect.objectContaining({
                    msg: '[AWS Credential Refresh] Force refresh initiated',
                }),
            );

            // Should log successful refresh
            expect(mockLogger.log).toHaveBeenCalledWith(
                expect.objectContaining({
                    msg: expect.stringContaining(
                        '[AWS Credential Refresh] Force refresh successful',
                    ),
                }),
            );

            jest.useRealTimers();
        });

        it('should prevent race conditions when multiple concurrent refresh calls are made', async () => {
            // Setup: Initial credentials
            const oldCredentials: AwsCredentialIdentity = {
                accessKeyId: 'old-key-id',
                secretAccessKey: 'old-secret-key',
                sessionToken: 'old-session-token',
                expiration: new Date('2025-09-30T14:00:00Z'),
            };

            const newCredentials: AwsCredentialIdentity = {
                accessKeyId: 'new-key-id',
                secretAccessKey: 'new-secret-key',
                sessionToken: 'new-session-token',
                expiration: new Date('2025-09-30T14:30:00Z'),
            };

            jest.spyOn(awsHelpers, 'awsSetup').mockResolvedValue({ credentials: oldCredentials });
            await builder.buildConfiguration();

            // Track how many times awsSetup is called
            let awsSetupCallCount = 0;
            jest.spyOn(awsHelpers, 'awsSetup').mockImplementation(async () => {
                awsSetupCallCount++;
                return { credentials: newCredentials };
            });

            // Make 3 concurrent refresh calls
            const [result1, result2, result3] = await Promise.all([
                builder.refreshCredentials(),
                builder.refreshCredentials(),
                builder.refreshCredentials(),
            ]);

            // All should return the same builder instance
            expect(result1).toBe(builder);
            expect(result2).toBe(builder);
            expect(result3).toBe(builder);

            // awsSetup should only be called ONCE despite 3 concurrent calls
            expect(awsSetupCallCount).toBe(1);

            // Should log that subsequent calls are waiting
            expect(mockLogger.log).toHaveBeenCalledWith(
                expect.objectContaining({
                    msg: '[AWS Credential Refresh] Force refresh already in progress, waiting for completion',
                }),
            );
        });

        it('should allow new refresh after previous refresh completes', async () => {
            // Setup: Initial credentials
            const oldCredentials: AwsCredentialIdentity = {
                accessKeyId: 'old-key-id',
                secretAccessKey: 'old-secret-key',
                sessionToken: 'old-session-token',
                expiration: new Date('2025-09-30T14:00:00Z'),
            };

            const newCredentials1: AwsCredentialIdentity = {
                accessKeyId: 'new-key-id-1',
                secretAccessKey: 'new-secret-key-1',
                sessionToken: 'new-session-token-1',
                expiration: new Date('2025-09-30T14:30:00Z'),
            };

            const newCredentials2: AwsCredentialIdentity = {
                accessKeyId: 'new-key-id-2',
                secretAccessKey: 'new-secret-key-2',
                sessionToken: 'new-session-token-2',
                expiration: new Date('2025-09-30T15:00:00Z'),
            };

            jest.spyOn(awsHelpers, 'awsSetup').mockResolvedValue({ credentials: oldCredentials });
            await builder.buildConfiguration();

            // First refresh
            jest.spyOn(awsHelpers, 'awsSetup').mockResolvedValue({ credentials: newCredentials1 });
            await builder.refreshCredentials();

            // Second refresh (should be allowed after first completes)
            jest.spyOn(awsHelpers, 'awsSetup').mockResolvedValue({ credentials: newCredentials2 });
            await builder.refreshCredentials();

            // Should log force refresh initiated twice
            const initiatedLogs = mockLogger.log.mock.calls.filter(call =>
                call[0].msg?.includes('[AWS Credential Refresh] Force refresh initiated'),
            );
            expect(initiatedLogs.length).toBe(2);
        });

        it('should clear refresh promise even if refresh fails', async () => {
            // Setup: Initial credentials
            const oldCredentials: AwsCredentialIdentity = {
                accessKeyId: 'old-key-id',
                secretAccessKey: 'old-secret-key',
                sessionToken: 'old-session-token',
                expiration: new Date('2025-09-30T14:00:00Z'),
            };

            jest.spyOn(awsHelpers, 'awsSetup').mockResolvedValue({ credentials: oldCredentials });
            await builder.buildConfiguration();

            // Mock refresh to fail
            jest.spyOn(awsHelpers, 'awsSetup').mockRejectedValue(new Error('AWS API error'));

            // First refresh (will fail)
            await builder.refreshCredentials();

            // Should log failure
            expect(mockLogger.log).toHaveBeenCalledWith(
                expect.objectContaining({
                    msg: expect.stringContaining('[AWS Credential Refresh] Force refresh failed'),
                }),
            );

            // Mock refresh to succeed
            const newCredentials: AwsCredentialIdentity = {
                accessKeyId: 'new-key-id',
                secretAccessKey: 'new-secret-key',
                sessionToken: 'new-session-token',
                expiration: new Date('2025-09-30T14:30:00Z'),
            };
            jest.spyOn(awsHelpers, 'awsSetup').mockResolvedValue({ credentials: newCredentials });

            // Second refresh should be allowed (promise was cleared after failure)
            await builder.refreshCredentials();

            // Should log successful refresh
            expect(mockLogger.log).toHaveBeenCalledWith(
                expect.objectContaining({
                    msg: expect.stringContaining(
                        '[AWS Credential Refresh] Force refresh successful',
                    ),
                }),
            );
        });
    });
});
