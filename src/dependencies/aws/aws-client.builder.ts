import { AwsCredentialIdentity } from '@aws-sdk/types';
import { AwsSetupType } from 'app/apis/enums/aws-setup-type.enum';
import { ConfigurationParams } from 'app/apis/types/aws/aws-configuration-params.type';
import { AwsConfiguration } from 'app/apis/types/aws/aws-configuration.type';
import { PolloAdapter } from 'commons/adapters/pollo.adapter';
import { LoggingContext } from 'commons/adapters/types/logging-context.type';
import { awsSetup, awsSetupGovCloud } from 'commons/helpers/aws.helper';
import { hasExpired, isWithinMinutes } from 'commons/helpers/date.helper';
import { AwsClientBuilderBase } from 'dependencies/aws/aws-client.builder.base';
import { ClientBuilderWrapper } from 'dependencies/aws/aws-client.builder.wrapper';
import { isUndefined } from 'lodash';
import { PolloLogger } from 'pollo-logger/pollo.logger';

/**
 * This class is responsible for creating and managing AWS service clients.
 * It handles credential management, client configuration, and client instantiation.
 *
 * Usage:
 * 1. Create an instance with appropriate configuration parameters
 * 2. Call buildConfiguration() to initialize credentials
 * 4. Use client() method to create service clients then build client
 * 5. You can optionally set region and httpOptions for specific clients
 *
 * Example: Initialize and use the client builder
 * ```
 * const builder = new AwsClientBuilder(params);
 * await builder.buildConfiguration();
 * const s3Client = await builder.client(S3Client).setRegion('us-west-2').build();
 * ```
 *
 * Example setting region, httpOptions and building for a specific service client:
 * ```
 * const s3Client = await awsClientBuilder
 *   .client(S3Client)
 *   .setRegion('us-east-2')
 *   .setHttpOptions({ timeout: 5000 })
 *   .build();
 * ```
 */
export class AwsClientBuilder implements AwsClientBuilderBase {
    // Credential refresh thresholds
    private static readonly CREDENTIAL_REFRESH_THRESHOLD_MINUTES = 15;
    private static readonly AWS_CREDENTIAL_LIFETIME_MINUTES = 60;
    private static readonly CLOCK_SKEW_THRESHOLD_MINUTES = 5;
    private static readonly CLOCK_SKEW_MIN_FETCH_TIME_MINUTES = 5;

    private logger = PolloLogger.logger(this.constructor.name);
    private defaultRegion: string | undefined;
    private endpoint: string | undefined;
    private credentials: AwsCredentialIdentity | undefined;
    private lastCredentialFetchTime: Date | undefined;
    private refreshPromise: Promise<AwsClientBuilderBase> | null = null;

    constructor(
        private readonly configurationParams: ConfigurationParams,
        private readonly setupType: AwsSetupType,
        private readonly loggingContext?: LoggingContext,
    ) {}

    get region(): string | undefined {
        return this.defaultRegion;
    }

    setDefaultRegion(region: string): AwsClientBuilderBase {
        this.defaultRegion = region;
        return this;
    }

    setEndpoint(endpoint: string): AwsClientBuilderBase {
        this.endpoint = endpoint;
        return this;
    }

    /**
     * Creates a client wrapper for the specified AWS service
     *
     * @param clientConstructor AWS service constructor (e.g., S3, EC2)
     * @returns A client wrapper that can be further configured
     */
    client<T>(clientConstructor: new (config: AwsConfiguration) => T): ClientBuilderWrapper<T> {
        return new ClientBuilderWrapper<T>(this, clientConstructor);
    }

    /**
     * Initializes the AWS client builder with credentials and configuration.
     * This method must be called before using the builder to create AWS clients.
     *
     * @returns Promise resolving to this instance for method chaining
     */
    async buildConfiguration(): Promise<AwsClientBuilderBase> {
        // Fetch initial AWS configuration (credentials, region, endpoint)
        const config = await this.fetchConfiguration();

        if (config?.credentials) {
            this.credentials = config.credentials;
            this.lastCredentialFetchTime = new Date();
        }

        if (config?.endpoint) {
            this.endpoint = config.endpoint;
        }

        if (config?.region) {
            this.setDefaultRegion(config.region);
        }

        return this;
    }

    /**
     * Gets the AWS configuration with the latest credentials and specified options.
     * This method checks if credentials need to be refreshed before returning the configuration.
     *
     * @param config Partial configuration to merge with the base configuration
     * @returns Promise resolving to the AWS configuration
     */
    async getConfiguration(config: Partial<AwsConfiguration>): Promise<AwsConfiguration> {
        await this.refreshAwsCredentialsCheck();

        return {
            credentials: this.credentials,
            endpoint: this.endpoint,
            region: config.region || this.defaultRegion,
            httpOptions: config.httpOptions,
        } as AwsConfiguration;
    }

    /**
     * Manually forces a credential refresh regardless of expiration time.
     * Useful for long-running tasks that need to ensure fresh credentials.
     *
     * If a refresh is already in progress, this method will return the existing
     * refresh promise instead of initiating a new refresh operation. This prevents
     * race conditions when multiple concurrent calls are made.
     *
     * @returns Promise resolving to this instance for method chaining
     */
    async refreshCredentials(): Promise<AwsClientBuilderBase> {
        // If a refresh is already in progress, return the existing promise
        if (this.refreshPromise !== null) {
            this.logCredentialRefresh(
                '[AWS Credential Refresh] Force refresh already in progress, waiting for completion',
            );
            return this.refreshPromise;
        }

        // Create and store the refresh promise
        this.refreshPromise = this.performCredentialRefresh();

        try {
            // Wait for the refresh to complete
            await this.refreshPromise;
        } finally {
            // Clear the promise once complete (success or failure)
            this.refreshPromise = null;
        }

        return this;
    }

    /**
     * Performs the actual credential refresh operation.
     * This is separated from refreshCredentials() to allow promise reuse.
     *
     * @returns Promise resolving to this instance
     */
    private async performCredentialRefresh(): Promise<AwsClientBuilderBase> {
        this.logCredentialRefresh('[AWS Credential Refresh] Force refresh initiated');

        try {
            const { credentials } = await this.fetchConfiguration();

            if (credentials) {
                this.credentials = credentials;
                this.lastCredentialFetchTime = new Date();
                this.logCredentialRefresh(
                    `[AWS Credential Refresh] Force refresh successful. ` +
                        `New expiration: ${credentials.expiration}`,
                );
            } else {
                this.logCredentialRefresh(
                    '[AWS Credential Refresh] Force refresh failed - no credentials returned',
                );
            }
        } catch (error) {
            this.logCredentialRefresh(
                `[AWS Credential Refresh] Force refresh failed: ${error.message}`,
            );
        }

        return this;
    }

    private fetchConfiguration(): Promise<AwsConfiguration> {
        return this.setupType === AwsSetupType.GOV_CLOUD
            ? awsSetupGovCloud(this.configurationParams)
            : awsSetup(this.configurationParams);
    }

    /**
     * Checks if AWS credentials need to be refreshed and refreshes them if necessary.
     * Uses two strategies to determine if refresh is needed:
     * 1. hasExpired() - Check if credentials have already expired
     * 2. isWithinMinutes() - Check if credentials will expire within the threshold (future only)
     *
     * The isWithinMinutes() check only returns true for future dates, making it immune
     * to clock skew issues where expired credentials might appear to be in the past.
     *
     * If a refresh is already in progress, this method will wait for it to complete
     * instead of performing duplicate expiration checks.
     */
    private async refreshAwsCredentialsCheck(): Promise<AwsClientBuilderBase> {
        // If a refresh is already in progress, wait for it to complete
        if (this.refreshPromise !== null) {
            this.logCredentialRefresh(
                '[AWS Credential Refresh] Refresh already in progress, waiting for completion',
            );
            await this.refreshPromise;
            return this;
        }

        // Skip refresh if credentials don't have an expiration
        if (isUndefined(this.credentials?.expiration)) {
            this.logCredentialRefresh(
                '[AWS Credential Refresh] Skipped. Credentials have no expiration.',
            );
            return this;
        }

        // Check if credentials have expired or will expire soon
        const credentialsHasExpired = hasExpired(this.credentials.expiration);
        const credentialsWillExpire = isWithinMinutes(
            this.credentials.expiration,
            AwsClientBuilder.CREDENTIAL_REFRESH_THRESHOLD_MINUTES,
        );

        // Calculate minutes since last fetch for logging
        let minutesSinceLastFetch = 0;
        if (this.lastCredentialFetchTime) {
            const timeSinceLastFetch = Date.now() - this.lastCredentialFetchTime.getTime();
            minutesSinceLastFetch = timeSinceLastFetch / (60 * 1000);
        }

        // CLOCK SKEW DETECTION: Check if system time is significantly different from credential expiration
        this.detectClockSkew(minutesSinceLastFetch);

        // Only refresh if needed
        if (credentialsHasExpired || credentialsWillExpire) {
            const refreshReason = credentialsHasExpired ? 'expired' : 'expiring soon';

            this.logCredentialRefresh(
                `[AWS Credential Refresh] Reason: ${refreshReason}. ` +
                    `Expiration: ${this.credentials.expiration}. ` +
                    `Minutes since last fetch: ${minutesSinceLastFetch.toFixed(2)}`,
            );

            try {
                await this.refreshCredentials();
            } catch (error) {
                this.logCredentialRefresh(`[AWS Credential Refresh] Failed: ${error.message}`);
            }
        }

        return this;
    }

    /**
     * Detects potential clock skew by comparing system time with credential expiration.
     * Logs a warning if significant clock skew is detected.
     */
    private detectClockSkew(minutesSinceLastFetch: number): void {
        if (!this.credentials?.expiration || !this.lastCredentialFetchTime) {
            return;
        }

        const now = new Date();
        const expirationTime = new Date(this.credentials.expiration);
        const timeUntilExpiration = expirationTime.getTime() - now.getTime();
        const minutesUntilExpiration = timeUntilExpiration / (60 * 1000);

        // AWS credentials typically expire after 1 hour
        // If we fetched credentials more than 30 minutes ago but they still show 50+ minutes
        // until expiration, this indicates clock skew (system time is behind)
        const expectedMinutesRemaining =
            AwsClientBuilder.AWS_CREDENTIAL_LIFETIME_MINUTES - minutesSinceLastFetch;
        const skewMinutes = minutesUntilExpiration - expectedMinutesRemaining;

        // Log warning if clock skew is detected
        if (
            Math.abs(skewMinutes) > AwsClientBuilder.CLOCK_SKEW_THRESHOLD_MINUTES &&
            minutesSinceLastFetch > AwsClientBuilder.CLOCK_SKEW_MIN_FETCH_TIME_MINUTES
        ) {
            const skewDirection = skewMinutes > 0 ? 'behind' : 'ahead';
            const skewAmount = Math.abs(skewMinutes).toFixed(1);

            this.logger.warn(
                PolloAdapter.cxt(
                    `⚠️  CLOCK SKEW DETECTED: System time appears to be ${skewDirection} ` +
                        `by approximately ${skewAmount} minutes. ` +
                        `Credentials fetched ${minutesSinceLastFetch.toFixed(1)} minutes ago, ` +
                        `but show ${minutesUntilExpiration.toFixed(1)} minutes until expiration ` +
                        `(expected ~${expectedMinutesRemaining.toFixed(1)} minutes). ` +
                        `System time: ${now.toISOString()}, ` +
                        `Credential expiration: ${expirationTime.toISOString()}. ` +
                        `This may cause "Signature expired" errors. ` +
                        `Please restart ECS tasks to fix system time.`,
                    this.loggingContext,
                ),
            );
        }
    }

    private logCredentialRefresh(message: string): void {
        if (this.loggingContext) {
            this.logger.log(PolloAdapter.cxt(message, this.loggingContext));
        }
    }
}
