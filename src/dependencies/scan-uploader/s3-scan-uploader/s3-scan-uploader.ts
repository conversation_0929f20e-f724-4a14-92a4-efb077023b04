import { GetObjectCommand, S3Client, S3ClientConfig } from '@aws-sdk/client-s3';
import { Upload } from '@aws-sdk/lib-storage';
import { Injectable, ServiceUnavailableException } from '@nestjs/common';
import { AwsCredentialsFactory } from 'app/apis/factories/aws-credentials.factory';
import { Account } from 'auth/entities/account.entity';
import { fileNameNoExt } from 'commons/helpers/upload.helper';
import { generateMetadata } from 'commons/helpers/uploader.helper';
import { UploadedFileType } from 'commons/types/uploaded-file.type';
import config from 'config';
import { ScanBucketLocation } from 'dependencies/scan-uploader/enums/scan-bucket-location.enum';
import { ScanUploader } from 'dependencies/scan-uploader/scan-uploader';
import { ScannerUploaderPayloadType } from 'dependencies/scan-uploader/types/scanner-uploader-payload.type';
import https from 'https';
import { isEmpty } from 'lodash';

@Injectable()
export class S3ScanUploader extends ScanUploader {
    private client: S3Client;

    constructor() {
        super();
        this.client = this.getClient();
    }

    async uploadFile(
        account: Account,
        file: UploadedFileType,
        key: string,
    ): Promise<ScannerUploaderPayloadType> {
        try {
            const params = {
                Body: file.buffer,
                Bucket: config.get('scannerUploader.s3.scanningBucket'),
                Key: key,
                ContentType: file.mimetype,
                ContentLength: file.size,
                Metadata: generateMetadata(),
            };

            const upload = new Upload({
                client: this.getClient(),
                params,
            });

            await upload.done();

            this.log('Document sent to scan', account, {
                key,
            });

            return {
                key,
                fileName: fileNameNoExt(file.originalname),
                scanning: true,
            };
        } catch (e) {
            this.error(e, account, { key });
            throw new ServiceUnavailableException('Could not upload file');
        }
    }

    getClient(): S3Client {
        const clientConfig: S3ClientConfig = {
            region: config.get('scannerUploader.s3.region'),
            credentials: AwsCredentialsFactory.buildCredentials(),
            requestHandler: {
                httpsAgent: new https.Agent({
                    keepAlive: true,
                    rejectUnauthorized: true,
                }),
            },
        };

        // Add LocalStack configuration for local development
        if (config.get('aws.localAws.enabled')) {
            clientConfig.endpoint = config.get('aws.localAws.url');
            clientConfig.forcePathStyle = true;
        }

        return new S3Client(clientConfig);
    }

    private async existsFileInBucket(key: string, fromBucket: string): Promise<boolean> {
        try {
            const command = new GetObjectCommand({
                Bucket: fromBucket,
                Key: key,
            });

            const response = await this.client.send(command);
            return !isEmpty(response);
        } catch (error) {
            return false;
        }
    }

    async checkFileCurrentBucket(key: string): Promise<ScanBucketLocation> {
        const production = await this.existsFileInBucket(key, config.get('aws.s3.appBucket'));

        if (production) {
            return ScanBucketLocation.PRODUCTION;
        }

        const staging = await this.existsFileInBucket(
            key,
            config.get('scannerUploader.s3.scanningBucket'),
        );
        if (staging) {
            return ScanBucketLocation.STAGING;
        }

        const quarantine = await this.existsFileInBucket(
            key,
            config.get('scannerUploader.s3.scanningQuarantineBucket'),
        );
        if (quarantine) {
            return ScanBucketLocation.QUARANTINE;
        }
    }
}
