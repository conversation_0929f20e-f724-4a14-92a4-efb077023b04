import {
    GetO<PERSON><PERSON>ommand,
    HeadO<PERSON>Command,
    ListObjectVersionsCommand,
    ListObjectVersionsOutput,
    S3Client,
    S3ClientConfig,
} from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import {
    BadRequestException,
    Injectable,
    InternalServerErrorException,
    NotFoundException,
} from '@nestjs/common';
import { AwsCredentialsFactory } from 'app/apis/factories/aws-credentials.factory';
import { getBufferFromStream } from 'commons/helpers/buffer.helper';
import { getFileMetadataFromStream } from 'commons/helpers/upload.helper';
import config from 'config';
import { IStorable } from 'dependencies/archiver/interfaces/storable.interface';
import { Downloader } from 'dependencies/downloader/downloader';
import { DownloadOptions } from 'dependencies/downloader/types/download-options.type';
import { DownloadStreamDetails } from 'dependencies/downloader/types/download-stream-details.type';
import { DownloaderPayloadType } from 'dependencies/downloader/types/downloader-payload.interface';
import core from 'file-type/core';
import https from 'https';
import { basename } from 'path';
import { Readable } from 'stream';

@Injectable()
export class S3Downloader extends Downloader {
    private client: S3Client;

    constructor() {
        // setup parent constructor
        super();
        // set the S3 client
        this.client = this.getClient();
    }

    getClient(): S3Client {
        const clientConfig: S3ClientConfig = {
            region: config.get('aws.s3.region'),
            credentials: AwsCredentialsFactory.buildCredentials(),
            requestHandler: {
                httpsAgent: new https.Agent({
                    keepAlive: true,
                    rejectUnauthorized: true,
                }),
            },
        };

        // Add LocalStack configuration for local development
        if (config.get('aws.localAws.enabled')) {
            clientConfig.endpoint = config.get('aws.localAws.url');
            clientConfig.forcePathStyle = true;
        }

        return new S3Client(clientConfig);
    }

    /**
     * From stackoverflow:
     * @aws-sdk/client-s3 (2022 Update)
     * https://stackoverflow.com/questions/36942442/how-to-get-response-from-s3-getobject-in-node-js
     *
     * @param key
     */
    async getPrivateFileBuffer(key: string): Promise<Buffer> {
        const getObjectCommand = new GetObjectCommand({
            Bucket: config.get('aws.s3.appBucket'),
            Key: key,
        });

        const s3Object = await this.client.send(getObjectCommand);

        return getBufferFromStream(s3Object.Body as Readable);
    }

    /**
     * Use this method to get binary buffer from s3 object
     *
     * @param key
     * @returns File buffer data
     */
    async getPrivateFileBinaryBuffer(key: string): Promise<Buffer> {
        const params = new GetObjectCommand({
            Bucket: config.get('aws.s3.appBucket'),
            Key: key,
        });

        const s3Object = await this.client.send(params);

        if (!s3Object.Body) {
            return Buffer.alloc(0);
        }

        return getBufferFromStream(s3Object.Body as Readable);
    }

    /**
     *
     * @param key
     */
    async getPrivateFileStream(key: string): Promise<Readable> {
        const getObjectCommand = new GetObjectCommand({
            Bucket: config.get('aws.s3.appBucket'),
            Key: key,
        });

        const s3Object = await this.client.send(getObjectCommand);

        return s3Object.Body as Readable;
    }

    /**
     *
     * From stackoverflow:
     * @aws-sdk/client-s3 (2022 Update)
     * https://stackoverflow.com/questions/36942442/how-to-get-response-from-s3-getobject-in-node-js
     *
     *
     * @param bucket
     * @param key
     * @returns
     *
     */
    async getPrivateFileWithBucket<T>(bucket: string, key: string): Promise<T> {
        const getObjectCommand = new GetObjectCommand({
            Bucket: bucket,
            Key: key,
        });

        const response = await this.client.send(getObjectCommand);

        const readableBody = response.Body as Readable;

        return new Promise<T>(async (resolve, reject) => {
            try {
                const responseChunks = [];

                readableBody.once('error', reject);
                readableBody.on('data', chunk => responseChunks.push(chunk));
                readableBody.once('end', () => resolve(JSON.parse(responseChunks.join(''))));
            } catch (err) {
                reject(err);
            }
        });
    }

    /**
     *
     * @param key
     * @param options
     * @returns
     *
     */
    async getDownloadUrl(key: string, options?: DownloadOptions): Promise<DownloaderPayloadType> {
        const command = new GetObjectCommand({
            Bucket: config.get('aws.s3.appBucket'),
            Key: key,
            ResponseContentDisposition: options?.directDownload ? 'attachment' : undefined,
        });

        const signedUrl = await getSignedUrl(this.client, command, {
            expiresIn: options?.expirationTime ?? config.get('aws.s3.expires'),
        });

        const payload: DownloaderPayloadType = {
            signedUrl,
        };

        return payload;
    }

    /**
     *
     * @param key
     * @returns
     *
     */
    async getFileMetadataFromFile(key: string): Promise<core.FileTypeResult> {
        const command = new GetObjectCommand({
            Bucket: config.get('aws.s3.appBucket'),
            Key: key,
        });

        const s3Response = await this.client.send(command);

        const stream = s3Response.Body as Readable;

        const fileTypeResult = await getFileMetadataFromStream(stream);

        if (!fileTypeResult) {
            throw new InternalServerErrorException('Unable to determine file type.');
        }

        return fileTypeResult;
    }

    /**
     *
     * @param files
     * @param bucket
     *
     */
    async getReadStreams(files: IStorable[], bucket: string): Promise<DownloadStreamDetails[]> {
        const promises = files.map(async file => {
            try {
                const headCommand = new HeadObjectCommand({
                    Bucket: bucket,
                    Key: file.file,
                });

                await this.client.send(headCommand);
                return file;
            } catch (error) {
                // File does not exist or an error occurred, return null or undefined
                return null;
            }
        });

        const validFiles: IStorable[] = (await Promise.all(promises)).filter(file => file !== null);

        if (validFiles.length === 0) {
            throw new NotFoundException();
        }

        const downloadStreams: DownloadStreamDetails[] = await Promise.all(
            validFiles.map(async (file: IStorable) => {
                if (!file?.file) {
                    throw new BadRequestException('File missing');
                }

                const command = new GetObjectCommand({ Bucket: bucket, Key: file.file });

                const { Body } = await this.client.send(command);

                if (!Body) {
                    throw new InternalServerErrorException(
                        `Failed to get object stream for file: ${file.file}`,
                    );
                }

                return {
                    stream: await getBufferFromStream(Body as Readable),
                    filename: file.name || basename(file.file),
                };
            }),
        );

        return downloadStreams;
    }

    /**
     *
     * @param key
     *
     */
    getObjectVersions(key: string): Promise<ListObjectVersionsOutput> {
        const command = new ListObjectVersionsCommand({
            Bucket: config.get('aws.s3.appBucket'),
            Prefix: key,
        });

        return this.client.send(command);
    }

    /**
     *
     * @param key
     * @returns file size in bytes
     *
     */
    async getFileSize(key: string): Promise<number> {
        const command = new HeadObjectCommand({
            Bucket: config.get('aws.s3.appBucket'),
            Key: key,
        });

        const headObject = await this.client.send(command);
        return headObject?.ContentLength ?? 0;
    }
}
