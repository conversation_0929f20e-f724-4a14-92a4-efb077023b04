import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Test, TestingModule } from '@nestjs/testing';
import { CacheService } from 'cache/cache.service';
import Redis from 'ioredis';
import { PolloLogger } from 'pollo-logger/pollo.logger';
import { DataSource } from 'typeorm';

describe('CacheService - Distributed Locks', () => {
    let cacheService: CacheService;
    let redisClient: Redis;
    let module: TestingModule;

    beforeEach(async () => {
        // Create a mock Redis client
        redisClient = {
            exists: jest.fn(),
            set: jest.fn(),
            del: jest.fn(),
            get: jest.fn(),
            setex: jest.fn(),
            expire: jest.fn(),
            ttl: jest.fn(),
            pipeline: jest.fn(() => ({
                get: jest.fn().mockReturnThis(),
                setex: jest.fn().mockReturnThis(),
                exec: jest.fn().mockResolvedValue([]),
            })),
        } as any;

        const mockLogger = {
            log: jest.fn(),
            error: jest.fn(),
            warn: jest.fn(),
            debug: jest.fn(),
            verbose: jest.fn(),
        };

        const mockDataSource = {
            getMetadata: jest.fn(),
            entityMetadatas: [],
        };

        module = await Test.createTestingModule({
            providers: [
                CacheService,
                {
                    provide: 'REDIS_CLIENT',
                    useValue: redisClient,
                },
                {
                    provide: CACHE_MANAGER,
                    useValue: redisClient,
                },
                {
                    provide: PolloLogger,
                    useValue: mockLogger,
                },
                {
                    provide: DataSource,
                    useValue: mockDataSource,
                },
            ],
        }).compile();

        cacheService = module.get<CacheService>(CacheService);
        // Override the internal cache property to use our mock
        (cacheService as any).cache = redisClient;
    });

    afterEach(async () => {
        jest.clearAllMocks();
    });

    describe('isLocked', () => {
        it('should return true when lock exists', async () => {
            (redisClient.exists as jest.Mock).mockResolvedValue(1);

            const result = await cacheService.isLocked('test-resource');

            expect(result).toBe(true);
            expect(redisClient.exists).toHaveBeenCalledWith('{lock}:test-resource');
        });

        it('should return false when lock does not exist', async () => {
            (redisClient.exists as jest.Mock).mockResolvedValue(0);

            const result = await cacheService.isLocked('test-resource');

            expect(result).toBe(false);
            expect(redisClient.exists).toHaveBeenCalledWith('{lock}:test-resource');
        });

        it('should handle Redis errors gracefully', async () => {
            (redisClient.exists as jest.Mock).mockRejectedValue(new Error('Redis error'));

            const result = await cacheService.isLocked('test-resource');

            expect(result).toBe(false); // Returns false on error
        });
    });

    describe('setLocked', () => {
        it('should acquire lock successfully when not exists', async () => {
            (redisClient.set as jest.Mock).mockResolvedValue('OK');

            const result = await cacheService.acquireLock('test-resource', 30, 'server-1');

            expect(result).toBe(true);
            expect(redisClient.set).toHaveBeenCalledWith(
                '{lock}:test-resource',
                '\"server-1\"', // JSON.stringify('server-1')
                'EX',
                30,
                'NX',
            );
        });

        it('should fail to acquire lock when already exists', async () => {
            (redisClient.set as jest.Mock).mockResolvedValue(null);

            const result = await cacheService.acquireLock('test-resource', 30);

            expect(result).toBe(false);
            expect(redisClient.set).toHaveBeenCalledWith(
                '{lock}:test-resource',
                'locked',
                'EX',
                30,
                'NX',
            );
        });

        it('should use default TTL of 60 seconds', async () => {
            (redisClient.set as jest.Mock).mockResolvedValue('OK');

            await cacheService.acquireLock('test-resource');

            expect(redisClient.set).toHaveBeenCalledWith(
                '{lock}:test-resource',
                'locked',
                'EX',
                60,
                'NX',
            );
        });

        it('should handle Redis errors gracefully', async () => {
            (redisClient.set as jest.Mock).mockRejectedValue(new Error('Redis error'));

            const result = await cacheService.acquireLock('test-resource');

            expect(result).toBe(false);
        });
    });

    describe('releaseLock', () => {
        it('should release lock successfully', async () => {
            (redisClient.del as jest.Mock).mockResolvedValue(1);

            await cacheService.releaseLock('test-resource');

            expect(redisClient.del).toHaveBeenCalledWith('{lock}:test-resource');
        });

        it('should handle Redis errors gracefully', async () => {
            (redisClient.del as jest.Mock).mockRejectedValue(new Error('Redis error'));

            // Should not throw
            await expect(cacheService.releaseLock('test-resource')).resolves.not.toThrow();
        });
    });

    describe('executeWithLock', () => {
        it('should execute processor when lock is acquired immediately', async () => {
            (redisClient.set as jest.Mock).mockResolvedValue('OK');
            (redisClient.del as jest.Mock).mockResolvedValue(1);

            const processor = jest.fn().mockResolvedValue('result');
            const result = await cacheService.executeWithLock('test-resource', processor);

            expect(result).toBe('result');
            expect(processor).toHaveBeenCalledTimes(1);
            expect(redisClient.set).toHaveBeenCalledTimes(1);
            expect(redisClient.del).toHaveBeenCalledTimes(1);
        });

        it('should retry and execute processor when lock becomes available', async () => {
            // First attempt fails, second succeeds
            (redisClient.set as jest.Mock)
                .mockResolvedValueOnce(null) // First attempt - locked
                .mockResolvedValueOnce('OK'); // Second attempt - success
            (redisClient.del as jest.Mock).mockResolvedValue(1);

            const processor = jest.fn().mockResolvedValue('result');
            const result = await cacheService.executeWithLock('test-resource', processor, {
                retryDelay: 10,
                maxRetries: 2,
            });

            expect(result).toBe('result');
            expect(processor).toHaveBeenCalledTimes(1);
            expect(redisClient.set).toHaveBeenCalledTimes(2);
        });

        it('should execute processor as fallback when max retries exceeded', async () => {
            // All attempts fail
            (redisClient.set as jest.Mock).mockResolvedValue(null);

            const processor = jest.fn().mockResolvedValue('fallback-result');

            const result = await cacheService.executeWithLock('test-resource', processor, {
                retryDelay: 10,
                maxRetries: 2,
            });

            expect(result).toBe('fallback-result');
            expect(processor).toHaveBeenCalledTimes(1); // Should execute as fallback
            expect(redisClient.set).toHaveBeenCalledTimes(3); // Initial + 2 retries
        });

        it('should release lock even if processor throws', async () => {
            (redisClient.set as jest.Mock).mockResolvedValue('OK');
            (redisClient.del as jest.Mock).mockResolvedValue(1);

            const processor = jest.fn().mockRejectedValue(new Error('Process error'));

            await expect(cacheService.executeWithLock('test-resource', processor)).rejects.toThrow(
                'Process error',
            );

            expect(redisClient.del).toHaveBeenCalledWith('{lock}:test-resource');
        });

        it('should use custom lock TTL and value', async () => {
            (redisClient.set as jest.Mock).mockResolvedValue('OK');
            (redisClient.del as jest.Mock).mockResolvedValue(1);

            const processor = jest.fn().mockResolvedValue('result');
            await cacheService.executeWithLock('test-resource', processor, {
                lockTtl: 120,
                lockData: 'custom-server',
            });

            expect(redisClient.set).toHaveBeenCalledWith(
                '{lock}:test-resource',
                '\"custom-server\"',
                'EX',
                120,
                'NX',
            );
        });
    });

    describe('Concurrent Lock Testing', () => {
        it('should prevent concurrent access to same resource', async () => {
            let lockHolder: string | null = null;
            let concurrentAttempts = 0;

            // Simulate lock behavior
            (redisClient.set as jest.Mock).mockImplementation((key, value) => {
                if (lockHolder === null) {
                    lockHolder = value;
                    return Promise.resolve('OK');
                }
                concurrentAttempts++;
                return Promise.resolve(null);
            });

            (redisClient.del as jest.Mock).mockImplementation(() => {
                lockHolder = null;
                return Promise.resolve(1);
            });

            // Try to acquire lock from two different "servers"
            const promise1 = cacheService.acquireLock('shared-resource', 60, 'server-1');
            const promise2 = cacheService.acquireLock('shared-resource', 60, 'server-2');

            const [result1, result2] = await Promise.all([promise1, promise2]);

            // Only one should succeed
            expect([result1, result2].filter(r => r === true).length).toBe(1);
            expect([result1, result2].filter(r => r === false).length).toBe(1);
            expect(concurrentAttempts).toBe(1);
        });
    });

    describe('TTL Expiration Testing', () => {
        it('should set correct TTL on lock', async () => {
            (redisClient.set as jest.Mock).mockResolvedValue('OK');

            await cacheService.acquireLock('ttl-test', 45);

            expect(redisClient.set).toHaveBeenCalledWith('{lock}:ttl-test', 'locked', 'EX', 45, 'NX');
        });

        it('should use default TTL when not specified', async () => {
            (redisClient.set as jest.Mock).mockResolvedValue('OK');

            await cacheService.acquireLock('ttl-test');

            expect(redisClient.set).toHaveBeenCalledWith(
                '{lock}:ttl-test',
                'locked',
                'EX',
                60, // Default TTL
                'NX',
            );
        });
    });
});
