import { Inject } from '@nestjs/common';
import { EntryService } from 'auth/services/entry.service';
import { getAccount } from 'cache/cache.helper';
import { CacheService } from 'cache/cache.service';
import config from 'config';
import { chunk } from 'lodash';
import { PolloMessage } from 'pollo-logger/classes/pollo.message';
import { PolloLogger } from 'pollo-logger/pollo.logger';

/**
 * DO NOT USE DECORATOR WITHIN SCOPES THAT IT REQUIRES:
 * - CacheService
 * - EntryService
 *
 * The decorator requires that the method being decorated contain EXACTLY
 * one parameter of Account type.
 *
 * Specialized cache buster to delete cache entries of the form:
 *     <GLOBALSTORE>:entry:<ENTRY_ID>
 *
 * For entry records belonging to a given account.
 */
export function AccountEntriesCacheBuster<ReturnType>() {
    /**
     * Prepare injectors
     */
    const injectCacheService = Inject(CacheService);
    const injectEntryService = Inject(EntryService);

    return (target: any, propertyKey: string, propertyDescriptor: PropertyDescriptor) => {
        const logger = PolloLogger.logger('AccountEntriesCacheBuster');

        /**
         * this is the same as using constructor(private readonly cacheServiceForAccountEntriesBuster: CacheService)
         * in a class. Note the name, make it unique so as to prevent constructor defined dependency collision.
         */
        injectCacheService(target, 'cacheServiceForAccountEntriesBuster');

        /**
         * this is the same as using constructor(private readonly cacheServiceForAccountEntriesBuster: CacheService)
         * in a class. Note the name, make it unique so as to prevent constructor defined dependency collision.
         */
        injectEntryService(target, 'entryServiceForAccountEntriesBuster');

        const originalMethod = propertyDescriptor.value;

        //redefine descriptor value within own function block
        propertyDescriptor.value = async function (...args: any[]): Promise<ReturnType> {
            const cacheService: CacheService = this.cacheServiceForAccountEntriesBuster;
            const entryService: EntryService = this.entryServiceForAccountEntriesBuster;
            const account = getAccount(this, args);

            return new Promise<ReturnType>((resolve, reject) => {
                (originalMethod.apply(this, args) as Promise<ReturnType>)
                    .then(async result => {
                        try {
                            // Use 'global' hash tag for entry keys since entries are globally scoped
                            const keyPrefix = `${config.get('cache.globalPrefix')}:{global}:entry`;
                            const entryIds = await entryService.getEntryIdsForAccount(account);
                            const accountId = account.id;
                            if (entryIds) {
                                // this is from EntryCoreService.getEntryById
                                const keysOld = entryIds.map(entryId => `${keyPrefix}:${entryId}`);
                                // this is from EntryCoreService.getEntryByIdWithAccount
                                const keysNew = entryIds.map(
                                    entryId => `${keyPrefix}:${entryId},${accountId}`,
                                );

                                const chunksOld = chunk(
                                    keysOld,
                                    config.get('cache.entryBusterChunkSize'),
                                );
                                for (const keyChunkOld of chunksOld) {
                                    // eslint-disable-next-line no-await-in-loop
                                    await cacheService.multiDel(keyChunkOld);
                                }

                                const chunksNew = chunk(
                                    keysNew,
                                    config.get('cache.entryBusterChunkSize'),
                                );
                                for (const keyChunkNew of chunksNew) {
                                    // eslint-disable-next-line no-await-in-loop
                                    await cacheService.multiDel(keyChunkNew);
                                }
                            }
                        } catch (error) {
                            /**
                             * If there is a failure, report it, but do not panic.
                             */
                            logger.error(
                                PolloMessage.msg(
                                    `AccountEntriesCacheBuster error occurred during cache busting => ${error.message}`,
                                )
                                    .setContext('AccountEntriesCacheBuster')
                                    .setError(error),
                            );
                        }

                        resolve(result);
                    })
                    .catch(error => {
                        logger.error(
                            PolloMessage.msg(
                                `AccountEntriesCacheBuster error occurred during decorated method execution`,
                            )
                                .setContext('AccountEntriesCacheBuster')
                                .setIdentifier({
                                    method: originalMethod?.name,
                                })
                                .setError(error),
                        );
                        reject(error);
                    });
            });
        };
    };
}
