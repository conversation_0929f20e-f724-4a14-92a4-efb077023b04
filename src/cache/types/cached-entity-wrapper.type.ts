/**
 * Wrapper for cached entities that includes metadata for relationship tracking and rehydration
 */
export interface CachedEntityWrapper<T> {
    /**
     * The actual cached data (null for phantom entries that only track dependencies)
     */
    data: T | null;

    /**
     * The entity class name for rehydration (e.g., "Requirement", "Control")
     * Only present when caching TypeORM entities
     */
    entityClassName: string;

    /**
     * List of property names in the data field that contain relationship data
     * Used for cascade invalidation and relationship validation
     *
     * Example: ["requirements", "controls", "policies"]
     * These fields in the data object will contain { __entityClassName: string, __ids: number[] }
     */
    relationships: string[];

    /**
     * Reverse dependencies - things that depend on this entity
     * Used for invalidating lists and entities when this entity changes
     */
    reverseDependencies: {
        /**
         * List cache keys that depend on this entity
         */
        lists: string[];

        /**
         * Entity keys that depend on this entity (for cascade invalidation)
         * Format: "namespace:accountId:entityId"
         * Example: ["Entry:global:entry-123", "UserRole:account-456:role-789"]
         */
        entities: string[];
    };

    /**
     * Lookup keys that were created for this entity
     * Used for invalidating alternate access patterns (e.g., email lookups)
     * Stored as field:value pairs that were used to create lookup keys
     */
    lookupKeys: Record<string, string | number>;
}
