import { computeReg<PERSON><PERSON><PERSON><PERSON>, extractHashTag, validateKeyForNonLock } from 'cache/cache.service';

const gp = 'globalstore';

describe('cache.service exports: extractHashTag', () => {
  it('extracts tag from braces', () => {
    expect(extractHashTag('globalstore:{abc}:ns:key')).toBe('abc');
  });

  it('returns null when no tag present', () => {
    expect(extractHashTag('globalstore:abc:ns:key')).toBeNull();
  });

  it('handles lock tag', () => {
    expect(extractHashTag('globalstore:{lock}:ns:key')).toBe('lock');
  });
});

describe('cache.service exports: validateKeyForNonLock', () => {
  it('throws when missing hash tag', () => {
    expect(() => validateKeyForNonLock('globalstore:abc:ns:key', 'get')).toThrow(/Missing hash tag/);
  });

  it('does not throw for lock-tagged keys', () => {
    expect(() => validateKeyForNonLock('globalstore:{lock}:ns:key', 'get')).not.toThrow();
  });

  it('throws for invalid tag value', () => {
    expect(() => validateKeyForNonLock('globalstore:{foo}:ns:key', 'set')).toThrow(/Invalid hash tag/);
  });

  it('does not throw for global tag', () => {
    expect(() => validateKeyForNonLock('globalstore:{global}:ns:key', 'get')).not.toThrow();
  });

  it('does not throw for UUID tag', () => {
    const uuid = '123e4567-e89b-12d3-a456-************';
    expect(() => validateKeyForNonLock(`globalstore:{${uuid}}:ns:key`, 'get')).not.toThrow();
  });

  it('throws when not exactly 4 segments for non-registry key', () => {
    expect(() => validateKeyForNonLock('globalstore:{global}:ns', 'get')).toThrow(/Expected 4 segments/);
  });

  it('throws when registry key exceeds 5 segments', () => {
    // 5 segments: ok
    expect(() => validateKeyForNonLock('globalstore:{global}:ns:foo:_registry', 'get')).not.toThrow();

    // 6 segments with last being _registry should throw
    expect(() => validateKeyForNonLock('globalstore:{global}:ns:extra:foo:bar:_registry', 'get')).toThrow(
      /Registry key should not exceed 5 segments/
    );
  });
});

describe('cache.service exports: computeRegistryKey', () => {
  it('builds registry for prefixed workspace key', () => {
    const out = computeRegistryKey('globalstore:{acc123}:USER_BY_ENTRY_ID:workspace456:entry789', gp);
    expect(out).toBe('globalstore:{acc123}:USER_BY_ENTRY_ID:workspace456:_registry');
  });

  it('builds registry for prefixed standard key', () => {
    const out = computeRegistryKey('globalstore:{acc123}:account:test', gp);
    expect(out).toBe('globalstore:{acc123}:account:_registry');
  });

  it('builds registry for non-prefixed underscore key', () => {
    const out = computeRegistryKey('entryservice_getentrybyid_123');
    expect(out).toBe('entryservice_getentrybyid:_registry');
  });

  it('falls back to global registry for simple key', () => {
    const out = computeRegistryKey('simplekey', gp);
    expect(out).toBe('globalstore:{global}:_registry');
  });
});

