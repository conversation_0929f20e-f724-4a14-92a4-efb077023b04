import { InternalServerErrorException, Type } from '@nestjs/common';
import { Account } from 'auth/entities/account.entity';
import { CacheOptions } from 'cache/cache.options';
import { CacheService } from 'cache/cache.service';
import { searchInstance } from 'commons/helpers/instance.helper';
import { isPositiveInteger } from 'commons/helpers/number.helper';
import { AccountIdOrGlobal, isAccountIdType } from 'commons/types/account-id.type';
import config from 'config';
import { isEmpty, isNil, isString, lt, slice } from 'lodash';
import { PolloMessage } from 'pollo-logger/classes/pollo.message';
import { PolloLogger } from 'pollo-logger/pollo.logger';

/**
 * A promise-based method signature that can be cached.
 */
export type Cacheable<T> = (...args: any[]) => Promise<T>;

/**
 * Creates a raw key from className, methodName, args, and optional store setting,
 * then normalizes it so we remove quotes and lowercase it.
 * Automatically detects account in args and uses appropriate hash tag.
 */
export function getCacheKey(
    className: string,
    methodName: string,
    args: any[],
    options?: CacheOptions,
): string {
    const globalPrefix = config.get<string>('cache.globalPrefix');

    // Try to detect account in args for proper hash tagging
    let hashTag = 'global'; // {global} is intentional, so entries all fall on the same redis shard
    const account: Account | null = searchInstance<Account>(args, Account);
    if (account && account.id) {
        hashTag = account.id; // Use hash tag for account ID to ensure all account-based keys are on the same shard
    }

    let prefix = `${className}:${methodName}`;

    if (options?.store && isString(options.store)) {
        // Use appropriate hash tag based on account availability
        prefix = `${globalPrefix}:{${hashTag}}:${options.store}`;
    } else {
        // For className:methodName format, also use hash tag
        prefix = `${globalPrefix}:{${hashTag}}:${className}:${methodName}`;
    }

    if (
        options?.useArgs &&
        isPositiveInteger(options.useArgs) &&
        lt(options.useArgs, args.length)
    ) {
        args = slice(args, 0, options.useArgs);
    }

    if (isEmpty(args)) {
        throw new InternalServerErrorException(`Cache decorated method arguments can not be empty`);
    }

    // Combine prefix + JSON of args => raw key
    const rawKey = `${prefix}:${args.map(a => JSON.stringify(a)).join()}`;
    return decoratorNormalizeKey(rawKey);
}

/**
 * Retrieves the CacheService from the current instance (this),
 * or throws an error if not found.
 */
export function getCacheService(_this: any): CacheService {
    const cache = _this.cacheService || _this._cacheService;
    if (!cache || !(cache instanceof CacheService)) {
        throw new InternalServerErrorException(
            `CacheService has not been properly initialized on ${_this.constructor.name}`,
        );
    }
    return cache;
}

/**
 * By default, the Nest Cache decorators do not normalize keys.
 * Because application use is inconsistent, we are forced to.
 *
 * 1) remove double & single quotes
 * 2) toLowerCase
 */
export function decoratorNormalizeKey(raw: string): string {
    let newKey = raw.replace(/["']/g, '');
    newKey = newKey.toLowerCase();
    return newKey;
}

/**
 * Extracts an Account object from method arguments,
 * or throws if none found. Used for the prefix decorators.
 */
export function getAccount(_this: any, args?: any[]): Account {
    const account: Account = searchInstance<Account>(args, Account);
    if (!isNil(account)) {
        return account;
    }
    throw new Error('Cache decorator needs Account in method arguments');
}

export function getEntity<EntityType>(_this: any, args: any[], type: Type<EntityType>): EntityType {
    const entity: EntityType = searchInstance<EntityType>(args, type);
    if (!isNil(entity)) {
        return entity;
    }
    throw new Error('Cache decorator needs entity in method arguments');
}

/**
 * Safely extract and validate account ID from an Account object for cache key generation.
 * Returns a properly typed AccountIdOrGlobal value with appropriate validation and logging.
 *
 * @param account - The account object (may be null/undefined)
 * @param context - Context string for logging (e.g., 'CacheBuster', 'SuperCache')
 * @returns A properly typed AccountIdOrGlobal value
 */
export function getAccountIdForCache(account: Account | null | undefined, context: string): AccountIdOrGlobal {
    if (!account?.id) {
        return 'global';
    }

    if (!isAccountIdType(account.id)) {
        const logger = PolloLogger.logger();
        logger.warn(
            PolloMessage.msg(
                `Invalid account ID for cache key: ${account.id}. Must be a valid UUID. Using 'global' as fallback.`,
            ).setContext(context),
        );
        return 'global';
    }

    // Type assertion is safe here because we've validated with isAccountIdType
    return account.id as AccountIdOrGlobal;
}
