import { Cacheable, getAccount, getAccountIdFor<PERSON>ache, getCacheKey, getCacheService } from 'cache/cache.helper';
import { CACHE_IS_UP } from 'cache/cache.module';
import { SuperCacheAccountOptions, SuperCacheOptions } from 'cache/cache.options';
import { isAccountIdType } from 'commons/types/account-id.type';
import config from 'config';
import tracer from 'dd-trace';
import { chunk } from 'lodash';
import { PolloMessage } from 'pollo-logger/classes/pollo.message';
import { PolloLogger } from 'pollo-logger/pollo.logger';

/**
 * Assumes account is in the arguments list to build keys using account id
 *
 *
 * @param options
 * @returns
 */
export function SuperCacheAccountBuster<T>(options?: SuperCacheAccountOptions) {
    return (
        target: { constructor: { name: string } },
        methodName: string,
        descriptor: TypedPropertyDescriptor<any>,
    ): TypedPropertyDescriptor<Cacheable<T>> => {
        const originalMethod = descriptor.value;
        const className = target.constructor.name;
        const logger = PolloLogger.logger('SuperCacheAccountBuster');

        descriptor.value = function (...args: any[]): Promise<T> {
            if (config.get<boolean>('cache.enabled') && CACHE_IS_UP) {
                const cache = getCacheService(this);
                const account = getAccount(this, args);
                const globalPrefix = config.get<string>('cache.globalPrefix');

                return new Promise<T>((resolve, reject) => {
                    (originalMethod.apply(this, args) as Promise<T>)
                        .then(async result => {
                            try {
                                if (options?.stores) {
                                    const chunks = chunk(
                                        options?.stores,
                                        config.get('cache.superBusterChunkSize'),
                                    );

                                    for (const storeChunk of chunks) {
                                        const promises: Promise<any>[] = [];
                                        for (const store of storeChunk) {
                                            // Get properly validated and typed account ID
                                            const accountIdOrGlobal = getAccountIdForCache(account, 'SuperCacheAccountBuster');

                                            /**
                                             * Handle account level items
                                             */
                                            const key = getCacheKey(
                                                className,
                                                methodName,
                                                [accountIdOrGlobal],
                                                {
                                                    useArgs: options?.useArgs,
                                                    store: store,
                                                },
                                            );
                                            promises.push(cache.unlink(key));

                                            /**
                                             * Handle prefixed items using registry-based deletion
                                             * Use hash tag for account ID to ensure colocation
                                             */
                                            const prefixedRegistryKey = `${globalPrefix}:{${accountIdOrGlobal}}:${store}:_registry`;

                                            promises.push(
                                                (async () => {
                                                    try {
                                                        const cacheKeys =
                                                            await cache.smembers(
                                                                prefixedRegistryKey,
                                                            );

                                                        if (cacheKeys.length > 0) {
                                                            const allKeys = [...cacheKeys, prefixedRegistryKey];
                                                            void cache.multiDel(allKeys).catch(error => {
                                                                logger.warn(
                                                                    PolloMessage.msg(
                                                                        `SuperCacheAccountBuster remove keys error => ${error.message}`,
                                                                    )
                                                                        .setContext(
                                                                            'SuperCacheAccountBuster',
                                                                        )
                                                                        .setError(error),
                                                                );
                                                            });
                                                        }
                                                    } catch (registryError) {
                                                        // Registry failure - fallback to SCAN for this specific registry
                                                        // Use hash tag in SCAN pattern for account ID
                                                        const match = `${globalPrefix}:{${accountIdOrGlobal}}:${store}:*`;
                                                        const count = config.get<number>(
                                                            'cache.entryBusterChunkSize',
                                                        );

                                                        // Send Datadog metric for SCAN fallback
                                                        tracer.dogstatsd.increment(
                                                            'cache.registry.fallback.scan',
                                                            1,
                                                            {
                                                                store: store,
                                                                operation:
                                                                    'super_cache_account_buster',
                                                                reason: 'registry_error',
                                                            },
                                                        );

                                                        try {
                                                            void cache
                                                                .scanDel({
                                                                    match,
                                                                    count,
                                                                })
                                                                .catch(error => {
                                                                    logger.warn(
                                                                        PolloMessage.msg(
                                                                            `SuperCacheAccountBuster scanDel error => ${error.message}`,
                                                                        )
                                                                            .setContext(
                                                                                'SuperCacheAccountBuster',
                                                                            )
                                                                            .setError(error),
                                                                    );
                                                                });
                                                        } catch (scanError) {
                                                            // Both registry and SCAN failed - log but continue
                                                            logger.warn(
                                                                PolloMessage.msg(
                                                                    `Both registry and SCAN failed for ${store}: ${scanError.message}`,
                                                                )
                                                                    .setContext(
                                                                        'SuperCacheAccountBuster',
                                                                    )
                                                                    .setError(scanError),
                                                            );
                                                        }
                                                    }
                                                })(),
                                            );

                                            /**
                                             * Also handle non-prefixed cache registries for stores like ENTRY
                                             */
                                            // Check common patterns for non-prefixed caches
                                            const nonPrefixedRegistries = [
                                                // INC-521: comment this out for now to see if it helps the system
                                                // `${globalPrefix}:${store}:_registry`, // Simple global store registry
                                                `${store}:_registry`, // Simple store registry
                                                `${className.toLowerCase()}_${methodName.toLowerCase()}:_registry`, // Method-based registry
                                                `${globalPrefix}:general:_registry`, // General fallback registry
                                            ];

                                            for (const registryKey of nonPrefixedRegistries) {
                                                promises.push(
                                                    (async () => {
                                                        try {
                                                            const cacheKeys =
                                                                await cache.smembers(registryKey);

                                                            if (cacheKeys.length > 0) {
                                                                const allKeys = [...cacheKeys, registryKey];
                                                                void cache.multiDel(allKeys).catch(error => {
                                                                    logger.warn(
                                                                        PolloMessage.msg(
                                                                            `SuperCacheAccountBuster remove keys error => ${error.message}`,
                                                                        )
                                                                            .setContext(
                                                                                'SuperCacheAccountBuster',
                                                                            )
                                                                            .setError(error),
                                                                    );
                                                                });
                                                            }
                                                        } catch (registryError) {
                                                            // Silent fail - registry might not exist
                                                        }
                                                    })(),
                                                );
                                            }
                                        }

                                        void Promise.allSettled(promises).catch(error => {
                                            logger.warn(
                                                PolloMessage.msg(
                                                    `SuperCacheAccountBuster error => ${error.message}`,
                                                )
                                                    .setContext('SuperCacheAccountBuster')
                                                    .setError(error),
                                            );
                                        });
                                    }
                                }
                            } catch (error) {
                                logger.warn(
                                    PolloMessage.msg(
                                        `SuperCacheAccountBuster method error => ${error.message}`,
                                    )
                                        .setContext('SuperCacheAccountBuster')
                                        .setError(error),
                                );
                            }
                            resolve(result);
                        })
                        .catch(error => {
                            logger.log(
                                PolloMessage.msg(
                                    `SuperCacheAccountBuster method error => ${error.message}`,
                                )
                                    .setContext('SuperCacheAccountBuster')
                                    .setError(error),
                            );
                            reject(error);
                        });
                });
            }

            // If disabled, just run
            return new Promise((resolve, reject) => {
                (originalMethod.apply(this, args) as Promise<T>).then(resolve).catch(error => {
                    logger.log(
                        PolloMessage.msg(
                            `SuperCacheAccountBuster with cache disabled => ${error.message}`,
                        )
                            .setContext('SuperCacheAccountBuster')
                            .setError(error),
                    );
                    reject(error);
                });
            });
        };

        return descriptor;
    };
}

export function SuperCacheBuster<T>(options?: SuperCacheOptions) {
    return (
        target: { constructor: { name: string } },
        methodName: string,
        descriptor: TypedPropertyDescriptor<any>,
    ): TypedPropertyDescriptor<Cacheable<T>> => {
        const originalMethod = descriptor.value;
        const className = target.constructor.name;
        const logger = PolloLogger.logger('SuperCacheBuster');

        descriptor.value = function (...args: any[]): Promise<T> {
            if (config.get<boolean>('cache.enabled') && CACHE_IS_UP) {
                const cache = getCacheService(this);
                const superBusterChunkSize = config.get<number>('cache.superBusterChunkSize');
                const globalPrefix = config.get<string>('cache.globalPrefix');

                return new Promise<T>((resolve, reject) => {
                    (originalMethod.apply(this, args) as Promise<T>)
                        .then(async result => {
                            try {
                                let accountId: string | undefined = undefined;
                                if (options?.bustAccount) {
                                    accountId = args.find(() => true);
                                }
                                if (options?.stores) {
                                    /**
                                     * Cut up the array to an array (storeChunk) of arrays (chunks),
                                     * each storeChunk is of length superBusterChunkSize
                                     */
                                    const chunks = chunk(options?.stores, superBusterChunkSize);
                                    for (const storeChunk of chunks) {
                                        const promises: Promise<any>[] = [];
                                        for (const store of storeChunk) {
                                            /**
                                             * Handle account level items
                                             */
                                            const key = getCacheKey(className, methodName, args, {
                                                useArgs: options?.useArgs,
                                                store: store,
                                            });
                                            promises.push(cache.unlink(key));
                                            if (accountId) {
                                                // Validate account ID - only check if it's a valid UUID
                                                if (!isAccountIdType(accountId)) {
                                                    logger.warn(
                                                        PolloMessage.msg(
                                                            `Invalid account ID for cache key: ${accountId}. Must be a valid UUID.`,
                                                        ).setContext('SuperCacheBuster'),
                                                    );
                                                    continue;
                                                }
                                                /**
                                                 * If the first argument of the method is an account id,
                                                 * handle prefixed items using registry-based deletion
                                                 * Use hash tag for account ID to ensure colocation
                                                 */
                                                const registryKey = `${globalPrefix}:{${accountId}}:${store}:_registry`;

                                                promises.push(
                                                    (async () => {
                                                        try {
                                                            const cacheKeys =
                                                                await cache.smembers(registryKey);

                                                            if (cacheKeys.length > 0) {
                                                                const allKeys = [...cacheKeys, registryKey];
                                                                void cache.multiDel(allKeys).catch(error => {
                                                                    logger.warn(
                                                                        PolloMessage.msg(
                                                                            `SuperCacheBuster remove keys error => ${error.message}`,
                                                                        )
                                                                            .setContext(
                                                                                'SuperCacheBuster',
                                                                            )
                                                                            .setError(error),
                                                                    );
                                                                });
                                                            }
                                                        } catch (registryError) {
                                                            logger.warn(
                                                                PolloMessage.msg(
                                                                    `SuperCacheBuster registry smembers error => ${registryError.message}`,
                                                                )
                                                                    .setContext('SuperCacheBuster')
                                                                    .setError(registryError),
                                                            );
                                                        }
                                                    })(),
                                                );
                                            }
                                        }
                                        void Promise.allSettled(promises).catch(error => {
                                            logger.warn(
                                                PolloMessage.msg(
                                                    `SuperCacheBuster error => ${error.message}`,
                                                )
                                                    .setContext('SuperCacheBuster')
                                                    .setError(error),
                                            );
                                        });
                                    }
                                }
                            } catch (error) {
                                logger.warn(
                                    PolloMessage.msg(
                                        `SuperCacheBuster method error => ${error.message}`,
                                    )
                                        .setContext('SuperCacheBuster')
                                        .setError(error),
                                );
                            }
                            resolve(result);
                        })
                        .catch(error => {
                            logger.log(
                                PolloMessage.msg(
                                    `SuperCacheBuster method error => ${error.message}`,
                                )
                                    .setContext('SuperCacheBuster')
                                    .setError(error),
                            );
                            reject(error);
                        });
                });
            }

            // If disabled, just run
            return new Promise((resolve, reject) => {
                (originalMethod.apply(this, args) as Promise<T>).then(resolve).catch(error => {
                    logger.log(
                        PolloMessage.msg(`SuperCacheBuster with cache disabled => ${error.message}`)
                            .setContext('SuperCacheBuster')
                            .setError(error),
                    );
                    reject(error);
                });
            });
        };

        return descriptor;
    };
}
