import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Test, TestingModule } from '@nestjs/testing';
import { CacheService } from 'cache/cache.service';
import { CachedEntityWrapper } from 'cache/types/cached-entity-wrapper.type';
import { Redis } from 'ioredis';

describe('CacheService - CacheWrapper Implementation', () => {
    let cacheService: CacheService;
    let mockRedis: Partial<Redis>;

    beforeEach(async () => {
        mockRedis = {
            get: jest.fn(),
            setex: jest.fn(),
            del: jest.fn(),
            unlink: jest.fn(),
            exists: jest.fn(),
            ping: jest.fn().mockResolvedValue('PONG'),
        };

        const module: TestingModule = await Test.createTestingModule({
            providers: [
                CacheService,
                {
                    provide: CACHE_MANAGER,
                    useValue: mockRedis,
                },
            ],
        }).compile();

        cacheService = module.get<CacheService>(CacheService);
    });

    describe('set() method with CacheWrapper', () => {
        it('should wrap value with CacheWrapper and auto-detect className', async () => {
            const testKey = 'test:key';
            const testValue = { id: 1, name: 'Test Entity' };

            // Create a class instance to test className detection
            class TestEntity {
                id: number;
                name: string;
                constructor(data: any) {
                    this.id = data.id;
                    this.name = data.name;
                }
            }
            const testEntity = new TestEntity(testValue);

            await cacheService.set(testKey, testEntity, 3600);

            expect(mockRedis.setex).toHaveBeenCalled();
            const [, , serialized] = (mockRedis.setex as jest.Mock).mock.calls[0];

            const wrapper: CachedEntityWrapper<any> = JSON.parse(serialized);

            // Verify wrapper structure
            expect(wrapper).toHaveProperty('data');
            expect(wrapper).toHaveProperty('entityClassName');
            expect(wrapper).toHaveProperty('relationships');
            expect(wrapper).toHaveProperty('reverseDependencies');
            expect(wrapper).toHaveProperty('lookupKeys');

            // Verify data is stored correctly
            expect(wrapper.data).toEqual(testEntity);

            // Verify className was auto-detected
            expect(wrapper.entityClassName).toBe('TestEntity');

            // Verify default values for future features
            expect(wrapper.relationships).toEqual([]);
            expect(wrapper.reverseDependencies).toEqual({ lists: [], entities: [] });
            expect(wrapper.lookupKeys).toEqual({});
        });

        it('should use "object" as className for plain objects', async () => {
            const testKey = 'test:key';
            const plainObject = { id: 1, name: 'Plain Object' };

            await cacheService.set(testKey, plainObject, 3600);

            const [, , serialized] = (mockRedis.setex as jest.Mock).mock.calls[0];
            const wrapper: CachedEntityWrapper<any> = JSON.parse(serialized);

            expect(wrapper.entityClassName).toBe('object');
        });

        it('should use "object" as className for arrays', async () => {
            const testKey = 'test:key';
            const arrayValue = [1, 2, 3];

            await cacheService.set(testKey, arrayValue, 3600);

            const [, , serialized] = (mockRedis.setex as jest.Mock).mock.calls[0];
            const wrapper: CachedEntityWrapper<any> = JSON.parse(serialized);

            expect(wrapper.entityClassName).toBe('object');
        });
    });

    describe('get() method with CacheWrapper', () => {
        it('should unwrap CacheWrapper and return data', async () => {
            const testKey = 'test:key';
            const testData = { id: 1, name: 'Test Entity' };
            const wrapper: CachedEntityWrapper<any> = {
                data: testData,
                entityClassName: 'TestEntity',
                relationships: [],
                reverseDependencies: { lists: [], entities: [] },
                lookupKeys: {},
            };

            (mockRedis.get as jest.Mock).mockResolvedValue(JSON.stringify(wrapper));

            const result = await cacheService.get(testKey);

            // Note: With entityClassName as 'TestEntity', plainToInstance will be called
            // but without a proper class constructor, it returns the data as-is
            expect(result).toEqual(testData);
        });

        it('should handle legacy unwrapped values for backward compatibility', async () => {
            const testKey = 'test:key';
            const legacyValue = { id: 1, name: 'Legacy Value' };

            // Simulate old cache format (direct value, not wrapped)
            (mockRedis.get as jest.Mock).mockResolvedValue(JSON.stringify(legacyValue));

            const result = await cacheService.get(testKey);

            expect(result).toEqual(legacyValue);
        });

        it('should return null when wrapper has null data', async () => {
            const testKey = 'test:key';
            const wrapper: CachedEntityWrapper<any> = {
                data: null, // Phantom entry
                entityClassName: 'TestEntity',
                relationships: [],
                reverseDependencies: { lists: [], entities: [] },
                lookupKeys: {},
            };

            (mockRedis.get as jest.Mock).mockResolvedValue(JSON.stringify(wrapper));

            const result = await cacheService.get(testKey);

            expect(result).toBeNull();
        });

        it('should return null when key does not exist', async () => {
            const testKey = 'test:key';

            (mockRedis.get as jest.Mock).mockResolvedValue(null);

            const result = await cacheService.get(testKey);

            expect(result).toBeNull();
        });

        it('should handle primitive values for backward compatibility', async () => {
            const testKey = 'test:key';
            const primitiveValue = 'simple string';

            (mockRedis.get as jest.Mock).mockResolvedValue(JSON.stringify(primitiveValue));

            const result = await cacheService.get(testKey);

            expect(result).toBe(primitiveValue);
        });

        it('should handle number values for backward compatibility', async () => {
            const testKey = 'test:key';
            const numberValue = 42;

            (mockRedis.get as jest.Mock).mockResolvedValue(JSON.stringify(numberValue));

            const result = await cacheService.get(testKey);

            expect(result).toBe(numberValue);
        });

        it('should handle boolean values for backward compatibility', async () => {
            const testKey = 'test:key';
            const boolValue = true;

            (mockRedis.get as jest.Mock).mockResolvedValue(JSON.stringify(boolValue));

            const result = await cacheService.get(testKey);

            expect(result).toBe(boolValue);
        });

        it('should handle array values for backward compatibility', async () => {
            const testKey = 'test:key';
            const arrayValue = [1, 2, 3, 'test'];

            (mockRedis.get as jest.Mock).mockResolvedValue(JSON.stringify(arrayValue));

            const result = await cacheService.get(testKey);

            expect(result).toEqual(arrayValue);
        });
    });

    describe('Backward Compatibility', () => {
        it('should handle mixed cache entries (wrapped and unwrapped)', async () => {
            const wrappedKey = 'wrapped:key';
            const unwrappedKey = 'unwrapped:key';

            const wrappedData = { id: 1, name: 'Wrapped' };
            const wrapper: CachedEntityWrapper<any> = {
                data: wrappedData,
                entityClassName: 'Entity',
                relationships: [],
                reverseDependencies: { lists: [], entities: [] },
                lookupKeys: {},
            };

            const unwrappedData = { id: 2, name: 'Unwrapped' };

            // First call returns wrapped, second returns unwrapped
            (mockRedis.get as jest.Mock)
                .mockResolvedValueOnce(JSON.stringify(wrapper))
                .mockResolvedValueOnce(JSON.stringify(unwrappedData));

            const result1 = await cacheService.get(wrappedKey);
            const result2 = await cacheService.get(unwrappedKey);

            // With entityClassName 'Entity', hydration is attempted but returns data as-is
            expect(result1).toEqual(wrappedData);
            expect(result2).toEqual(unwrappedData);
        });

        it('should preserve existing cache behavior for del() and unlink()', async () => {
            const testKey = 'test:key';

            await cacheService.del(testKey);
            expect(mockRedis.del).toHaveBeenCalledWith('test:key');

            await cacheService.unlink(testKey);
            expect(mockRedis.unlink).toHaveBeenCalledWith('test:key');
        });
    });

    describe('Edge Cases', () => {
        it('should handle malformed JSON gracefully', async () => {
            const testKey = 'test:key';

            (mockRedis.get as jest.Mock).mockResolvedValue('invalid json {');

            const result = await cacheService.get(testKey);

            // Should return null and log warning
            expect(result).toBeNull();
        });

        it('should handle null values', async () => {
            const testKey = 'test:key';

            await cacheService.set(testKey, null as any, 3600);

            const [, , serialized] = (mockRedis.setex as jest.Mock).mock.calls[0];
            const wrapper: CachedEntityWrapper<any> = JSON.parse(serialized);

            expect(wrapper.data).toBeNull();
            expect(wrapper.entityClassName).toBe('object');
        });

        it('should handle undefined values', async () => {
            const testKey = 'test:key';

            await cacheService.set(testKey, undefined as any, 3600);

            const [, , serialized] = (mockRedis.setex as jest.Mock).mock.calls[0];
            const wrapper: CachedEntityWrapper<any> = JSON.parse(serialized);

            expect(wrapper.data).toBeUndefined();
            expect(wrapper.entityClassName).toBe('object');
        });

        it('should detect className from custom classes', async () => {
            const testKey = 'test:key';

            class CustomEntity {
                constructor(public value: string) {}
            }

            const instance = new CustomEntity('test');
            await cacheService.set(testKey, instance, 3600);

            const [, , serialized] = (mockRedis.setex as jest.Mock).mock.calls[0];
            const wrapper: CachedEntityWrapper<any> = JSON.parse(serialized);

            expect(wrapper.entityClassName).toBe('CustomEntity');
        });
    });
});
