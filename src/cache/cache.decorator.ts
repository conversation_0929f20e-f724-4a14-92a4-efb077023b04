/* eslint-disable no-await-in-loop */
import { NotImplementedException, Type } from '@nestjs/common';
import { Account } from 'auth/entities/account.entity';
import {
    Cacheable,
    decoratorNormalize<PERSON>ey,
    getAccount,
    getAccountIdForCache,
    getCacheKey,
    getCacheService,
    getEntity,
} from 'cache/cache.helper';
import { CACHE_IS_UP } from 'cache/cache.module';
import { CacheEntityOptions, CacheOptions } from 'cache/cache.options';
import { isPositiveInteger } from 'commons/helpers/number.helper';
import config from 'config';
import tracer from 'dd-trace';
import { isArray, isNil, isString, lt, slice } from 'lodash';
import { PolloMessage } from 'pollo-logger/classes/pollo.message';
import { PolloLogger } from 'pollo-logger/pollo.logger';
import 'reflect-metadata';

function logHint(message: string, context: string, subcontext?: string): void {
    const logger = PolloLogger.logger('Cache');
    if (!config.get<boolean>('cache.log.enabled')) {
        return;
    }
    const msg = PolloMessage.msg(message).setContext(context);
    if (!isNil(subcontext)) {
        msg.setSubContext(subcontext);
    }
    logger.log(msg);
}
/**
 * Similar to getCacheKey, but includes account.id, store, and (optionally) a workspace.
 * We also do the same minimal normalization after building the raw prefix.
 */
function getPrefixCacheKey(
    account: Account,
    args: any[],
    options: CacheOptions,
    onlyPrefix = false,
): string {
    const globalPrefix = config.get<string>('cache.globalPrefix') || 'globalstore';

    if (!options?.store || !isString(options.store)) {
        throw new NotImplementedException(
            'CacheWithPrefix / CacheBusterWithPrefix requires a "store" property in CacheOptions.',
        );
    }

    // Get properly validated and typed account ID
    const accountIdOrGlobal = getAccountIdForCache(account, 'getPrefixCacheKey');

    // Use hash tag for account ID to ensure all account keys are on the same shard
    const rawPrefix = `${globalPrefix}:{${accountIdOrGlobal}}:${options.store}`;

    if (onlyPrefix) {
        return decoratorNormalizeKey(rawPrefix);
    }

    if (
        options?.useArgs &&
        isPositiveInteger(options.useArgs) &&
        lt(options.useArgs, args.length)
    ) {
        args = slice(args, 0, options.useArgs);
    }

    const rawKey = `${rawPrefix}:${args.map(a => JSON.stringify(a)).join()}`;
    return decoratorNormalizeKey(rawKey);
}

/* -------------------------------------------------------------------
 * DECORATORS
 * ------------------------------------------------------------------- */

/**
 * Cache Decorator:
 *  - If cache is disabled or not up, run method.
 *  - Else try cache.get -> if miss, run method and set. if hit, return cached object.
 */
export function Cache<T>(type: Type<T>, options?: CacheOptions) {
    return (
        target: unknown,
        methodName: string,
        descriptor: TypedPropertyDescriptor<Cacheable<T>>,
    ): TypedPropertyDescriptor<Cacheable<T>> => {
        const originalMethod = descriptor.value;
        const className = target.constructor.name;
        const logger = PolloLogger.logger('Cache');

        descriptor.value = function (...args: any[]): Promise<T> {
            if (!config.get<boolean>('cache.enabled') || !CACHE_IS_UP) {
                // No caching => just run
                return new Promise((resolve, reject) => {
                    (originalMethod.apply(this, args) as Promise<T>).then(resolve).catch(error => {
                        logger.log(
                            PolloMessage.msg(`Error while caching disabled: ${error.message}`)
                                .setContext('Cache')
                                .setError(error),
                        );
                        reject(error);
                    });
                });
            }

            const cache = getCacheService(this);
            const key = getCacheKey(className, methodName, args, options);

            return new Promise<T>((resolve, reject) => {
                cache
                    .get<T>(key, type)
                    .then(cached => {
                        if (cached) {
                            logHint(`Cache HIT => key="${key}"`, 'Cache', 'hit');
                            resolve(cached);
                        } else {
                            logHint(`Cache MISS => key="${key}"`, 'Cache', 'miss');
                            (originalMethod.apply(this, args) as Promise<T>)
                                .then(async result => {
                                    if (result) {
                                        try {
                                            const status = await cache.set<T>(
                                                key,
                                                result,
                                                options?.ttl,
                                                type,
                                            );
                                            logHint(
                                                `Cache SET => key="${key}" status="${status}"`,
                                                'Cache',
                                                'set',
                                            );
                                        } catch (err) {
                                            logger.log(
                                                PolloMessage.msg(`Cache set error: ${err?.message}`)
                                                    .setContext('Cache')
                                                    .setError(err),
                                            );
                                        }
                                        resolve(result);
                                    } else {
                                        resolve(null as unknown as T);
                                    }
                                })
                                .catch(error => {
                                    logger.log(
                                        PolloMessage.msg(
                                            `Error after MISS => key="${key}" => ${error.message}`,
                                        )
                                            .setContext('Cache')
                                            .setError(error),
                                    );
                                    reject(error);
                                });
                        }
                    })
                    .catch(error => {
                        logger.log(
                            PolloMessage.msg(`Error on get => key="${key}" => ${error.message}`)
                                .setContext('Cache')
                                .setError(error),
                        );
                        reject(error);
                    });
            });
        };

        return descriptor;
    };
}

/**
 * CacheBuster Decorator:
 *  - On method call, remove the single key derived from className/methodName/args, then run method normally.
 */
export function CacheBuster<T>(options?: CacheOptions) {
    return (
        target: unknown,
        methodName: string,
        descriptor: TypedPropertyDescriptor<any>,
    ): TypedPropertyDescriptor<Cacheable<T>> => {
        const originalMethod = descriptor.value;
        const className = target.constructor.name;
        const logger = PolloLogger.logger('CacheBuster');

        descriptor.value = function (...args: any[]): Promise<T> {
            if (config.get<boolean>('cache.enabled') && CACHE_IS_UP) {
                const cache = getCacheService(this);
                const key = getCacheKey(className, methodName, args, options);

                return new Promise<T>((resolve, reject) => {
                    (originalMethod.apply(this, args) as Promise<T>)
                        .then(async result => {
                            await cache
                                .unlink(key)
                                .then(() => {
                                    logHint(
                                        `CacheBuster UNLINK => key="${key}"`,
                                        'CacheBuster',
                                        'del',
                                    );
                                })
                                .catch(error => {
                                    logger.log(
                                        PolloMessage.msg(
                                            `CacheBuster del error => ${error.message}`,
                                        )
                                            .setContext('CacheBuster')
                                            .setError(error),
                                    );
                                });
                            resolve(result);
                        })
                        .catch(error => {
                            logger.log(
                                PolloMessage.msg(`CacheBuster method error => ${error.message}`)
                                    .setContext('CacheBuster')
                                    .setError(error),
                            );
                            reject(error);
                        });
                });
            }

            // If disabled, just run
            return new Promise((resolve, reject) => {
                (originalMethod.apply(this, args) as Promise<T>).then(resolve).catch(error => {
                    logger.log(
                        PolloMessage.msg(`CacheBuster with cache disabled => ${error.message}`)
                            .setContext('CacheBuster')
                            .setError(error),
                    );
                    reject(error);
                });
            });
        };

        return descriptor;
    };
}

/**
 * CacheWithPrefix Decorator:
 *  - We build a prefix key based on Account + store + optional workspace,
 *    then do the same logic of get => set if missing.
 */
export function CacheWithPrefix<T>(type: Type<T>, options?: CacheOptions) {
    return (
        target: unknown,
        methodName: string,
        descriptor: TypedPropertyDescriptor<Cacheable<T>>,
    ): TypedPropertyDescriptor<Cacheable<T>> => {
        const originalMethod = descriptor.value;
        const logger = PolloLogger.logger('CacheWithPrefix');

        descriptor.value = function (...args: any[]): Promise<T> {
            if (!config.get<boolean>('cache.enabled') || !CACHE_IS_UP) {
                return new Promise((resolve, reject) => {
                    (originalMethod.apply(this, args) as Promise<T>).then(resolve).catch(error => {
                        logger.log(
                            PolloMessage.msg(
                                `CacheWithPrefix => disabled => ${error.message}`,
                            ).setContext('CacheWithPrefix'),
                        );
                        reject(error);
                    });
                });
            }

            const cache = getCacheService(this);
            const account = getAccount(this, args);
            const key = getPrefixCacheKey(account, args, options ?? {}, false);
            const registryKey =
                getPrefixCacheKey(account, args, options ?? {}, true) + ':_registry';

            return new Promise<T>((resolve, reject) => {
                cache
                    .get<T>(key, type)
                    .then(cached => {
                        if (cached) {
                            logHint(
                                `CacheWithPrefix HIT => key="${key}"`,
                                'CacheWithPrefix',
                                'hit',
                            );
                            // No more plainToInstance here - get() already hydrated it
                            resolve(cached);
                        } else {
                            logHint(
                                `CacheWithPrefix MISS => key="${key}"`,
                                'CacheWithPrefix',
                                'miss',
                            );
                            (originalMethod.apply(this, args) as Promise<T>)
                                .then(async result => {
                                    if (result) {
                                        try {
                                            const status = await cache.set<T>(
                                                key,
                                                result,
                                                options?.ttl,
                                                type,
                                            );

                                            // Add key to registry for fast invalidation
                                            try {
                                                await cache.sadd(registryKey, key);
                                                // Registry TTL should be longer than cache entries
                                                const registryTtl = (options?.ttl || 3600) + 3600; // +1 hour

                                                // Always update the registry expiration to ensure it outlives all cache entries
                                                try {
                                                    // Get current TTL of the registry
                                                    const currentTtl = await cache.ttl(registryKey);

                                                    // Update expiration if:
                                                    // - Registry has no expiration (TTL = -1)
                                                    // - Registry is about to expire (TTL = -2, key doesn't exist)
                                                    // - New TTL is longer than current TTL
                                                    if (
                                                        currentTtl === -1 ||
                                                        currentTtl === -2 ||
                                                        currentTtl < registryTtl
                                                    ) {
                                                        await cache.expire(
                                                            registryKey,
                                                            registryTtl,
                                                        );
                                                    }
                                                } catch (ttlError) {
                                                    // If TTL check fails, just set the expiration anyway
                                                    await cache.expire(registryKey, registryTtl);
                                                }
                                            } catch (registryError) {
                                                // Registry failure shouldn't break caching
                                                logger.warn(
                                                    PolloMessage.msg(
                                                        `CacheWithPrefix registry error => ${registryError?.message}`,
                                                    ).setError(registryError),
                                                );
                                            }

                                            logHint(
                                                `CacheWithPrefix SET => key="${key}" status="${status}"`,
                                                'CacheWithPrefix',
                                                'set',
                                            );
                                        } catch (error) {
                                            logger.log(
                                                PolloMessage.msg(
                                                    `CacheWithPrefix set error => ${error?.message}`,
                                                ).setError(error),
                                            );
                                        }
                                        resolve(result);
                                    } else {
                                        resolve(null as unknown as T);
                                    }
                                })
                                .catch(error => {
                                    logger.log(
                                        PolloMessage.msg(
                                            `CacheWithPrefix => error => ${error.message}`,
                                        ).setError(error),
                                    );
                                    reject(error);
                                });
                        }
                    })
                    .catch(error => {
                        logger.log(
                            PolloMessage.msg(
                                `CacheWithPrefix => get error => ${error.message}`,
                            ).setError(error),
                        );
                        reject(error);
                    });
            });
        };

        return descriptor;
    };
}

/**
 * CacheBusterWithPrefix Decorator:
 *  - On method call, we do a prefix-based bust for one or multiple stores
 *    associated with the Account.
 */
export function CacheBusterWithPrefix<T>(options?: CacheOptions) {
    return (
        target: unknown,
        methodName: string,
        descriptor: TypedPropertyDescriptor<any>,
    ): TypedPropertyDescriptor<Cacheable<T>> => {
        const originalMethod = descriptor.value;
        const logger = PolloLogger.logger('CacheBusterWithPrefix');

        descriptor.value = function (...args: any[]): Promise<T> {
            if (config.get<boolean>('cache.enabled') && CACHE_IS_UP) {
                // get the cache service from the service class of the method being decorated
                const cache = getCacheService(this);
                // get the account argument from the method arguments being decorated
                const account = getAccount(this, args);
                // get the global prefix config
                const globalPrefix = config.get<string>('cache.globalPrefix');

                return new Promise<T>((resolve, reject) => {
                    logger.log(
                        PolloMessage.msg('CacheBusterWithPrefix triggered').setContext(
                            'CacheBusterWithPrefix',
                        ),
                    );

                    (originalMethod.apply(this, args) as Promise<T>)
                        .then(async result => {
                            try {
                                // Get properly validated and typed account ID
                                const accountIdOrGlobal = getAccountIdForCache(
                                    account,
                                    'CacheBusterWithPrefix',
                                );

                                // single store
                                if (options?.store && isString(options.store)) {
                                    // Use registry-based deletion instead of expensive SCAN
                                    const registryKey = `${globalPrefix}:{${accountIdOrGlobal}}:${options.store}:_registry`;

                                    logHint(
                                        `Bust single store => registry="${registryKey}"`,
                                        'CacheBusterWithPrefix',
                                        'registryDel',
                                    );

                                    try {
                                        // Get all cache keys from registry
                                        const cacheKeys = await cache.smembers(registryKey);

                                        if (cacheKeys.length > 0) {
                                            logHint(
                                                `Deleting ${cacheKeys.length} cache keys for store "${options.store}"`,
                                                'CacheBusterWithPrefix',
                                                'registryDel',
                                            );

                                            // Delete all cache keys + registry using batch operation
                                            // Add registry key to the list of keys to delete
                                            const allKeys = [...cacheKeys, registryKey];

                                            // Use multiDel for efficient batched deletion
                                            await cache.multiDel(allKeys).catch(error => {
                                                logger.warn(
                                                    PolloMessage.msg(
                                                        `CacheBusterWithPrefix remove keys error => ${error.message}`,
                                                    )
                                                        .setContext('CacheBusterWithPrefix')
                                                        .setError(error),
                                                );
                                            });
                                        } else {
                                            logHint(
                                                `No cache keys found in registry for store "${options.store}"`,
                                                'CacheBusterWithPrefix',
                                                'registryDel',
                                            );
                                        }
                                    } catch (registryError) {
                                        // Registry failure - fallback to SCAN
                                        logger.warn(
                                            PolloMessage.msg(
                                                `Registry deletion failed for store "${options.store}" => ${registryError.message}. Falling back.`,
                                            )
                                                .setContext('CacheBusterWithPrefix')
                                                .setError(registryError),
                                        );

                                        // Send Datadog metric for SCAN fallback
                                        tracer.dogstatsd.increment(
                                            'cache.registry.fallback.scan',
                                            1,
                                            {
                                                store: options.store,
                                                operation: 'single_store',
                                                reason: 'registry_error',
                                            },
                                        );

                                        // Fallback to SCAN operation
                                        const match = `${globalPrefix}:{${accountIdOrGlobal}}:${options.store}:*`;
                                        const count = config.get<number>(
                                            'cache.entryBusterChunkSize',
                                        );

                                        logHint(
                                            `Fallback SCAN => prefix="${match}"`,
                                            'CacheBusterWithPrefix',
                                            'scanDel',
                                        );

                                        await cache
                                            .scanDel({
                                                match,
                                                count,
                                            })
                                            .catch(error => {
                                                logger.warn(
                                                    PolloMessage.msg(
                                                        `CacheBusterWithPrefix scanDel error => ${error.message}`,
                                                    )
                                                        .setContext('CacheBusterWithPrefix')
                                                        .setError(error),
                                                );
                                            });
                                    }
                                }

                                // multiple
                                if (options?.stores && isArray(options.stores)) {
                                    logHint(
                                        `Bust multiple stores => stores="${options.stores.join(',')}"`,
                                        'CacheBusterWithPrefix',
                                        'registryDel',
                                    );
                                    for (const store of options.stores) {
                                        // Use registry-based deletion instead of expensive SCAN
                                        const registryKey = `${globalPrefix}:{${accountIdOrGlobal}}:${store}:_registry`;

                                        logHint(
                                            `registry => "${registryKey}"`,
                                            'CacheBusterWithPrefix',
                                            'registryDel',
                                        );

                                        try {
                                            // Get all cache keys from registry
                                            // eslint-disable-next-line no-await-in-loop
                                            const cacheKeys = await cache.smembers(registryKey);

                                            if (cacheKeys.length > 0) {
                                                logHint(
                                                    `Deleting ${cacheKeys.length} cache keys for store "${store}"`,
                                                    'CacheBusterWithPrefix',
                                                    'registryDel',
                                                );

                                                // Delete all cache keys + registry using proper batching
                                                const allKeys = [...cacheKeys, registryKey];
                                                await cache.multiDel(allKeys).catch(error => {
                                                    logger.warn(
                                                        PolloMessage.msg(
                                                            `CacheBusterWithPrefix remove keys error => ${error.message}`,
                                                        )
                                                            .setContext('CacheBusterWithPrefix')
                                                            .setError(error),
                                                    );
                                                });
                                            } else {
                                                logHint(
                                                    `No cache keys found in registry for store "${store}"`,
                                                    'CacheBusterWithPrefix',
                                                    'registryDel',
                                                );
                                            }
                                        } catch (registryError) {
                                            // Registry failure - fallback to SCAN
                                            logger.warn(
                                                PolloMessage.msg(
                                                    // eslint-disable-next-line max-len
                                                    `Registry deletion failed for store "${store}" => ${registryError.message}. Falling back to SCAN.`,
                                                )
                                                    .setContext('CacheBusterWithPrefix')
                                                    .setError(registryError),
                                            );

                                            // Send Datadog metric for SCAN fallback
                                            tracer.dogstatsd.increment(
                                                'cache.registry.fallback.scan',
                                                1,
                                                {
                                                    store: store,
                                                    operation: 'multiple_stores',
                                                    reason: 'registry_error',
                                                },
                                            );

                                            // Fallback to SCAN operation
                                            const match = `${globalPrefix}:{${accountIdOrGlobal}}:${store}:*`;
                                            const count = config.get<number>(
                                                'cache.entryBusterChunkSize',
                                            );

                                            logHint(
                                                `Fallback SCAN => prefix="${match}"`,
                                                'CacheBusterWithPrefix',
                                                'scanDel',
                                            );

                                            await cache
                                                .scanDel({
                                                    match,
                                                    count,
                                                })
                                                .catch(error => {
                                                    logger.warn(
                                                        PolloMessage.msg(
                                                            `CacheBusterWithPrefix scanDel error => ${error.message}`,
                                                        )
                                                            .setContext('CacheBusterWithPrefix')
                                                            .setError(error),
                                                    );
                                                });
                                        }
                                    }
                                }
                            } catch (error) {
                                logger.warn(
                                    PolloMessage.msg(
                                        `CacheBusterWithPrefix method error => ${error.message}`,
                                    )
                                        .setContext('CacheBusterWithPrefix')
                                        .setError(error),
                                );
                            }

                            resolve(result);
                        })
                        .catch(error => {
                            logger.log(
                                PolloMessage.msg(`CacheBusterWithPrefix error => ${error.message}`)
                                    .setContext('CacheBusterWithPrefix')
                                    .setError(error),
                            );
                            reject(error);
                        });
                });
            }

            // If disabled
            return new Promise((resolve, reject) => {
                (originalMethod.apply(this, args) as Promise<T>).then(resolve).catch(error => {
                    logger.log(
                        PolloMessage.msg(
                            `CacheBusterWithPrefix => disabled => error => ${error.message}`,
                        )
                            .setContext('CacheBusterWithPrefix')
                            .setError(error),
                    );
                    reject(error);
                });
            });
        };

        return descriptor;
    };
}

export function CacheEntityBuster<T, E>(options: CacheEntityOptions<E>) {
    return (
        target: { constructor: { name: string } },
        methodName: string,
        descriptor: TypedPropertyDescriptor<any>,
    ): TypedPropertyDescriptor<Cacheable<T>> => {
        const originalMethod = descriptor.value;
        const logger = PolloLogger.logger('CacheEntityBuster');

        descriptor.value = function (...args: any[]): Promise<T> {
            if (config.get<boolean>('cache.enabled') && CACHE_IS_UP) {
                const cache = getCacheService(this);
                const globalPrefix = config.get<string>('cache.globalPrefix');

                return new Promise<T>((resolve, reject) => {
                    (originalMethod.apply(this, args) as Promise<T>)
                        .then(async result => {
                            try {
                                const entity = getEntity<E>(this, args, options.type);
                                const targetId = entity[options.by];
                                // {global} is intentional, so entries all fall on the same redis shard
                                const key = `${globalPrefix}:{global}:${options.store}:${targetId}`;
                                await cache
                                    .unlink(key)
                                    .then(() => {
                                        logHint(
                                            `CacheEntityBuster UNLINK => key="${key}"`,
                                            'CacheEntityBuster',
                                            'del',
                                        );
                                    })
                                    .catch(error => {
                                        logger.warn(
                                            PolloMessage.msg(
                                                `CacheEntityBuster UNLINK error => ${error.message}`,
                                            )
                                                .setContext('CacheEntityBuster')
                                                .setError(error),
                                        );
                                    });
                            } catch (error) {
                                logger.warn(
                                    PolloMessage.msg(
                                        `CacheEntityBuster entity error => ${error.message}`,
                                    )
                                        .setContext('CacheEntityBuster')
                                        .setError(error),
                                );
                            }
                            resolve(result);
                        })
                        .catch(error => {
                            logger.warn(
                                PolloMessage.msg(
                                    `CacheEntityBuster method error => ${error.message}`,
                                )
                                    .setContext('CacheEntityBuster')
                                    .setError(error),
                            );
                            reject(error);
                        });
                });
            }

            // If disabled, just run
            return new Promise((resolve, reject) => {
                (originalMethod.apply(this, args) as Promise<T>).then(resolve).catch(error => {
                    logger.warn(
                        PolloMessage.msg(
                            `CacheEntityBuster with cache disabled => ${error.message}`,
                        )
                            .setContext('CacheEntityBuster')
                            .setError(error),
                    );
                    reject(error);
                });
            });
        };
        return descriptor;
    };
}
