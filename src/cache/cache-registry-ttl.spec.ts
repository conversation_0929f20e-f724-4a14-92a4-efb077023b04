import { CacheService } from 'cache/cache.service';

jest.mock('nestjs-ddtrace', () => ({
    Span: jest.fn(() => jest.fn()),
}));

jest.mock('cache/cache.module', () => ({
    CACHE_IS_UP: true,
}));

jest.mock('config', () => ({
    get: jest.fn().mockImplementation((key: string) => {
        if (key === 'cache.enabled') {
            return true;
        }
        if (key === 'cache.globalPrefix') {
            return 'globalstore';
        }
        return undefined;
    }),
}));

describe('Cache Registry TTL Management', () => {
    let cacheService: CacheService;
    let mockStore: any;

    beforeEach(() => {
        const mockPipeline = {
            sadd: jest.fn().mockReturnThis(),
            ttl: jest.fn().mockReturnThis(),
            exec: jest.fn().mockResolvedValue([[null, 1], [null, -1]]), // [saddResult, ttlResult]
        };

        mockStore = {
            sadd: jest.fn().mockResolvedValue(1),
            expire: jest.fn().mockResolvedValue(1),
            ttl: jest.fn().mockResolvedValue(-1),
            srem: jest.fn().mockResolvedValue(1),
            pipeline: jest.fn().mockReturnValue(mockPipeline),
        };

        cacheService = new CacheService(mockStore);
    });

    describe('autoRegisterCacheKey', () => {
        it('should set registry TTL when registry has no expiration (TTL = -1)', async () => {
            // Mock TTL to return -1 (no expiration)
            mockStore.ttl.mockResolvedValue(-1);

            // Call the private method via reflection
            const autoRegisterMethod = (cacheService as any).autoRegisterCacheKey.bind(
                cacheService,
            );
            await autoRegisterMethod(
                'globalstore:{account123}:USER_BY_ENTRY_ID:workspace456:user789',
                3600,
            );

            // Verify registry operations via pipeline
            expect(mockStore.pipeline).toHaveBeenCalled();
            const pipeline = mockStore.pipeline();
            expect(pipeline.sadd).toHaveBeenCalledWith(
                'globalstore:{account123}:USER_BY_ENTRY_ID:workspace456:_registry',
                'globalstore:{account123}:USER_BY_ENTRY_ID:workspace456:user789',
            );
            expect(pipeline.ttl).toHaveBeenCalledWith(
                'globalstore:{account123}:USER_BY_ENTRY_ID:workspace456:_registry',
            );
            expect(pipeline.exec).toHaveBeenCalled();
            expect(mockStore.expire).toHaveBeenCalledWith(
                'globalstore:{account123}:USER_BY_ENTRY_ID:workspace456:_registry',
                7200, // 3600 + 3600
            );
        });

        it('should set registry TTL when registry does not exist (TTL = -2)', async () => {
            // Mock TTL to return -2 (key doesn't exist)
            mockStore.ttl.mockResolvedValue(-2);

            const autoRegisterMethod = (cacheService as any).autoRegisterCacheKey.bind(
                cacheService,
            );
            await autoRegisterMethod(
                'globalstore:{account123}:USER_BY_ENTRY_ID:workspace456:user789',
                1800,
            );

            expect(mockStore.expire).toHaveBeenCalledWith(
                'globalstore:{account123}:USER_BY_ENTRY_ID:workspace456:_registry',
                5400, // 1800 + 3600
            );
        });

        it('should extend registry TTL when new TTL is longer than current', async () => {
            // Mock TTL to return 1000 (current TTL)
            mockStore.ttl.mockResolvedValue(1000);

            const autoRegisterMethod = (cacheService as any).autoRegisterCacheKey.bind(
                cacheService,
            );
            await autoRegisterMethod(
                'globalstore:{account123}:USER_BY_ENTRY_ID:workspace456:user789',
                3600,
            );

            // New TTL (7200) > current TTL (1000), so should update
            expect(mockStore.expire).toHaveBeenCalledWith(
                'globalstore:{account123}:USER_BY_ENTRY_ID:workspace456:_registry',
                7200,
            );
        });

        it('should NOT update registry TTL when current TTL is longer', async () => {
            // Mock pipeline to return 10000 TTL (current TTL is longer)
            const mockPipeline = mockStore.pipeline();
            mockPipeline.exec.mockResolvedValue([[null, 1], [null, 10000]]); // TTL result: 10000

            const autoRegisterMethod = (cacheService as any).autoRegisterCacheKey.bind(
                cacheService,
            );
            await autoRegisterMethod(
                'globalstore:{account123}:USER_BY_ENTRY_ID:workspace456:user789',
                1800,
            );

            // Current TTL (10000) > new TTL (5400), so should NOT update
            expect(mockStore.expire).not.toHaveBeenCalled();
        });

        it('should fallback to setting TTL if TTL check fails', async () => {
            // Mock TTL to throw an error
            mockStore.ttl.mockRejectedValue(new Error('TTL check failed'));

            const autoRegisterMethod = (cacheService as any).autoRegisterCacheKey.bind(
                cacheService,
            );
            await autoRegisterMethod(
                'globalstore:{account123}:USER_BY_ENTRY_ID:workspace456:user789',
                3600,
            );

            // Should still set the TTL as fallback
            expect(mockStore.expire).toHaveBeenCalledWith(
                'globalstore:{account123}:USER_BY_ENTRY_ID:workspace456:_registry',
                7200,
            );
        });

        it('should register non-prefixed keys in general registry', async () => {
            const autoRegisterMethod = (cacheService as any).autoRegisterCacheKey.bind(
                cacheService,
            );
            await autoRegisterMethod('some-random-key', 3600);

            // Should register in the global registry with hash tag via pipeline
            const pipeline = mockStore.pipeline();
            expect(pipeline.sadd).toHaveBeenCalledWith(
                'globalstore:{global}:_registry',
                'some-random-key',
            );
            expect(pipeline.ttl).toHaveBeenCalledWith('globalstore:{global}:_registry');
            expect(mockStore.expire).toHaveBeenCalledWith('globalstore:{global}:_registry', 7200); // 3600 + 3600
        });

        it('should register method-based keys in method-specific registry', async () => {
            const autoRegisterMethod = (cacheService as any).autoRegisterCacheKey.bind(
                cacheService,
            );
            await autoRegisterMethod('entryservice_getentrybyid_123', 3600);

            // Should register in the method-specific registry via pipeline
            const pipeline = mockStore.pipeline();
            expect(pipeline.sadd).toHaveBeenCalledWith(
                'entryservice_getentrybyid:_registry',
                'entryservice_getentrybyid_123',
            );
            expect(pipeline.ttl).toHaveBeenCalledWith('entryservice_getentrybyid:_registry');
            expect(mockStore.expire).toHaveBeenCalledWith(
                'entryservice_getentrybyid:_registry',
                7200,
            ); // 3600 + 3600
        });

        it('should register keys with minimal parts at account level', async () => {
            const autoRegisterMethod = (cacheService as any).autoRegisterCacheKey.bind(
                cacheService,
            );
            await autoRegisterMethod('globalstore:account123', 3600);

            // Should register at account level via pipeline
            const pipeline = mockStore.pipeline();
            expect(pipeline.sadd).toHaveBeenCalledWith(
                'globalstore:_registry',
                'globalstore:account123',
            );
            expect(pipeline.ttl).toHaveBeenCalledWith('globalstore:_registry');
            expect(mockStore.expire).toHaveBeenCalledWith('globalstore:_registry', 7200);
        });
    });
});
