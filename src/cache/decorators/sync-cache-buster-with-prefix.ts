import { Inject } from '@nestjs/common';
import { getAccount, getAccountIdForCache } from 'cache/cache.helper';
import { CACHE_IS_UP } from 'cache/cache.module';
import { SuperCacheAccountOptions } from 'cache/cache.options';
import { CacheService } from 'cache/cache.service';
import config from 'config';
import isEmpty from 'lodash';
import { PolloMessage } from 'pollo-logger/classes/pollo.message';
import { PolloLogger } from 'pollo-logger/pollo.logger';

/**
 * Decorating a method with this decorator will invoke cache deletion AFTER the decorated method completes.
 * The stores defined at declaration time will be targeted.
 */
export function SyncCacheBusterWithPrefix<ReturnType>(options: SuperCacheAccountOptions) {
    /**
     * Prepare injectors
     */

    const injectCacheService = Inject(CacheService);

    return (target: any, methodName: string, descriptor: PropertyDescriptor) => {
        const originalMethod = descriptor.value;
        const logger = PolloLogger.logger();

        /**
         * this is the same as using constructor(private readonly cacheServiceSyncCacheBusterWithPrefix: CacheService)
         * in a class. Note the name, make it unique so as to prevent constructor defined dependency collision.
         */
        injectCacheService(target, 'cacheServiceSyncCacheBusterWithPrefix');

        /**
         * Hijack the original method definition..
         * - Call the original method to run the already existing logic.
         * - Run cache killing logic.
         */
        descriptor.value = async function (...args: any[]): Promise<ReturnType> {
            /**
             * Run the decorated method's logic
             */
            const result = await originalMethod.apply(this, args);

            const cacheable = config.get<boolean>('cache.enabled') && CACHE_IS_UP;
            if (!cacheable) {
                /**
                 * Cache system not running, just return the result.
                 */
                return result;
            }

            if (!options?.stores) {
                /**
                 * No stores provided, just return the result.
                 */
                return result;
            }
            try {
                // get the cache service from the service class of the method being decorated
                const cacheService: CacheService = this.cacheServiceSyncCacheBusterWithPrefix;
                // get the global prefix config
                const globalPrefix = config.get<string>('cache.globalPrefix');
                // get the account argument from the method arguments being decorated
                const account = getAccount(this, args);

                const promises: Promise<void>[] = [];

                for (const store of options.stores) {
                    // Use registry-based deletion instead of expensive SCAN
                    const workspace = !isEmpty(account.getCurrentProduct())
                        ? account.getCurrentProduct().id
                        : '';

                    // Get properly validated and typed account ID
                    const accountIdOrGlobal = getAccountIdForCache(account, 'SyncCacheBusterWithPrefix');

                    // Use hash tag for account ID to ensure colocation
                    const registryKey = `${globalPrefix}:{${accountIdOrGlobal}}:${store}:${workspace}:_registry`;

                    logger.log(
                        PolloMessage.msg(
                            `SyncCacheBusterWithPrefix resolved registry: ${registryKey}`,
                        ).setContext('SyncCacheBusterWithPrefix'),
                    );

                    promises.push(
                        (async () => {
                            try {
                                // Get all cache keys from registry
                                const cacheKeys = await cacheService.smembers(registryKey);

                                if (cacheKeys.length > 0) {
                                    logger.log(
                                        PolloMessage.msg(
                                            `SyncCacheBusterWithPrefix deleting ${cacheKeys.length} cache keys for store "${store}"`,
                                        ).setContext('SyncCacheBusterWithPrefix'),
                                    );

                                    // Delete all cache keys + registry in parallel
                                    await Promise.all([
                                        ...cacheKeys.map(key => cacheService.del(key)),
                                        cacheService.del(registryKey),
                                    ]);
                                } else {
                                    logger.log(
                                        PolloMessage.msg(
                                            `SyncCacheBusterWithPrefix no cache keys found in registry for store "${store}"`,
                                        ).setContext('SyncCacheBusterWithPrefix'),
                                    );
                                }
                            } catch (registryError) {
                                // Registry failure shouldn't break the operation
                                logger.warn(
                                    PolloMessage.msg(
                                        `SyncCacheBusterWithPrefix registry deletion failed for store "${store}" => ${registryError.message}`,
                                    )
                                        .setContext('SyncCacheBusterWithPrefix')
                                        .setError(registryError),
                                );
                            }
                        })(),
                    );
                }

                const results = await Promise.allSettled(promises);
                // do some inspection on the results..
                const rejectedPromises = results.filter(({ status }) => status === 'rejected');
                if (!isEmpty(rejectedPromises)) {
                    logger.log(
                        PolloMessage.msg(`SyncCacheBusterWithPrefix scanDel rejections`)
                            .setContext('SyncCacheBusterWithPrefix')
                            .setIdentifier({
                                rejectedPromises,
                            }),
                    );
                }
            } catch (error) {
                /**
                 * Record the error, but do not panic
                 */
                logger.warn(
                    PolloMessage.msg(
                        `SyncCacheBusterWithPrefix decorator error => ${error.message}`,
                    )
                        .setContext(`SyncCacheBusterWithPrefix`)
                        .setError(error),
                );
            }

            return result;
        };

        return descriptor;
    };
}
