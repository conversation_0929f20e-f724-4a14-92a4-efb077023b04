import { MigrationInterface, QueryRunner } from "typeorm";

export class AddIsInternalAuditFieldOnAuditorFramework1759167845822 implements MigrationInterface {
    name = 'AddIsInternalAuditFieldOnAuditorFramework1759167845822'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`auditor_framework\` ADD \`is_internal_audit\` tinyint(1) NOT NULL DEFAULT 0`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`auditor_framework\` DROP COLUMN \`is_internal_audit\``);
    }

}
