import { Account } from 'auth/entities/account.entity';
import { TenantsBackfillBeta } from 'commons/backfill/tenants-backfill-beta';
import { type DrataDataSource } from 'commons/classes/drata-data-source.class';
import { DrataEntityManager } from 'commons/classes/drata-entity-manager.class';
import { PolicyVersionStatus } from 'commons/enums/users/policies/policy-version-status.enum';
import { get, isNil, isUndefined } from 'lodash';
import { PolloMessage } from 'pollo-logger/classes/pollo.message';

/**
 * Represents a policy record with associated metadata and versions.
 */
type PolicyRecord = {
    policy_id: number;
    policy_name: string;
    policy_scope: number;
    policy_created_at: string;
    policy_updated_at: string;
    policy_status: number;
    version: CurrentPublishedPolicyVersionRecord;
};

/**
 * Represents a current published policy version of a policy with its associated details.
 */
type CurrentPublishedPolicyVersionRecord = {
    policy_version_id: number;
    policy_version_number: number;
    policy_version_current: number;
    policy_version_approved_at: string;
    policy_version_created_at: string;
    policy_version_updated_at: string;
    policy_version_deleted_at: string;
};

/**
 * Represents a user policy version record with its associated details.
 */
type UPVRecord = {
    id: number;
    accepted_at: string;
    created_at: string;
    updated_at: string;
    fk_user_id: number;
    fk_policy_version_id: number;
    fk_policy_id: number;
    deleted_at: number;
};

/**
 * Class to handle the backfill of duplicate user policy versions.
 */
class BackfillDuplicateUpvs extends TenantsBackfillBeta {
    /**
     * SQL query to select relevant policies that are not deleted and have any status.
     * Updated to include all policy statuses (ACTIVE, ARCHIVED, REPLACED, UNACCEPTABLE, OUTDATED)
     * to handle duplicate user policy versions across all policy states.
     */
    // eslint-disable-next-line local-rules/no-disable-mandatory-rules
    // eslint-disable-next-line local-rules/backfill-multi-thread-safety-check
    private relevantPoliciesSql = `
        SELECT DISTINCT
            p.id AS policy_id
            , p.name AS policy_name
            , p.scope AS policy_scope
            , p.created_at AS policy_created_at
            , p.updated_at AS policy_updated_at
            , p.policy_status AS policy_status
            , p.deleted_at AS policy_deleted_at
        FROM policy p
        LEFT JOIN policies_frameworks pf ON pf.fk_policy_id = p.id
        LEFT JOIN framework f ON f.id = pf.fk_framework_id
        WHERE
            p.deleted_at IS NULL
            AND (f.enabled_at IS NOT NULL OR p.fk_template_id IS NULL)
    `;

    /**
     * SQL query to select current published policy versions that are not deleted and belong to a specific policy.
     */
    // eslint-disable-next-line local-rules/no-disable-mandatory-rules
    // eslint-disable-next-line local-rules/backfill-multi-thread-safety-check
    private currentPublishedPolicyVersionsSql = `
        SELECT
            pv.id AS policy_version_id
            , pv.version AS policy_version_number
            , pv.current AS policy_version_current
            , pv.approved_at AS policy_version_approved_at
            , pv.created_at AS policy_version_created_at
            , pv.updated_at AS policy_version_updated_at
            , pv.deleted_at AS policy_version_deleted_at
        FROM
            policy_version pv
        WHERE
            pv.deleted_at IS NULL
            AND pv.current = 1
        	AND pv.status = '${PolicyVersionStatus.PUBLISHED}'
            AND pv.fk_policy_id = ?
            LIMIT 1;
    `;

    /**
     * SQL query to select user policy versions that are not deleted and belong to a specific policy version.
     */
    // eslint-disable-next-line local-rules/no-disable-mandatory-rules
    // eslint-disable-next-line local-rules/backfill-multi-thread-safety-check
    private upvsForUsersSql = `
        SELECT
            upv.*
        FROM
            user_policy_version upv
        WHERE
            fk_policy_version_id = ?
            AND deleted_at IS NULL
        ORDER BY fk_user_id
    `;

    /**
     * SQL query template to soft delete user policy versions by setting the deleted_at field to the current timestamp.
     */
    // eslint-disable-next-line local-rules/no-disable-mandatory-rules
    // eslint-disable-next-line local-rules/backfill-multi-thread-safety-check
    private templateUpdateSql = `
        UPDATE user_policy_version
        SET deleted_at = NOW()
        WHERE id IN (:idList:)
    `;

    /**
     * Sets the script to run in dry run mode, which means no changes will be made to the database.
     */
    protected setDryRun() {
        this.isDryRun = true;
    }

    /**
     *
     * @param account
     * @param globalConnection
     * @returns
     */
    protected async runTenantBackfill(account: Account): Promise<void> {
        let tenantConnection: DrataDataSource | undefined = undefined;
        try {
            tenantConnection = await this.getTenantConnection(account);
            const entityManager: DrataEntityManager = tenantConnection.manager;

            const relevantPolicies: PolicyRecord[] = await this.getRelevantPolicies(
                account,
                entityManager,
            );

            // hydrate current published policy versions for each policy
            await this.populateCurrentPublishedPolicyVersions(
                account,
                entityManager,
                relevantPolicies,
            );

            /**
             * Process them now
             */
            await this.processPolicies(account, entityManager, relevantPolicies);
        } catch (error) {
            const message = get(error, 'message', 'Something went wrong');
            this.logger.error(
                PolloMessage.msg(message, this.getClassName())
                    .setDomain(account.domain)
                    .setError(error)
                    .setIdentifier({ accountId: account.id }),
            );
            // rethrow for now, higher scope catches
            throw error;
        } finally {
            if (tenantConnection) {
                await this.disconnect(tenantConnection);
            }
        }
    }

    /**
     * Gets the policies that are not deleted and have any status.
     * Updated to include all policy statuses to handle duplicate user policy versions
     * across ACTIVE, ARCHIVED, REPLACED, UNACCEPTABLE, and OUTDATED policies.
     *
     * @param account
     * @param entityManager
     * @returns {Promise<PolicyRecord[]>}
     */
    private async getRelevantPolicies(
        account: Account,
        entityManager: DrataEntityManager,
    ): Promise<PolicyRecord[]> {
        this.log('getRelevantPolicies: Running query', account, account.domain, {
            sql: this.relevantPoliciesSql,
        });

        const relevantPolicies: PolicyRecord[] = await entityManager.query(
            this.relevantPoliciesSql,
        );
        this.log('getRelevantPolicies: Query results', account, account.domain, {
            sql: this.relevantPoliciesSql,
            resultLength: relevantPolicies.length,
        });
        return relevantPolicies;
    }

    /**
     * Process the set of current published policy versions
     *
     * @param account
     * @param entityManager
     * @param relevantPolicies
     */
    private async processPolicies(
        account: Account,
        entityManager: DrataEntityManager,
        relevantPolicies: PolicyRecord[],
    ): Promise<void> {
        for (const policyRecord of relevantPolicies) {
            const currentPublishedPolicyVersionRecord = policyRecord.version;

            if (!currentPublishedPolicyVersionRecord) {
                this.log(
                    `processPolicies: Policy with id ${policyRecord.policy_id} has no current published version`,
                    account,
                    account.domain,
                    {
                        policyRecord,
                    },
                );
                continue;
            }
            this.log(
                `processPolicies: Processing policy version with id ${currentPublishedPolicyVersionRecord.policy_version_id}`,
                account,
                account.domain,
                {
                    currentPublishedPolicyVersionRecord: currentPublishedPolicyVersionRecord,
                    policyRecord,
                },
            );
            // eslint-disable-next-line no-await-in-loop
            await this.processCurrentPublishedPolicyVersion(
                account,
                entityManager,
                policyRecord,
                currentPublishedPolicyVersionRecord,
            );
        }
    }

    /**
     * Process the current published policy version
     *
     * @param account
     * @param entityManager
     * @param currentPublishedPolicyVersionRecord
     * @returns {Promise<void>}
     */
    private async processCurrentPublishedPolicyVersion(
        account: Account,
        entityManager: DrataEntityManager,
        policyRecord: PolicyRecord,
        currentPublishedPolicyVersionRecord: CurrentPublishedPolicyVersionRecord,
    ): Promise<void> {
        /**
         * Grab the set of user policy versions for this policy version.
         */
        const params = [currentPublishedPolicyVersionRecord.policy_version_id];
        this.log(
            `currentPublishedPolicyVersionRecord: Running query for version ${currentPublishedPolicyVersionRecord.policy_version_id}`,
            account,
            account.domain,
            {
                sql: this.upvsForUsersSql,
                params,
                currentPublishedPolicyVersionRecord: currentPublishedPolicyVersionRecord,
                policyRecord,
            },
        );

        const userPolicyVersions: UPVRecord[] = await entityManager.query(
            this.upvsForUsersSql,
            params,
        );
        if (userPolicyVersions.length < 1) {
            this.log(
                'currentPublishedPolicyVersionRecord: No user policy versions detected',
                account,
                account.domain,
                {
                    sql: this.upvsForUsersSql,
                    params,
                    latestApprovedPolicyVersion: currentPublishedPolicyVersionRecord,
                    policyRecord,
                },
            );
            return;
        }

        /**
         * Create a map of users to user policy versions.
         */
        const userToUPVmap: Map<number, UPVRecord[]> = this.createUserToUPVmap(userPolicyVersions);

        for (const [userId, records] of userToUPVmap) {
            if (records.length < 2) {
                const message =
                    'No duplicates for (user id, policy version id, policy id) = (:uid:, :pvid:, :pid:)'
                        .replace(':uid:', `${userId}`)
                        .replace(
                            ':pvid:',
                            `${currentPublishedPolicyVersionRecord.policy_version_id}`,
                        )
                        .replace(':pid:', `${policyRecord.policy_id}`);

                this.log(message, account, account.domain, {
                    currentPublishedPolicyVersion: currentPublishedPolicyVersionRecord,
                    userId,
                    records,
                    policyRecord,
                });
                continue;
            }
            // eslint-disable-next-line no-await-in-loop
            await this.processUPVsForUser(account, entityManager, userId, records);
        }
    }

    /**
     * Process the user policy version records for each user
     *
     * @param account
     * @param entityManager
     * @param userId
     * @param records
     * @returns {Promise<void>}
     */
    private async processUPVsForUser(
        account: Account,
        entityManager: DrataEntityManager,
        userId: number,
        records: UPVRecord[],
    ): Promise<void> {
        // find the earliest accepted if any
        const acceptedRecords = records.filter(record => !isNil(record.accepted_at));
        let primaryRecord: UPVRecord | undefined = this.findEarliestUPVRecord(
            acceptedRecords,
            'accepted_at',
        );

        // if none found, find the earliest created
        if (isNil(primaryRecord)) {
            primaryRecord = this.findEarliestUPVRecord(records, 'created_at');
        }

        if (isNil(primaryRecord)) {
            this.log(
                'processUPVsForUser: No primary record found, skipping..',
                account,
                account.domain,
                {
                    userId,
                    records,
                },
            );
            return;
        }
        // delete all except the primary
        const recordsToSoftDelete = records.filter(record => record.id != primaryRecord.id);
        const placeholders = recordsToSoftDelete.map(() => '?');
        const updateSql = this.templateUpdateSql.replace(':idList:', placeholders.join(', '));
        const parameters = recordsToSoftDelete.map(({ id }) => id);

        if (this.isDryRun) {
            this.log(
                'Dry run detected, not soft deleting user policy versions',
                account,
                account.domain,
                {
                    primaryRecord,
                    recordsToSoftDelete,
                    updateSql,
                    parameters,
                },
            );
            return;
        }
        this.log('Soft deleting user policy versions with query', account, account.domain, {
            updateSql,
            parameters,
            primaryRecord,
            recordsToSoftDelete,
        });
        const result = await entityManager.query(updateSql, parameters);

        this.log('Query executed', account, account.domain, {
            updateSql,
            parameters,
            result,
        });
    }

    /**
     * Find the earliest record, by the denoted field
     *
     * @param records
     * @param field
     * @returns {UPVRecord | undefined}
     */
    private findEarliestUPVRecord(records: UPVRecord[], field: string): UPVRecord | undefined {
        let primaryRecord: UPVRecord | undefined = undefined;
        for (const record of records) {
            /**
             * handle initial set
             * handle when current record is earler than primary
             */
            if (
                isUndefined(primaryRecord) ||
                new Date(record[field]) < new Date(primaryRecord[field])
            ) {
                // initial set
                primaryRecord = record;
            }
        }
        return primaryRecord;
    }

    /**
     *
     * @param account
     * @param entityManager
     * @returns {Promise<void>}
     */
    private async populateCurrentPublishedPolicyVersions(
        account: Account,
        entityManager: DrataEntityManager,
        relevantPolicies: PolicyRecord[],
    ): Promise<void> {
        // hydrate the policy records
        for (const policyRecord of relevantPolicies) {
            const parameters = [policyRecord.policy_id];
            this.log(
                'populateCurrentPublishedPolicyVersions: Running query',
                account,
                account.domain,
                {
                    sql: this.currentPublishedPolicyVersionsSql,
                    parameters,
                },
            );
            const policyVersions: CurrentPublishedPolicyVersionRecord[] =
                // eslint-disable-next-line no-await-in-loop
                await entityManager.query(this.currentPublishedPolicyVersionsSql, parameters);
            this.log(
                'populateCurrentPublishedPolicyVersions: Query results',
                account,
                account.domain,
                {
                    sql: this.currentPublishedPolicyVersionsSql,
                    resultLength: policyVersions.length,
                },
            );
            if (isNil(policyVersions) || policyVersions.length < 1) {
                // no versions yet done
                this.log('No current published policy version detected', account, account.domain);
                continue;
            }
            policyRecord.version = policyVersions[0];
        }
    }

    /**
     * create a map of users to user policy versions
     */
    private createUserToUPVmap(records: UPVRecord[]): Map<number, UPVRecord[]> {
        const userToUPVmap: Map<number, UPVRecord[]> = new Map<number, UPVRecord[]>();

        for (const record of records) {
            const item = userToUPVmap.get(record.fk_user_id);
            if (isUndefined(item)) {
                userToUPVmap.set(record.fk_user_id, [record]);
            } else {
                item.push(record);
            }
        }
        return userToUPVmap;
    }
}
void new BackfillDuplicateUpvs().runTenantsBackfill();
