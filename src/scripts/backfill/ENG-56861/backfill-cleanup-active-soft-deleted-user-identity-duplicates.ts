import { Account } from 'auth/entities/account.entity';
import { RestrictedTenantBackfill } from 'commons/backfill/restricted-tenant-backfill';
import { type DrataDataSource } from 'commons/classes/drata-data-source.class';
import { DrataQueryRunner } from 'commons/classes/drata-query-runner.class';

/**
 * Backfill to clean up soft-deleted user_identity records that conflict with active records.
 *
 * Context: ENG-56861 - Fix user_identity unique constraint for soft deletes
 *
 * IMPORTANT EXECUTION ORDER:
 * 1. Run migration *************-MakeUserIdentityFKsNullable (makes FKs nullable, creates archive table)
 * 2. Run backfill-cleanup-user-identity-duplicates (archives duplicate soft-deleted records)
 * 3. Run THIS BACKFILL (cleans up soft-deleted records that conflict with active records)
 * 4. Run migration *************-CreateUserIdentityUniqueIndex (creates unique index)
 *
 * This backfill:
 * 1. Identifies active user_identity records
 * 2. Finds soft-deleted records with same identity_id + connection_id as active records
 * 3. Archives the conflicting soft-deleted records to user_identity_archive table
 * 4. Orphans audit records ONLY for the archived soft-deleted records
 * 5. Deletes mapping table records ONLY for the archived soft-deleted records
 *
 * This ensures the new 2-column unique index can be created successfully.
 */
export class CleanupActiveSoftDeletedConflicts extends RestrictedTenantBackfill {
    constructor() {
        super();
    }

    protected async runTenantBackfill(
        account: Account,
        tenantConnection: DrataDataSource,
    ): Promise<void> {
        let queryRunner: DrataQueryRunner | null = null;

        try {
            queryRunner = tenantConnection.createQueryRunner();

            await queryRunner.connect();
            await queryRunner.startTransaction();

            // Local stats for this tenant
            const stats = {
                tenantId: account.id,
                conflictingPairs: 0,
                archivedRecords: 0,
                orphanedApplicationUserAccess: 0,
                orphanedAccessReviewPeriodApplicationUser: 0,
                orphanedInfrastructure: 0,
                orphanedObservability: 0,
                orphanedVersionControl: 0,
                deletedGroupMappings: 0,
                errors: [] as string[],
            };

            // Step 1: Identify soft-deleted records that conflict with active records
            // Find identity_id + connection_id pairs where BOTH an active AND soft-deleted record exist
            const conflictingPairs = await queryRunner.query(`
                SELECT
                    active.identity_id,
                    active.fk_connection_id,
                    COUNT(DISTINCT CASE WHEN ui.deleted_at IS NULL THEN ui.id END) as active_count,
                    COUNT(DISTINCT CASE WHEN ui.deleted_at IS NOT NULL THEN ui.id END) as soft_deleted_count
                FROM user_identity active
                INNER JOIN user_identity ui ON
                    ui.identity_id = active.identity_id
                    AND ui.fk_connection_id = active.fk_connection_id
                WHERE active.deleted_at IS NULL
                GROUP BY active.identity_id, active.fk_connection_id
                HAVING COUNT(DISTINCT CASE WHEN ui.deleted_at IS NOT NULL THEN ui.id END) > 0
            `);

            stats.conflictingPairs = conflictingPairs.length;

            if (conflictingPairs.length === 0) {
                this.logInfo({
                    msg: 'No soft-deleted records conflicting with active records found',
                    account,
                });
                await queryRunner.commitTransaction();
                await queryRunner.release();
                return;
            }

            this.logInfo({
                msg: `Found ${conflictingPairs.length} identity/connection pairs with active + soft-deleted conflicts`,
                account,
            });

            // Step 2: Get the specific soft-deleted records to archive
            // Only archive soft-deleted records where an active record exists for same identity+connection
            const recordsToArchive = await queryRunner.query(`
                SELECT ui_soft.*
                FROM user_identity ui_soft
                INNER JOIN user_identity ui_active ON
                    ui_active.identity_id = ui_soft.identity_id
                    AND ui_active.fk_connection_id = ui_soft.fk_connection_id
                    AND ui_active.deleted_at IS NULL
                WHERE ui_soft.deleted_at IS NOT NULL
            `);

            if (recordsToArchive.length === 0) {
                this.logInfo({
                    msg: 'No soft-deleted records to archive (unexpected state)',
                    account,
                });
                await queryRunner.commitTransaction();
                await queryRunner.release();
                return;
            }

            const archiveIds = recordsToArchive.map(r => r.id);
            stats.archivedRecords = archiveIds.length;

            this.logInfo({
                msg: `Archiving ${archiveIds.length} soft-deleted records that conflict with active records`,
                account,
                metadata: {
                    sampleConflicts: conflictingPairs.slice(0, 5).map(c => ({
                        identity_id: c.identity_id,
                        connection_id: c.fk_connection_id,
                        active_count: c.active_count,
                        soft_deleted_count: c.soft_deleted_count,
                    })),
                },
            });

            // Step 3: Archive conflicting soft-deleted records to user_identity_archive
            const archiveResult = await queryRunner.query(
                `
                INSERT INTO user_identity_archive (
                    identity_id, username, email, connected_at, disconnected_at,
                    service_account, service_account_reason, has_mfa, last_checked_at,
                    avatar_source, user_avatar, created_at, updated_at, deleted_at,
                    fk_user_id, fk_connection_id, fk_service_account_marked_by_id,
                    unlinked_at, secondary_email, firstname, lastname,
                    started_at, separated_at, is_contractor, job_title,
                    manager_id, manager_name, archive_reason, original_id
                )
                SELECT
                    identity_id, username, email, connected_at, disconnected_at,
                    service_account, service_account_reason, has_mfa, last_checked_at,
                    avatar_source, user_avatar, created_at, updated_at, deleted_at,
                    fk_user_id, fk_connection_id, fk_service_account_designator_id,
                    unlinked_at, secondary_email, firstname, lastname,
                    started_at, separated_at, is_contractor, job_title,
                    manager_id, manager_name, 'ACTIVE_SOFT_DELETE_CONFLICT', id
                FROM user_identity
                WHERE id IN (${archiveIds.map(() => '?').join(',')})
            `,
                archiveIds,
            );

            // Log archive operation results
            this.logInfo({
                msg: `Archive operation completed`,
                account,
                metadata: {
                    recordsArchived: archiveResult.affectedRows || 0,
                    firstArchivedId: archiveResult.insertId || null,
                    archiveWarnings: archiveResult.warningCount || 0,
                    expectedRecords: archiveIds.length,
                    archiveMatches: (archiveResult.affectedRows || 0) === archiveIds.length,
                },
            });

            // Step 4: Orphan audit records ONLY for the archived soft-deleted records
            const orphanedAccessResult = await queryRunner.query(
                `
                UPDATE application_user_access
                SET fk_user_identity_id = NULL
                WHERE fk_user_identity_id IN (${archiveIds.map(() => '?').join(',')})
            `,
                archiveIds,
            );
            stats.orphanedApplicationUserAccess = orphanedAccessResult.affectedRows || 0;

            const orphanedReviewResult = await queryRunner.query(
                `
                UPDATE access_review_period_application_user
                SET fk_user_identity_id = NULL
                WHERE fk_user_identity_id IN (${archiveIds.map(() => '?').join(',')})
            `,
                archiveIds,
            );
            stats.orphanedAccessReviewPeriodApplicationUser =
                orphanedReviewResult.affectedRows || 0;

            const orphanedInfraResult = await queryRunner.query(
                `
                UPDATE user_identity_infrastructure
                SET fk_user_identity_id = NULL
                WHERE fk_user_identity_id IN (${archiveIds.map(() => '?').join(',')})
            `,
                archiveIds,
            );
            stats.orphanedInfrastructure = orphanedInfraResult.affectedRows || 0;

            const orphanedObsResult = await queryRunner.query(
                `
                UPDATE user_identity_observability
                SET fk_user_identity_id = NULL
                WHERE fk_user_identity_id IN (${archiveIds.map(() => '?').join(',')})
            `,
                archiveIds,
            );
            stats.orphanedObservability = orphanedObsResult.affectedRows || 0;

            const orphanedVcResult = await queryRunner.query(
                `
                UPDATE user_identity_version_control
                SET fk_user_identity_id = NULL
                WHERE fk_user_identity_id IN (${archiveIds.map(() => '?').join(',')})
            `,
                archiveIds,
            );
            stats.orphanedVersionControl = orphanedVcResult.affectedRows || 0;

            // Step 5: Delete mapping table records ONLY for archived soft-deleted records
            const deletedMappingsResult = await queryRunner.query(
                `
                DELETE FROM application_group_user_identity_map
                WHERE fk_user_identity_id IN (${archiveIds.map(() => '?').join(',')})
            `,
                archiveIds,
            );
            stats.deletedGroupMappings = deletedMappingsResult.affectedRows || 0;

            // Step 6: Delete the soft-deleted records from user_identity (now that they're archived)
            const deleteResult = await queryRunner.query(
                `
                DELETE FROM user_identity
                WHERE id IN (${archiveIds.map(() => '?').join(',')})
            `,
                archiveIds,
            );

            // Log delete operation results
            this.logInfo({
                msg: `Delete operation completed`,
                account,
                metadata: {
                    recordsDeleted: deleteResult.affectedRows || 0,
                    deleteWarnings: deleteResult.warningCount || 0,
                    expectedRecords: archiveIds.length,
                    deleteMatches: (deleteResult.affectedRows || 0) === archiveIds.length,
                },
            });

            // Validation: Check for operation mismatches
            if ((archiveResult.affectedRows || 0) !== archiveIds.length) {
                this.logInfo({
                    msg: `WARNING: Archive operation mismatch: expected ${archiveIds.length} records, archived ${archiveResult.affectedRows || 0}`,
                    account,
                    metadata: {
                        validationWarning: 'ARCHIVE_MISMATCH',
                        expectedRecords: archiveIds.length,
                        actualArchived: archiveResult.affectedRows || 0,
                        archiveIds: archiveIds.slice(0, 10), // First 10 for debugging
                    },
                });
            }

            if ((deleteResult.affectedRows || 0) !== archiveIds.length) {
                this.logInfo({
                    msg: `WARNING: Delete operation mismatch: expected ${archiveIds.length} records, deleted ${deleteResult.affectedRows || 0}`,
                    account,
                    metadata: {
                        validationWarning: 'DELETE_MISMATCH',
                        expectedRecords: archiveIds.length,
                        actualDeleted: deleteResult.affectedRows || 0,
                        archiveIds: archiveIds.slice(0, 10), // First 10 for debugging
                    },
                });
            }

            // Step 7: Verify no conflicts remain
            const remainingConflicts = await queryRunner.query(`
                SELECT
                    active.identity_id,
                    active.fk_connection_id,
                    COUNT(*) as total_count
                FROM user_identity active
                INNER JOIN user_identity ui ON
                    ui.identity_id = active.identity_id
                    AND ui.fk_connection_id = active.fk_connection_id
                WHERE active.deleted_at IS NULL
                GROUP BY active.identity_id, active.fk_connection_id
                HAVING COUNT(*) > 1
            `);

            if (remainingConflicts.length > 0) {
                this.logInfo({
                    msg: `WARNING: ${remainingConflicts.length} conflicts still remain after cleanup`,
                    account,
                    metadata: {
                        validationWarning: 'REMAINING_CONFLICTS',
                        remainingConflicts: remainingConflicts.slice(0, 5),
                    },
                });
            }

            // Commit the transaction
            await queryRunner.commitTransaction();
            await queryRunner.release();

            // Log results for this tenant
            const orphanedTotal =
                stats.orphanedApplicationUserAccess +
                stats.orphanedAccessReviewPeriodApplicationUser +
                stats.orphanedInfrastructure +
                stats.orphanedObservability +
                stats.orphanedVersionControl;

            this.logInfo({
                msg:
                    `Cleanup completed: Archived ${stats.archivedRecords} conflicting soft-deleted records, ` +
                    `deleted ${deleteResult.affectedRows || 0} from main table, ` +
                    `orphaned ${orphanedTotal} audit records, deleted ${stats.deletedGroupMappings} mappings, ` +
                    `${remainingConflicts.length} conflicts remaining`,
                account,
                metadata: {
                    ...stats,
                    archiveOperationDetails: {
                        affectedRows: archiveResult.affectedRows || 0,
                        warningCount: archiveResult.warningCount || 0,
                    },
                    deleteOperationDetails: {
                        affectedRows: deleteResult.affectedRows || 0,
                        warningCount: deleteResult.warningCount || 0,
                    },
                    remainingConflicts: remainingConflicts.length,
                },
            });
        } catch (error) {
            // Rollback on error
            if (queryRunner) {
                await queryRunner.rollbackTransaction();
                await queryRunner.release();
            }

            this.logError({
                msg: 'Cleanup failed',
                account,
                error,
            });

            throw error;
        } finally {
            // QueryRunner cleanup is handled in catch/success blocks
            // Connection cleanup is handled by parent class
        }
    }
}

// Execution wrapper
void new CleanupActiveSoftDeletedConflicts().runTenantsBackfill();
