import { FileValidationIssue } from 'commons/enums/file-validation/file-validation-issue.enum';
import { FileValidationScope } from 'commons/enums/file-validation/file-validation-scope.enum';

export const FileValidationScopeMap = new Map([
    [
        FileValidationScope.FILE,
        [
            FileValidationIssue.INVALID_FILE_EXTENSION,
            FileValidationIssue.INVALID_FILE_SIZE,
            FileValidationIssue.INVALID_FILE_FORMAT,
        ],
    ],
    [FileValidationScope.COLUMN, [FileValidationIssue.MISSING_COLUMNS]],
    [
        FileValidationScope.ROW,
        [
            FileValidationIssue.MISSING_REQUIRED_VALUES,
            FileValidationIssue.DUPLICATED_CODES,
            FileValidationIssue.INVALID_DATA,
            FileValidationIssue.INVALID_FIELD_SCHEMA,
            FileValidationIssue.DUPLICATED_NAMES,
            FileValidationIssue.NO_RECORDS,
            FileValidationIssue.EMPTY_ROW,
            FileValidationIssue.DUPLICATED_REQUIREMENT,
            FileValidationIssue.CONTROL_NOT_ENABLED,
            FileValidationIssue.MAX_LENGTH_EXCEEDED,
            FileValidationIssue.DUPLICATED_REQUEST_IDS,
            FileValidationIssue.DUPLICATED_TITLES,
            FileValidationIssue.DUPLICATED_CUSTOMER_REQUESTS,
            FileValidationIssue.DUPLICATED_CUSTOMER_REQUEST_TITLES,
            FileValidationIssue.MAX_ROWS_EXCEEDED,
            FileValidationIssue.DUPLICATED_ROWS,
        ],
    ],
]);
