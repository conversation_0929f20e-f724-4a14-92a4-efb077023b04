import { FileValidationIssue } from 'commons/enums/file-validation/file-validation-issue.enum';

export const FileValidationDescription = {
    [FileValidationIssue.INVALID_FILE_EXTENSION]:
        'The file extension is invalid. Make sure to upload a file with a .csv extension.',
    [FileValidationIssue.INVALID_FILE_SIZE]: 'The file size exceeds the limit.',
    [FileValidationIssue.INVALID_FILE_FORMAT]:
        'The file format is invalid. Make sure the number of headers matches the number of items in each row.',
    [FileValidationIssue.MISSING_COLUMNS]:
        'There are missing columns. It is strongly recommended to use the template previously provided.',
    [FileValidationIssue.MISSING_REQUIRED_VALUES]: 'There are missing required values.',
    [FileValidationIssue.DUPLICATED_CODES]:
        'There are duplicate codes. Make sure all values from the Code column are unique.',
    [FileValidationIssue.INVALID_DATA]:
        'There is invalid data in this file. Please look for special characters and remove them.',
    [FileValidationIssue.INVALID_FIELD_SCHEMA]:
        'Some values have invalid data for their set schema or field type.',
    [FileValidationIssue.DUPLICATED_NAMES]:
        'There are duplicate names. It is recommended to make them unique.',
    [FileValidationIssue.NO_RECORDS]: 'The file is empty. Requirements will not be stored.',
    [FileValidationIssue.EMPTY_ROW]:
        'At least one empty row was found. Every empty row will be omitted.',
    [FileValidationIssue.DUPLICATED_REQUIREMENT]: 'The requirement already exists.',
    [FileValidationIssue.CONTROL_NOT_ENABLED]: 'The control is not enabled or does not exist.',
    [FileValidationIssue.MAX_LENGTH_EXCEEDED]: 'Some values have exceeded their maximum length.',
    [FileValidationIssue.DUPLICATED_REQUEST_IDS]:
        'There are duplicate request IDs. Make sure all values from the Request ID column are unique.',
    [FileValidationIssue.DUPLICATED_TITLES]:
        'There are duplicated titles. Make sure all values from the Title column are unique.',
    [FileValidationIssue.DUPLICATED_CUSTOMER_REQUESTS]: 'The customer request already exists.',
    [FileValidationIssue.DUPLICATED_CUSTOMER_REQUEST_TITLES]:
        'The customer request title already exists.',
    [FileValidationIssue.MAX_ROWS_EXCEEDED]: 'Limit of %s rows exceeded',
    [FileValidationIssue.DUPLICATED_ROWS]:
        'There are duplicate rows. It is not possible to process the same exact row more than once.',
};
