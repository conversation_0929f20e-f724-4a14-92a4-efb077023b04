import { FileValidationIssue } from 'commons/enums/file-validation/file-validation-issue.enum';
import { FileValidation } from 'file-validation/validations/base/file-validation';
import { ControlNotEnabledValidation } from 'file-validation/validations/control-not-enabled.validation';
import { DuplicatedCodesValidation } from 'file-validation/validations/duplicated-codes.validation';
import { DuplicatedCustomerRequestTitlesValidation } from 'file-validation/validations/duplicated-customer-request-titles.validation';
import { DuplicatedCustomerRequestsValidation } from 'file-validation/validations/duplicated-customer-requests.validation';
import { DuplicatedNamesValidation } from 'file-validation/validations/duplicated-names.validation';
import { DuplicatedRequestIdsValidation } from 'file-validation/validations/duplicated-request-ids.validation';
import { DuplicatedRequirementsValidation } from 'file-validation/validations/duplicated-requirements.validation';
import { DuplicatedRowsValidation } from 'file-validation/validations/duplicated-rows.validation';
import { DuplicatedTitlesValidation } from 'file-validation/validations/duplicated-titles.validation';
import { EmptyRowValidation } from 'file-validation/validations/empty-row.validation';
import { InvalidDataValidation } from 'file-validation/validations/invalid-data.validation';
import { InvalidFieldSchemaValidation } from 'file-validation/validations/invalid-field-schema.validation';
import { InvalidFileValidationExtensionvalidation } from 'file-validation/validations/invalid-file-extension.validation';
import { InvalidFileSizeValidation } from 'file-validation/validations/invalid-file-size.validation';
import { MaxLengthExceededValidation } from 'file-validation/validations/max-length-exceeded.validation';
import { MaxRowsValidation } from 'file-validation/validations/max-rows.validation';
import { MissingColumnsValidation } from 'file-validation/validations/missing-columns.validation';
import { MissingRequiredValuesValidation } from 'file-validation/validations/missing-required-values.validation';
import { NoRecordsValidation } from 'file-validation/validations/no-records.validation';

export const FileValidationMap = new Map<FileValidationIssue, FileValidation>([
    [FileValidationIssue.INVALID_FILE_EXTENSION, new InvalidFileValidationExtensionvalidation()],
    [FileValidationIssue.INVALID_FILE_SIZE, new InvalidFileSizeValidation()],
    [FileValidationIssue.MISSING_COLUMNS, new MissingColumnsValidation()],
    [FileValidationIssue.MISSING_REQUIRED_VALUES, new MissingRequiredValuesValidation()],
    [FileValidationIssue.DUPLICATED_CODES, new DuplicatedCodesValidation()],
    [FileValidationIssue.INVALID_DATA, new InvalidDataValidation()],
    [FileValidationIssue.INVALID_FIELD_SCHEMA, new InvalidFieldSchemaValidation()],
    [FileValidationIssue.DUPLICATED_NAMES, new DuplicatedNamesValidation()],
    [FileValidationIssue.NO_RECORDS, new NoRecordsValidation()],
    [FileValidationIssue.EMPTY_ROW, new EmptyRowValidation()],
    [FileValidationIssue.DUPLICATED_REQUIREMENT, new DuplicatedRequirementsValidation()],
    [FileValidationIssue.CONTROL_NOT_ENABLED, new ControlNotEnabledValidation()],
    [FileValidationIssue.MAX_LENGTH_EXCEEDED, new MaxLengthExceededValidation()],
    [FileValidationIssue.DUPLICATED_REQUEST_IDS, new DuplicatedRequestIdsValidation()],
    [FileValidationIssue.DUPLICATED_TITLES, new DuplicatedTitlesValidation()],
    [FileValidationIssue.DUPLICATED_CUSTOMER_REQUESTS, new DuplicatedCustomerRequestsValidation()],
    [
        FileValidationIssue.DUPLICATED_CUSTOMER_REQUEST_TITLES,
        new DuplicatedCustomerRequestTitlesValidation(),
    ],
    [FileValidationIssue.MAX_ROWS_EXCEEDED, new MaxRowsValidation()],
    [FileValidationIssue.DUPLICATED_ROWS, new DuplicatedRowsValidation()],
]);
