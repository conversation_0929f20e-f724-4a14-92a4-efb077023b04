import { CustomerRequestFileHeader } from 'commons/enums/customer-request-file-header.enum';
import { FileValidationIssue } from 'commons/enums/file-validation/file-validation-issue.enum';
import { FileValidationScope } from 'commons/enums/file-validation/file-validation-scope.enum';
import { FileValidationStatus } from 'commons/enums/file-validation/file-validation-status.enum';
import { DuplicatedTitlesValidation } from 'file-validation/validations/duplicated-titles.validation';

describe('DuplicatedTitlesValidation', () => {
    let validation: DuplicatedTitlesValidation;

    beforeEach(() => {
        validation = new DuplicatedTitlesValidation();
    });

    describe('validate', () => {
        it('should return undefined when parsedData is empty', () => {
            const result = validation.validate([]);
            expect(result).toBeUndefined();
        });

        it('should return undefined when no duplicated titles are found', () => {
            const parsedData = [
                {
                    [CustomerRequestFileHeader.REQUEST_ID]: 'REQ-001',
                    [CustomerRequestFileHeader.TITLE]: 'Title 1',
                    [CustomerRequestFileHeader.DESCRIPTION]: 'Description 1',
                    [CustomerRequestFileHeader.CONTROLS]: 'CTRL-001',
                },
                {
                    [CustomerRequestFileHeader.REQUEST_ID]: 'REQ-002',
                    [CustomerRequestFileHeader.TITLE]: 'Title 2',
                    [CustomerRequestFileHeader.DESCRIPTION]: 'Description 2',
                    [CustomerRequestFileHeader.CONTROLS]: 'CTRL-002',
                },
            ];

            const result = validation.validate(parsedData);
            expect(result).toBeUndefined();
        });

        it('should return undefined when all titles are empty', () => {
            const parsedData = [
                {
                    [CustomerRequestFileHeader.REQUEST_ID]: 'REQ-001',
                    [CustomerRequestFileHeader.TITLE]: '',
                    [CustomerRequestFileHeader.DESCRIPTION]: 'Description 1',
                    [CustomerRequestFileHeader.CONTROLS]: 'CTRL-001',
                },
                {
                    [CustomerRequestFileHeader.REQUEST_ID]: 'REQ-002',
                    [CustomerRequestFileHeader.TITLE]: '',
                    [CustomerRequestFileHeader.DESCRIPTION]: 'Description 2',
                    [CustomerRequestFileHeader.CONTROLS]: 'CTRL-002',
                },
            ];

            const result = validation.validate(parsedData);
            expect(result).toBeUndefined();
        });

        it('should return undefined when some titles are empty and others are unique', () => {
            const parsedData = [
                {
                    [CustomerRequestFileHeader.REQUEST_ID]: 'REQ-001',
                    [CustomerRequestFileHeader.TITLE]: '',
                    [CustomerRequestFileHeader.DESCRIPTION]: 'Description 1',
                    [CustomerRequestFileHeader.CONTROLS]: 'CTRL-001',
                },
                {
                    [CustomerRequestFileHeader.REQUEST_ID]: 'REQ-002',
                    [CustomerRequestFileHeader.TITLE]: 'Unique Title',
                    [CustomerRequestFileHeader.DESCRIPTION]: 'Description 2',
                    [CustomerRequestFileHeader.CONTROLS]: 'CTRL-002',
                },
            ];

            const result = validation.validate(parsedData);
            expect(result).toBeUndefined();
        });

        it('should detect duplicated titles (case insensitive) and return ERROR status', () => {
            const parsedData = [
                {
                    [CustomerRequestFileHeader.REQUEST_ID]: 'REQ-001',
                    [CustomerRequestFileHeader.TITLE]: 'Duplicate Title',
                    [CustomerRequestFileHeader.DESCRIPTION]: 'Description 1',
                    [CustomerRequestFileHeader.CONTROLS]: 'CTRL-001',
                },
                {
                    [CustomerRequestFileHeader.REQUEST_ID]: 'REQ-002',
                    [CustomerRequestFileHeader.TITLE]: 'DUPLICATE TITLE',
                    [CustomerRequestFileHeader.DESCRIPTION]: 'Description 2',
                    [CustomerRequestFileHeader.CONTROLS]: 'CTRL-002',
                },
            ];

            const result = validation.validate(parsedData);

            expect(result).toBeDefined();
            expect(result.type).toBe(FileValidationIssue.DUPLICATED_TITLES);
            expect(result.status).toBe(FileValidationStatus.ERROR);
            expect(result.scope).toBe(FileValidationScope.ROW);
            expect(result.description).toBe(
                'There are duplicated titles. Make sure all values from the Title column are unique.',
            );
            expect(result.details).toHaveLength(1);
            expect(result.details[0]).toEqual({
                row: 3, // getRowNumber(1) returns 3 (1-based + header row)
                column: CustomerRequestFileHeader.TITLE,
                value: 'DUPLICATE TITLE',
                metadata: parsedData[1],
            });
        });

        it('should detect multiple duplicated titles', () => {
            const parsedData = [
                {
                    [CustomerRequestFileHeader.REQUEST_ID]: 'REQ-001',
                    [CustomerRequestFileHeader.TITLE]: 'Title A',
                    [CustomerRequestFileHeader.DESCRIPTION]: 'Description 1',
                    [CustomerRequestFileHeader.CONTROLS]: 'CTRL-001',
                },
                {
                    [CustomerRequestFileHeader.REQUEST_ID]: 'REQ-002',
                    [CustomerRequestFileHeader.TITLE]: 'Title B',
                    [CustomerRequestFileHeader.DESCRIPTION]: 'Description 2',
                    [CustomerRequestFileHeader.CONTROLS]: 'CTRL-002',
                },
                {
                    [CustomerRequestFileHeader.REQUEST_ID]: 'REQ-003',
                    [CustomerRequestFileHeader.TITLE]: 'title a', // duplicate of first
                    [CustomerRequestFileHeader.DESCRIPTION]: 'Description 3',
                    [CustomerRequestFileHeader.CONTROLS]: 'CTRL-003',
                },
                {
                    [CustomerRequestFileHeader.REQUEST_ID]: 'REQ-004',
                    [CustomerRequestFileHeader.TITLE]: 'TITLE B', // duplicate of second
                    [CustomerRequestFileHeader.DESCRIPTION]: 'Description 4',
                    [CustomerRequestFileHeader.CONTROLS]: 'CTRL-004',
                },
            ];

            const result = validation.validate(parsedData);

            expect(result).toBeDefined();
            expect(result.status).toBe(FileValidationStatus.ERROR);
            expect(result.details).toHaveLength(2);

            // First duplicate
            expect(result.details[0]).toEqual({
                row: 4, // getRowNumber(2) returns 4
                column: CustomerRequestFileHeader.TITLE,
                value: 'title a',
                metadata: parsedData[2],
            });

            // Second duplicate
            expect(result.details[1]).toEqual({
                row: 5, // getRowNumber(3) returns 5
                column: CustomerRequestFileHeader.TITLE,
                value: 'TITLE B',
                metadata: parsedData[3],
            });
        });

        it('should ignore empty titles when detecting duplicates', () => {
            const parsedData = [
                {
                    [CustomerRequestFileHeader.REQUEST_ID]: 'REQ-001',
                    [CustomerRequestFileHeader.TITLE]: 'Valid Title',
                    [CustomerRequestFileHeader.DESCRIPTION]: 'Description 1',
                    [CustomerRequestFileHeader.CONTROLS]: 'CTRL-001',
                },
                {
                    [CustomerRequestFileHeader.REQUEST_ID]: 'REQ-002',
                    [CustomerRequestFileHeader.TITLE]: '', // empty
                    [CustomerRequestFileHeader.DESCRIPTION]: 'Description 2',
                    [CustomerRequestFileHeader.CONTROLS]: 'CTRL-002',
                },
                {
                    [CustomerRequestFileHeader.REQUEST_ID]: 'REQ-003',
                    [CustomerRequestFileHeader.TITLE]: 'VALID TITLE', // duplicate
                    [CustomerRequestFileHeader.DESCRIPTION]: 'Description 3',
                    [CustomerRequestFileHeader.CONTROLS]: 'CTRL-003',
                },
                {
                    [CustomerRequestFileHeader.REQUEST_ID]: 'REQ-004',
                    [CustomerRequestFileHeader.TITLE]: '', // empty (should not be considered duplicate)
                    [CustomerRequestFileHeader.DESCRIPTION]: 'Description 4',
                    [CustomerRequestFileHeader.CONTROLS]: 'CTRL-004',
                },
            ];

            const result = validation.validate(parsedData);

            expect(result).toBeDefined();
            expect(result.status).toBe(FileValidationStatus.ERROR);
            expect(result.details).toHaveLength(1);
            expect(result.details[0]).toEqual({
                row: 4, // getRowNumber(2) returns 4
                column: CustomerRequestFileHeader.TITLE,
                value: 'VALID TITLE',
                metadata: parsedData[2],
            });
        });

        it('should return undefined when titles contain only whitespace', () => {
            const parsedData = [
                {
                    [CustomerRequestFileHeader.REQUEST_ID]: 'REQ-001',
                    [CustomerRequestFileHeader.TITLE]: '   ', // whitespace only
                    [CustomerRequestFileHeader.DESCRIPTION]: 'Description 1',
                    [CustomerRequestFileHeader.CONTROLS]: 'CTRL-001',
                },
                {
                    [CustomerRequestFileHeader.REQUEST_ID]: 'REQ-002',
                    [CustomerRequestFileHeader.TITLE]: '\t\n', // whitespace only
                    [CustomerRequestFileHeader.DESCRIPTION]: 'Description 2',
                    [CustomerRequestFileHeader.CONTROLS]: 'CTRL-002',
                },
            ];

            const result = validation.validate(parsedData);
            expect(result).toBeUndefined();
        });
    });
});
