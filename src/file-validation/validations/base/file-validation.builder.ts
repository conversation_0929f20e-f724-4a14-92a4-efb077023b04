import { CustomerRequest } from 'app/customer-request/entities/customer-request.entity';
import { Requirement } from 'app/frameworks/entities/requirement.entity';
import { Control } from 'app/grc/entities/control.entity';
import { FileValidationIssue } from 'commons/enums/file-validation/file-validation-issue.enum';

export class FileValidationBuilder {
    constructor(private validations = new Map<FileValidationIssue, unknown[]>()) {}

    /**
     * Set invalid file extension validation
     * @param mimeType File mime type
     * @param validMimeTypes List of valid mime types
     * @returns Builder
     */
    setInvalidFileExtensionValidation(
        mimeType: string,
        validMimeTypes: string[],
    ): FileValidationBuilder {
        this.validations.set(FileValidationIssue.INVALID_FILE_EXTENSION, [
            mimeType,
            validMimeTypes,
        ]);
        return this;
    }

    /**
     * Set invalid file size validation
     * @param fileSize File size
     * @param maxFileSize Max file size
     * @returns Builder
     */
    setInvalidFileSizeValidation(fileSize: number, maxFileSize: number): FileValidationBuilder {
        this.validations.set(FileValidationIssue.INVALID_FILE_SIZE, [fileSize, maxFileSize]);
        return this;
    }

    /**
     * Set missing columns validation
     * @param requiredHeaders List of required headers
     * @returns Builder
     */
    setMissingColumnsValidation(requiredHeaders: string[], isStrict = true): FileValidationBuilder {
        this.validations.set(FileValidationIssue.MISSING_COLUMNS, [requiredHeaders, isStrict]);
        return this;
    }

    /**
     * Set missing required values validation
     * @param requiredHeaders List of required headers
     * @returns Builder
     */
    setMissingRequiredValuesValidation(requiredHeaders: string[]): FileValidationBuilder {
        this.validations.set(FileValidationIssue.MISSING_REQUIRED_VALUES, [requiredHeaders]);
        return this;
    }

    /**
     * Set duplicated codes validation
     * @returns Builder
     */
    setDuplicatedCodesValidation(): FileValidationBuilder {
        this.validations.set(FileValidationIssue.DUPLICATED_CODES, []);
        return this;
    }

    /**
     * Set invalid data validation
     * @param validationRegex Regex to look for invalid data
     * @returns Builder
     */
    setInvalidDataValidation(validationRegex: RegExp[]): FileValidationBuilder {
        this.validations.set(FileValidationIssue.INVALID_DATA, [validationRegex]);
        return this;
    }

    /**
     * Set invalid field schema validation
     * @param fieldSchemaMap Map containing field schema values by column
     * @returns Builder
     */
    setInvalidFieldSchemaValidation(
        fieldSchemaMap: Map<string, (value: unknown | string) => boolean>,
        requiredHeaders?: string[],
    ): FileValidationBuilder {
        this.validations.set(FileValidationIssue.INVALID_FIELD_SCHEMA, [
            fieldSchemaMap,
            requiredHeaders,
        ]);
        return this;
    }

    /**
     * Set duplicated names validation
     * @returns Builder
     */
    setDuplicatedNamesValidation(): FileValidationBuilder {
        this.validations.set(FileValidationIssue.DUPLICATED_NAMES, []);
        return this;
    }

    /**
     * Set no records validation
     * @returns Builder
     */
    setNoRecordsValidation(): FileValidationBuilder {
        this.validations.set(FileValidationIssue.NO_RECORDS, []);
        return this;
    }

    /**
     * Set empty row validation
     * @returns Builder
     */
    setEmptyRowValidation(): FileValidationBuilder {
        this.validations.set(FileValidationIssue.EMPTY_ROW, []);
        return this;
    }

    /**
     * Set duplicated requirements validation
     * @param requirements List of requirements
     * @returns Builder
     */
    setDuplicatedRequirementValidation(requirements: Requirement[]): FileValidationBuilder {
        this.validations.set(FileValidationIssue.DUPLICATED_REQUIREMENT, [requirements]);
        return this;
    }

    /**
     * Set control not enabled validation
     * @param controls List of controls
     * @param separatorsRegex Regex to determine separators
     * @returns Builder
     */
    setControlNotEnabledValidation(
        controls: Control[],
        separatorsRegex: RegExp,
    ): FileValidationBuilder {
        this.validations.set(FileValidationIssue.CONTROL_NOT_ENABLED, [controls, separatorsRegex]);
        return this;
    }

    /**
     * Set max length exceeded validation
     * @param maxLengthMap Map containing max length values by column
     * @returns Builder
     */
    setMaxLengthExceededValidation(maxLengthMap: Map<string, number>): FileValidationBuilder {
        this.validations.set(FileValidationIssue.MAX_LENGTH_EXCEEDED, [maxLengthMap]);
        return this;
    }

    /**
     * Set duplicated request IDs validation
     * @returns Builder
     */
    setDuplicatedRequestIdsValidation(): FileValidationBuilder {
        this.validations.set(FileValidationIssue.DUPLICATED_REQUEST_IDS, []);
        return this;
    }

    /**
     * Set duplicated titles
     * @returns Builder
     */
    setDuplicatedTitles(): FileValidationBuilder {
        this.validations.set(FileValidationIssue.DUPLICATED_TITLES, []);
        return this;
    }

    /**
     * Set duplicated customer requests validation
     * @param customerRequests List of customer requests
     * @returns Builder
     */
    setDuplicatedCustomerRequestsValidation(
        customerRequests: CustomerRequest[],
    ): FileValidationBuilder {
        this.validations.set(FileValidationIssue.DUPLICATED_CUSTOMER_REQUESTS, [customerRequests]);
        return this;
    }

    /**
     * Set duplicated customer request titles validation
     * @param customerRequests List of customer requests
     * @returns FileValidationBuilder
     */
    setDuplicatedCustomerRequestTitlesValidation(
        customerRequests: CustomerRequest[],
    ): FileValidationBuilder {
        this.validations.set(FileValidationIssue.DUPLICATED_CUSTOMER_REQUEST_TITLES, [
            customerRequests,
        ]);
        return this;
    }

    /**
     * Set max rows exceeded validation
     * @param maxRows Validate maximum rows
     * @returns Builder
     */
    setMaxRowsExceededValidation(maxRows: number): FileValidationBuilder {
        this.validations.set(FileValidationIssue.MAX_ROWS_EXCEEDED, [maxRows]);
        return this;
    }

    /**
     * Set duplicated rows validation
     * @returns Builder
     */
    setDuplicatedRowsValidation(): FileValidationBuilder {
        this.validations.set(FileValidationIssue.DUPLICATED_ROWS, []);
        return this;
    }

    /**
     * Returns the map that will be used to validate file content
     * @returns File validations map
     */
    build(): Map<FileValidationIssue, unknown[]> {
        return this.validations;
    }
}
