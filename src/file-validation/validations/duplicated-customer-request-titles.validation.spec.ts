import { CustomerRequest } from 'app/customer-request/entities/customer-request.entity';
import { CustomerRequestFileHeader } from 'commons/enums/customer-request-file-header.enum';
import { FileValidationIssue } from 'commons/enums/file-validation/file-validation-issue.enum';
import { FileValidationScope } from 'commons/enums/file-validation/file-validation-scope.enum';
import { FileValidationStatus } from 'commons/enums/file-validation/file-validation-status.enum';
import { DuplicatedCustomerRequestTitlesValidation } from 'file-validation/validations/duplicated-customer-request-titles.validation';

describe('DuplicatedCustomerRequestTitlesValidation', () => {
    let validation: DuplicatedCustomerRequestTitlesValidation;

    beforeEach(() => {
        validation = new DuplicatedCustomerRequestTitlesValidation();
    });

    describe('validate', () => {
        it('should return undefined when parsedData is empty', () => {
            const customerRequests = [
                { id: 1, title: 'Existing Title' } as CustomerRequest,
            ];
            const result = validation.validate([], customerRequests);
            expect(result).toBeUndefined();
        });

        it('should return undefined when customerRequests is empty', () => {
            const parsedData = [
                {
                    [CustomerRequestFileHeader.REQUEST_ID]: 'REQ-001',
                    [CustomerRequestFileHeader.TITLE]: 'New Title',
                    [CustomerRequestFileHeader.DESCRIPTION]: 'Description 1',
                    [CustomerRequestFileHeader.CONTROLS]: 'CTRL-001',
                },
            ];
            const result = validation.validate(parsedData, []);
            expect(result).toBeUndefined();
        });

        it('should return undefined when no duplicated titles are found', () => {
            const parsedData = [
                {
                    [CustomerRequestFileHeader.REQUEST_ID]: 'REQ-001',
                    [CustomerRequestFileHeader.TITLE]: 'New Title 1',
                    [CustomerRequestFileHeader.DESCRIPTION]: 'Description 1',
                    [CustomerRequestFileHeader.CONTROLS]: 'CTRL-001',
                },
                {
                    [CustomerRequestFileHeader.REQUEST_ID]: 'REQ-002',
                    [CustomerRequestFileHeader.TITLE]: 'New Title 2',
                    [CustomerRequestFileHeader.DESCRIPTION]: 'Description 2',
                    [CustomerRequestFileHeader.CONTROLS]: 'CTRL-002',
                },
            ];

            const customerRequests = [
                { id: 1, title: 'Existing Title 1' } as CustomerRequest,
                { id: 2, title: 'Existing Title 2' } as CustomerRequest,
            ];

            const result = validation.validate(parsedData, customerRequests);
            expect(result).toBeUndefined();
        });

        it('should detect duplicated title (case insensitive) and return ERROR status', () => {
            const parsedData = [
                {
                    [CustomerRequestFileHeader.REQUEST_ID]: 'REQ-001',
                    [CustomerRequestFileHeader.TITLE]: 'EXISTING TITLE',
                    [CustomerRequestFileHeader.DESCRIPTION]: 'Description 1',
                    [CustomerRequestFileHeader.CONTROLS]: 'CTRL-001',
                },
            ];

            const customerRequests = [
                { id: 1, title: 'existing title' } as CustomerRequest,
            ];

            const result = validation.validate(parsedData, customerRequests);

            expect(result).toBeDefined();
            expect(result.type).toBe(FileValidationIssue.DUPLICATED_CUSTOMER_REQUEST_TITLES);
            expect(result.status).toBe(FileValidationStatus.ERROR);
            expect(result.scope).toBe(FileValidationScope.ROW);
            expect(result.description).toBe('The customer request title already exists.');
            expect(result.details).toHaveLength(1);
            expect(result.details[0]).toEqual({
                row: 2, // getRowNumber(0) returns 2 (1-based + header row)
                column: CustomerRequestFileHeader.TITLE,
                value: 'EXISTING TITLE',
                metadata: parsedData[0],
            });
        });

        it('should detect multiple duplicated titles', () => {
            const parsedData = [
                {
                    [CustomerRequestFileHeader.REQUEST_ID]: 'REQ-001',
                    [CustomerRequestFileHeader.TITLE]: 'Title A',
                    [CustomerRequestFileHeader.DESCRIPTION]: 'Description 1',
                    [CustomerRequestFileHeader.CONTROLS]: 'CTRL-001',
                },
                {
                    [CustomerRequestFileHeader.REQUEST_ID]: 'REQ-002',
                    [CustomerRequestFileHeader.TITLE]: 'Title B',
                    [CustomerRequestFileHeader.DESCRIPTION]: 'Description 2',
                    [CustomerRequestFileHeader.CONTROLS]: 'CTRL-002',
                },
                {
                    [CustomerRequestFileHeader.REQUEST_ID]: 'REQ-003',
                    [CustomerRequestFileHeader.TITLE]: 'title c', // duplicate
                    [CustomerRequestFileHeader.DESCRIPTION]: 'Description 3',
                    [CustomerRequestFileHeader.CONTROLS]: 'CTRL-003',
                },
            ];

            const customerRequests = [
                { id: 1, title: 'TITLE A' } as CustomerRequest,
                { id: 2, title: 'Title C' } as CustomerRequest,
            ];

            const result = validation.validate(parsedData, customerRequests);

            expect(result).toBeDefined();
            expect(result.status).toBe(FileValidationStatus.ERROR);
            expect(result.details).toHaveLength(2);

            // First duplicate
            expect(result.details[0]).toEqual({
                row: 2, // getRowNumber(0) returns 2
                column: CustomerRequestFileHeader.TITLE,
                value: 'Title A',
                metadata: parsedData[0],
            });

            // Second duplicate
            expect(result.details[1]).toEqual({
                row: 4, // getRowNumber(2) returns 4
                column: CustomerRequestFileHeader.TITLE,
                value: 'title c',
                metadata: parsedData[2],
            });
        });

        it('should only detect titles that match existing customer requests', () => {
            const parsedData = [
                {
                    [CustomerRequestFileHeader.REQUEST_ID]: 'REQ-001',
                    [CustomerRequestFileHeader.TITLE]: 'Existing Title',
                    [CustomerRequestFileHeader.DESCRIPTION]: 'Description 1',
                    [CustomerRequestFileHeader.CONTROLS]: 'CTRL-001',
                },
                {
                    [CustomerRequestFileHeader.REQUEST_ID]: 'REQ-002',
                    [CustomerRequestFileHeader.TITLE]: 'New Title',
                    [CustomerRequestFileHeader.DESCRIPTION]: 'Description 2',
                    [CustomerRequestFileHeader.CONTROLS]: 'CTRL-002',
                },
                {
                    [CustomerRequestFileHeader.REQUEST_ID]: 'REQ-003',
                    [CustomerRequestFileHeader.TITLE]: 'Another Existing',
                    [CustomerRequestFileHeader.DESCRIPTION]: 'Description 3',
                    [CustomerRequestFileHeader.CONTROLS]: 'CTRL-003',
                },
            ];

            const customerRequests = [
                { id: 1, title: 'existing title' } as CustomerRequest,
                { id: 2, title: 'another existing' } as CustomerRequest,
            ];

            const result = validation.validate(parsedData, customerRequests);

            expect(result).toBeDefined();
            expect(result.status).toBe(FileValidationStatus.ERROR);
            expect(result.details).toHaveLength(2);
            expect(result.details[0].value).toBe('Existing Title');
            expect(result.details[1].value).toBe('Another Existing');
        });

        it('should handle empty titles gracefully', () => {
            const parsedData = [
                {
                    [CustomerRequestFileHeader.REQUEST_ID]: 'REQ-001',
                    [CustomerRequestFileHeader.TITLE]: '',
                    [CustomerRequestFileHeader.DESCRIPTION]: 'Description 1',
                    [CustomerRequestFileHeader.CONTROLS]: 'CTRL-001',
                },
                {
                    [CustomerRequestFileHeader.REQUEST_ID]: 'REQ-002',
                    [CustomerRequestFileHeader.TITLE]: 'Existing Title',
                    [CustomerRequestFileHeader.DESCRIPTION]: 'Description 2',
                    [CustomerRequestFileHeader.CONTROLS]: 'CTRL-002',
                },
            ];

            const customerRequests = [
                { id: 1, title: '' } as CustomerRequest,
                { id: 2, title: 'existing title' } as CustomerRequest,
            ];

            const result = validation.validate(parsedData, customerRequests);

            expect(result).toBeDefined();
            expect(result.details).toHaveLength(2);
        });

        it('should handle null and undefined titles', () => {
            const parsedData = [
                {
                    [CustomerRequestFileHeader.REQUEST_ID]: 'REQ-001',
                    [CustomerRequestFileHeader.TITLE]: null,
                    [CustomerRequestFileHeader.DESCRIPTION]: 'Description 1',
                    [CustomerRequestFileHeader.CONTROLS]: 'CTRL-001',
                },
                {
                    [CustomerRequestFileHeader.REQUEST_ID]: 'REQ-002',
                    [CustomerRequestFileHeader.TITLE]: undefined,
                    [CustomerRequestFileHeader.DESCRIPTION]: 'Description 2',
                    [CustomerRequestFileHeader.CONTROLS]: 'CTRL-002',
                },
            ];

            const customerRequests = [
                { id: 1, title: 'Some Title' } as CustomerRequest,
            ];

            const result = validation.validate(parsedData, customerRequests);

            // Should not throw and should not find matches for null/undefined
            expect(result).toBeUndefined();
        });
    });
});

