import { CustomerRequest } from 'app/customer-request/entities/customer-request.entity';
import { CustomerRequestFileHeader } from 'commons/enums/customer-request-file-header.enum';
import { FileValidationIssue } from 'commons/enums/file-validation/file-validation-issue.enum';
import { compareLowerCase } from 'commons/helpers/string.helper';
import { FileValidationDescription } from 'file-validation/configs/file-validation-descriptions.config';
import { getRowNumber } from 'file-validation/helpers/file-validation.helper';
import { FileValidationIssueType } from 'file-validation/types/file-validation-issue.type';
import { FileValidation } from 'file-validation/validations/base/file-validation';
import { isEmpty, isNil } from 'lodash';

export class DuplicatedCustomerRequestTitlesValidation extends FileValidation {
    type = FileValidationIssue.DUPLICATED_CUSTOMER_REQUEST_TITLES;

    /**
     * Validate if the customer request titles already exist
     * @param parsedData Parsed data from file
     * @param customerRequests List of customer requests
     * @returns Issue data (only if detected)
     */
    validate<T>(
        parsedData: T[],
        customerRequests: CustomerRequest[],
    ): FileValidationIssueType | undefined {
        if (!isEmpty(parsedData) && !isEmpty(customerRequests)) {
            const issue: FileValidationIssueType = {
                description: FileValidationDescription[this.type],
                type: this.type,
                status: this.getStatus(),
                scope: this.getScope(),
                details: [],
            };

            parsedData.forEach((item: T, index: number) => {
                const existingRequest = customerRequests.find(request =>
                    compareLowerCase(request.title, item[CustomerRequestFileHeader.TITLE]),
                );

                if (!isNil(existingRequest)) {
                    issue.details.push({
                        row: getRowNumber(index),
                        column: CustomerRequestFileHeader.TITLE,
                        value: item[CustomerRequestFileHeader.TITLE],
                        metadata: item,
                    });
                }
            });

            if (!isEmpty(issue.details)) {
                return issue;
            }
        }
    }
}
