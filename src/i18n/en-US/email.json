{"standardTemplate": {"buttonNotWorking": "Button not working? Paste the following link into your browser:", "footerText": "This email contains a secure link to <PERSON><PERSON>. Please do not forward this email or link to others. If you need help, please email", "footerCtaOne": "LOGIN", "footerCtaTwo": "HELP CENTER"}, "mainV2Template": {"buttonNotWorking": "Button not working? Paste the following link into your browser:", "footerText": "If you need assistance, please email ", "footerCtaOne": "SIGN IN", "footerCtaTwo": "HELP CENTER", "footerCtaThree": "EMAIL NOTIFICATION PREFERENCES"}, "mainV3Template": {"snippet": "Trust, Automated.", "copyright": "Drata Inc. © {currentYear} All rights reserved.", "footerText": "This email contains a secure link to <PERSON><PERSON>. Please do not forward this email or link to others. If you need help, please email"}, "upcomingTasksTemplate": {"subject": "Upcoming tasks for %s", "title": "Tasks", "message": "Since you have tasks coming up in %s, now is the perfect time to review them. Go to Drata to prioritize your work and set yourself up for success. You've got this!", "ctaText": "View Tasks", "ctaUrl": "/tasks?ownerId=%s"}, "annualComplianceRenewalTasksTemplate": {"footerText": "If you need assistance, please email ", "footerCtaOne": "SIGN IN", "footerCtaTwo": "HELP CENTER", "footerCtaThree": "EMAIL NOTIFICATION PREFERENCES"}, "trustCenterTemplate": {"header": "Trust Center", "subHeader": "Access Request", "signNDA": "Sign NDA", "ctaNDA": "Here is the NDA", "textNDA": "for this request.", "downloadTitle": "Requested Documents", "downloadAction": "Download requested documents", "buttonNotWorking": "Button not working? Paste the following link into your browser:", "reviewUrlText": "View Request", "footerText": "If you need assistance, please", "footerCtaOne": "contact %s support", "footerTrustCenter": "%s Trust Center", "poweredBy": "Powered by"}, "controlStatusTemplate": {"headerOne": "Security Controls", "headerTwo": "Status Update", "subHeaderOne": "Control Test Failures", "subHeaderTwo": "Test failed at", "subHeaderThree": "Controls Without Evidence", "subHeaderFour": "Control Tasks", "salutation": "Dear %s,", "paragraphOne": "Below you'll find the latest notifications and reminders regarding %s's security controls", "paragraphTwo": "assigned to you within Drata. You can update the frequency in which you receive these notifications anytime in", "paragraphThree": "Remediate these failures ASAP to maintain the operating effectiveness of your controls and ensure a successful future audit:", "paragraphFour": "It's all systems go! All tests are passing for the controls you own.", "paragraphFive": "Add evidence of these controls assigned to you or mark them out of scope:", "paragraphSix": "It's all systems go! There's evidence linked to each control you own.", "paragraphSeven": "It's all systems go! There are no control tasks that require your attention.", "paragraphEight": "Complete these assigned tasks ASAP to maintain the operating effectiveness of your controls and ensure a successful future audit:", "subHeaderNine": "Drata recognized separated hires, requiring you to perform the offboarding checklist", "forText": "for", "remainingFailedTestsText": "control tests.", "noRemainingFailedTestsText": "No more control test failures.", "noEvidenceText": "No Evidence", "noRemainingEvidenceText": "No more controls without evidence.", "remainingEvidenceText": "controls without evidence.", "separatedOnText": "Separated on", "moreText": "more", "moreFailedText": "more failed", "assignedTasksText": "assigned tasks.", "yourSettingsCta": "your settings", "viewAllCta": "View all", "footerText": "If you need assistance, please email ", "footerCtaOne": "SIGN IN", "footerCtaTwo": "HELP CENTER", "footerCtaThree": "EMAIL NOTIFICATION PREFERENCES"}, "controlTestErrorTemplate": {"headerOne": "Automated Test", "headerTwo": "Errors", "salutation:": "Dear %s,", "paragraphOne": "We found a problem that's preventing at least one of your automated tests from collecting evidence.", "paragraphTwo": "Don't worry, we've included helpful info about each error for you in the 'Last Test Result' section of each test.", "listParagraph": "Automated tests with errors:", "footerTextOne": "Email", "footerMailCta": "Customer Support", "footerTextTwo": "at any time. We're here to help.", "footerCtaOne": "SIGN IN", "footerCtaTwo": "HELP CENTER", "footerCtaThree": "EMAIL NOTIFICATION PREFERENCES"}, "welcomeEmailFromSiteAdmin": {"subject": "Welcome to Drata", "snippet": "Get Started Today!", "title": "Welcome to Drata", "message": "Click the Get Started button below to setup your new Drata account.", "subMessage": "After creating your account you can easily get started with just a few steps:<br><br><ul><li>Start by connecting your identity provider such as Google Workspace (Formerly G Suite) or Microsoft 365.</li><li>Next, you'll want to connect an HRIS system like Gusto to sync your personnel.</li><li>Finally, you can start editing your company's information, uploading policies, and adding any additional integrations you have.</li></ul>", "ctaText": "Get Started"}, "magicLinkEmail": {"subject": "Sign into Drata", "snippet": "Trust, Automated.", "title": "Hi, %s", "paragraphOne": "Here's your secure link to sign into Drata. This link expires in one hour and can only be used once.", "paragraphTwo": "Sign in to automate your GRC journey.", "btnText": "Sign in", "buttonNotWorking": "Button not working? Paste the following link into your browser:"}, "magicLinkEmailAuditorInvite": {"subject": "Auditor invitation to %s", "snippet": "Trust, Automated.", "title": "Hello, %s", "paragraphOne": "You have been added as an auditor to %s. This button is a secure, one-time use button. For all future logins to Drata, you will enter your email address at <a href=\"https://app.drata.com/\" target=\"_blank\" class=\"em_font_16\" style=\"text-decoration: underline; font-size: 16px; color: black;\" >app.drata.com/auditor</a>", "paragraphBelowCtaBtn": "From within Drata you can:<br><br><ul style=\"padding-left: 20px; list-style-position: outside;\"><li style=\"font-size: 16px; line-height: 24px; padding-left: 10px;\">View %s’s pre-audit package</li><li style=\"font-size: 16px; line-height: 24px; padding-left: 10px;\">Download the package containing reports and documents on assets, vendors, policies, integrations, and personnel information.</li></ul>", "btnText": "Accept Invitation", "buttonNotWorking": "Button not working? Paste the following link into your browser:"}, "welcomeEmailServiceProviderInvite": {"subject": "Service Provider Invitation to %s's Dr<PERSON> Account", "snippet": "Service Provider access to manage different Drata accounts", "title": "Hello", "message": "You've been invited to be a Guest administrator on %s's Drata account.<br>Accepting the invite will take you to your account list in the application.<br><br>Here's how it works:<br><ul><li style=\"font-size: 16px; line-height: 24px;\">View any account that's granted you access.</li><li style=\"font-size: 16px; line-height: 24px;\">Switch between different accounts by going back to your account list.</li></ul>", "subMessage": "This is a secure, one-time use button. For future visits, sign in by going to the &nbsp;<a href=\"https://app.drata.com/\" target=\"_blank\" class=\"em_font_12\" style=\"font-size: 12px\" >https://app.drata.com/</a> and entering your email.", "ctaText": "Accept Invitation"}, "incomplianceReminderEmail": {"subject": "[Drata] You have actions to complete for security compliance", "snippet": "Complete these actions today on Drata", "title": "Hi %s", "message": "You have actions to complete in Drata. Please complete them as soon as possible to help %s meet our security and compliance requirements. Thank You!", "ctaText": "Review"}, "wysiwygCommentReplyEmail": {"subject": "[Dr<PERSON>] New security policy comment", "snippet": "Review the security policy on <PERSON><PERSON>.", "title": "Hi %s", "message": "%s (%s) replied to your comment on a security policy on Drata.", "ctaText": "View Policy"}, "policyVersionDeletedEmail": {"subject": "%s draft was deleted", "snippet": "%s draft was deleted", "title": "Policy Draft Deleted", "salutation": "Dear %s,", "message": "A draft for <strong>%s</strong> has been deleted by %s. Please sign in to Drata to review.", "ctaText": "Go to Policy Center"}, "policyVersionUpdatedEmail": {"subject": "Update to your assigned policy", "snippet": "Update to your assigned policy", "title": "Policy Updated", "salutation": "Dear %s,", "message": "The <strong>%s</strong> has been updated by %s. Please sign in to Drata to review the updates.", "ctaText": "View Policy"}, "policyVersionNeedsApprovalEmail": {"subject": "Policy needs approval %s v%s", "snippet": "Policy needs approval", "title": "Policy Needs Approval", "salutation": "Dear %s,", "message": "A draft was finalized for <strong>%s</strong> by %s and it needs your approval. Please sign in to Drata to review.", "messageExplanationOfChanges": "<br><br>Explanation of changes - %s", "ctaText": "View Policy"}, "policyVersionNeedsApprovalEmailV2": {"subject": "Policy needs approval %s v%s", "snippet": "Policy needs approval", "title": "Review and Approve %s v%s", "paragraphOne": "Dear %s,<br>A draft was finalized for %s v%s by %s and it needs your approval. Please sign in to Drata to review.", "paragraphTwo": "<strong>Policy owner:</strong> %s<br><strong>Explanation of changes:</strong> %s<br><strong>Approval deadline: </strong> %s", "btnText": "View Policy"}, "policyVersionNeedsApprovalEmailV3": {"subject": "Policy needs approval %s v%s", "snippet": "Policy needs approval", "title": "Review and Approve %s v%s", "paragraphOne": "Dear %s,<br>A draft was finalized for %s v%s by %s and it needs your approval. Please sign in to Drata to review.", "paragraphTwo": "<strong>Policy owner:</strong> %s<br><strong>Approval deadline: </strong> %s", "btnText": "View Policy"}, "securityAwarenessEmail": {"subject": "[Drata] Security Awareness Training Completed", "snippet": "Your Security Awareness Training Certificate.", "title": "Great job %s", "message": "You've completed your annual security awareness training within Drata and are now better equipped to help protect %s against potential threats and risk.<br><br>Please keep this email for your records, but also know that Drata is automatically storing your completion certificate on your behalf.<br><br>Have a great day!<br><br>Since<PERSON>y,<br>Your Drata Team"}, "hipaaTrainingEmail": {"subject": "[Drata] HIPAA Training Completed", "snippet": "Your HIPAA Training Certificate.", "title": "Great job %s", "message": "You've completed your annual HIPAA Training within Drata and are now better equipped to help protect %s against potential threats and risk.<br><br>Please keep this email for your records, but also know that Drata is automatically storing your completion certificate on your behalf.<br><br>Have a great day!<br><br>Since<PERSON>y,<br>Your Drata Team"}, "nistAiTrainingEmail": {"subject": "[Drata] AI Awareness Training Completed", "snippet": "Your AI Awareness Training Certificate.", "title": "Great job %s", "message": "You've completed your annual AI Awareness Training within Drata and are now better equipped to help protect %s against potential threats and risk.<br><br>Please keep this email for your records, but also know that Drata is automatically storing your completion certificate on your behalf.<br><br>Have a great day!<br><br>Sincerely,<br>Your Drata Team"}, "acceptNewPolicyEmail": {"subject": "Policy acknowledgment required", "title": "Policy acknowledgment required", "message": "%s needs you to acknowledge a policy: <strong>%s</strong>. Please sign in to Drata to review and acknowledge it as soon as possible.", "ctaText": "Review policy"}, "acceptPolicyVersionEmail": {"subject": "Policy acknowledgment required", "title": "Policy acknowledgment required", "message": "%s needs you to acknowledge a policy: <strong>%s</strong>. Please sign in to Drata to review and acknowledge it as soon as possible.", "ctaText": "Review policy", "changesTitle": "<br><br>Here's what changed:<br><br>%s"}, "versionControlPermissionsCheck": {"subject": "[Drata] Bitbucket Integration Permissions Issue", "snippet": "You need to be an ADMIN on all Workspaces and Repositories.", "title": "Hi %s,", "message": "The account that was used to link Bitbucket to Drata for %s does not have ADMIN permission on all Workspaces and Repositories within its scope. Drata requires that the account has ADMIN access to all Workspaces and Repositories that it's attached to.", "ctaText": "View Connections", "list": "<br><br>The user %s does not have admin access to the following %s:<br><ul>%s</ul>", "listItem": "<li>%s - Current access level: %s</li>"}, "csvIdPErrorEmail": {"subject": "[Drata] CSV IdP Upload Issues", "snippet": "Please check the format of your CSV file.", "title": "Hi %s,", "message": "There was an issue processing the CSV file: %s", "ctaText": "View Connections", "list": "<br><br>The CSV has the following invalid fields:<br><br><table>%s</table>", "listItem": "<tr><td>Row: %s<br>Column: %s<br>Value: %s<br><br></td></tr>"}, "addedAsAdminOrTechGovEmail": {"subject": "You've been added as %s Team Member for %s", "snippet": "You are now %s Team Member for %s's Drata platform", "title": "Hi %s,", "message": "You've been added to Drata as %s Team Member for %s. With this role within Drata, you are now able to engage in security and compliance activities for %s. You don't need to take any immediate action, but may login to Drata at anytime using your email address here:<br><br><a href=\"%s\" target=\"_blank\" class=\"em_font_12\" style=\"font-size: 12px\" >%s</a><br><br>If you have any questions or need assistance with your account, <NAME_EMAIL>."}, "addedAsRiskManagerEmail": {"subject": "You've been added as a Risk Manager for %s", "snippet": "You are now a Risk Manager Team Member for %s's Drata platform", "title": "Hi %s,", "message": "You've been added as a Risk Manager for %s in Drata, in addition to any other roles you currently have. Now you can use a suite of risk management tools that let you build a risk register, score risks, assign risk owners, and complete other tasks related to threat-based risks. No immediate action is required. Sign in to Drata to view your new risk management tools:<br><a href=\"%s\" target=\"_blank\" class=\"em_font_12\" style=\"font-size: 12px\" >%s</a><br><br>If you have any questions or need assistance with your account, <NAME_EMAIL>."}, "addedAsAdminEmail": {"subject": "You've been added as an Admin for %s", "snippet": "You are now an Admin Team Member for %s's Drata platform", "title": "Hi %s,", "message": "You've been added as an Admin for %s in Drata, in addition to any other roles you currently have. This role gives you unrestricted access to the entire application. No immediate action is required. Sign in to Drata anytime using your email address here:<br><a href=\"%s\" target=\"_blank\" class=\"em_font_12\" style=\"font-size: 12px\" >%s</a><br><br>If you have any questions or need assistance with your account, <NAME_EMAIL>."}, "addedAsGuestAdminEmail": {"subject": "You've been added as a Guest administrator for %s", "snippet": "You are now a Guest administrator for %s's Drata platform", "title": "Hi %s,", "message": "You've been added as a Guest administrator for %s in Drata. As a Guest administrator, you can access and help manage accounts you've been invited to. No immediate action is required. Sign in to Drata anytime using your email address here:<br><a href=\"%s\" target=\"_blank\" class=\"em_font_12\" style=\"font-size: 12px\" >%s</a><br><br>If you have any questions or need assistance with your account, <NAME_EMAIL>."}, "addedAsInformationSecurityLeadEmail": {"subject": "You've been added as an Information Security Lead for %s", "snippet": "You are now a Information Security Lead Team Member for %s's Drata platform", "title": "Hi %s,", "message": "Your new information security lead role gives you broad access to Drata that lets you complete daily tasks, troubleshoot, and oversee compliance operations within the application. You’ll have access to your new abilities the next time you sign in.<br><br><a href=\"%s\" target=\"_blank\" >Sign in to Drata</a>"}, "addedAsControlManagerEmail": {"subject": "You've been added as a Control Manager for %s", "snippet": "You are now a Control Manager Team Member for %s's Drata platform", "title": "Hi %s,", "message": "You've been added as a Control Manager for %s in Drata, in addition to any other roles you currently have. This role lets you review controls and related evidence to make sure they're ready for compliance goals. No immediate action is required. Sign in to Drata anytime using your email address here:<br><a href=\"%s\" target=\"_blank\" class=\"em_font_12\" style=\"font-size: 12px\" >%s</a><br><br>If you have any questions or need assistance with your account, <NAME_EMAIL>."}, "addedAsPersonnelComplianceManagerEmail": {"subject": "You've been added as Personnel Compliance Manager for %s", "snippet": "You are now a Personnel Compliance Manager Team Member for %s's Drata platform", "title": "Hi %s,", "message": "You've been added as Personnel Compliance Manager for %s in Drata, in addition to any other roles you currently have. This role lets you access background checks, edit employee status, and general personnel compliance. No immediate action is required. Sign in to Drata anytime using your email address here:<br><a href=\"%s\" target=\"_blank\" class=\"em_font_12\" style=\"font-size: 12px\" >%s</a><br><br>If you have any questions or need assistance with your account, <NAME_EMAIL>."}, "addedAsPolicyManagerEmail": {"subject": "You've been added as Policy Manager for %s", "snippet": "You are now a Policy Manager Team Member for %s's Drata platform", "title": "Hi %s,", "message": "You've been added as Policy Manager for %s in Drata, in addition to any other roles you currently have. This role lets you create and maintain policies throughout their lifecycles. No immediate action is required. Sign in to Drata anytime using your email address here:<br><a href=\"%s\" target=\"_blank\" class=\"em_font_12\" style=\"font-size: 12px\" >%s</a><br><br>If you have any questions or need assistance with your account, <NAME_EMAIL>."}, "magicLinkAgentRegisterEmail": {"subject": "[<PERSON><PERSON>] Verify Dr<PERSON> Agent", "snippet": "Click magic-link to verify your Drata Agent.", "message": "You asked us to send you a magic link for quickly verifying your Drata Agent. Your wish is our command!", "title": "Hi %s", "ctaText": "Verify Drata Agent"}, "preAuditPackageReady": {"subject": "Your audit package download is ready", "snippet": "Download is ready in Drata", "title": "Download is ready", "message": "Your audit package download is ready for %s %s. Download is available in Drata.", "messageWorkspaces": "Your audit package download is ready for %s %s for workspace - %s. Download is available in Drata.", "ctaText": "Open Drata"}, "preAuditPackageFailed": {"subject": "Your audit package download has failed", "snippet": "Download has failed. Please try again.", "title": "Download failed", "message": "Your audit package download has failed to generate for %s %s. You can request the download again in Drata.", "messageWorkspaces": "Your audit package download has failed to generate for %s %s for workspace - %s. You can request the download again in Drata.", "ctaText": "Open Drata"}, "controlEvidenceReadyEmail": {"subject": "Your control evidence download is ready", "snippet": "Download is ready in Drata.", "title": "Download is ready", "message": "Your control evidence download is ready for {companyName} {frameworkName}. Download is available in Drata.", "messageWorkspaces": "Your control evidence download is ready for {companyName} {frameworkName} for workspace - {productName}. Download is available in Drata.", "ctaText": "Open Drata"}, "controlEvidenceFailedEmail": {"subject": "Your control evidence download has failed", "snippet": "Download has failed. Please try again.", "title": "Download failed", "message": "Your control evidence download has failed to generate for %s %s. You can request the download again in Drata.", "messageWorkspaces": "Your control evidence download has failed to generate for %s %s for workspace - %s. You can request the download again in Drata.", "ctaText": "Open Drata"}, "failedTestNotificationEmail": {"subject": "Action required: Automated test error detected", "snippet": "Action required: Automated test error detected", "ctaText": "Go to Drata"}, "vendorSecurityQuestionnaireEmail": {"subject": "You have a questionnaire from %s to complete.", "snippet": "You have a questionnaire from %s to complete.", "title": "Hi,", "message": "{companyName} is conducting a security review and would like you to complete the questionnaire.", "messageWithEmail": "{companyName} ({email}) is conducting a security review and would like you to complete the questionnaire.", "btnText": "View questionnaire", "buttonNotWorking": "Button not working? Paste the following link into your browser:"}, "vendorSecurityQuestionnaireEmailV2": {"subject": "%s Security Questionnaire", "snippet": "%s Security Questionnaire", "title": "Vendor Security Questionnaire", "message": "Hello,<br><br>%s is requesting you to complete this Vendor Security Questionnaire.<br />Click on the link below to get started.", "messageWithEmail": "Hello,<br><br>%s ({email}) is requesting you to complete this Vendor Security Questionnaire.<br />Click on the link below to get started.", "ctaText": "Get Started", "subMessage": "If you are not the right recipient, please forward this email to the person responsible for the security questionnaire at %s"}, "complianceCheckLabels": {"ACCEPTED_POLICIES": "Accepted Policies", "IDENTITY_MFA": "Identity MFA", "BG_CHECK": "Background Check", "PASSWORD_MANAGER": "Password Manager", "AUTO_UPDATES": "Auto Updates", "HDD_ENCRYPTION": "Disk Encrypted", "ANTIVIRUS": "Anti-Virus", "LOCK_SCREEN": "Lock Screen", "SECURITY_TRAINING": "Security Training", "HIPAA_TRAINING": "HIPAA Training", "OFFBOARDING": "Offboarding Evidence"}, "expireSoonExclusionEmail": {"subject": "Personnel exclusion expiring soon", "title": "Personnel exclusion expiring soon", "salutation": "Dear %s,", "personnelMessage": "The personnel <b>%s</b> will soon end their temporary exclusions. The details are: <br /><br />Excluded checks: <b>%s</b> <br />Exclusion reason: <b>%s</b> <br />Duration: <b>%s</b> <br /><br />If this looks correct, you don't need to do anything. If the exclusion needs to be extended or modified, please <a target=\"_blank\" href=\"%s\">click here.</a>", "groupMessage": "The personnel in <b>%s</b> group will soon end their temporary exclusions. The details are: <br /><br />Excluded checks: <b>%s</b> <br />Exclusion reason: <b>%s</b> <br />Duration: <b>%s</b> <br /><br />If this looks correct, you don't need to do anything. If the exclusion needs to be extended or modified, please <a target=\"_blank\" href=\"%s\">click here.</a>", "statusMessage": "The personnel with <b>%s</b> status will soon end their temporary exclusions. The details are: <br /><br />Excluded checks: <b>%s</b> <br />Exclusion reason: <b>%s</b> <br />Duration: <b>%s</b> <br /><br />If this looks correct, you don't need to do anything. If the exclusion needs to be extended or modified, please <a target=\"_blank\" href=\"%s\">click here.</a>", "companyMessage": "All personnel at <b>%s</b> will soon end their temporary exclusions. The details are: <br /><br />Excluded checks: <b>%s</b> <br />Exclusion reason: <b>%s</b> <br />Duration: <b>%s</b> <br /><br />If this looks correct, you don't need to do anything. If the exclusion needs to be extended or modified, please <a target=\"_blank\" href=\"%s\">click here.</a>", "subMessage": "Thanks and have a great day! <br /><br />Since<PERSON>y, <br />Your Drata Team"}, "expireSoonExclusionsEmail": {"subject": "Personnel exclusions expiring soon", "title": "Personnel exclusions expiring soon", "salutation": "Dear %s,", "message": "Multiple personnel will soon end their temporary exclusions. The details are: <br /><br />Personnel: <b>%s + %s more</b><br /><br />If this looks correct, you don't need to do anything. If the exclusion needs to be extended or modified, or you want to check additional information, please <a target=\"_blank\" href=\"%s\">click here.</a>", "subMessage": "Thanks and have a great day! <br /><br />Since<PERSON>y, <br />Your Drata Team"}, "vendorSecurityQuestionnaireResponseEmail": {"subject": "You have a new Vendor Response!", "snippet": "You have a new Vendor Response!", "salutation": "Hi %s,", "title": "Vendor Security Questionnaire Response!", "message": "%s has completed your security questionnaire. Login to your Drata account to view the answers.", "ctaText": "Take me to the Questionnaire"}, "controlStatusEmail": {"subject": "🚀 Your Security Controls Status Update", "snippet": "See how your controls are performing and what actions you need to take.", "title": "Security Controls Status Update", "message": "Here is your digest for controls you own.", "ctaText": "Go to Drata", "expiringHeader": "Controls With Evidence Expiring Within 30 Days", "expiringBadDescription": "Update the renewal date or replace the evidence for these controls to maintain readiness and ensure a successful future audit:", "expiringGoodDescription": "It's all systems go! Each linked report, document and external file or URL is more than 30 days from its renewal date.", "expiringMoreDescription": "controls with evidence up for renewal within 30 days.", "expiringAdditional": "No more controls with expiring evidence", "expiredHeader": "Controls With Expired Evidence", "expiredBadDescription": "Update the renewal date or replace the evidence for these controls ASAP to maintain readiness and ensure a successful future audit:", "expiredGoodDescription": "It's all systems go! No linked report, document or external file or URL is past its renewal date.", "expiredMoreDescription": "controls with expired evidence.", "expiredAdditional": "No more controls with expired evidence"}, "newGroupMemberAcceptPolicyEmail": {"subject": "Policy acknowledgment required", "title": "Policy acknowledgment required", "message": "%s needs you to acknowledge a policy: <strong>%s</strong>. Please sign in to Drata to review and acknowledge it as soon as possible.", "ctaText": "View Policy"}, "orphanPolicyEmail": {"subject": "Policy not assigned to any personnel", "snippet": "Please review unassigned policy", "title": "Policy Not Assigned To Any Personnel", "salutation": "Dear %s,", "message": "The <strong>%s</strong>, which you are responsible for, is no longer assigned to any personnel. Disregard this email if the change is intentional. Otherwise, please go to the Policy Center and assign the appropriate personnel to this policy.", "ctaText": "View Policy"}, "annualComplianceRenewalTasksNineMonthsEmail": {"subject": "⏰ Time to complete {companyName}'s annual compliance tasks", "greeting": "Dear {userName}", "snippet": "Prepare now to maintain compliance over the next 12 months", "titleLine1": "Annual Compliance", "titleLine2": "Review Tasks", "paragraphOne": "Time flies when you’re managing compliance! While Drata continues to automatically monitor your controls and gather evidence, there are some tasks you need to complete every 12 months to ensure {companyName} maintains its security posture.", "olItemOne": "<a target=\"_blank\" href=\"{webAppUrl}/governance/policies\">Sign into Drata</a> to review, update and approve your policies", "olItemTwo": "Check out the <a target=\"_blank\" href=\"https://help.drata.com/en/articles/5696227-annual-compliance-tasks\">help guide</a> for more information about:", "ulItemOne": "What you need to update in Dr<PERSON>", "ulItemTwo": "What actions your employees need to take", "ulItemThree": "General best practices", "paragraphTwo": "This reminder is intended to provide you with ample time to review, update, and take action on the annual tasks required to maintain compliance over the coming 12 months. Each Admin of your Drata account will receive another reminder email in 60 days.", "paragraphThree": "If you have any questions or need assistance, please reach out to your Customer Success Manager or email <a target=\"_blank\" href=\"mailto:<EMAIL>\"><EMAIL></a>.", "paragraphFour": "<PERSON><PERSON><PERSON>,", "paragraphFive": "Your Drata Customer Success Team"}, "annualComplianceRenewalTasksElevenMonthsEmail": {"subject": "⏰ Time to complete {companyName}'s annual compliance tasks", "greeting": "Dear {userName}", "snippet": "Prepare now to maintain compliance over the next 12 months", "titleLine1": "Annual Compliance", "titleLine2": "Review Tasks", "paragraphOne": "This is your second reminder to complete your annual compliance tasks to ensure {companyName} maintains its security posture. While Drata continues to automatically monitor your controls and gather evidence, there are some tasks you need to complete every 12 months.", "olItemOne": "If you haven't already done so, <a target=\"_blank\" href=\"{webAppUrl}/governance/policies\">sign into Drata</a> to update and approve your policies", "olItemTwo": "Check out the <a target=\"_blank\" href=\"https://help.drata.com/en/articles/5696227-annual-compliance-tasks\">help guide</a> for more information about:", "ulItemOne": "What you need to update in Dr<PERSON>", "ulItemTwo": "What actions your employees need to take", "ulItemThree": "General best practices", "paragraphTwo": " ", "paragraphThree": "If you have any questions or need assistance, please reach out to your Customer Success Manager or email <a target=\"_blank\" href=\"mailto:<EMAIL>\"><EMAIL></a>.", "paragraphFour": "<PERSON><PERSON><PERSON>,", "paragraphFive": "Your Drata Customer Success Team"}, "newPolicyOwnerEmail": {"subject": "Policy assigned to you", "snippet": "Policy assigned to you", "salutation": "Dear %s,", "title": "Policy Assigned to You", "message": "You have been assigned as the owner of the <strong>%s</strong>. Please sign in to Dr<PERSON> to review the policy.", "ctaText": "View Policy"}, "personnelCsvEmail": {"subject": "Personnel Compliance Overview Request Link", "snippet": "Personnel Compliance Overview Request Link", "salutation": "Dear %s,", "title": "[Drata] Personnel Compliance Overview Request Link", "message": "Click on the link below to download your requested list. For your security, this link will expire in 24 hours.", "ctaText": "View Personnel"}, "eventPdfEmail": {"subject": "Event Download Request Link", "snippet": "Event Download Request Link", "salutation": "Dear %s,", "title": "[Drata] Event Download Request Link", "message": "Click on the link below to download your requested list. For your security, this link will expire in 24 hours.", "ctaText": "View Event"}, "reportsEmail": {"subject": "Drata Evidence Library download link", "snippet": "Drata Evidence Library download link", "salutation": "Dear %s,", "title": "[Drata] Drata Evidence Library download link", "message": "Click on the link below to download your requested list. For your security, this link will expire in 24 hours.", "ctaText": "Get Evidence Library"}, "policyArchivedEmail": {"subject": "Policy was archived", "snippet": "Your policy was archived", "salutation": "Dear %s,", "title": "Policy Archived", "message": "The policy: %s has been archived by %s.", "ctaText": "Go To Policy Center"}, "policyReplacedEmail": {"subject": "Policy was replaced", "snippet": "Your policy was replaced", "salutation": "Dear %s,", "title": "Policy Replaced", "message": "The policy: %s was replaced with %s by %s.", "ctaText": "Go To Policy Center"}, "policyRestoredEmailFromArchived": {"subject": "Policy was restored", "snippet": "Your policy was restored", "salutation": "Dear %s,", "title": "Policy Restored", "message": "The policy: %s, which was previously archived, has been restored by %s", "ctaText": "Go To Policy Center"}, "policyRestoredEmailFromReplaced": {"subject": "Policy was restored", "snippet": "Your policy was restored", "salutation": "Dear %s,", "title": "Policy Restored", "message": "The policy: %s, which was previously replaced, has been restored by %s", "ctaText": "Go To Policy Center"}, "previousPolicyOwnerEmail": {"subject": "Policy Owner removed", "snippet": "Policy Owner removed", "salutation": "Dear %s,", "title": "Policy Ownership Changed", "message": "You have been removed as the owner of the <strong>%s</strong> by %s. Please sign in to Drata to review.", "ctaText": "Go to Policy Center"}, "policyVersionCancelReviewAndApprovalEmail": {"subject": "Review and approval cancelled for %s v%s", "title": "Review and approval cancelled for %s v%s", "paragraphOne": "<strong>Cancelled by:</strong> %s", "btnText": "View Policy"}, "policyVersionPastDueEmail": {"subject": "%s v%s approval past deadline", "title": "%s v%s Approval Past Deadline", "paragraphOne": "This is a reminder that the approval deadline for the %s v%s has passed as of %s.", "paragraphTwo": "<strong>Policy Owner: </strong>%s<br><strong>Explanation of Changes: </strong>%s<br><strong>Original Approval Deadline: </strong>%s", "paragraphTwoNoExplanation": "<strong>Policy Owner: </strong>%s<br><strong>Original Approval Deadline: </strong>%s", "paragraphThree": "Please review and approve the policy as soon as possible.", "btnText": "View Policy"}, "policyVersionRequestChangesEmail": {"title": "Changes are requested for %s v%s", "subject": "Changes requested for %s v%s", "paragraphOne": "<strong>Requested by:</strong> %s", "paragraphTwo": "<strong>Change(s) requested:</strong> %s", "btnText": "View Policy"}, "policyVersionApprovedEmail": {"title": "All required approvers have successfully reviewed and approved %s v%s.", "subject": "%s v%s has been approved", "paragraphOne": "<strong>Approval date:</strong> %s", "paragraphTwo": "The policy is now ready to publish.", "btnText": "View Policy"}, "policyApprovalOverrideEmail": {"subject": "Approval override for %s v%s", "tierSubject": "Approval override for tier-%s for %s v%s", "title": "Approval override for %s v%s", "tierTitle": "Approval override for tier-%s for %s v%s", "paragraphOne": "<strong>Approval has been overridden by:</strong> %s<br><strong>Explanation for override:</strong> %s", "btnText": "View Policy"}, "trustCenterApprovedAccessRequestEmail": {"subject": "%s - Access to files approved", "snippet": "%s - Access to files approved", "title": "Access Request approved", "message": "%s has granted access for your request"}, "trustCenterDeniedAccessRequestEmail": {"subject": "%s - Access to files denied", "snippet": "%s - Access to files denied", "title": "Access Request denied", "message": "Your request was denied by %s"}, "trustCenterAccessRequestWithoutApproveEmail": {"subject": "Trust Center Private Access Request: %s", "snippet": "Trust Center Private Access Request", "title": "Trust Center", "message": "<strong>%s</strong> (%s) from <strong>%s</strong> wants access to these documents."}, "trustCenterAdminApprovedAccessRequestEmail": {"subject": "Trust Center Private Access Granted: %s", "snippet": "Trust Center Private Access Granted", "title": "Access Request approved", "message": "<strong>%s</strong> (%s) from <strong>%s</strong> was granted access for %s days to the documents below. They were approved to access files on %s.", "messagePreApproved": "<strong>%s</strong> (%s) from <strong>%s</strong> was granted access for %s days to the documents below. They were pre-approved to access files on %s."}, "trustCenterSignNdaEmail": {"subject": "%s - Sign NDA", "message": "Please sign the NDA below. Once complete, you will receive the files you requested."}, "resyncCompleteEmail": {"subject": "[Drata] Data Sync Complete", "snippet": "The resync process has finished", "title": "Hi, %s", "message": "Your requested data resync has completed."}, "offboardingEvidenceCollectionErrorEmail": {"subject": "[Action Required] Automated Offboarding Evidence Collection Error", "snippet": "Automated Offboarding Evidence Collection Error", "title": "Unable to Collect Offboarding Evidence", "salutation": "Dear %s,", "message": "Dr<PERSON> is unable to automatically collect your offboarding evidence due to a connection error. Please review your %s connection at %s."}, "newAuditMessageEmail": {"subject": "Review new message for %s %s", "snippet": "New message added at %s on %s", "title": "Review new message for %s %s", "message": "%s added a message to your request for %s %s at %s.", "messageWithWorkspace": "%s added a message to your request for %s %s for workspace - %s at %s.", "ctaText": "View Message"}, "newEvidenceAuditMessageEmail": {"subject": "Review new evidence for %s %s", "snippet": "New evidence added at %s on %s", "title": "Review new evidence for %s %s", "message": "Someone added evidence to your request for %s %s for workspace - %s at %s.", "ctaText": "View Request", "ctaUrl": "https://app.drata.com"}, "updatedEvidenceAuditMessageEmail": {"subject": "Review modified evidence for %s %s", "snippet": "Evidence modified at %s on %s", "title": "Review modified evidence for %s %s", "message": "Someone modified evidence to your request for %s %s for workspace - %s at %s.", "ctaText": "View Request", "ctaUrl": "https://app.drata.com"}, "requestStatusChangedEmail": {"subject": "Review status change for %s %s", "snippet": "New status change at %s on %s", "title": "Review status change for %s %s", "message": "%s changed the status to your request for %s %s at %s.", "messageWithWorkspace": "%s changed the status to your request for %s %s for workspace - %s at %s.", "ctaText": "View Request"}, "newWorkspaceAdminEmail": {"subject": "You’ve been added as a Workspace Manager of %s for %s", "salutation": "Hi %s,", "message": "You’ve been added as a workspace manager of %s for %s in Drata. Now you can manage Compliance and Connections for this workspace, plus Risk and Governance. No immediate action is required. Sign in to Drata to manage your workspace:", "ctaText": "<PERSON><PERSON>", "ctaUrl": "https://app.drata.com"}, "outDatedVersion": {"subject": "Review changes", "title": "Review changes to %s", "snippet": "Review changes to %s", "salutation": "Hi %s,", "message": "There are changes to the policy %s in %s. Please click the button below to review these changes. ", "ctaText": "Review Changes", "ctaUrl": "https://app.drata.com"}, "auditRequestStatusChange": {"subject": "Status update for request %s", "snippet": "Status update for request %s", "salutation": "Dear %s,", "title": "Audit Hub: Status update for a request", "message": "You have a status update for request %s. <br><br> The status was changed from %s to %s.", "ctaText": "View Request"}, "auditRequestStatusChangeBulk": {"subject": "Review status changes for %s %s requests", "snippet": "New status changes at %s on %s", "title": "Review status changes for %s %s requests", "message": "%s changed the statuses to your requests for %s %s at %s.", "messageWithWorkspace": "%s changed the statuses to your requests for %s %s for workspace - %s at %s.", "ctaText": "View Requests"}, "userAssignedAsRiskOwner": {"subject": "You have been assigned as a risk owner in Drata", "title": "You have a risk to review", "message": "You have been made the owner of a risk. Please follow the link to review in Drata.", "ctaText": "Open Drata"}, "userAssignedAsRiskOwnerWithDueDate": {"message": "You have been made the owner of a risk, with an expected completion date of %s.  Please follow the link to review in Drata."}, "userAssignedAsRiskAssessmentOwner": {"subject": "Risk assessment section assigned to you", "snippet": "Risk assessment section assigned to you", "title": "Hi %s,", "message": "You've got a new assignment from %s to complete the %s section of the risk assessment questionnaire.<br><a href=\"%s\" target=\"_blank\" class=\"em_font_12\" style=\"font-size: 12px\">Open risk assessment questionnaire</a><br><br>Reach out to %s if you have any questions."}, "userAssignedAsRiskAssessmentOwnerWithDueDate": {"message": "You've got a new assignment from %s to complete the %s section of the risk assessment questionnaire by %s.<br><a href=\"%s\" target=\"_blank\" class=\"em_font_12\" style=\"font-size: 12px\">Open risk assessment questionnaire</a><br><br>Reach out to %s if you have any questions."}, "rejectedFileScan": {"subject": "Drata File Reject", "title": "Hi %s,", "message": "Drata detected an issue with a file you tried to upload during an initial security scan. We automatically prevented the upload to protect your security.<br><br>Impacted file: %s<br><br>You can get more info in Event Details.", "ctaText": "View details", "ctaUrl": "https://app.drata.com/events"}, "selfServiceAccountCreationInviteFutureContract": {"subject": "{accountName} <> Drata Account Information", "contactSupport": "Contact support", "title": "Hi {accountName} Team,", "paragraphOne": "Welcome to Drata, the world's most advanced compliance automation platform!", "paragraphTwo": "Your contract start date with us is <strong>{contractStartDate}</strong>.", "paragraphThree": "On {contractStartDate}, you will receive an email from us to activate your account and begin your compliance journey with Drata. Please take this opportunity to add drata.com to your list of “safe senders” to assure continued receipt of your access information and communications from our teams.", "snippet": "Trust, Automated.", "copyright": "Drata Inc. © 2023 All rights reserved."}, "selfServiceAccountCreationInvite": {"subject": "Welcome to Drata {customerName}", "contactSupport": "Contact support", "title": "Welcome to Drata {customerName}", "paragraphOne": "We're so glad you chose <PERSON><PERSON> for your GRC journey! With <PERSON><PERSON>, you can streamline risk and compliance, save time on audits, accelerate your sales cycle and achieve comprehensive end-to-end risk management.", "paragraphTwo": "The first step is to create your account and complete activation.", "paragraphBelowCtaBtn": "Activation link expired? Don’t worry, you’ll be able to generate a new one!", "btnText": "Get started", "snippet": "Trust, Automated.", "copyright": "Drata Inc. © 2023 All rights reserved."}, "selfServiceAccountCreationInviteReminder": {"subject": "Reminder: Create your Drata account", "title": "Start your compliance journey with <PERSON><PERSON>"}, "selfServiceAccountCreationOnboarding": {"subject": "Welcome to Drata", "contactSupport": "Contact support", "title": "Ready to go to Drata, {firstName}?", "paragraphOne": "With one click of a button, you'll activate your profile and enter Drata. The way you do compliance will change forever and, personally, we're excited.", "paragraphTwo": "We've provided a secure link to get you to Drata for the first time. After that, just go to the application and enter your email to sign in!", "btnText": "<PERSON><PERSON>", "snippet": "Trust, Automated.", "copyright": "Drata Inc. © 2023 All rights reserved."}, "selfServiceAdminInvitation": {"subject": "You've been added as {role} Team Member for {companyName}", "contactSupport": "Contact support", "title": "Hi {name},", "paragraphOne": "You've been added to Dr<PERSON> as {role} Team Member for {companyName}. With this role within Drata, you are now able to engage in security and compliance activities for {companyName}. You don't need to take any immediate action, but may login to Drata at anytime using your email address here.", "btnText": "Get started", "snippet": "Trust, Automated.", "copyright": "Drata Inc. © {currentYear} All rights reserved."}, "policyAcceptanceCsvEmail": {"subject": "Personnel Policy Acknowledgement Request Link", "snippet": "Personnel Policy Acknowledgement Request Link", "salutation": "Dear %s,", "title": "[Drata] Personnel Policy Acknowledgement Request Link", "message": "Click on the link below to download your requested list. For your security, this link will expire in 24 hours.", "ctaText": "View Report"}, "newReviewPeriodEmail": {"subject": "You’ve been added as a User Access Reviewer in {companyName}’s Drata platform", "snippet": "You’ve been added as a User Access Reviewer in {companyName}’s Drata platform", "salutation": "Dear %s,", "title": "User access review assignment", "messageSingleApp": "You’ve been assigned to perform a user access review on <strong>%s</strong> by <strong>%s</strong>. Please click the button below to start your review process.", "messageMultipleApps": "You’ve been assigned to perform a user access review on <strong>%s</strong> and <strong>%s</strong> by <strong>%s</strong>. Please click the button below to start your review process.", "subMessage": "Thanks and have a great day!<br><br><PERSON><PERSON><PERSON>,<br>Your Drata Team", "ctaText": "Start review"}, "vulnerabilitiesEmail": {"subject": "Vulnerability Scan Results download link", "snippet": "Drata Vulnerability Scan Results download link", "salutation": "Dear %s,", "title": "[Drata] Drata Vulnerability Scan Results download link", "message": "Click on the link below to download your requested file. For your security, this link will expire in 24 hours.", "ctaText": "<PERSON>an Results"}, "newNoteAccessReviewEmail": {"subject": "A new note has been added to the account in {companyName}’s current Access Review.", "snippet": "A new note has been added to the account in {companyName}’s current Access Review.", "salutation": "Dear %s,", "title": "New note in your assigned Access Review Application", "message": "A new note has been created in your assigned Access Review Application: <strong>%s</strong>, for account <strong>%s</strong>.", "subMessage": "Thanks and have a great day!<br><br><PERSON><PERSON><PERSON>,<br>Your Drata Team", "ctaText": "View Note"}, "completeReviewPeriodEmail": {"subject": "The latest review period was completed in {companyName}’s Access Review.", "snippet": "The latest review period was completed in {companyName}’s Access Review.", "salutation": "Dear %s,", "title": "Latest Access Review Period Completed", "message": "The latest review period was completed in {companyName}’s Access Review.", "subMessage": "Thanks and have a great day!<br><br><PERSON><PERSON><PERSON>,<br>Your Drata Team", "ctaText": "View Completed Access Review"}, "reopenReviewPeriodEmail": {"subject": "A review period has been re-opened for {companyName}.", "snippet": "A review period has been re-opened for {companyName}.", "salutation": "Dear %s,", "title": "A review period has been re-opened", "message": "A review period has been re-opened for further review in {companyName}’s Access Review.", "subMessage": "Thanks and have a great day!<br><br><PERSON><PERSON><PERSON>,<br>Your Drata Team", "ctaText": "View Access Review Period"}, "accessApplicationRequestChangeEmail": {"subject": "There has been a request to re-open a completed Access Review.", "snippet": "There has been a request to re-open a completed Access Review.", "salutation": "Dear %s,", "title": "Request to re-open a completed Access Review", "message": "A request to re-open a completed Access Review has been made by user <strong>%s</strong>.", "subMessage": "Thanks and have a great day!<br><br><PERSON><PERSON><PERSON>,<br>Your Drata TeamThanks and have a great day!<br><br>Since<PERSON>y,<br>Your Drata Team", "ctaText": "Review request"}, "requiredControlApprovalEmail": {"subject": "You have a required approval task for %s %s", "snippet": "%s sent you a required approval", "title": "Required approval task", "message": "%s sent a required approval for control %s - %s for you to complete by %s.", "messageWithWorkspace": "%s sent a required approval for control %s - %s in workspace - %s for you to complete by %s.", "ctaText": "View control"}, "requestChangesControlApprovalEmail": {"subject": "You have a change request task for %s %s", "snippet": "%s sent you a change request", "title": "Changes requested for a required approval", "message": "%s sent you a change request for %s - %s.", "messageWithWorkspace": "%s sent you a change request for %s - %s in workspace - %s.", "ctaText": "View control"}, "controlEvidenceUpdateEmail": {"subject": "Control was updated", "title": "Control was updated", "message": "%s made a change to %s - %s. This could include updates to the policy, deleting it, or removing evidence. You can view specific details in Drata.", "ctaText": "View %s"}, "approveControlApprovalEmail": {"subject": "%s approved %s %s", "snippet": "Required approval is complete", "title": "Required approval is complete", "message": "%s completed the required approval for %s - %s.", "messageWithWorkspace": "%s completed the required approval for %s - %s in workspace - %s.", "ctaText": "View control"}, "controlInternalNoteEmail": {"subject": "%s added a note to %s %s", "snippet": "A new note was added to your control", "title": "Review new internal note", "message": "%s sent an internal note for %s - %s.", "messageWithWorkspace": "%s sent an internal note for %s - %s in workspace - %s.", "ctaText": "View control"}, "controlApprovalStateChangeEmail": {"subject": "You need to take action on %s %s", "snippet": "%s updated evidence linked to your control in Evidence Library", "title": "Required approval stage changed", "message": "Your control’s stage has changed to prepare for approvers and requires your review. %s updated the evidence to the %s - %s from Evidence Library.", "messageWithWorkspace": "Your control’s stage has changed to prepare for approvers and requires your review. %s updated the evidence to the %s - %s in workspace - %s from Evidence Library.", "ctaText": "View control"}, "controlApprovalBulkStateChangeEmail": {"subject": "You need to take action on %s controls", "snippet": "%s updated evidence linked to your controls in Evidence Library", "title": "Review your control’s updated evidence", "message": "Your controls’ stages have changed to prepare for approvers and require your review. %s updated the evidence to the controls from Evidence Library.", "messageWithWorkspace": "Your controls’ stages have changed to prepare for approvers and require your review. %s updated the evidence to the controls in workspace - %s from Evidence Library.", "ctaText": "View controls"}, "controlApprovalDeadlineEmail": {"subject": "You have a required approval task for %s %s", "snippet": "Review and approve your control", "title": "Required approval task", "message": "The evidence for your control hasn’t changed since your last review, and it’s time for another approval for %s - %s. Complete the approval by %s.", "messageWithWorkspace": "The evidence for your control hasn’t changed since your last review, and it’s time for another approval for %s - %s. Complete the approval by %s in workspace - %s.", "ctaText": "View control"}, "requestControlEvidenceEmailPackageReady": {"subject": "Review download control evidence package for %s %s audit", "snippet": "Download link is ready", "title": "Review download control evidence package for %s %s audit", "message": "The control evidence sample download link is ready for %s %s for workspace - %s. For your security, the link will expire in 24 hours.", "ctaText": "Open Drata"}, "requestControlEvidenceEmailPackageFailed": {"subject": "Review download control evidence package for %s %s audit", "snippet": "Download link generation has failed", "title": "Review download control evidence package for %s %s audit", "message": "The control evidence sample download link generation has failed for %s %s for workspace - %s. You can request to regenerate again on Drata.", "ctaText": "Open Drata"}, "auditorSampleOutOfPeriod": {"subject": "You need to get new samples for an audit", "title": "You need to get new samples for an audit", "message": "Someone updated the audit period for one of your audits from %s to %s. Go to the audit in Drata and request the updated samples as soon as possible to make sure your samples are still relevant. <br><br>You can disregard this if you’ve already requested an updated sample.", "ctaText": "View audit"}, "controlApprovalStageChangePolicyEmail": {"subject": "You need to take action on %s %s", "snippet": "%s linked a policy to your control in Policy Center", "title": "Required approval stage changed", "message": "Your control’s stage has changed to prepare for approvers and requires your review. %s mapped a policy to the %s - %s from Policy Center.", "ctaText": "View control"}, "idpConnectionAdminVerificationEmail": {"subject": "Validate your updated email", "contactSupport": "Contact support", "title": "Hi, {username}", "paragraphOne": "Please validate your updated email as soon as possible. This link expires in one hour and can only be used once. Once you complete validation, you’ll be able to pick up where you left off.", "btnText": "Validate", "ctaUrl": "Validate", "buttonNotWorking": "Button not working? Paste the following link into your browser:"}, "idpConnectionAdminNotificationEmail": {"subject": "Email updated notification", "contactSupport": "Contact support", "title": "Hi, {username}", "paragraphOne": "An admin needed to update your current Drata account to an account in your identity provider. This usually happens when someone signs up using an email alias. Please use your updated email in the future:", "paragraphTwo": "<ul><li align='left' valign='top' style=\"font-family: Arial; font-size: 16px; line-height: 24px; list-style-position: inside;\">Updated email: {email}</li><li align='left' valign='top' style=\"font-family: Arial; font-size: 16px; line-height: 24px; list-style-position: inside;\">Previous email: {prevEmail}</li></ul>"}, "tokenExpirationNotification": {"subject": "Sentinel One token expiration notification", "snippet": "Your access token is expired", "title": "Sentinel One token expiration notification", "message": "We regret to inform you that your access token for Sentinel One %s"}, "auditorSamplePersonnelOutOfPeriod": {"subject": "You need to get new samples for an audit", "title": "You need to get new samples for an audit", "message": "Some personnel start and end dates have changed. Go to audit in Drata and choose new personnel to ensure your samples are relevant. <br><br>You can disregard this if you’ve already requested an updated sample.", "ctaText": "View audit"}, "vulnerabilitySla": {"title": "Hi %s,", "subject": "Vulnerability SLA due dates", "message": "There are critical and high severity vulnerabilities past due and/or with upcoming due dates.", "subMessage": "<br/>Thanks and have a great day!<br/><br/>Since<PERSON><PERSON>,<br/>Your Drata Team<br/></br>", "ctaText": "View vulnerability findings", "list": "<table>%s</table><br/>To check the details of these vulnerabilities, click the link below:", "listItem": "<br/><br/>%s (%s):<br/><strong>%s</strong> vulnerability past due<br/><strong>%s</strong> vulnerabilities with due dates in the next %s days"}, "infrastructureAccounts": {"subject": "Infrastructure Accounts download link", "snippet": "Infrastructure Accounts download link", "salutation": "Dear %s,", "title": "[Drata] Infrastructure Accounts Download Link", "message": "Click on the link below to download your requested list. For your security, this link will expire in 24 hours.", "ctaText": "Get Infrastructure Accounts"}, "auditorApiEvidenceRequestDownloadReady": {"subject": "Your audit package download is ready", "snippet": "Download is ready in Drata", "title": "Download is ready", "message": "Your audit package download is ready for %s.", "ctaText": "Download", "buttonNotWorking": "Button not working? Try using the following link:"}, "inboundSecurityQuestionnaireProcessed": {"subject": "Security questionnaire ready for review", "title": "Hi, %s", "paragraphOne": "Drata has finished processing your questionnaire %s and is now ready to review.", "btnText": "Review questionnaire", "copyright": "Drata Inc. © %s All rights reserved.", "contactSupport": "Contact support", "buttonNotWorking": "Button not working? Try using the following link:"}, "inboundSecurityQuestionnaireFailed": {"subject": "Security questionnaire failed to process", "title": "Hi, %s", "paragraphOne": "There was an issue processing your questionnaire %s. Please log into Drata and try again.", "btnText": "Sign in", "copyright": "Drata Inc. © %s All rights reserved.", "contactSupport": "Contact support", "buttonNotWorking": "Button not working? Try using the following link:"}, "trustCenterReportDownload": {"subject": "Your Trust Center report download is ready: %s", "contactSupport": "Contact support", "title": "Trust Center report download is ready", "paragraphOne": "The Trust Center report you recently requested is now available to download, this link expires after 7 days.", "btnText": "Download report", "buttonNotWorking": "Button not working? Paste the following link into your browser:", "snippet": "Trust, Automated.", "copyright": "Drata Inc. © {currentYear} All rights reserved."}, "followUpReminderEmail": {"subject": "You have a questionnaire from {companyName} to complete.", "title": "Hi,", "message": "{companyName} is reminding you to complete their questionnaire as part of their security review.", "messageWithEmail": "{companyName} ({email}) is reminding you to complete their questionnaire as part of their security review.", "btnText": "View questionnaire", "buttonNotWorking": "Button not working? Paste the following link into your browser:", "paragraphBelowCtaBtn": "If you need assistance, please email", "contactSupport": "Contact support"}, "workflowNotificationEmail": {"subject": "{subject}", "snippet": "Trust, Automated.", "title": "%s", "paragraphOne": "%s", "btnText": "Go to control", "buttonNotWorking": "Button not working? Paste the following link into your browser:"}, "workflowNotificationEmailForRisk": {"subject": "{subject}", "snippet": "Trust, Automated.", "title": "%s", "paragraphOne": "%s", "btnText": "Go to risk", "buttonNotWorking": "Button not working? Paste the following link into your browser:"}, "workflowNotificationEmailForEvidence": {"subject": "{subject}", "snippet": "Trust, Automated.", "title": "%s", "paragraphOne": "%s", "btnText": "Go to evidence", "buttonNotWorking": "Button not working? Paste the following link into your browser:"}, "vendorMonthlyDigestEmail": {"subject": "Upcoming vendor reviews from {startDate} to {endDate}", "title": "Hi,", "message": "{companyName} is conducting a security review and would like you to complete the questionnaire.", "messageWithEmail": "{companyName} ({email}) is conducting a security review and would like you to complete the questionnaire.", "btnText": "View questionnaire", "buttonNotWorking": "Button not working? Paste the following link into your browser:"}, "questionnaireSend": {"contactSupport": "Contact support", "paragraphBelowCtaBtn": "If you need assistance, please email", "buttonNotWorking": "Button not working? Paste the following link into your browser:", "message": "Hi,\n\nWe'd like to conduct a security review and would like some information from you. Use this link to complete the questionnaire.\n\nThank you.", "subject": "{companyName} is conducting a security review of you.", "btnText": "View questionnaire"}, "workflowPublishedNotificationEmail": {"subject": "Workflow, {workflowName}, was published", "snippet": "Trust, Automated.", "title": "Workflow published", "paragraphOne": "%s", "btnText": "View workflow", "buttonNotWorking": "Button not working? Paste the following link into your browser:"}, "workflowUnpublishedNotificationEmail": {"subject": "Workflow, {workflowName}, was unpublished", "snippet": "Trust, Automated.", "title": "Workflow unpublished", "paragraphOne": "%s", "btnText": "View workflow", "buttonNotWorking": "Button not working? Paste the following link into your browser:"}, "workflowArchivedNotificationEmail": {"subject": "Workflow, {workflowName}, was archived", "snippet": "Trust, Automated.", "title": "Workflow archived", "paragraphOne": "%s", "btnText": "View workflow", "buttonNotWorking": "Button not working? Paste the following link into your browser:"}, "workflowRunErrorNotificationEmail": {"subject": "Workflow, {workflowName}, complete with errors", "snippet": "Trust, Automated.", "title": "Run error", "paragraphOne": "%s", "btnText": "View workflow run", "buttonNotWorking": "Button not working? Paste the following link into your browser:"}, "workflowDeletedNotificationEmail": {"subject": "Workflow, {workflowName}, was deleted", "snippet": "Trust, Automated.", "title": "Workflow deleted", "paragraphOne": "%s"}, "customTaskEmailCommons": {"control": "Control", "risk": "Risk", "complete": "Complete", "incomplete": "Incomplete", "btnText": "View task", "ctaUrl": "/workspaces/{workspaceId}/tasks?{urlParams}", "Recurs": "Recurs ", "endsOn": "Ends on ", "monthlyOnLastDay": "every {interval} months on the last day of the month", "monthlyOnLastDaySingular": "every month on the last day of the month", "monthlyOnDayOfMonth": "every {interval} months on day {dayOfMonth}", "monthlyOnDayOfMonthSingular": "every month on day {dayOfMonth}", "monthlyOnWeekNumberAndWeekday": "every {interval} months on the {weekNumberFormatted} {monthlyDayOfWeek}", "monthlyOnWeekNumberAndWeekdaySingular": "every month on the {weekNumberFormatted} {monthlyDayOfWeek}", "weeklyOnDaysOfWeek": "every {interval} weeks on {daysOfWeekFormatted}", "weeklySingular": "every week on {daysOfWeekFormatted}", "yearlyOnMonthAndDay": "every {interval} years on {dateFormatted}", "yearlySingular": "every year on {dateFormatted}", "endDateText": ", ends on {endDateText}"}, "customTaskUpdatedEmail": {"subject": "A task assigned to you in Drata has been updated", "title": "A task assigned to you has been updated", "message": "<strong>Name{titleUpdated}:</strong> {taskName}<br/><strong>Description{descriptionUpdated}:</strong> {description}<br/><strong>Owner(s){ownersUpdated}:</strong> {owners}<br/><strong>Due date{dueDateUpdated}:</strong> {dueDate}<span style=\"{style}\"><br/><strong>Mapped {mappedObject}{mappedObjectUpdated}:</strong> {objectInfo}</span><br/><strong>Status{statusUpdated}:</strong> {status}<br/><br/><strong>Updated by:</strong> {updatedBy}", "updatedSuffix": " (updated)"}, "customTaskAssignedEmail": {"subject": "You were assigned a task in Drata", "title": "You were assigned a task by {assigner}", "paragraphOne": "<strong>Name:</strong> {taskName}<br><strong>Description:</strong> {description}<br><strong>Owner(s):</strong> {owners}<br><strong>Due date:</strong> {dueDate}<span style=\"{style}\"><br><strong>Mapped {mappedObject}:</strong> {objectInfo}</span><br/><strong>Status:</strong> {status}</br><br/><strong>Assigned by:</strong> {assigner}"}, "customTaskUnassignedEmail": {"subject": "You have been removed as an owner of a task in Drata", "title": "You have been removed from a task by {assigner}", "paragraphOne": "<strong>Name:</strong> {taskName}<br><strong>Description:</strong> {description}<br><strong>Owner(s):</strong> {owners}<span style=\"{style}\"><br><strong>Mapped {mappedObject}:</strong> {objectInfo}</span><br><strong>Status:</strong> {status}<br/><br/><strong>Removed by:</strong> {assigner}"}, "customTaskDeletedEmail": {"subject": "A task you were assigned has been deleted in Drata", "title": "A task you were assigned has been deleted: {taskName}", "paragraphOne": "<strong>Name:</strong> {taskName}<br><strong>Description:</strong> {description}<br><strong>Owner(s):</strong> {owners}<br><strong>Due date:</strong> {dueDate}<span style=\"{style}\"><br><strong>Mapped {mappedObject}:</strong> {objectInfo}</span><br/><strong>Status:</strong> {status}</br><br/><strong>Deleted by:</strong> {deletedBy}"}, "taskOverdueEmail": {"subject": "You have an overdue task in Dr<PERSON>", "title": "You have an overdue task", "paragraphOne": "<strong>Name:</strong> {taskName}<span style=\"{descriptionStyle}\"><br><strong>Description:</strong> {description}</span><br><strong>Owner(s):</strong> {owners}<br><strong>Due date:</strong> {dueDate}<span style=\"{style}\"><br><strong>Mapped {mappedObject}:</strong> {objectInfo}</span><br/>", "paragraphBelowCtaBtn": "<br>You can customize your task notification settings anytime from the <a target=\"_blank\" href=\"{webAppUrl}\">user notifications area</a> in Drata.", "btnText": "View task", "buttonNotWorking": "Button not working? Paste the following link into your browser:", "ctaUrl": "/workspaces/{workspaceId}/tasks?{urlParams}"}, "overdueTasksSummaryEmail": {"subject": "You have overdue tasks in Drata", "title": "You have {tasksCount} overdue tasks", "paragraphOne": "<strong>Name:</strong> {taskName}<span style=\"{descriptionStyle}\"><br><strong>Description:</strong> {description}</span><br><strong>Owner(s):</strong> {owners}<br><strong>Due date:</strong> {dueDate}<span style=\"{style}\"><br><strong>Mapped {mappedObject}:</strong> {objectInfo}</span><br/>", "paragraphTwo": "+ {remainingTaskCount} overdue {taskPluralization}", "paragraphThree": "Note: These tasks are across multiple workspaces. The button below will take you to the tasks in one of your workspaces.", "paragraphBelowCtaBtn": "<br>You can customize your task notification settings anytime from the <a target=\"_blank\" href=\"{webAppUrl}\">user notifications area</a> in Drata.", "btnText": "View all overdue tasks", "buttonNotWorking": "Button not working? Paste the following link into your browser:", "ctaUrl": "/workspaces/{workspaceId}/tasks?{urlParams}"}, "taskUpcomingEmail": {"subject": "You have a task due this week in Drata", "title": "You have 1 task due this week", "paragraphOne": "<strong>Name:</strong> {taskName}<span style=\"{descriptionStyle}\"><br><strong>Description:</strong> {description}</span><br><strong>Owner(s):</strong> {owners}<br><strong>Due date:</strong> {dueDate}<span style=\"{style}\"><br><strong>Mapped {mappedObject}:</strong> {objectInfo}</span><br/>", "paragraphBelowCtaBtn": "<br>You can customize your task notification settings anytime from the <a target=\"_blank\" href=\"{webAppUrl}\">user notifications area</a> in Drata.", "btnText": "View task", "buttonNotWorking": "Button not working? Paste the following link into your browser:", "ctaUrl": "/workspaces/{workspaceId}/tasks?{urlParams}"}, "upcomingTasksSummaryEmail": {"subject": "You have upcoming tasks due this week in Drata", "title": "You have {tasksCount} tasks due this week", "paragraphOne": "<strong>Name:</strong> {taskName}<span style=\"{descriptionStyle}\"><br><strong>Description:</strong> {description}</span><br><strong>Owner(s):</strong> {owners}<br><strong>Due date:</strong> {dueDate}<span style=\"{style}\"><br><strong>Mapped {mappedObject}:</strong> {objectInfo}</span><br/>", "paragraphTwo": "+ {remainingTaskCount} more upcoming {taskPluralization}", "paragraphThree": "Note: These tasks are across multiple workspaces. The button below will take you to the tasks in one of your workspaces.", "paragraphBelowCtaBtn": "<br>You can customize your task notification settings anytime from the <a target=\"_blank\" href=\"{webAppUrl}\">user notifications area</a> in Drata.", "btnText": "View tasks", "buttonNotWorking": "Button not working? Paste the following link into your browser:", "ctaUrl": "/workspaces/{workspaceId}/tasks?{urlParams}"}}