/**
 * Private ENUM: This enum was found to be used only in this repository during https://drata.atlassian.net/browse/ENG-60010.
 * Move to shared package in https://github.com/drata/platform/tree/release/packages/enums, if sharing is required.
 **/
export enum FileValidationIssue {
    INVALID_FILE_EXTENSION = 'INVALID_FILE_EXTENSION',
    INVALID_FILE_SIZE = 'INVALID_FILE_SIZE',
    INVALID_FILE_FORMAT = 'INVALID_FILE_FORMAT',
    MISSING_COLUMNS = 'MISSING_COLUMNS',
    MISSING_REQUIRED_VALUES = 'MISSING_REQUIRED_VALUES',
    DUPLICATED_CODES = 'DUPLICATED_CODES',
    INVALID_DATA = 'INVALID_DATA',
    INVALID_FIELD_SCHEMA = 'INVALID_FIELD_SCHEMA',
    DUPLICATED_NAMES = 'DUPLICATED_NAMES',
    NO_RECORDS = 'NO_RECORDS',
    EMPTY_ROW = 'EMPTY_ROW',
    DUPLICATED_REQUIREMENT = 'DUPLICATED_REQUIREMENT',
    CONTROL_NOT_ENABLED = 'CONTROL_NOT_ENABLED',
    MAX_LENGTH_EXCEEDED = 'MAX_LENGTH_EXCEEDED',
    DUPLICATED_REQUEST_IDS = 'DUPLICATED_REQUEST_IDS',
    DUPLICATED_TITLES = 'DUPLICATED_TITLES',
    DUPLICATED_CUSTOMER_REQUESTS = 'DUPLICATED_CUSTOMER_REQUESTS',
    DUPLICATED_CUSTOMER_REQUEST_TITLES = 'DUPLICATED_CUSTOMER_REQUEST_TITLES',
    MAX_ROWS_EXCEEDED = 'MAX_ROWS_EXCEEDED',
    DUPLICATED_ROWS = 'DUPLICATED_ROWS',
}
