/**
 * Private ENUM: This enum was found to be used only in this repository during https://drata.atlassian.net/browse/ENG-60010.
 * Move to shared package in https://github.com/drata/platform/tree/release/packages/enums, if sharing is required.
 **/
export enum FeatureAnnouncementDismissalType {
    TRUST_CENTER = 1,
    EXTERNAL_POLICIES_BAMBOO_HR = 2,
    EXTERNAL_POLICIES_CONFLUENCE = 3,
    RISK_TAB = 4,
    ADD_VENDOR_RISK_MODAL = 5,
    SELF_SERVICE_ONBOARDING = 6,
    YES_VENDORS_SSO = 7,
    ENABLE_AI = 8,
    WELCOME_AI = 9,
    E<PERSON>ER<PERSON><PERSON>_POLICIES_NOTION = 10,
    CONDUCT_SECURITY_REVIEW = 11,
    SECURITY_REVIEW_OBSERVATION_CARD = 12,
    SECURITY_QUESTIONNAIRES_SETUP = 13,
    TRUST_CENTER_PURCHASED = 14,
    WELCOME_EXPERIENCE_ACTIVITY = 15,
    PINNED_INTRO_EVIDENCE_LIBRARY = 16,
    PINNED_INTRO_ASSETS = 17,
    PINNED_INTRO_AUDIT_HUB = 18,
    SOC_2_AI = 19,
    PINNED_INTRO_PERSONNEL = 20,
    PINNED_INTRO_CONNECTIONS = 21,
    PINNED_INTRO_TASKS = 22,
    MONITOR_NEW_COMMENT_NOTIFICATION = 23,
    MONITOR_NEW_TICKET_NOTIFICATION = 24,
    KNOWLEDGE_BASE_FIRST_VISIT = 25,
    SCHEDULE_QUESTIONNAIRE_DISMISSAL_CARD = 26,
    PINNED_INTRO_EVENT_TRACKING = 27,
    PINNED_INTRO_MONITORING = 28,
    PINNED_INTRO_ACCESS_REVIEW = 29,
    PINNED_INTRO_VENDORS = 30,
    PINNED_INTRO_RISK_MANAGEMENT = 31,
    PINNED_INTRO_RISK_ASSESSMENT = 32,
    PINNED_INTRO_CONTROLS = 33,
    PINNED_INTRO_VULNERABILITIES = 34,
    PINNED_INTRO_POLICIES = 35,
    PINNED_INTRO_FRAMEWORKS = 36,
    PINNED_INTRO_DASHBOARD = 38,
    VENDORS_INTRO_TRUST_CENTER_DOCUMENTS = 39,
    VRM_AGENT_INITIAL_BANNER = 40,
    VRM_AGENT_ASSESSMENT_BANNER = 41,
    VRM_AGENT_WORKFLOW_WITH_DOCUMENTS_BANNER = 42,
    VRM_AGENT_WORKFLOW_WITHOUT_DOCUMENTS_BANNER = 43,
    VRM_AGENT_SAFEBASE_NO_SECURITY_REVIEW_BANNER = 44,
    VRM_AGENT_SAFEBASE_REVIEW_IN_PROGRESS_BANNER = 45,
}
