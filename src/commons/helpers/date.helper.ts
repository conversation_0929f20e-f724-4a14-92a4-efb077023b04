import { EmailFrequency } from '@drata/enums';
import { UserFeature } from 'app/feature-toggling/entities/user-feature.entity';
import { InvalidArgumentException } from 'commons/exceptions/invalid-argument.exception';
import { FileNameDateOptions } from 'commons/types/file-name-date-options.type';
import { isNil, isString, parseInt } from 'lodash';
import moment from 'moment';
import { Between, FindOperator, LessThanOrEqual, MoreThanOrEqual } from 'typeorm';

/**
 *
 */
declare global {
    /**
     *
     */
    interface Date {
        /**
         *
         */
        toISO8601String(): string;
    }
}

/**
 *
 */
Date.prototype.toISO8601String = function (): string {
    // return the iso string split at T
    return convertToISO8601String(this);
};

export const EVENT_TIME_FORMAT = 'MMM DD, YYYY @ h:mm:ss A UTC';
export const ONE_DAY = 1;
export const ONE_WEEK = 7;
const MONTH_DAY_YEAR = 'MM/DD/YYYY';

/**
 * Convert date object into date string in ISO8601 format
 * @param date Date object
 * @returns Date in ISO8601 format
 */
export function convertToISO8601String(date: Date): string {
    return date.toISOString().split('T')[0];
}

/**
 * Safely format a Date to ISO string. Returns '' for null/undefined, non-Date, or Invalid Date.
 */
export function safeDateToISOString(date: Date | null | undefined): string {
    if (isNil(date) || !(date instanceof Date) || isNaN(date.getTime())) {
        return '';
    }
    return date.toISOString();
}

/**
 * Checks if the value is a valid Date/DateTime
 * @param value
 * @returns
 */
export function isValidDate(value: string | null | undefined): boolean {
    // if value is just a number (Epoch Time) - bail out
    if (!Number.isNaN(Number(value))) {
        return false;
    }

    // if value is null/undefined - bail out - JS will think this is Epoch Time 0 (1970)
    if (isNil(value)) {
        return false;
    }

    try {
        // make it a date object
        const date = new Date(value);
        // if we get back Epoch Time (number), then all good, otherwise there was an issue
        return !Number.isNaN(date.getTime());
    } catch (error) {
        return false;
    }
}

/**
 * Retrieves the current year.
 * @returns {number} - The current year as a four-digit number.
 */
export function currentYear(): number {
    // get the full year
    return new Date().getFullYear();
}

/**
 * return a moment in time (UTC)
 * then strip the time values.
 */
export function momentWithoutTime(date?: string): Date {
    // get the timestamp
    const timeStamp = date ? normalizeDate(date) : new Date();
    // remove the time
    return new Date(timeStamp.getFullYear(), timeStamp.getMonth(), timeStamp.getDate());
}

/**
 * Checks if a string is a valid date.
 * @param {string} date - The date string to validate.
 * @returns {boolean} - True if the string represents a valid date, false otherwise.
 */
export function isDate(date: string): boolean {
    return Date.parse(date) > 0;
}

/**
 *
 * @param {string} options Optional FileNameDateOptions - date string in ISO format (e.g. 2021-01-20)
 * @returns {string}
 */
export function fileNameDate(options?: FileNameDateOptions): string {
    const { date, format } = options || {};
    // set the current date
    const fileDate = date ? normalizeDate(date) : new Date();
    // get the year
    const year = fileDate.getFullYear();
    // get the month
    const month = (1 + fileDate.getMonth()).toString().padStart(2, '0');
    // get the day
    const day = fileDate.getDate().toString().padStart(2, '0');
    // return formatted string here
    if (format) {
        return moment(fileDate).format(format);
    }
    return month + day + year;
}

/**
 * Determines if a date is within the past twelve months.
 * @param {Date} date - The date to check.
 * @returns {boolean}
 */
export function dateIsWithinTwelveMonths(date: Date): boolean {
    if (isNil(date)) {
        return false;
    }

    const dateDiff = moment().diff(moment(date, 'YYYY-MM-DD'), 'months');
    return dateDiff >= 0 && dateDiff < 12;
}

/**
 * Determines if a date falls within the last three months.
 * @param {Date} date - The date to evaluate.
 * @returns {boolean}
 */
export function dateIsWithinThreeMonths(date: Date): boolean {
    if (isNil(date)) {
        return false;
    }

    const dateDiff = moment().diff(moment(date, 'YYYY-MM-DD'), 'months');
    return dateDiff >= 0 && dateDiff < 3;
}

/**
 * Calculates the human-readable time difference between two dates.
 * @param {Date} start - The start date. Defaults to current date/time if not provided.
 * @param {Date} end - The end date. Defaults to current date/time if not provided.
 * @returns {string}
 */
export function timeDiff(start = new Date(), end = new Date()): string {
    return moment.duration(moment(end).diff(moment(start))).humanize();
}

/**
 * Checks if an expiration date has passed.
 * @param {Date} expirationDate - The expiration date to check.
 * @param {boolean} [inclusive=false] - If true, considers the expiration date itself as expired.
 */
export function hasExpired(expirationDate: Date, inclusive = false): boolean {
    // default the expired flag
    let expired = false;
    // sanity check if there's an expiration date
    if (!isNil(expirationDate)) {
        // get timestamp moment from expiration date
        const expiresMoment = moment.utc(expirationDate);
        // get current timestamp moment
        let now = moment().utc();
        if (inclusive) {
            const currentDate = new Date(new Date().toDateString());
            now = moment.utc(currentDate);
        }
        // sanity check if the date has expired
        if (now.isAfter(expiresMoment)) {
            // set flag
            expired = true;
        }
    }
    // return the expired flag here
    return expired;
}

/**
 * Determines if a given date string represents a future date.
 * @param {string} date - The date string to evaluate. Expected in 'YYYY-MM-DD' format.
 * @returns {boolean}
 */
export function isFutureDateString(date: string): boolean {
    const today = moment().format('YYYY-MM-DD');
    return moment(date, 'YYYY-MM-DD').isValid() && moment(date).isAfter(today);
}

/**
 * Determines if a given date string represents a past date.
 * @param {string} date - The date string to evaluate. Expected in 'YYYY-MM-DD' format.
 * @returns {boolean}
 */
export function isPastDateString(date: string): boolean {
    const today = moment().format('YYYY-MM-DD');
    return moment(date, 'YYYY-MM-DD').isValid() && moment(date).isBefore(today);
}

/**
 * Checks if a given date string is up to and including today's date.
 * @param {string} date - The date string to check. Expected in 'YYYY-MM-DD' format.
 * @returns {boolean}
 */
export function isDateUpToToday(date: string): boolean {
    const today = moment().format('YYYY-MM-DD');
    return moment(date, 'YYYY-MM-DD').isValid() && moment(date, 'YYYY-MM-DD').isSameOrBefore(today);
}

/**
 * Checks if the first date comes before the second date
 * @param {string} firstDate
 * @param {string} secondDate
 * @returns {boolean}
 */
export function isFirstDateBeforeSecond(firstDate: string, secondDate: string): boolean {
    // Compare the dates
    return (
        moment(firstDate, 'YYYY-MM-DD').isValid() &&
        moment(secondDate, 'YYYY-MM-DD').isValid() &&
        moment(firstDate, 'YYYY-MM-DD').isBefore(moment(secondDate, 'YYYY-MM-DD'))
    );
}

/**
 * Returns the today date in the ISO format without the time
 */
export function getTodayDateISOString(): string {
    const today = new Date();
    return today.toISOString().split('T')[0];
}

/**
 * Returns the today date midnight in the ISO format without the time
 */
export function getTodayDateMidnightISOString(): string {
    const today = new Date(new Date().setHours(0, 0, 0, 0));
    return today.toISOString().split('T')[0];
}

/**
 *
 * @param expirationDate
 */
export function expiresSoon(expirationDate: Date, timeFrame: number, inclusive = false): boolean {
    // default the expired flag
    let isExpiringSoon = false;
    // sanity check if there's an expiration date
    if (!isNil(expirationDate)) {
        // get timestamp moment from expiration date
        const expiresMoment = moment.utc(expirationDate);
        // get date starting at noon
        const expiresSoonDate = new Date(
            // Cast to string to remove the time stamp to set time at 0:00:00
            new Date(
                // Create new date with substrated days
                new Date(expirationDate).setDate(expirationDate.getDate() - timeFrame),
            ).toDateString(),
        );
        const expiresSoonMoment = moment.utc(expiresSoonDate);
        // get current timestamp moment
        let now = moment().utc();
        if (inclusive) {
            // Substract one day to match the between comparison for inclusie date
            const currentDate = new Date(
                new Date(new Date().setDate(new Date().getDate() - 1)).toDateString(),
            );
            now = moment.utc(currentDate);
        }

        // sanity check if the date has expired
        if (now.isBetween(expiresSoonMoment, expiresMoment)) {
            // set flag
            isExpiringSoon = true;
        }
    }
    // return the expired flag here
    return isExpiringSoon;
}

/**
 *
 * @param {string} date Date only string in ISO format (e.g. 2021-01-20)
 * @returns {Date}
 */
export function normalizeDate(date?: string): Date {
    let newDate: Date;

    if (isNil(date)) {
        newDate = new Date();
    } else {
        // a new Date with date only in ISO format (with hyphens) is treated as UTC,
        // any other format without timestamp/timezone is parsed as local time instead
        newDate = new Date(date.replace(/-/g, '/'));
    }

    // normalize time in dates
    newDate.setHours(0, 0, 0, 0);

    return newDate;
}

/**
 *
 * @param {string} date Date only string in ISO format (e.g. 2021-01-20)
 * @returns {number}
 */
export function normalizeDateInTime(date?: string): number {
    return normalizeDate(date).getTime();
}

/**
 *
 * @param {string} date
 * @returns {string}
 */
export function getLocalFormatedDate(date: string, format = 'MMM D, YYYY'): string {
    // sanity check
    if (!isString(date)) {
        return '';
    }
    return moment(new Date(date)).local().format(format);
}

export function getUtcFormatedDate(dateString: string, useLocal = false, separator = '-') {
    const date = moment.utc(dateString);

    if (!date.isValid()) {
        return separator;
    }

    return useLocal ? date.local().format('MMM D, YYYY') : date.format('MMM D, YYYY');
}

/**
 *
 * @param {string} date Date only string in ISO format (e.g. 2021-01-20)
 * @returns {string}
 */
export function formatISODateStringToMonthDayYear(date: string): string {
    // eslint-disable-next-line prefer-const
    let [year, month, day] = date.split('-');
    month = month.replace(/^0+/, '');
    day = day.replace(/^0+/, '');

    return `${month}/${day}/${year}`;
}

/**
 * Determines if a given date falls on a Monday.
 * @param {Date} day - The date to check. Defaults to the current date if not provided.
 * @returns {boolean} - True if the specified date is a Monday, false otherwise.
 */
export function isMonday(day = new Date()): boolean {
    return moment(day).get('day') === 1;
}

/**
 * Adds a specified number of milliseconds to the current date and time.
 * @param {number} time - The number of milliseconds to add.
 * @returns {Date} - The new date and time after adding the specified milliseconds.
 */
export function AddMilliSecondsToActualDateTime(time: number): Date {
    const currentDate = new Date().getTime();
    const addMlSeconds = 1000 * time;
    return new Date(currentDate + addMlSeconds);
}

/**
 * Adds a specified number of days to the current date.
 * @param {number} days - The number of days to add.
 * @returns {Date} - The new date after adding the specified number of days.
 */
export function addDaysToActualDate(days: number): Date {
    const currentDate = new Date();
    const newDate = currentDate.setDate(currentDate.getDate() + days);
    return new Date(newDate);
}

export function subtractDaysToDate(days: number, date: Date = new Date()): Date {
    const momentDate = moment(date);
    return momentDate.subtract(days, 'days').utcOffset(0, true).toDate();
}

/**
 * Checks if a date is within a specified number of hours from the current time.
 * @param {Date} date - The date to compare with the current time.
 * @param {number} hours - The number of hours to check within.
 * @returns {boolean} - True if the difference between the given date and the current time is less than the specified hours, false otherwise.
 */
export function isWithinHours(date: Date, hours: number): boolean {
    if (isNil(date)) {
        return false;
    }
    const now = new Date();
    const msBetweenDates = Math.abs(date.getTime() - now.getTime());
    const hoursBetweenDates = msBetweenDates / (60 * 60 * 1000);
    return hoursBetweenDates < hours;
}

/**
 * Checks if a date is within a specified number of minutes in the FUTURE.
 * This function only returns true for dates that are in the future and within the specified window.
 * Dates in the past will return false.
 *
 * @param {Date} date - The date to compare with the current time.
 * @param {number} minutes - The number of minutes to check within.
 * @returns {boolean} - True if the date is in the future and within the specified minutes, false otherwise.
 */
export function isWithinMinutes(date: Date | undefined | null, minutes: number): boolean {
    if (isNil(date)) {
        return false;
    }
    const now = new Date();
    // Calculate time difference (positive = future, negative = past)
    const msBetweenDates = date.getTime() - now.getTime();
    const minutesBetweenDates = msBetweenDates / (60 * 1000);

    // Only return true if date is in the future (positive) and within the specified minutes
    return minutesBetweenDates > 0 && minutesBetweenDates < minutes;
}

/**
 * Returns a Date object representing the end of the day (23:59:59) in UTC for a given date.
 * @param {Date|string} date - The date, either as a Date object or a string, to set to the end of the day.
 * @returns {Date} - The adjusted Date object set to 23:59:59 UTC of the given date.
 */
export function getEndOfDayUTCDateTime(date: Date | string): Date {
    let dateTime: Date;

    if (typeof date === 'string') {
        dateTime = new Date(date);
    } else {
        dateTime = date;
    }

    dateTime.setUTCHours(23, 59, 59);

    return dateTime;
}

/**
 * Returns a new Date object representing a date that is a specified number of days before the given date.
 * @param {number} days - The number of days to subtract from the date.
 * @param {Date} date - The initial date. Defaults to the current date if not provided.
 * @returns {Date} - The new Date object representing the date after subtracting the specified number of days.
 */

export function getDateBySubstractedDays(days: number, date = new Date()): Date {
    // Avoid mutate the date passed as a argument
    const copyDate = new Date(date);
    copyDate.setDate(copyDate.getDate() - days);

    return copyDate;
}

export function isTimeToSendEmail(
    userFeature: UserFeature,
    time: Date,
    hourOffset: number,
): boolean {
    if (isNil(userFeature.lastSent)) {
        return true;
    }

    let isTime = false;

    switch (userFeature.value) {
        case EmailFrequency.DAILY.toString():
            const compareTime = moment(userFeature.lastSent).add(hourOffset, 'h');

            isTime = moment(time).isSameOrAfter(compareTime);

            break;
        case EmailFrequency.WEEKLY.toString():
            // Compare using the start of the day
            isTime =
                moment(time)
                    .startOf('day')
                    .diff(moment(userFeature.lastSent).startOf('day'), 'week') >= 1;

            break;
        default:
            //do not send if we don't know frequency
            break;
    }

    return isTime;
}

/**
 * Returns true if it's time to send an email based on the user's email frequency
 * @param userFeature The user's email frequency feature.
 * @param time Todays date.
 * @param hourOffset The hour offset for the comparison.
 */
export function isTimeToSendEmailDayCheck(
    userFeature: UserFeature,
    time: Date,
    hourOffset: number,
): boolean {
    switch (userFeature.value) {
        case EmailFrequency.DAILY.toString():
            // If the user has never received an email, send one now.
            if (isNil(userFeature.lastSent)) {
                return true;
            }

            const compareTime = moment(userFeature.lastSent).add(hourOffset, 'h');
            return moment(time).isSameOrAfter(compareTime);

        case EmailFrequency.WEEKLY.toString():
            // Only send the email on Mondays.
            if (time.getDay() !== 1) {
                return false;
            }

            // If the user has never received an email, send one now.
            if (isNil(userFeature.lastSent)) {
                return true;
            }

            // Compare using the start of the day
            return (
                moment(time)
                    .startOf('day')
                    .diff(moment(userFeature.lastSent).startOf('day'), 'week') >= 1
            );

        default:
            // Do not send an email if the user's email frequency is unknown.
            return false;
    }
}

export function getEvidenceDateFormatted(date: string): string {
    const shortTime = new Intl.DateTimeFormat('en', {
        dateStyle: 'long',
    });
    return shortTime.format(new Date(date.replace('-', ',')));
}

export function getDateDiffAsHours(startTime: Date, endTime: Date): number {
    const duration = moment.duration(moment(endTime).diff(startTime));
    return Math.floor(duration.asHours());
}

export function isFutureDate(date: Date): boolean {
    if (!isNil(date)) {
        const dateMoment = moment(date);
        const now = moment();
        if (!now.isAfter(dateMoment, 'day') && !now.isSame(dateMoment, 'day')) {
            return true;
        }
    }

    return false;
}

export function getDateDiffAsDays(startTime: Date, endTime: Date): number {
    const duration = moment.duration(moment(endTime).diff(startTime));
    return Math.floor(duration.asDays());
}

export function getBeginningOfDayFromDateString(date: string): Date {
    return moment(date).utc().startOf('day').toDate();
}

export function getEndOfDayFromDateString(date: string): Date {
    return moment(date).utc().endOf('day').toDate();
}

export function getBeginningAndEndOfDayFromDateString(date: string): {
    start: Date;
    end: Date;
} {
    return {
        start: getBeginningOfDayFromDateString(date),
        end: getEndOfDayFromDateString(date),
    };
}

export function convertDateStringArrayToBeginningAndEndDates(dates: string[]) {
    if (dates.length < 1) {
        throw new Error('Dates must be an array of date strings in the format YYYY-MM-DD');
    }
    return dates.map(getBeginningAndEndOfDayFromDateString);
}

/**
 * March 1st 2024
 * @param date
 * @returns
 */
export function formatDateToReadable(date: Date): string {
    return moment(date).format('MMMM Do YYYY');
}

function formatDateStringToMonthDayYear(dateString: string): string {
    return moment(dateString).format(MONTH_DAY_YEAR);
}

export function dateRangeMonthDayYearFormat(firstDate: string, secondDate: string): string {
    if (!firstDate?.length || !secondDate?.length) {
        return '';
    }
    return `${formatDateStringToMonthDayYear(firstDate)} - ${formatDateStringToMonthDayYear(
        secondDate,
    )}`;
}

/**
 * This function receives a date string like 2023-03-23 and
 * converts it to Date object
 */
export function convertYearMonthDayStringToDate(dateString: string): Date {
    if (!isValidDate(dateString)) {
        throw new InvalidArgumentException('Date is invalid');
    }

    const [year, month, day] = dateString.split('-').map(num => parseInt(num));
    return new Date(year, month - 1, day);
}

export function getDateDifferenceIgnoringTime(date1: Date, date2: Date, utc = false) {
    const moment1 = utc ? moment.utc(date1) : moment(date1);
    const moment2 = utc ? moment.utc(date2) : moment(date2);

    return moment1.startOf('day').diff(moment2.startOf('day'), 'days');
}

export function convertDateStringToEpoch(date: string): number {
    return Date.parse(date);
}

export function getNextDay(date: string): string {
    const nextDay = new Date(date).getTime() + 24 * 60 * 60 * 1000;
    return convertToISO8601String(new Date(nextDay));
}

/**
 * Returns the appropriate TypeORM FindOperator based on the provided dates.
 *
 * @param startDate - The start date for the range (inclusive).
 * @param endDate - The end date for the range (inclusive).
 * @returns A TypeORM FindOperator or undefined if no dates are provided.
 */
export function getDateCondition(
    startDate?: Date | null,
    endDate?: Date | null,
): FindOperator<Date> | undefined {
    if (startDate && endDate) {
        return Between(startDate, endDate);
    }
    if (startDate) {
        return MoreThanOrEqual(startDate);
    }
    if (endDate) {
        return LessThanOrEqual(endDate);
    }
    return undefined;
}

/**
 * Parses a date string in various formats and returns a valid moment object
 * @param dateString The date string to parse
 * @param formats Optional array of specific formats to try (in order of preference)
 * @returns A valid moment object or null if parsing fails
 */
export function parseMultiFormatDate(dateString: string, formats?: string[]): moment.Moment | null {
    if (isNil(dateString) || !isString(dateString) || dateString.trim() === '') {
        return null;
    }

    // Default formats to try in order of preference
    const defaultFormats = [
        'YYYY-MM-DD HH:mm:ss', // ISO
        'YYYY-MM-DDTHH:mm:ss', // ISO8601 with T separator
        'YYYY-MM-DDTHH:mm:ssZ', // ISO8601 with timezone
        'DD/MM/YYYY', // European format
        'MM/DD/YYYY', // US format
        'YYYY-MM-DD', // ISO date only
        'DD-MM-YYYY', // European with dashes
        'MM-DD-YYYY', // US with dashes
        'DD.MM.YYYY', // European with dots
        'MM.DD.YYYY', // US with dots
        moment.ISO_8601, // Any ISO8601 format
    ];

    // Try each format until one works
    let date = moment(dateString, formats || defaultFormats, true);

    if (!date.isValid()) {
        // If strict parsing fails, try non-strict as last resort
        date = moment(dateString);

        if (!date.isValid()) {
            return null;
        }
    }

    return date;
}

export function parseToISOString(
    dateString: string,
    formats?: string[],
    fallbackToNow = false,
): string | null {
    const parsedDate = parseMultiFormatDate(dateString, formats);

    // If we couldn't parse and no fallback requested, return null
    if (!parsedDate && !fallbackToNow) {
        return null;
    }

    // If parsing failed but fallback is enabled, preserve previous behavior
    if (!parsedDate) {
        return moment().utc().toISOString();
    }

    // Determine if the original input or the parsed result implies a date-only value.
    // We consider it date-only when:
    // - The input string lacks a time component (no 'T' or ':') OR
    // - The parsed time components are all zero (00:00:00)
    const lacksExplicitTime = !/[T:]/.test(dateString);
    const hasZeroTime =
        parsedDate.hour() === 0 && parsedDate.minute() === 0 && parsedDate.second() === 0;

    if (lacksExplicitTime || hasZeroTime) {
        // Build ISO string explicitly to avoid any timezone conversions
        const ymd = parsedDate.format('YYYY-MM-DD');
        return `${ymd}T00:00:00.000Z`;
    }

    // Otherwise, preserve the parsed time and convert to UTC
    return parsedDate.utc().toISOString();
}

export function getSecondsSinceTime(startTimeNanoseconds: bigint): number {
    const currentTimeNanoseconds = process.hrtime.bigint();
    const nanosecondsPerSecond = 1000000000;
    return Number(currentTimeNanoseconds - startTimeNanoseconds) / nanosecondsPerSecond;
}

export function getMillisecondsSinceTime(startTimeNanoseconds: bigint): number {
    const currentTimeNanoseconds = process.hrtime.bigint();
    const nanosecondsPerMillisecond = 1000000;
    return Number(currentTimeNanoseconds - startTimeNanoseconds) / nanosecondsPerMillisecond;
}
