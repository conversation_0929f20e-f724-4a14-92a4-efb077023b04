import { AgentPlatform } from '@drata/enums';
import { BadRequestException, InternalServerErrorException, Type } from '@nestjs/common';
import { Account } from 'auth/entities/account.entity';
import { isUtf8 } from 'buffer';
import { ResponseDto } from 'commons/dtos/response.dto';
import { FileValidationType } from 'commons/enums/file-validation-type.enum';
import { LibraryDocumentVersionType } from 'commons/enums/library-document-version-type.enum';
import { UploadType } from 'commons/enums/upload-type.enum';
import { getFileNameFromPath } from 'commons/helpers/file.helper';
import { FileTypes } from 'commons/maps/file-extensions-and-mime-types-validation.map';
import { FileBufferType, FileBufferWithEvidenceType } from 'commons/types/file-buffer.type';
import { SymlinkType } from 'commons/types/symlink.type';
import config from 'config';
import stringify from 'csv-stringify/lib/sync';
import { IStorable } from 'dependencies/archiver/interfaces/storable.interface';
import { Downloader } from 'dependencies/downloader/downloader';
import { DownloadStreamDetails } from 'dependencies/downloader/types/download-stream-details.type';
import { DownloaderPayloadType } from 'dependencies/downloader/types/downloader-payload.interface';
import { Uploader } from 'dependencies/uploader/uploader';
import { find, first, isEmpty, isNil, replace } from 'lodash';
import moment from 'moment';
import { basename, extname } from 'path';
import { PassThrough, Readable } from 'stream';
import archiver = require('archiver');

const emptyCsvData = {
    'Account Id': 'N/A',
    'Account Alias': 'N/A',
    Personnel: '',
    Email: null,
    'Initial Job Title': 'N/A',
    'Job Title': 'N/A',
    'Initial Access Data': 'N/A',
    'Access Data': 'N/A',
    Groups: 'N/A',
    'Employment Status': 'N/A',
    'Review Status': 'N/A',
    'Reviewed At': 'N/A',
    'Completed by': 'N/A',
    Notes: 'N/A',
};

export function createCsvBuffer(
    data: any[],
    target: Type<ResponseDto>,
    ignoreEmpty?: boolean,
    additionalData?: any,
): Buffer | undefined {
    const list: Record<string, unknown>[] = [];
    if (!isEmpty(data) || ignoreEmpty) {
        data.forEach((value: any) => {
            const curatedDto = curate(value, target, additionalData);
            list.push(curatedDto as unknown as Record<string, unknown>);
        });
        if (isEmpty(data)) {
            list.push(emptyCsvData);
        }
        return Buffer.from(
            stringify(list, {
                header: true,
                quoted: true,
            }),
            'utf8',
        );
    }
}

export function getCsvHeaderBuffer(data: Buffer): Buffer {
    return Buffer.from(data.toString().split('\n')[0], 'utf8');
}

export function mapFilesToPrefixWithEvidenceType(
    files: FileBufferType[],
    prefix: string,
    evidenceVersionType: LibraryDocumentVersionType,
): FileBufferWithEvidenceType[] {
    return files.map(({ stream, filename }) => ({
        stream,
        filename: `${prefix}/${filename}`,
        evidenceVersionType,
    }));
}

export function hasTestEvidenceType(
    x: DownloadStreamDetails | FileBufferWithEvidenceType,
): x is FileBufferWithEvidenceType {
    return !!x && typeof x === 'object' && 'evidenceVersionType' in x;
}

export function mapFilesToPrefix(files: FileBufferType[], prefix: string): FileBufferType[] {
    return files.map<FileBufferType>(({ stream, filename }) => ({
        stream: stream,
        filename: `${prefix}/${filename}`,
    }));
}

function appendFilesToArchive(filterFiles: FileBufferType[], archive: any) {
    filterFiles.forEach(({ stream, filename }) => {
        archive.append(stream, {
            name: filename,
        });
    });
}

export async function createZipBuffer(
    files: FileBufferType[],
    symlinks?: SymlinkType[],
): Promise<Buffer> {
    const filterFiles = files.filter(file => {
        return !isNil(file) && file.filename?.trim()?.length;
    });

    return new Promise<Buffer>((resolve, reject) => {
        const passThrough = new PassThrough({ writableObjectMode: true });
        let buffer = Buffer.from([]);
        const archive = archiver(config.get('archive.format'));
        archive.on('error', (error: string) => {
            reject(error);
        });
        archive.pipe(passThrough);
        appendFilesToArchive(filterFiles, archive);

        if (!isNil(symlinks) && !isEmpty(symlinks)) {
            for (const symlink of symlinks) {
                archive.symlink(symlink.link, symlink.original, 493);
            }
        }

        passThrough.on('data', buf => {
            buffer = Buffer.concat([buffer, buf]);
        });
        passThrough.on('end', () => {
            resolve(buffer);
        });
        archive.finalize();
    });
}

function addWindowsShortcut(archive: any, symlink: SymlinkType) {
    const originalFilename = symlink.original.replace(/\//g, '\\');
    const batFileBuffer = Buffer.from(`start "" "${originalFilename}"`, 'utf8');
    archive.append(batFileBuffer, {
        name: `${symlink.link}.bat`,
    });
}

export async function createZipBufferWithPassThrough(
    files: FileBufferType[],
    symlinks: SymlinkType[],
    passThrough: PassThrough,
    platform: AgentPlatform | null,
    onProgressEvent?: (progress: archiver.ProgressData) => void,
    onWarningEvent?: (error: archiver.ArchiverError) => void,
): Promise<void> {
    const filterFiles = files.filter(
        file => !isNil(file) && !isNil(file.filename) && !isNil(file.stream),
    );

    if (isEmpty(filterFiles)) {
        throw new BadRequestException('Cannot create zip buffer without files');
    }

    return new Promise<void>(async (resolve, reject) => {
        const archive = archiver(config.get('archive.format'));

        archive.on('warning', (error: archiver.ArchiverError) => {
            if (onWarningEvent) {
                onWarningEvent(error);
            } else {
                reject(error);
            }
        });

        archive.on('error', reject);
        archive.pipe(passThrough);

        appendFilesToArchive(filterFiles, archive);

        if (!isEmpty(symlinks)) {
            symlinks.forEach(symlink => {
                if (platform === AgentPlatform.WINDOWS) {
                    addWindowsShortcut(archive, symlink); // Assuming this is a custom function you have
                } else {
                    archive.symlink(symlink.link, symlink.original, 493);
                }
            });
        }

        if (onProgressEvent) archive.on('progress', onProgressEvent);

        passThrough.on('end', resolve);
        passThrough.on('error', reject);

        await archive.finalize();
    });
}

export async function getStreamsFromFiles(
    files: IStorable[],
    downloader: Downloader,
    useOriginalFilename?: boolean,
    bucket?: string,
): Promise<DownloadStreamDetails[]> {
    try {
        return await downloader.getReadStreams(
            files.map(({ file, name, version, originalFileName }) => {
                let fileName = name || '';

                if (useOriginalFilename && originalFileName) {
                    fileName = originalFileName;
                }
                // Set file name based on file
                if (file) {
                    //sanity check
                    fileName = fileName || basename(file);
                }
                // Set file name based on version
                if (!isNil(version)) {
                    fileName = `${basename(fileName, extname(fileName))} - v${version}`;
                }
                // Add file extension based on file
                if (file) {
                    const extension = extname(file) ?? '';
                    fileName += extension;
                }

                return {
                    name: fileName,
                    file,
                };
            }),
            isNil(bucket) ? config.get('aws.s3.appBucket') : bucket,
        );
    } catch (e) {
        return Promise.resolve([]);
    }
}

export async function getCSVStream(
    csvData: {
        name: string;
        createdAt: Date;
        url?: string;
        description?: string;
    }[],
    filename: string,
): Promise<FileBufferType> {
    let csv = 'Name,Creation Date,URL,Description\n';
    csvData.forEach(data => {
        csv += `${sanitizeCsvString(data.name)},${moment(data.createdAt).format('MMMM DD YYYY')},${
            data.url
        },${sanitizeCsvString(data.description || '')}\n`;
    });
    return {
        stream: Buffer.from(csv, 'utf8'),
        filename: filename,
    };
}

export async function getCSVCustomStream(
    csvData: {
        name: string;
        fileAt: string;
        renewalDate: Date;
        url?: string;
        description?: string;
    }[],
    filename: string,
): Promise<FileBufferType> {
    let csv = 'Name,Description,URL,Creation Date, Renewal Date\n';
    csvData.forEach(data => {
        csv += `${sanitizeCsvString(data.name)},${sanitizeCsvString(data.description || '')},${
            data.url
        },${moment(data.fileAt).format('MMMM DD YYYY')},${moment(data.renewalDate).format(
            'MMMM DD YYYY',
        )}\n`;
    });
    return {
        stream: Buffer.from(csv, 'utf8'),
        filename: filename,
    };
}

function curate(value: any, target: Type<ResponseDto>, additionalData?: any) {
    return !isNil(additionalData)
        ? new target().build(value, additionalData)
        : new target().build(value);
}

export function getFileBufferType(
    stream: Buffer,
    filePath: string,
    fileNameMessage: string,
): FileBufferType {
    const { fileName, fileExtension } = getFileNameFromPath(filePath);
    const newFileName = `${fileNameMessage}-${fileName}`;

    return {
        stream,
        filename: newFileName,
        fileType: first(
            find(FileTypes.get(FileValidationType.DOCUMENT), {
                extension: fileExtension,
            })?.mimeTypes,
        ),
    };
}

export function sanitizeCsvString(data: string): string {
    return replace(data, /[\n,]/g, ' ').trim();
}

/**
 *
 * See https://github.com/mafintosh/csv-parser?tab=readme-ov-file#byte-order-marks
 *
 * Some CSV files may be generated with, or contain a leading Byte Order Mark.
 * This may cause issues parsing headers and/or data from your file.
 *
 * From Wikipedia:
 * The Unicode Standard permits the BOM in UTF-8, but does not require nor recommend its use.
 * Byte order has no meaning in UTF-8.
 *
 * @param buffer
 * @returns a new buffer that points to the same memory as the passed in buffer
 */
export function stripBomBuffer(buffer: Buffer): Buffer | null {
    if (isEmpty(buffer)) {
        return null;
    }
    if (isUtf8(buffer) && buffer[0] === 0xef && buffer[1] === 0xbb && buffer[2] === 0xbf) {
        return buffer.subarray(3);
    }
    return buffer;
}

export async function uploadBuffer(
    account: Account,
    data: FileBufferType,
    uploadType: UploadType,
    downloader: Downloader,
    uploader: Uploader,
): Promise<DownloaderPayloadType> {
    const { filename, stream, fileType } = data;
    const oneHourInSeconds = 3600;

    if (isNil(fileType)) {
        throw new InternalServerErrorException('fileType is missing');
    }

    const { key } = await uploader.uploadPrivateFileFromBuffer(
        account.id,
        uploadType,
        stream as Buffer,
        filename,
        fileType,
    );

    return downloader.getDownloadUrl(key, {
        expirationTime: oneHourInSeconds,
        directDownload: true,
    });
}

export function getBufferFromStream(stream: Readable): Promise<Buffer> {
    return new Promise<Buffer>((resolve, reject) => {
        const chunks: Buffer[] = [];

        stream.on('data', (chunk: Buffer) => {
            chunks.push(chunk);
        });

        stream.once('end', () => {
            try {
                const buffer = Buffer.concat(chunks);
                resolve(buffer);
            } catch (error) {
                reject(error);
            }
        });

        stream.once('error', err => {
            reject(err);
        });
    });
}
