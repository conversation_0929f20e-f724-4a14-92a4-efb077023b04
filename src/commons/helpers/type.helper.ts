/**
 * Determine if an object is an instance of a type
 * @param unknownObject object whose type you wish to confirm
 * @param fieldName a field in the correct object type
 * @returns a boolean to indicate if the object is an instance of the given type
 */
export function instanceOfType<T>(unknownObject: any, fieldName: string): unknownObject is T {
    return fieldName in (unknownObject ?? {});
}

/**
 * Enforce exhaustive checks in switch statements.
 */
export function assertNever(x: never, message?: string): never {
    const details = typeof x === 'object' ? JSON.stringify(x) : String(x);
    throw new Error(message ?? `Unexpected value in exhaustive check: ${details}`);
}
