import { EmailFrequency } from '@drata/enums';
import { UserFeature } from 'app/feature-toggling/entities/user-feature.entity';
import {
    addDaysToActualDate,
    AddMilliSecondsToActualDateTime,
    convertDateStringArrayToBeginningAndEndDates,
    convertYearMonthDayStringToDate,
    dateIsWithinThreeMonths,
    dateIsWithinTwelveMonths,
    dateRangeMonthDayYearFormat,
    expiresSoon,
    getBeginningAndEndOfDayFromDateString,
    getBeginningOfDayFromDateString,
    getDateBySubstractedDays,
    getDateDiffAsDays,
    getDateDiffAsHours,
    getDateDifferenceIgnoringTime,
    getEndOfDayFromDateString,
    getEndOfDayUTCDateTime,
    getEvidenceDateFormatted,
    getLocalFormatedDate,
    getSecondsSinceTime,
    getUtcFormatedDate,
    hasExpired,
    isDateUpToToday,
    isFirstDateBeforeSecond,
    isFutureDateString,
    isMonday,
    isPastDateString,
    isTimeToSendEmail,
    isTimeToSendEmailDayCheck,
    isValidDate,
    isWithinHours,
    isWithinMinutes,
    parseMultiFormatDate,
    parseToISOString,
    subtractDaysToDate,
} from 'commons/helpers/date.helper';
import config from 'config';
import moment from 'moment';

import { safeDateToISOString } from 'commons/helpers/date.helper';

describe('DateHelper.isValidDate', () => {
    test('Empty String date should return false', () => {
        const date = '';
        expect(isValidDate(date)).toBe(false);
    });

    test('null date should return false', () => {
        const date = null;
        expect(isValidDate(date)).toBe(false);
    });

    test('undefined date should return false', () => {
        const date = undefined;
        expect(isValidDate(date)).toBe(false);
    });

    test('A random string should return false', () => {
        const date = 'Bob';
        expect(isValidDate(date)).toBe(false);
    });

    test('European (Day-Month-Year) Date Format string should return false', () => {
        const date = '31/01/2023';
        expect(isValidDate(date)).toBe(false);
    });

    test('European (Day-Month-Year) Date Time Format string should return false', () => {
        const date = '31/01/2023 15:00:00';
        expect(isValidDate(date)).toBe(false);
    });

    test('American (United States) Date Format string should return true', () => {
        const date = '01/31/2023';
        expect(isValidDate(date)).toBe(true);
    });

    test('American (United States) Date Time Format string should return true', () => {
        const date = '01/31/2023 15:00:00';
        expect(isValidDate(date)).toBe(true);
    });

    test('American (United States) Date Time Format (no trailing 0) string should return true', () => {
        const date = '1/31/2023 15:00:00';
        expect(isValidDate(date)).toBe(true);
    });

    test('Strict ISO8601 date should return true', () => {
        const date = '1978-02-24 09:00:00';
        expect(isValidDate(date)).toBe(true);
    });

    test('Non Strict ISO8601 date should return true', () => {
        const date = '1978-02-30 09:00:00';
        expect(isValidDate(date)).toBe(true);
    });

    test('RFC 3339 Date Format date should return true', () => {
        const date = '2023-01-31T12:45:00.000Z';
        expect(isValidDate(date)).toBe(true);
    });

    test('A real Leap Year date should return true', () => {
        const date = '2020-02-29 09:00:00';
        expect(isValidDate(date)).toBe(true);
    });

    test('A fake Leap Year date should return true (as it will carry it forward)', () => {
        const date = '2021-02-29 09:00:00';
        expect(isValidDate(date)).toBe(true);
    });
});

describe('DateHelper.dateIsWithinThreeMonths', () => {
    test('Date is null to Three Months from today', () => {
        expect(dateIsWithinThreeMonths(null)).toEqual(false);
    });

    test('Date is before Three Months from today', () => {
        const testBeforeDate = moment().subtract(4, 'months');
        expect(dateIsWithinThreeMonths(testBeforeDate.toDate())).toEqual(false);
    });

    test('Date is within Three Months of today', () => {
        const testWithinDate = moment().subtract(2, 'months');
        expect(dateIsWithinThreeMonths(testWithinDate.toDate())).toEqual(true);
    });

    test('Date is equal to today', () => {
        const testBeforeDate = moment();
        expect(dateIsWithinThreeMonths(testBeforeDate.toDate())).toEqual(true);
    });

    test('Date is after Three Months before today', () => {
        const testAfterDate = moment().add(4, 'months');
        expect(dateIsWithinThreeMonths(testAfterDate.toDate())).toEqual(false);
    });
});

describe('DateHelper.dateIsWithinTwelveMonths', () => {
    test('Date is null to Twelve Months from today', () => {
        expect(dateIsWithinTwelveMonths(null)).toEqual(false);
    });

    test('Date is before Twelve Months from today', () => {
        const testBeforeDate = moment().subtract(13, 'months');
        expect(dateIsWithinTwelveMonths(testBeforeDate.toDate())).toEqual(false);
    });

    test('Date is within Twelve Months of today', () => {
        const testWithinDate = moment().subtract(2, 'months');
        expect(dateIsWithinTwelveMonths(testWithinDate.toDate())).toEqual(true);
    });

    test('Date is equal to today', () => {
        const testBeforeDate = moment();
        expect(dateIsWithinTwelveMonths(testBeforeDate.toDate())).toEqual(true);
    });

    test('Date is after Twelve Months before today', () => {
        const testAfterDate = moment().add(13, 'months');
        expect(dateIsWithinTwelveMonths(testAfterDate.toDate())).toEqual(false);
    });
});

describe('DateHelper.isMonday', () => {
    it('should vaildate if a date is monday', () => {
        const date = new Date('July 11, 2022');

        expect(isMonday(date)).toBe(true);
    });

    it('should return false if the date is not monday', () => {
        const date = new Date('July 12, 2022');

        expect(isMonday(date)).toBe(false);
    });
});

describe('DateHelper.hasExpired', () => {
    describe('when the date is not inclusive', () => {
        it('should return true if the date is before today', () => {
            const date = new Date(new Date().setDate(new Date().getDate() - 1));
            expect(hasExpired(date)).toBe(true);
        });

        it('should return true if the date is today', () => {
            const date = new Date(new Date().toDateString());
            expect(hasExpired(date)).toBe(true);
        });

        it('should return false if the date is after today', () => {
            const date = new Date(new Date().setDate(new Date().getDate() + 1));
            expect(hasExpired(date)).toBe(false);
        });
    });

    describe('when the date is inclusive', () => {
        it('should return true if the date is before today', () => {
            const date = new Date(new Date().setDate(new Date().getDate() - 1));
            expect(hasExpired(date, true)).toBe(true);
        });

        it('should return false if the date is today', () => {
            const date = new Date(new Date().toDateString());
            expect(hasExpired(date, true)).toBe(false);
        });

        it('should return false if the date is after today', () => {
            const date = new Date(new Date().setDate(new Date().getDate() + 1));
            expect(hasExpired(date, true)).toBe(false);
        });
    });
});

describe('DateHelper.expiresSoon', () => {
    const timeframe = 30;
    describe('when the date is not inclusive', () => {
        it('should return true if today is within the timeframe', () => {
            const expirationDate = new Date(new Date().setDate(new Date().getDate() + 1));
            expect(expiresSoon(expirationDate, timeframe)).toBe(true);
        });

        it('should return false if today is today', () => {
            const expirationDate = new Date();
            expect(expiresSoon(expirationDate, timeframe)).toBe(false);
        });

        it('should return true if today is exact the timeframe', () => {
            const expirationDate = new Date(new Date().setDate(new Date().getDate() + timeframe));
            expect(expiresSoon(expirationDate, timeframe)).toBe(true);
        });

        it('should return false if today is after the timefrme', () => {
            const expirationDate = new Date(
                new Date().setDate(new Date().getDate() + 1 + timeframe),
            );
            expect(expiresSoon(expirationDate, timeframe)).toBe(false);
        });
    });

    describe('when the date is inclusive', () => {
        const isDateInclusive = true;
        it('should return true if today is within the timeframe', () => {
            const expirationDate = new Date(new Date().setDate(new Date().getDate() + 1));
            expect(expiresSoon(expirationDate, timeframe, isDateInclusive)).toBe(true);
        });

        it('should return true if today is today', () => {
            const expirationDate = new Date();
            expect(expiresSoon(expirationDate, timeframe, isDateInclusive)).toBe(true);
        });

        it('should return false if today is exact the timeframe', () => {
            const expirationDate = new Date(new Date().setDate(new Date().getDate() + timeframe));
            expect(expiresSoon(expirationDate, timeframe, isDateInclusive)).toBe(false);
        });

        it('should return false if today is after the timeframe', () => {
            const expirationDate = new Date(
                new Date().setDate(new Date().getDate() + 1 + timeframe),
            );
            expect(expiresSoon(expirationDate, timeframe, isDateInclusive)).toBe(false);
        });
    });
});

describe('DateHelper.AddMilliSecondsToActualDateTime', () => {
    test('Should return the date with one hour increment', () => {
        const timeToAdd = 3600;
        const date = new Date();
        expect(AddMilliSecondsToActualDateTime(timeToAdd).getTime()).toBeGreaterThan(
            date.getTime(),
        );
    });
});

describe('DateHelper.addDaysToActualDate', () => {
    test('Should return the date with 30 days increment', () => {
        const daysToAdd = 30;
        const date = new Date();
        expect(addDaysToActualDate(daysToAdd).getTime()).toBeGreaterThan(date.getTime());
    });
});

describe('DateHelper.subtractDaysToDate', () => {
    const daysToSubtract = 30;
    const date = new Date('01/31/2024');
    expect(subtractDaysToDate(daysToSubtract, date)).toEqual(new Date('2024-01-01'));
});

describe('DateHelper.getLocalFormatedDate', () => {
    test('Should return the date with the default format MMM D, YYYY', () => {
        const testDate = '2022-09-28 00:00';
        expect(getLocalFormatedDate(testDate)).toEqual('Sep 28, 2022');
    });
    test('Should return the date with format MM-DD-YYYY', () => {
        const testDate = '2022-09-28 00:00';
        const format = 'MM-DD-YYYY';
        expect(getLocalFormatedDate(testDate, format)).toEqual('09-28-2022');
    });
});

describe('DateHelper.isWithinHours', () => {
    test('Date is null', () => {
        expect(isWithinHours(null, 24)).toEqual(false);
    });

    test('Date is not within 24 hours', () => {
        const testBeforeDate = moment().subtract(25, 'hours');
        expect(isWithinHours(testBeforeDate.toDate(), 24)).toEqual(false);
    });

    test('Date is within 24 hours', () => {
        const testBeforeDate = moment().subtract(1, 'hours');
        expect(isWithinHours(testBeforeDate.toDate(), 24)).toEqual(true);
    });
});

describe('DateHelper.getEndOfDayUTCDateTime', () => {
    test('Should return the end of day date time given a date string', () => {
        const date = '2022-01-01';
        expect(getEndOfDayUTCDateTime(date)).toEqual(new Date('2022-01-01T23:59:59Z'));
    });

    test('Should return the end of day date time given a date', () => {
        const date = new Date('2022-01-01');
        expect(getEndOfDayUTCDateTime(date)).toEqual(new Date('2022-01-01T23:59:59Z'));
    });
});

describe('DateHelper.getDateBySubstractedDays', () => {
    test('Is less than Today subtracting 1 day', () => {
        expect(getDateBySubstractedDays(1).getTime()).toBeLessThan(new Date().getTime());
    });

    test('Is less than a Day before subtracting 90 days', () => {
        const currentDate = new Date().getTime();
        const daysToMls = 91 * 24 * 60 * 60 * 1000;
        expect(getDateBySubstractedDays(90).getTime()).toBeGreaterThan(
            new Date(currentDate - daysToMls).getTime(),
        );
    });

    test('Is less than a day ahead subtracting 90 days', () => {
        const currentDate = new Date().getTime();
        const daysToMls = 24 * 60 * 60 * 1000;
        expect(getDateBySubstractedDays(90).getTime()).toBeLessThan(
            new Date(currentDate + daysToMls).getTime(),
        );
    });
});

describe('DateHelper.isTimeToSendEmailDayCheck', () => {
    const offset = config.get('email.dailyHours');
    test('1 week difference but slightly longer', () => {
        const userFeature = {
            lastSent: moment('2024-11-04 03:03:03', 'YYYY-MM-DD HH:mm:ss').toDate(),
            value: EmailFrequency.WEEKLY.toString(),
        } as UserFeature;
        expect(
            isTimeToSendEmailDayCheck(
                userFeature,
                moment('2024-11-11 03:03:04', 'YYYY-MM-DD HH:mm:ss').toDate(),
                offset,
            ),
        ).toBe(true);
    });

    test('1 week difference but slightly shorter', () => {
        const userFeature = {
            lastSent: moment('2024-11-04 03:03:03', 'YYYY-MM-DD HH:mm:ss').toDate(),
            value: EmailFrequency.WEEKLY.toString(),
        } as UserFeature;
        expect(
            isTimeToSendEmailDayCheck(
                userFeature,
                moment('2024-11-11 03:03:02', 'YYYY-MM-DD HH:mm:ss').toDate(),
                offset,
            ),
        ).toBe(true);
    });

    test('1 week difference but slightly longer and not on Monday', () => {
        const userFeature = {
            lastSent: moment('2024-11-03 03:03:03', 'YYYY-MM-DD HH:mm:ss').toDate(),
            value: EmailFrequency.WEEKLY.toString(),
        } as UserFeature;
        expect(
            isTimeToSendEmailDayCheck(
                userFeature,
                moment('2024-11-10 03:03:04', 'YYYY-MM-DD HH:mm:ss').toDate(),
                offset,
            ),
        ).toBe(false);
    });
    test('1 week + 1 day difference and on Monday', () => {
        const userFeature = {
            lastSent: moment('2024-11-03 03:03:03', 'YYYY-MM-DD HH:mm:ss').toDate(),
            value: EmailFrequency.WEEKLY.toString(),
        } as UserFeature;
        expect(
            isTimeToSendEmailDayCheck(
                userFeature,
                moment('2024-11-11 03:03:04', 'YYYY-MM-DD HH:mm:ss').toDate(),
                offset,
            ),
        ).toBe(true);
    });
});

describe('DateHelper.isTimeToSendEmail', () => {
    const offset = config.get('email.dailyHours');

    test('Same exact day regular day', () => {
        const userFeature = {
            lastSent: new Date(),
            value: EmailFrequency.DAILY.toString(),
        } as UserFeature;
        expect(isTimeToSendEmail(userFeature, new Date(), offset)).toBe(false);
    });

    test('less than 1 day difference regular day', () => {
        const userFeature = {
            lastSent: new Date(),
            value: EmailFrequency.DAILY.toString(),
        } as UserFeature;
        expect(isTimeToSendEmail(userFeature, moment().add(12, 'hours').toDate(), offset)).toBe(
            false,
        );
    });

    test('1 day difference regular day', () => {
        const userFeature = {
            lastSent: new Date(),
            value: EmailFrequency.DAILY.toString(),
        } as UserFeature;
        expect(isTimeToSendEmail(userFeature, moment().add(1, 'day').toDate(), offset)).toBe(true);
    });

    test('1 day difference on different months', () => {
        const userFeature = {
            lastSent: moment('2023-01-31', 'YYYY-MM-DD').toDate(),
            value: EmailFrequency.DAILY.toString(),
        } as UserFeature;
        expect(
            isTimeToSendEmail(userFeature, moment('2023-02-01', 'YYYY-MM-DD').toDate(), offset),
        ).toBe(true);
    });

    test('1 day difference on different years', () => {
        const userFeature = {
            lastSent: moment('2022-12-31', 'YYYY-MM-DD').toDate(),
            value: EmailFrequency.DAILY.toString(),
        } as UserFeature;
        expect(
            isTimeToSendEmail(userFeature, moment('2023-01-01', 'YYYY-MM-DD').toDate(), offset),
        ).toBe(true);
    });

    test('Less than 1 week difference', () => {
        const userFeature = {
            lastSent: moment('2023-01-29', 'YYYY-MM-DD').toDate(),
            value: EmailFrequency.WEEKLY.toString(),
        } as UserFeature;
        expect(
            isTimeToSendEmail(userFeature, moment('2023-01-31', 'YYYY-MM-DD').toDate(), offset),
        ).toBe(false);
    });

    test('1 week difference', () => {
        const userFeature = {
            lastSent: moment('2023-01-01', 'YYYY-MM-DD').toDate(),
            value: EmailFrequency.WEEKLY.toString(),
        } as UserFeature;
        expect(
            isTimeToSendEmail(userFeature, moment('2023-01-09', 'YYYY-MM-DD').toDate(), offset),
        ).toBe(true);
    });

    test('Less than 1 week difference on different months', () => {
        const userFeature = {
            lastSent: moment('2023-01-28', 'YYYY-MM-DD').toDate(),
            value: EmailFrequency.WEEKLY.toString(),
        } as UserFeature;
        expect(
            isTimeToSendEmail(userFeature, moment('2023-02-01', 'YYYY-MM-DD').toDate(), offset),
        ).toBe(false);
    });

    test('1 week difference on different months', () => {
        const userFeature = {
            lastSent: moment('2023-01-25', 'YYYY-MM-DD').toDate(),
            value: EmailFrequency.WEEKLY.toString(),
        } as UserFeature;
        expect(
            isTimeToSendEmail(userFeature, moment('2023-02-01', 'YYYY-MM-DD').toDate(), offset),
        ).toBe(true);
    });

    test('Less than 1 week difference on different years', () => {
        const userFeature = {
            lastSent: moment('2022-12-31', 'YYYY-MM-DD').toDate(),
            value: EmailFrequency.WEEKLY.toString(),
        } as UserFeature;
        expect(
            isTimeToSendEmail(userFeature, moment('2023-01-03', 'YYYY-MM-DD').toDate(), offset),
        ).toBe(false);
    });

    test('1 week difference on different years', () => {
        const userFeature = {
            lastSent: moment('2022-12-31', 'YYYY-MM-DD').toDate(),
            value: EmailFrequency.WEEKLY.toString(),
        } as UserFeature;
        expect(
            isTimeToSendEmail(userFeature, moment('2023-01-09', 'YYYY-MM-DD').toDate(), offset),
        ).toBe(true);
    });

    test('1 week difference but slightly longer', () => {
        const userFeature = {
            lastSent: moment('2023-01-01 03:03:03', 'YYYY-MM-DD HH:mm:ss').toDate(),
            value: EmailFrequency.WEEKLY.toString(),
        } as UserFeature;
        expect(
            isTimeToSendEmail(
                userFeature,
                moment('2023-01-08 03:03:04', 'YYYY-MM-DD HH:mm:ss').toDate(),
                offset,
            ),
        ).toBe(true);
    });

    test('1 week difference but slightly shorter', () => {
        const userFeature = {
            lastSent: moment('2023-01-01 03:03:03', 'YYYY-MM-DD HH:mm:ss').toDate(),
            value: EmailFrequency.WEEKLY.toString(),
        } as UserFeature;
        expect(
            isTimeToSendEmail(
                userFeature,
                moment('2023-01-08 03:03:02', 'YYYY-MM-DD HH:mm:ss').toDate(),
                offset,
            ),
        ).toBe(true);
    });

    test('1 month difference', () => {
        const userFeature = {
            lastSent: moment('2023-01-10', 'YYYY-MM-DD').toDate(),
            value: EmailFrequency.WEEKLY.toString(),
        } as UserFeature;
        expect(
            isTimeToSendEmail(userFeature, moment('2023-02-10', 'YYYY-MM-DD').toDate(), offset),
        ).toBe(true);
    });

    test('1 year difference', () => {
        const userFeature = {
            lastSent: moment('2022-01-10', 'YYYY-MM-DD').toDate(),
            value: EmailFrequency.WEEKLY.toString(),
        } as UserFeature;
        expect(
            isTimeToSendEmail(userFeature, moment('2023-01-10', 'YYYY-MM-DD').toDate(), offset),
        ).toBe(true);
    });
});

describe('DateHelper.getEvidenceDateFormatted', () => {
    test('Expect to return a formatted date for evidence', () => {
        const date = '2022-07-19';
        expect(getEvidenceDateFormatted(date)).toMatch('July 19, 2022');
    });
});

describe('getDateDiffAsHours function', () => {
    test('getDateDiffAsHours negative', () => {
        const start = new Date('2022-12-02 23:00:23');
        const end = new Date('2022-12-02 12:00:23');
        const dateDifferenceAsHours = getDateDiffAsHours(start, end);
        expect(dateDifferenceAsHours).toBeLessThan(0);
    });
    test('getDateDiffAsHours 6 hours', () => {
        const start = new Date('2022-12-02 12:00:23');
        const end = new Date('2022-12-02 18:00:30');
        const dateDifferenceAsHours = getDateDiffAsHours(start, end);
        expect(dateDifferenceAsHours).toEqual(6);
    });
});

describe('getDateDiffAsDays function', () => {
    test('getDateDiffAsDays negative', () => {
        const start = new Date('2022-12-02 12:00:00');
        const end = new Date('2022-12-01 12:00:00');
        const dateDifferenceAsDays = getDateDiffAsDays(start, end);
        expect(dateDifferenceAsDays).toBeLessThan(0);
    });
    test('getDateDiffAsDays 365 days', () => {
        const start = new Date('2023-06-01 12:00:00');
        const end = new Date('2024-05-31 12:00:00');
        const dateDifferenceAsDays = getDateDiffAsDays(start, end);
        expect(dateDifferenceAsDays).toEqual(365);
    });
});

describe('DateHelper.getBeginningOfDayFromDateString', () => {
    it('should return the beginning of the day for a given date string', () => {
        const date = '2022-07-19';
        const beginningOfDay = getBeginningOfDayFromDateString(date);
        expect(beginningOfDay).toEqual(new Date('2022-07-19T00:00:00.000Z'));
    });
});

describe('DateHelper.getEndOfDayFromDateString', () => {
    it('should return the end of the day for a given date string', () => {
        const date = '2022-07-19';
        const endOfDay = getEndOfDayFromDateString(date);
        expect(endOfDay).toEqual(new Date('2022-07-19T23:59:59.999Z'));
    });
});

describe('getBeginningAndEndOfDayFromDateString', () => {
    it('should return the beginning and end of the day for a given date string', () => {
        const dateString = '2022-01-01';
        const { start, end } = getBeginningAndEndOfDayFromDateString(dateString);

        expect(start.toISOString()).toEqual('2022-01-01T00:00:00.000Z');
        expect(end.toISOString()).toEqual('2022-01-01T23:59:59.999Z');
    });
});

describe('convertDateStringArrayToBeginningAndEndDates', () => {
    it('should convert an array of date strings to an array of objects with start and end dates', () => {
        const dateStrings = ['2022-01-01', '2022-01-02'];
        const result = convertDateStringArrayToBeginningAndEndDates(dateStrings);

        expect(result).toEqual([
            {
                start: new Date('2022-01-01T00:00:00.000Z'),
                end: new Date('2022-01-01T23:59:59.999Z'),
            },
            {
                start: new Date('2022-01-02T00:00:00.000Z'),
                end: new Date('2022-01-02T23:59:59.999Z'),
            },
        ]);
    });

    it('should throw an error if the input array has less than one element', () => {
        const dateStrings = [];

        expect(() => convertDateStringArrayToBeginningAndEndDates(dateStrings)).toThrowError(
            'Dates must be an array of date strings in the format YYYY-MM-DD',
        );
    });
});

describe('DateHelper.isFirstDateBeforeSecond', () => {
    test('should return true when the first date is before the second date', () => {
        expect(isFirstDateBeforeSecond('2024-01-23', '2024-01-24')).toBe(true);
    });

    test('should return false when the first date is the same as the second date', () => {
        expect(isFirstDateBeforeSecond('2024-01-24', '2024-01-24')).toBe(false);
    });

    test('should return false when the first date is after the second date', () => {
        expect(isFirstDateBeforeSecond('2024-01-25', '2024-01-24')).toBe(false);
    });

    test('should return false for invalid first date', () => {
        expect(isFirstDateBeforeSecond('invalid-date', '2024-01-24')).toBe(false);
    });

    test('should return false for invalid second date', () => {
        expect(isFirstDateBeforeSecond('2024-01-23', 'invalid-date')).toBe(false);
    });

    test('should return false for both dates are null', () => {
        expect(isFirstDateBeforeSecond(null, null)).toBe(false);
    });

    test('should return false for empty first date', () => {
        expect(isFirstDateBeforeSecond('', '2024-01-24')).toBe(false);
    });

    test('should return false for empty second date', () => {
        expect(isFirstDateBeforeSecond('2024-01-23', '')).toBe(false);
    });
});

describe('DateHelper.isDateUpToToday', () => {
    test('should return true for a date that is today', () => {
        const date = moment().format('YYYY-MM-DD');
        expect(isDateUpToToday(date)).toBe(true);
    });

    test('should return true for a date in the past', () => {
        const date = '2023-12-31'; // A past date
        expect(isDateUpToToday(date)).toBe(true);
    });

    test('should return false for a date in the future', () => {
        const date = '2060-01-25'; // A future date
        expect(isDateUpToToday(date)).toBe(false);
    });

    test('should return false for an invalid date', () => {
        const date = 'invalid-date';
        expect(isDateUpToToday(date)).toBe(false);
    });

    test('should return false for an empty string', () => {
        const date = '';
        expect(isDateUpToToday(date)).toBe(false);
    });

    test('should return false for a null date', () => {
        const date = null;
        expect(isDateUpToToday(date)).toBe(false);
    });
});

describe('DateHelper.isFutureDateString', () => {
    test('should return true for a valid future date', () => {
        const futureDate = moment().add(1, 'days').format('YYYY-MM-DD');
        expect(isFutureDateString(futureDate)).toBe(true);
    });

    test("should return false for today's date", () => {
        const today = moment().format('YYYY-MM-DD');
        expect(isFutureDateString(today)).toBe(false);
    });

    test('should return false for a past date', () => {
        const pastDate = '2024-01-23'; // A past date
        expect(isFutureDateString(pastDate)).toBe(false);
    });

    test('should return false for an invalid date', () => {
        const invalidDate = 'invalid-date';
        expect(isFutureDateString(invalidDate)).toBe(false);
    });

    test('should return false for an empty string', () => {
        const emptyDate = '';
        expect(isFutureDateString(emptyDate)).toBe(false);
    });
});

describe('DateHelper.dateRangeMonthDayYearFormat', () => {
    test('should return a format of "FirstDate - SecondDate"', () => {
        const firstDate = '2024-02-01';
        const secondDate = '2024-03-01';

        expect(dateRangeMonthDayYearFormat(firstDate, secondDate)).toBe('02/01/2024 - 03/01/2024');
    });

    test('should return an empty string when passed invalid values"', () => {
        expect(dateRangeMonthDayYearFormat(null, null)).toBe('');
    });
});

describe('convertYearMonthDayStringToDate', () => {
    test('converts a valid date string to a Date object', () => {
        const dateString = '2023-05-21';
        const expectedDate = new Date(2023, 4, 21);
        const result = convertYearMonthDayStringToDate(dateString);
        expect(result).toEqual(expectedDate);
    });

    test('should throw error invalid string', () => {
        const dateString = 'invalid-date';
        expect(() => {
            convertYearMonthDayStringToDate(dateString);
        }).toThrow();
    });

    test('should throw error when empty date string', () => {
        const dateString = '';
        expect(() => {
            convertYearMonthDayStringToDate(dateString);
        }).toThrow();
    });
});

describe('DateHelper.getDateDifferenceIgnoringTime', () => {
    test('should properly return de difference of one day', () => {
        const firstDate = new Date('2024-01-01');
        const secondDate = new Date('2024-01-02');

        expect(getDateDifferenceIgnoringTime(firstDate, secondDate)).toBe(-1);
    });

    test('should properly return de difference of one day even with a time difference', () => {
        const firstDate = new Date('2024-01-02');
        firstDate.setHours(23, 59, 59);
        const secondDate = new Date('2024-01-01');
        secondDate.setHours(1, 59, 59);

        expect(getDateDifferenceIgnoringTime(firstDate, secondDate)).toBe(1);
    });

    test('should properly return a difference of 0 even with 0 UTC time', () => {
        const firstDate = new Date();
        firstDate.setHours(0, 0, 0, 0);
        firstDate.setUTCHours(0);

        const secondDate = new Date();
        secondDate.setHours(4, 59, 59, 0);

        expect(getDateDifferenceIgnoringTime(firstDate, secondDate, true)).toBe(0);
    });
});

describe('DateHelper.getUtcFormatedDate', () => {
    it('should return the date in UTC format', () => {
        const mockDate = '2024-12-01T00:00:00Z';
        const result = getUtcFormatedDate(mockDate);
        expect(result).toBe('Dec 1, 2024');
    });

    it('should return the date in UTC format when useLocal is true', () => {
        const mockDate = '2024-12-01T00:00:00Z';
        const result = getUtcFormatedDate(mockDate, true);
        const expectedDate = moment.utc(mockDate).local().format('MMM D, YYYY');
        expect(result).toBe(expectedDate);
    });

    it('should return the separator when the date is not valid', () => {
        const result = getUtcFormatedDate('invalid-date');
        expect(result).toBe('-');
    });

    it('should return a custom separator when the date is invalid', () => {
        const result = getUtcFormatedDate('invalid-date', false, '/');
        expect(result).toBe('/');
    });
});

describe('DateHelper.isPastDateString', () => {
    it('should return true for a past date', () => {
        expect(isPastDateString('2023-10-26')).toBe(true);
    });

    it("should return false for today's date", () => {
        const today = moment().format('YYYY-MM-DD');
        expect(isPastDateString(today)).toBe(false);
    });

    it('should return false for a future date', () => {
        const futureDate = moment().add(1, 'day').format('YYYY-MM-DD');
        expect(isPastDateString(futureDate)).toBe(false);
    });

    it('should return false for an invalid date string', () => {
        expect(isPastDateString('invalid-date')).toBe(false);
        expect(isPastDateString('2023-13-01')).toBe(false); // Invalid month
        expect(isPastDateString('2023-02-30')).toBe(false); // Invalid day for month
    });

    it('should return false for empty string', () => {
        expect(isPastDateString('')).toBe(false);
    });

    it('should return false for null', () => {
        expect(isPastDateString(null as any)).toBe(false);
    });

    it('should return false for undefined', () => {
        expect(isPastDateString(undefined as any)).toBe(false);
    });
});

describe('DateHelper.isWithinMinutes', () => {
    test('Date is null', () => {
        expect(isWithinMinutes(null, 5)).toEqual(false);
    });

    test('Date is not within 5 minutes (future - too far)', () => {
        const testFutureDate = moment().add(6, 'minutes');
        expect(isWithinMinutes(testFutureDate.toDate(), 5)).toEqual(false);
    });

    test('Date is within 5 minutes (future)', () => {
        const testFutureDate = moment().add(3, 'minutes');
        expect(isWithinMinutes(testFutureDate.toDate(), 5)).toEqual(true);
    });

    test('Date in the past should return false (not in future)', () => {
        const testPastDate = moment().subtract(7, 'minutes');
        expect(isWithinMinutes(testPastDate.toDate(), 10)).toEqual(false);
    });

    test('Date far in the past should return false', () => {
        const testPastDate = moment().subtract(11, 'minutes');
        expect(isWithinMinutes(testPastDate.toDate(), 10)).toEqual(false);
    });

    test('Date exactly now should return false (not in future)', () => {
        const fixedTime = new Date('2025-09-30T13:00:00.000Z');

        jest.useFakeTimers();
        jest.setSystemTime(fixedTime);

        // Same exact time - not in future
        expect(isWithinMinutes(fixedTime, 5)).toEqual(false);

        jest.useRealTimers();
    });
});

describe('DateHelper.parseMultiFormatDate', () => {
    test('should parse ISO format with time correctly', () => {
        const isoDate = '2024-04-05 21:51:23';
        const result = parseMultiFormatDate(isoDate);

        expect(result).not.toBeNull();
        if (result) {
            expect(result.isValid()).toBe(true);
            expect(result.format('YYYY-MM-DD HH:mm:ss')).toBe(isoDate);
        }
    });

    test('should parse European format (DD/MM/YYYY) correctly', () => {
        const europeanDate = '16/10/2020';
        const result = parseMultiFormatDate(europeanDate);

        expect(result).not.toBeNull();
        if (result) {
            expect(result.isValid()).toBe(true);
            expect(result.format('YYYY-MM-DD')).toBe('2020-10-16');
        }
    });

    test('should parse US format (MM/DD/YYYY) correctly', () => {
        const usDate = '10/16/2020';
        const result = parseMultiFormatDate(usDate);

        expect(result).not.toBeNull();
        if (result) {
            expect(result.isValid()).toBe(true);
            expect(result.format('YYYY-MM-DD')).toBe('2020-10-16');
        }
    });

    test('should parse ISO date-only format correctly', () => {
        const isoDateOnly = '2020-10-16';
        const result = parseMultiFormatDate(isoDateOnly);

        expect(result).not.toBeNull();
        if (result) {
            expect(result.isValid()).toBe(true);
            expect(result.format('YYYY-MM-DD')).toBe(isoDateOnly);
        }
    });

    test('should parse date with custom format correctly', () => {
        const customDate = '16.10.2020';
        const result = parseMultiFormatDate(customDate, ['DD.MM.YYYY']);

        expect(result).not.toBeNull();
        if (result) {
            expect(result.isValid()).toBe(true);
            expect(result.format('YYYY-MM-DD')).toBe('2020-10-16');
        }
    });

    test('should return null for invalid date formats', () => {
        const invalidDate = 'not-a-date';
        const result = parseMultiFormatDate(invalidDate);

        expect(result).toBeNull();
    });

    test('should return null for empty strings', () => {
        const emptyDate = '';
        const result = parseMultiFormatDate(emptyDate);

        expect(result).toBeNull();
    });

    test('should return null for null input', () => {
        const result = parseMultiFormatDate(null as unknown as string);

        expect(result).toBeNull();
    });

    test('should not return "Invalid date" for invalid inputs', () => {
        const invalidDate = 'not-a-date';
        const result = parseMultiFormatDate(invalidDate);

        // Should return null, not a moment object with "Invalid date"
        expect(result).toBeNull();

        // If we force moment to create an invalid date
        const invalidMoment = moment('invalid');
        expect(invalidMoment.isValid()).toBe(false);

        // Should never return such an object
        expect(result).not.toEqual(invalidMoment);
    });
});

describe('DateHelper.parseToISOString', () => {
    test('should convert valid date to ISO string', () => {
        const date = '16/10/2020';
        const result = parseToISOString(date);

        expect(result).toBe('2020-10-16T00:00:00.000Z');
    });

    test('should return null for invalid date when fallbackToNow is false', () => {
        const invalidDate = 'not-a-date';
        const result = parseToISOString(invalidDate, undefined, false);

        expect(result).toBeNull();
    });

    test('should return current date for invalid date when fallbackToNow is true', () => {
        const invalidDate = 'not-a-date';

        // Mock Date.now to return a fixed date
        const mockDate = new Date('2023-01-01T00:00:00Z');
        jest.spyOn(Date, 'now').mockImplementation(() => mockDate.getTime());

        const result = parseToISOString(invalidDate, undefined, true);

        expect(result).not.toBeNull();
        if (result) {
            expect(moment(result).isValid()).toBe(true);
            // Check that it has the correct ISO format
            expect(result.endsWith('Z')).toBe(true);
        }
    });

    test('should not return "Invalid date" string for invalid inputs when fallbackToNow is false', () => {
        const invalidDate = 'not-a-date';
        const result = parseToISOString(invalidDate, undefined, false);

        expect(result).toBeNull();
    });

    test('should not return "Invalid date" string for invalid inputs when fallbackToNow is true', () => {
        const invalidDate = 'not-a-date';
        const result = parseToISOString(invalidDate, undefined, true);

        expect(result).not.toBeNull();
        if (result) {
            expect(result).not.toBe('Invalid date');
            expect(moment(result).isValid()).toBe(true);
        }
    });
});

test('should normalize all supported date-only formats to UTC midnight', () => {
    const cases = [
        { input: '2020-10-16', expected: '2020-10-16' }, // YYYY-MM-DD
        { input: '16/10/2020', expected: '2020-10-16' }, // DD/MM/YYYY
        { input: '10/16/2020', expected: '2020-10-16' }, // MM/DD/YYYY
        { input: '16-10-2020', expected: '2020-10-16' }, // DD-MM-YYYY
        { input: '10-16-2020', expected: '2020-10-16' }, // MM-DD-YYYY
        { input: '16.10.2020', expected: '2020-10-16' }, // DD.MM.YYYY
        { input: '10.16.2020', expected: '2020-10-16' }, // MM.DD.YYYY
    ];

    for (const { input, expected } of cases) {
        const iso = parseToISOString(input);
        expect(iso).not.toBeNull();
        if (iso) {
            const m = moment.utc(iso);
            expect(m.isValid()).toBe(true);
            expect(m.hours()).toBe(0);
            expect(m.minutes()).toBe(0);
            expect(m.seconds()).toBe(0);
            expect(m.milliseconds()).toBe(0);
            expect(m.format('YYYY-MM-DD')).toBe(expected);
        }
    }
});

describe('DateHelper.getSecondsSinceTime', () => {
    test('should return a positive number', () => {
        const startTime = process.hrtime.bigint();
        expect(getSecondsSinceTime(startTime)).toBeGreaterThanOrEqual(0);
    });

    test('should return approximately the correct elapsed time', () => {
        const startTime = process.hrtime.bigint();

        // Sleep for 100ms
        const wait = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));
        return wait(100).then(() => {
            const elapsedSeconds = getSecondsSinceTime(startTime);
            // Should be approximately 0.1 seconds (with some tolerance)
            expect(elapsedSeconds).toBeGreaterThanOrEqual(0.09);
            expect(elapsedSeconds).toBeLessThanOrEqual(0.2);
        });
    });

    test('should return larger values for earlier start times', () => {
        const earlierTime = process.hrtime.bigint();

        // Sleep for a short time
        const wait = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));
        return wait(50).then(() => {
            const laterTime = process.hrtime.bigint();

            const earlierElapsed = getSecondsSinceTime(earlierTime);
            const laterElapsed = getSecondsSinceTime(laterTime);

            expect(earlierElapsed).toBeGreaterThan(laterElapsed);
        });
    });
});

describe('DateHelper.safeToISOString', () => {
    it('returns empty string for null', () => {
        expect(safeDateToISOString(null)).toBe('');
    });

    it('returns empty string for undefined', () => {
        expect(safeDateToISOString(undefined)).toBe('');
    });

    it('returns empty string for Invalid Date', () => {
        const invalidDate = new Date('0000-00-00' as any);
        expect(safeDateToISOString(invalidDate)).toBe('');
    });

    it('returns empty string for non-Date (string) values', () => {
        expect(safeDateToISOString('2024-01-01' as any)).toBe('');
    });

    it('formats a valid Date to ISO string', () => {
        const d = new Date(Date.UTC(2024, 0, 2, 3, 4, 5, 6));
        expect(safeDateToISOString(d)).toBe('2024-01-02T03:04:05.006Z');
    });
});
