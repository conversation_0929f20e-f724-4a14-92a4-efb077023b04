/**
 * UUID type for strict type checking of account IDs and other UUIDs
 * Matches the format: xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx
 */
export type AccountIdType = `${string}-${string}-${string}-${string}-${string}`;

// Simple runtime validator and type-narrowing helpers
const UUID_REGEX = /^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/;

export function isAccountIdType(val: unknown): val is AccountIdType {
    return typeof val === 'string' && UUID_REGEX.test(val);
}

/**
 * Convert an unknown string into a UUID type after validation.
 * Throws if the value is not a valid UUID.
 */
export function toAccountIdType(val: string): AccountIdType {
    if (!isAccountIdType(val)) {
        throw new Error(`Invalid UUID: ${val}`);
    }
    return val;
}

/**
 * Type for cache key account ID validation - either 'global' or a valid UUID
 * This ensures all cache keys are properly hash-tagged for Redis clustering
 */
export type AccountIdOrGlobal = 'global' | AccountIdType;

/**
 * Type guard to check if a value is AccountIdOrGlobal
 */
export function isAccountIdOrGlobal(val: unknown): val is AccountIdOrGlobal {
    return val === 'global' || isAccountIdType(val);
}

/**
 * Convert a string to AccountIdOrGlobal after validation
 * Throws if the value is neither 'global' nor a valid UUID
 */
export function toAccountIdOrGlobal(val: string): AccountIdOrGlobal {
    if (val === 'global') {
        return 'global';
    }
    if (!isAccountIdType(val)) {
        throw new Error(`Invalid account ID for cache key: ${val}. Must be 'global' or a valid UUID.`);
    }
    return val;
}
