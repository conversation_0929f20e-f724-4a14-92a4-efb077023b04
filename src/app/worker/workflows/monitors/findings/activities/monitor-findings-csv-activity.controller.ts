import { Injectable } from '@nestjs/common';
import { heartbeat } from '@temporalio/activity';
import { MonitorFindingsType } from 'app/monitors/enums/monitor-findings-type.enum';
import { MonitorsCoreService } from 'app/monitors/services/monitors-core.service';
import { User } from 'app/users/entities/user.entity';
import { ActivityNs } from 'app/worker/activity-ns.decorator';
import { Account } from 'auth/entities/account.entity';
import { UploadType } from 'commons/enums/upload-type.enum';
import { getBufferFromStream } from 'commons/helpers/buffer.helper';
import { WorkflowActivityController } from 'commons/services/workflow-activity-controller.service';
import { Downloader } from 'dependencies/downloader/downloader';
import { DownloaderPayloadType } from 'dependencies/downloader/types/downloader-payload.interface';
import { Uploader } from 'dependencies/uploader/uploader';
import { isNil, pick } from 'lodash';
import { PolloMessage } from 'pollo-logger/classes/pollo.message';

@Injectable()
export class MonitorFindingsCsvActivityController extends WorkflowActivityController {
    DEFAULT_EXPIRATION_TIME_IN_SECONDS: number = 3600;
    constructor(
        private readonly monitorsCoreService: MonitorsCoreService,
        private readonly uploader: Uploader,
        private readonly downloader: Downloader,
    ) {
        super();
    }

    @ActivityNs()
    async generateFindingsCsv(
        account: Account,
        user: User,
        testId: number,
        type: MonitorFindingsType,
        checkType?: string | null,
        workspaceId?: number,
    ): Promise<DownloaderPayloadType> {
        const onProgressFn = (details?: unknown) => heartbeat(details);

        this.logger.log(
            PolloMessage.msg('Starting monitor findings CSV generation')
                .setIdentifier({
                    user: pick(user, ['id', 'entryId', 'email']),
                    testId,
                    type,
                    checkType,
                })
                .setAccountId(account.id),
        );

        try {
            onProgressFn({ step: 'generating_csv', progress: 10 });

            const csvFile = await this.monitorsCoreService.generateMonitorTestReportCsv(
                account,
                testId,
                type,
                checkType,
                workspaceId,
            );

            if (isNil(csvFile)) {
                onProgressFn({ step: 'no_findings', progress: 100 });
                throw new Error('No findings found for CSV export');
            }

            this.logger.log(
                PolloMessage.msg('CSV file generated successfully')
                    .setIdentifier({
                        testId,
                        filename: csvFile.filename,
                        size: Buffer.isBuffer(csvFile.stream) ? csvFile.stream.length : undefined,
                    })
                    .setAccountId(account.id),
            );

            onProgressFn({ step: 'csv_generated', progress: 50 });

            const streamBuffer = Buffer.isBuffer(csvFile.stream)
                ? csvFile.stream
                : await getBufferFromStream(csvFile.stream);

            const uploadResult = await this.uploader.uploadPrivateFileFromBuffer(
                account.id,
                UploadType.MONITOR_FINDINGS,
                streamBuffer,
                csvFile.filename,
                'text/csv',
            );

            this.logger.log(
                PolloMessage.msg('CSV file uploaded to S3 successfully')
                    .setIdentifier({
                        testId,
                        s3Key: uploadResult.key,
                    })
                    .setAccountId(account.id),
            );

            onProgressFn({ step: 'uploaded_to_s3', progress: 80 });

            const downloadInfo = await this.downloader.getDownloadUrl(uploadResult.key, {
                expirationTime: this.DEFAULT_EXPIRATION_TIME_IN_SECONDS,
            });

            this.logger.log(
                PolloMessage.msg('Download URL generated successfully')
                    .setIdentifier({
                        testId,
                        downloadUrl: downloadInfo.signedUrl,
                    })
                    .setAccountId(account.id),
            );

            onProgressFn({ step: 'download_url_generated', progress: 100 });

            return downloadInfo;
        } catch (error) {
            this.logger.error(
                PolloMessage.msg(`Monitor findings CSV generation failed: ${error.message}`)
                    .setIdentifier({
                        user: pick(user, ['id', 'entryId', 'email']),
                        testId,
                        type,
                        checkType,
                    })
                    .setAccountId(account.id)
                    .setError(error),
            );

            throw error;
        }
    }
}
