import { Product } from 'app/companies/products/entities/product.entity';
import { MonitorFindingsType } from 'app/monitors/enums/monitor-findings-type.enum';
import { User } from 'app/users/entities/user.entity';
import { BaseTemporalExecutionEvent } from 'app/worker/types';
import { Account } from 'auth/entities/account.entity';
import { ExtractedRequestProps } from 'commons/decorators/get-request-notifier-props.decorator';

export interface MonitorReportDownloadEventType extends BaseTemporalExecutionEvent {
    account: Account;
    user: User;
    product: Product;
    testId: number;
    type: MonitorFindingsType;
    checkType?: string | null;
    requestMetadata: ExtractedRequestProps;
    workspaceId?: number;
}
