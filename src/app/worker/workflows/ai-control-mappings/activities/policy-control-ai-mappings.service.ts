import {
    AsyncActionType,
    AsyncEventType,
    AsyncNotificationType,
    ErrorCode,
    SocketEvent,
} from '@drata/enums';
import { Injectable } from '@nestjs/common';
import { CompanyRepository } from 'app/ai/knowledge-setup/repositories/company.repository';
import { CompanySettingRepository } from 'app/companies/repositories/company-setting.repository';
import { Control } from 'app/grc/entities/control.entity';
import { ControlRepository } from 'app/grc/repositories/control.repository';
import { User } from 'app/users/entities/user.entity';
import { PolicyVersionControlSuggestionExecution } from 'app/users/policies/entities/policy-version-control-suggestion-execution.entity';
import { PolicyVersionControlSuggestion } from 'app/users/policies/entities/policy-version-control-suggestion.entity';
import { PolicyVersion } from 'app/users/policies/entities/policy-version.entity';
import { AISuggestionsErrorState } from 'app/users/policies/enums/ai-suggesiton-error-state.enum';
import { SuggestionStatus as SuggestionStatusEnum } from 'app/users/policies/enums/suggestion-status.enum';
import { UserRepository } from 'app/users/repositories/user.repository';
import { ActivityNs } from 'app/worker/activity-ns.decorator';
import { ControlCodeMap } from 'app/worker/workflows/ai-control-mappings/activities/interface/control-code-map.interface';
import { FetchRecommendationsEventType } from 'app/worker/workflows/ai-control-mappings/interface/fetch-recommendations-event.interface';
import { GenerateControlRecommendationsEventType } from 'app/worker/workflows/ai-control-mappings/interface/generate-control-recommendations-event.interface';
import { SavePolicyVersionControlSuggestionsEventType } from 'app/worker/workflows/ai-control-mappings/interface/save-policy-version-control-suggestion-event.interface';
import { Account } from 'auth/entities/account.entity';
import { PolloAdapter } from 'commons/adapters/pollo.adapter';
import { AccountEntitlementType } from 'commons/enums/account-entitlement-type.enum';
import { CompanySettingType } from 'commons/enums/company-setting-type.enum';
import { FailedDependencyException } from 'commons/exceptions/failed-dependency.exception';
import { NotFoundException } from 'commons/exceptions/not-found.exception';
import { isProd } from 'commons/helpers/environment.helper';
import { promiseAllSettledInBatches } from 'commons/helpers/promise.helper';
import { WorkflowActivityController } from 'commons/services/workflow-activity-controller.service';
import config from 'config';
import { Llm } from 'dependencies/llm/llm';
import { PolicyToControlMappingResponseItem } from 'dependencies/llm/types/policy-control-mapping.type';
import { Socket } from 'dependencies/socket/socket';
import { AccountEntitlementService } from 'entitlements/entitlements.service';
import { cloneDeep, get, isArray, isEmpty, isNil, uniqBy } from 'lodash';
import { PolloMessage } from 'pollo-logger/classes/pollo.message';
import { In, Repository } from 'typeorm';
@Injectable()
export class PolicyControlAiMappingsService extends WorkflowActivityController {
    readonly SAVE_BATCH = 10;
    readonly MIN_POLICY_LENGTH_FOR_RECOMMENDATION = 450;
    readonly MAX_POLICY_LENGTH_FOR_RECOMMENDATION = 600_000;

    constructor(
        private readonly llm: Llm,
        private readonly accountEntitlementService: AccountEntitlementService,
        private readonly socket: Socket,
    ) {
        super();
    }

    @ActivityNs()
    async canGeneratePolicyControlRecommendations({
        account,
        content,
        name,
        description,
        policyVersion,
    }: GenerateControlRecommendationsEventType): Promise<boolean> {
        if (!account) {
            this.logger.log(PolloMessage.msg('Cannot generate suggestions without account'));
            return false;
        }
        const aiOptIn = await this.hasOptInToAI(account);
        if (!aiOptIn) {
            this.logger.log(PolloMessage.msg('Cannot generate suggestions without opting into AI'));
            return false;
        }
        if (!content || !name || !description) {
            this.logger.log(
                PolloAdapter.acct('Cannot generate suggestions without content', account),
            );
            return false;
        }

        if (!policyVersion) {
            this.logger.log(
                PolloAdapter.acct('Cannot generate suggestions without policyVersion', account),
            );
            return false;
        }
        return true;
    }

    @ActivityNs()
    async generatePolicyVersionControlSuggestionExecution(
        policyVersion: PolicyVersion,
    ): Promise<PolicyVersionControlSuggestionExecution> {
        const existingExecution =
            await this.policyVersionControlSuggestionExecutionRepository.findOne({
                where: {
                    policyVersion: {
                        id: policyVersion.id,
                    },
                },
            });
        if (existingExecution) {
            // we always want a single execution per policy version.
            await this.policyVersionControlSuggestionExecutionRepository.softDelete(
                existingExecution.id,
            );
        }
        const execution = new PolicyVersionControlSuggestionExecution();
        execution.status = SuggestionStatusEnum.IN_PROGRESS;
        execution.policyVersion = policyVersion;

        return this.policyVersionControlSuggestionExecutionRepository.save(execution);
    }

    @ActivityNs()
    async setExecutionErrorStatus(
        execution: PolicyVersionControlSuggestionExecution,
        error: Error | FailedDependencyException,
        account: Account,
    ): Promise<PolicyVersionControlSuggestionExecution> {
        if (error instanceof FailedDependencyException) {
            this.logger.warn(
                PolloAdapter.acct(
                    'Policy content is not appropriate for recommendations',
                    account,
                ).setError(error),
            );
        } else {
            this.logger.error(
                PolloAdapter.acct('Failed to fetch recommendations', account).setError(error),
            );
        }

        execution.status = SuggestionStatusEnum.ERROR;
        /**
         * All we get from Vellum is an exception. So we basically have to encrypt the rationale somehow whenever
         * it fails. As John put it somewhere else in this codebase, extracting the actual failure cause is like like picking fly sh&t out of pepper
         * with tweezers - wearing boxing gloves.
         * This is brittle in the sense that we are relying on the cause.failure.message property. But it can change in the future very easily.
         */
        const llmErrorCause = this.getJSONFromError(error);
        if (llmErrorCause && llmErrorCause['is_policy'] === 'no') {
            execution.errorType = AISuggestionsErrorState.INCORRECT_POLICY_FORMAT;
        } else {
            execution.errorType = AISuggestionsErrorState.UNKNOWN_ERROR;
        }
        if (llmErrorCause['reasons'] && isArray(llmErrorCause['reasons'])) {
            execution.errorDescription = llmErrorCause['reasons'].join(', ');
        } else {
            execution.errorDescription = error.message || 'Unknown error';
        }

        execution.erroredAt = new Date();
        return this.policyVersionControlSuggestionExecutionRepository.save(execution);
    }

    private getJSONFromError(error: Error): Record<string, unknown> {
        const errorMessage = get(error, 'cause.failure.message', '{}');

        // Find JSON boundaries
        const startIndex = errorMessage.indexOf('{');
        const lastIndex = errorMessage.lastIndexOf('}');

        if (startIndex === -1 || lastIndex === -1 || startIndex >= lastIndex) {
            return {};
        }

        try {
            const jsonString = errorMessage.substring(startIndex, lastIndex + 1);
            return JSON.parse(jsonString);
        } catch {
            return {};
        }
    }

    @ActivityNs()
    setExecutionErrorStatusByType(
        execution: PolicyVersionControlSuggestionExecution,
        errorType: AISuggestionsErrorState,
        errorMessage?: string,
    ): Promise<PolicyVersionControlSuggestionExecution> {
        execution.status = SuggestionStatusEnum.ERROR;
        execution.errorType = errorType;
        execution.erroredAt = new Date();
        execution.errorDescription = errorMessage;
        return this.policyVersionControlSuggestionExecutionRepository.save(execution);
    }

    @ActivityNs()
    async fetchPolicyControlRecommendations({
        account,
        content,
        name,
        description,
        policyVersion,
        primaryWorkspaceId,
        policyId,
        execution,
    }: FetchRecommendationsEventType): Promise<SavePolicyVersionControlSuggestionsEventType> {
        const mapAndSaveEvent: SavePolicyVersionControlSuggestionsEventType = {
            account,
            policyVersion,
            primaryWorkspaceId,
            suggestions: [],
            execution,
        };

        try {
            // Only God and whoever developed this LLM knows why we need to know if Exception Management is enabled.
            const isExceptionManagementEnabled =
                await this.isExceptionManagementFeatureFlagEnabled(account);
            const { executionId, advanced, light } =
                await this.llm.getControlsRecommendationFromPolicy(
                    this.getAccountForRecommendations(account),
                    name,
                    content,
                    description,
                    isExceptionManagementEnabled ? 1 : 0,
                    `${policyId}`,
                );
            execution.executionId = executionId;

            if (isEmpty(advanced.recommendations) && isEmpty(light.recommendations)) {
                this.logger.log(PolloAdapter.acct('No suggestions found', account));
            }
            const lightRecommendations = light.recommendations.map(item => ({
                ...item,
                identifiedDcfControlId: item.identifiedDcfControlId,
            }));
            const advancedRecommendations = advanced.recommendations.map(item => ({
                ...item,
                identifiedDcfControlId: item.identifiedDcfControlId,
            }));
            mapAndSaveEvent.suggestions?.push(
                ...[...lightRecommendations, ...advancedRecommendations],
            );
            return mapAndSaveEvent;
        } catch (err) {
            this.logger.error(
                PolloMessage.msg('Something went wrong')
                    .setError(err)
                    .setDomain(account?.domain ?? ''),
            );
            throw err;
        }
    }

    @ActivityNs()
    async mapLlmRecommendationToPolicyVersionControlSuggestions({
        account,
        policyVersion,
        suggestions,
        primaryWorkspaceId,
        execution,
    }: SavePolicyVersionControlSuggestionsEventType): Promise<PolicyVersionControlSuggestion[]> {
        try {
            if (!account) {
                this.logger.log(PolloMessage.msg('Cannot generate suggestions without account'));
                return [];
            }
            if (isNil(policyVersion)) {
                this.logger.log(
                    PolloAdapter.acct(
                        'Cannot map suggestion without policy version',
                        account as Account,
                        this.constructor.name,
                    ),
                );
                return [];
            }
            if (isNil(suggestions)) {
                this.logger.log(
                    PolloAdapter.acct(
                        'No suggestion to map',
                        account as Account,
                        this.constructor.name,
                    ),
                );
                return [];
            }
            const suggestedControlCodes = suggestions.map(
                recommendation => recommendation.identifiedDcfControlId,
            );
            if (isEmpty(suggestedControlCodes)) {
                this.logger.log(
                    PolloAdapter.acct(
                        `No suggested controls for policy version ${policyVersion.id}`,
                        account as Account,
                        this.constructor.name,
                    ),
                );
                return [];
            }
            const controlMap = await this.getControlsByCode(
                suggestedControlCodes,
                primaryWorkspaceId,
            );

            const policyVersionControlSuggestions =
                this.transformLlmSuggestionToPolicyVersionControlSuggestion(
                    suggestions.filter(
                        recommendation => !isNil(controlMap[recommendation.identifiedDcfControlId]),
                    ),
                    controlMap,
                    policyVersion,
                    execution,
                );
            const newS = await this.savePolicyVersionControlSuggestions(
                policyVersionControlSuggestions,
            );

            execution.status = SuggestionStatusEnum.SUCCESSFUL;
            await this.policyVersionControlSuggestionExecutionRepository.save(execution);
            return newS;
        } catch (err) {
            this.logger.error(
                PolloMessage.msg('Could not map suggestions to controls')
                    .setError(err)
                    .setDomain(account?.domain ?? ''),
            );
            await this.setExecutionErrorStatus(execution, err, account as Account);
            throw err;
        }
    }

    @ActivityNs()
    async isPolicyTooShort(content: string): Promise<boolean> {
        if (content.length < this.MIN_POLICY_LENGTH_FOR_RECOMMENDATION) {
            this.logger.log(PolloMessage.msg('Policy is too short to generate recommendations'));
            return true;
        }
        return false;
    }

    @ActivityNs()
    async isPolicyTooLong(content: string): Promise<boolean> {
        if (content.length > this.MAX_POLICY_LENGTH_FOR_RECOMMENDATION) {
            this.logger.log(PolloMessage.msg('Policy is too long to generate recommendations'));
            return true;
        }
        return false;
    }

    @ActivityNs()
    async sendAISuggestionsQueuedNotification(account: Account, user: User): Promise<void> {
        const messagePayload = {
            notificationType: AsyncNotificationType.FIRE_AND_FORGET,
            actionType: AsyncActionType.AI_CONTROL_SUGGESTIONS_GENERATED,
            type: AsyncEventType.INITIATE,
            metadata: {
                url: '',
                method: 'POST',
                requestId: null,
            },
            additionalData: {},
        };

        void this.socket.sendMessage(
            account.id,
            SocketEvent.SNACK_NOTIFICATION,
            messagePayload,
            user.id,
        );
    }

    @ActivityNs()
    async sendAISuggestionsReadyNotification(
        account: Account,
        user: User,
        policyId: number,
        policyName: string,
    ): Promise<void> {
        void this.socket.sendMessage(
            account.id,
            SocketEvent.SNACK_NOTIFICATION,
            {
                notificationType: AsyncNotificationType.FIRE_AND_FORGET,
                actionType: AsyncActionType.AI_CONTROL_SUGGESTIONS_GENERATED,
                type: AsyncEventType.COMPLETE,
                metadata: {
                    url: '',
                    method: 'POST',
                    requestId: null,
                },
                additionalData: {
                    policyId,
                    policyName,
                },
            },
            user.id,
        );
    }

    @ActivityNs()
    async sendAISuggestionsErrorNotification(account: Account, user: User): Promise<void> {
        void this.socket.sendMessage(
            account.id,
            SocketEvent.SNACK_NOTIFICATION,
            {
                notificationType: AsyncNotificationType.FIRE_AND_FORGET,
                actionType: AsyncActionType.AI_CONTROL_SUGGESTIONS_GENERATED,
                type: AsyncEventType.FAIL,
                metadata: {
                    url: '',
                    method: 'POST',
                    requestId: null,
                },
                additionalData: {},
            },
            user.id,
        );
    }

    /**
     * TODO: delete once UAT testing is done.
     * The reason we need this temporary method is because vellum does not index local accounts. So it cannot
     * generate proper suggestions on testing environments.
     */

    private getAccountForRecommendations(account: Account): Account {
        if (isProd()) {
            return account;
        }

        // cloning the account to avoid changing the original account further in the process.
        const clone = cloneDeep(account);
        clone.id = '7b50e039-cab0-4f4b-891a-a8982d2c864a';

        return clone;
    }

    private async getControlsByCode(
        code: string[],
        primaryWorkspaceId: number,
    ): Promise<ControlCodeMap> {
        const controls = await this.controlRepository.find({
            where: { code: In(code), products: { id: primaryWorkspaceId } },
            select: ['id', 'code'],
        });
        return controls.reduce<ControlCodeMap>((acc, control) => {
            acc[control.code] = control;
            return acc;
        }, {});
    }

    private transformLlmSuggestionToPolicyVersionControlSuggestion(
        suggestions: PolicyToControlMappingResponseItem[],
        controlMap: ControlCodeMap,
        policyVersion: PolicyVersion,
        execution: PolicyVersionControlSuggestionExecution,
    ): PolicyVersionControlSuggestion[] {
        const policyVersionControlSuggestions = suggestions.map(recommendation => {
            return this.buildPolicyVersionControlSuggestion(
                policyVersion,
                controlMap[recommendation.identifiedDcfControlId],
                recommendation.justification,
                recommendation.score,
                execution,
            );
        });
        return uniqBy(policyVersionControlSuggestions, 'control.id');
    }

    private buildPolicyVersionControlSuggestion(
        policyVersion: PolicyVersion,
        control: Control,
        justification: string,
        score: number,
        execution: PolicyVersionControlSuggestionExecution,
    ): PolicyVersionControlSuggestion {
        const newPolicyControlSuggestion = new PolicyVersionControlSuggestion();
        newPolicyControlSuggestion.policyVersion = policyVersion;
        newPolicyControlSuggestion.policyVersion = policyVersion;
        newPolicyControlSuggestion.control = control;
        //justification should be limited to the max allowed length in the DB.
        newPolicyControlSuggestion.justification =
            justification?.substring(0, config.get('db.varcharLongLength')) ?? '';
        newPolicyControlSuggestion.execution = execution;
        newPolicyControlSuggestion.score = score;
        newPolicyControlSuggestion.isRelevantSuggestion = true;
        return newPolicyControlSuggestion;
    }

    private async savePolicyVersionControlSuggestions(
        policyVersionControlSuggestions: PolicyVersionControlSuggestion[],
    ): Promise<PolicyVersionControlSuggestion[]> {
        // TODO - we also need to add the corresponding execution id / entity.
        const { rejected, fulfilled } = await promiseAllSettledInBatches(
            policyVersionControlSuggestions,
            this.SAVE_BATCH,
            async suggestion => {
                const existingSuggestion =
                    await this.policyVersionControlSuggestionRepository.findOne({
                        where: {
                            policyVersion: {
                                id: suggestion.policyVersion.id,
                            },
                            control: {
                                id: suggestion.control.id,
                            },
                        },
                    });
                if (existingSuggestion) {
                    const { score, justification } = suggestion;
                    existingSuggestion.score = score;
                    existingSuggestion.justification = justification;
                    existingSuggestion.isRelevantSuggestion = true;
                }
                return this.policyVersionControlSuggestionRepository.save(suggestion);
            },
        );

        if (rejected.count) {
            throw rejected.reasons;
        }

        return fulfilled;
    }

    private async hasOptInToAI(account: Account): Promise<boolean> {
        const company = await this.companyRepository.findOneOrFail({
            where: { accountId: account.id },
            select: ['id'],
        });
        if (isNil(company)) {
            throw new NotFoundException(ErrorCode.COMPANY_NOT_FOUND);
        }
        const companySetting =
            await this.companySettingRepository.getCompanySettingByCompanyAndType(
                company.id,
                CompanySettingType.AI_ENABLED,
            );
        return companySetting?.defaultValue;
    }

    private async isExceptionManagementFeatureFlagEnabled(account: Account): Promise<boolean> {
        return this.accountEntitlementService.isEntitlementEnabled(
            account.id,
            AccountEntitlementType.EXCEPTION_MANAGEMENT,
        );
    }

    private get controlRepository(): ControlRepository {
        return this.getCustomTenantRepository(ControlRepository);
    }

    private get companyRepository(): CompanyRepository {
        return this.getCustomTenantRepository(CompanyRepository);
    }

    private get companySettingRepository(): CompanySettingRepository {
        return this.getCustomTenantRepository(CompanySettingRepository);
    }

    private get policyVersionControlSuggestionRepository(): Repository<PolicyVersionControlSuggestion> {
        return this.getTenantRepository(PolicyVersionControlSuggestion);
    }

    private get policyVersionControlSuggestionExecutionRepository(): Repository<PolicyVersionControlSuggestionExecution> {
        return this.getTenantRepository(PolicyVersionControlSuggestionExecution);
    }

    private get userRepository(): UserRepository {
        return this.getCustomTenantRepository(UserRepository);
    }
}
