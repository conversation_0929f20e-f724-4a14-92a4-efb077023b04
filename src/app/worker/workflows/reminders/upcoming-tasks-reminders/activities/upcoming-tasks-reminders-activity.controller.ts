import { Injectable } from '@nestjs/common';
import { isRiskManagementActive } from 'app/risk-management/helpers/risk-management.helper';
import { UpcomingTasksRemindersProviderService } from 'app/tasks/upcoming-tasks-reminders/services/upcoming-tasks-reminders-provider.service';
import { UpcomingTasksRemindersService } from 'app/tasks/upcoming-tasks-reminders/services/upcoming-tasks-reminders.service';
import { UpcomingTasksUserRemindersCoreService } from 'app/tasks/upcoming-tasks-reminders/services/upcoming-tasks-user-reminders-core.service';
import { User } from 'app/users/entities/user.entity';
import { UsersCoreService } from 'app/users/services/users-core.service';
import { ActivityNs } from 'app/worker/activity-ns.decorator';
import { GenericUserReminderProviderConfig } from 'app/worker/workflows/reminders/interfaces/user-reminder-provider-config.interface';
import { ReminderAccount } from 'app/worker/workflows/reminders/types/regional-reminder-activities.type';
import { RemindersScheduleConfig } from 'app/worker/workflows/reminders/types/reminders-schedule-config.type';
import { Account } from 'auth/entities/account.entity';
import { PolloAdapter } from 'commons/adapters/pollo.adapter';
import { FeatureType } from 'commons/enums/feature-toggling/feature.enum';
import { WorkflowActivityController } from 'commons/services/workflow-activity-controller.service';
import { PolloMessage } from 'pollo-logger/classes/pollo.message';

export interface ProcessTenantBatchParams {
    accounts: ReminderAccount[];
    hostId: string;
    batchIndex: number;
    tenantBatchSize?: number;
}

export interface ProcessTenantBatchResult {
    processedCount: number;
    failedCount: number;
    batchIndex: number;
    hostId: string;
    accountDetails: Array<{
        accountId: string;
        domain: string;
        status: 'success' | 'failed' | 'skipped';
        reason?: string;
        userNotificationsSent: number;
    }>;
}

@Injectable()
export class UpcomingTasksRemindersActivityController extends WorkflowActivityController {
    constructor(
        private readonly usersCoreService: UsersCoreService,
        private readonly upcomingTasksRemindersService: UpcomingTasksRemindersService,
        private readonly upcomingTasksUserRemindersCoreService: UpcomingTasksUserRemindersCoreService,
        private readonly upcomingTasksRemindersProviderService: UpcomingTasksRemindersProviderService,
    ) {
        super();
    }

    @ActivityNs()
    isFeatureEnabled(account: Account): Promise<boolean> {
        return this.upcomingTasksRemindersService.isFeatureEnabled(account);
    }

    @ActivityNs()
    public async getUserIdsWithUpcomingTasks(
        account: Account,
        remindersScheduleConfig: RemindersScheduleConfig,
    ): Promise<number[]> {
        return this.upcomingTasksRemindersService.getUserIdsWithUpcomingTasks(
            account,
            remindersScheduleConfig,
        );
    }

    @ActivityNs()
    public async getUsersFromUserIds(account: Account, userIds: number[]): Promise<User[]> {
        return this.usersCoreService.getUsersByIds(userIds);
    }

    @ActivityNs()
    public async getUserRemindersConfiguration(
        account: Account,
        user: User,
        remindersScheduleConfig: RemindersScheduleConfig,
    ): Promise<GenericUserReminderProviderConfig[FeatureType.UPCOMING_TASKS_REMINDERS] | null> {
        const includeRiskTasks = isRiskManagementActive(account);

        return this.upcomingTasksUserRemindersCoreService.getUserRemindersConfiguration(
            account,
            user,
            FeatureType.UPCOMING_TASKS_REMINDERS,
            // The actual list and number of tasks will be retrieved later in the flow.
            {
                userId: user.id,
                tasksToIncludeInTheMail: [],
                totalNumberOfTasks: 0,
                includeRiskTasks,
            },
            remindersScheduleConfig,
        );
    }

    @ActivityNs()
    public async sendReminder(
        account: Account,
        userReminderConfig: GenericUserReminderProviderConfig[FeatureType.UPCOMING_TASKS_REMINDERS],
    ): Promise<void> {
        try {
            const { data, user, featureType, ...remindersScheduleConfig } = userReminderConfig;
            this.logger.log(
                PolloAdapter.acct(
                    `Preparing to send reminder to user ${user.id} for feature type ${
                        FeatureType[featureType]
                    }`,
                    account,
                )
                    .setContext(this.constructor.name)
                    .setSubContext(this.sendReminder.name)
                    .addTemporalMetadata(),
            );

            const validatedData =
                await this.upcomingTasksRemindersProviderService.validateReminderToBeSent(
                    data,
                    remindersScheduleConfig,
                );

            if (validatedData) {
                await this.upcomingTasksRemindersProviderService.send(account, {
                    ...userReminderConfig,
                    data: validatedData,
                });
            }
        } catch (error) {
            this.logger.error(
                PolloMessage.msg(
                    `Failed to send reminder for feature type: ${userReminderConfig.featureType}`,
                )
                    .setContext(this.constructor.name)
                    .setSubContext(this.sendReminder.name)
                    .setError(error)
                    .addTemporalMetadata(),
            );
        }
    }
}
