import { TestingModule } from '@nestjs/testing';
import { UpcomingTasksRemindersProviderService } from 'app/tasks/upcoming-tasks-reminders/services/upcoming-tasks-reminders-provider.service';
import { UpcomingTasksRemindersService } from 'app/tasks/upcoming-tasks-reminders/services/upcoming-tasks-reminders.service';
import { UpcomingTasksUserRemindersCoreService } from 'app/tasks/upcoming-tasks-reminders/services/upcoming-tasks-user-reminders-core.service';
import { User } from 'app/users/entities/user.entity';
import { UsersCoreService } from 'app/users/services/users-core.service';
import { GenericUserReminderProviderConfig } from 'app/worker/workflows/reminders/interfaces/user-reminder-provider-config.interface';
import { RemindersScheduleConfig } from 'app/worker/workflows/reminders/types/reminders-schedule-config.type';
import { UpcomingTasksRemindersActivityController } from 'app/worker/workflows/reminders/upcoming-tasks-reminders/activities/upcoming-tasks-reminders-activity.controller';
import { Account } from 'auth/entities/account.entity';
import { FeatureType } from 'commons/enums/feature-toggling/feature.enum';
import { createAppTestingModule } from 'commons/helpers/app-testing-module.helper';

// Ensure Date.prototype.toISO8601String exists in tests (used by provider internally)
import 'commons/helpers/date.helper';

describe('UpcomingTasksRemindersActivityController', () => {
    let controller: UpcomingTasksRemindersActivityController;

    let usersCoreService: jest.Mocked<UsersCoreService>;
    let upcomingTasksRemindersService: jest.Mocked<UpcomingTasksRemindersService>;
    let upcomingTasksUserRemindersCoreService: jest.Mocked<UpcomingTasksUserRemindersCoreService>;
    let upcomingTasksRemindersProviderService: jest.Mocked<UpcomingTasksRemindersProviderService>;

    const mockAccount: Account = {
        id: 'acc-1',
        domain: 'test.com',
    } as unknown as Account;

    const mockConfig: RemindersScheduleConfig = {
        year: 2024,
        month: 0,
        day: 15,
        hour: 6,
        minute: 30,
        timezone: 'America/Chicago',
    };

    beforeAll(async () => {
        const module: TestingModule = await createAppTestingModule({
            providers: [
                UpcomingTasksRemindersActivityController,
                {
                    provide: UsersCoreService,
                    useValue: {
                        getUsersByIds: jest.fn(),
                    },
                },
                {
                    provide: UpcomingTasksRemindersService,
                    useValue: {
                        getUserIdsWithUpcomingTasks: jest.fn(),
                    },
                },
                {
                    provide: UpcomingTasksUserRemindersCoreService,
                    useValue: {
                        getUserRemindersConfiguration: jest.fn(),
                    },
                },
                {
                    provide: UpcomingTasksRemindersProviderService,
                    useValue: {
                        validateReminderToBeSent: jest.fn(),
                        send: jest.fn(),
                    },
                },
            ],
        }).compile();

        controller = module.get<UpcomingTasksRemindersActivityController>(
            UpcomingTasksRemindersActivityController,
        );
        usersCoreService = module.get(UsersCoreService);
        upcomingTasksRemindersService = module.get(UpcomingTasksRemindersService);
        upcomingTasksUserRemindersCoreService = module.get(UpcomingTasksUserRemindersCoreService);
        upcomingTasksRemindersProviderService = module.get(UpcomingTasksRemindersProviderService);
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    it('should be defined', () => {
        expect(controller).toBeDefined();
    });

    describe('getUserIdsWithUpcomingTasks', () => {
        it('should delegate to UpcomingTasksRemindersService and return result', async () => {
            const expectedUserIds = [1, 2, 3];
            upcomingTasksRemindersService.getUserIdsWithUpcomingTasks.mockResolvedValue(
                expectedUserIds,
            );

            const result = await controller.getUserIdsWithUpcomingTasks(mockAccount, mockConfig);

            expect(upcomingTasksRemindersService.getUserIdsWithUpcomingTasks).toHaveBeenCalledWith(
                mockAccount,
                mockConfig,
            );
            expect(result).toEqual(expectedUserIds);
        });
    });

    describe('getUsersFromUserIds', () => {
        it('should delegate to UsersCoreService and return users', async () => {
            const userIds = [10, 20];
            const mockUsers = [
                { id: 10, email: '<EMAIL>', firstName: 'A', lastName: 'B' },
                { id: 20, email: '<EMAIL>', firstName: 'C', lastName: 'D' },
            ] as unknown as User[];

            usersCoreService.getUsersByIds.mockResolvedValue(mockUsers);

            const result = await controller.getUsersFromUserIds(mockAccount, userIds);

            expect(usersCoreService.getUsersByIds).toHaveBeenCalledWith(userIds);
            expect(result).toEqual(mockUsers);
        });
    });

    describe('getUserRemindersConfiguration', () => {
        it('should delegate to UpcomingTasksUserRemindersCoreService with correct params and return value', async () => {
            const user: User = {
                id: 42,
                email: '<EMAIL>',
                firstName: 'Test',
                lastName: 'User',
            } as unknown as User;

            const expectedConfig: GenericUserReminderProviderConfig[FeatureType.UPCOMING_TASKS_REMINDERS] =
                {
                    featureType: FeatureType.UPCOMING_TASKS_REMINDERS,
                    data: {
                        userId: 42,
                        totalNumberOfTasks: 0,
                        tasksToIncludeInTheMail: [],
                        includeRiskTasks: false,
                    },
                    user: { id: 42, email: '<EMAIL>', firstName: 'Test', lastName: 'User' },
                    ...mockConfig,
                };

            upcomingTasksUserRemindersCoreService.getUserRemindersConfiguration.mockResolvedValue(
                expectedConfig,
            );

            const result = await controller.getUserRemindersConfiguration(
                mockAccount,
                user,
                mockConfig,
            );

            expect(
                upcomingTasksUserRemindersCoreService.getUserRemindersConfiguration,
            ).toHaveBeenCalledWith(
                mockAccount,
                user,
                FeatureType.UPCOMING_TASKS_REMINDERS,
                {
                    userId: user.id,
                    tasksToIncludeInTheMail: [],
                    totalNumberOfTasks: 0,
                    includeRiskTasks: false,
                },
                mockConfig,
            );
            expect(result).toEqual(expectedConfig);
        });

        it('should pass through null when core service returns null', async () => {
            const user = { id: 5 } as User;
            upcomingTasksUserRemindersCoreService.getUserRemindersConfiguration.mockResolvedValue(
                null,
            );

            const result = await controller.getUserRemindersConfiguration(
                mockAccount,
                user,
                mockConfig,
            );

            expect(result).toBeNull();
        });
    });

    describe('sendReminder', () => {
        const userForReminder = { id: 7, email: '<EMAIL>', firstName: 'U', lastName: 'T' };
        const baseReminderConfig: GenericUserReminderProviderConfig[FeatureType.UPCOMING_TASKS_REMINDERS] =
            {
                featureType: FeatureType.UPCOMING_TASKS_REMINDERS,
                data: {
                    userId: userForReminder.id,
                    totalNumberOfTasks: 0,
                    tasksToIncludeInTheMail: [],
                    includeRiskTasks: false,
                },
                user: userForReminder,
                ...mockConfig,
            };

        it('should validate and send when validated data is returned', async () => {
            const validatedData = {
                userId: userForReminder.id,
                totalNumberOfTasks: 3,
                tasksToIncludeInTheMail: [{ id: 1 } as any],
                includeRiskTasks: false,
            };

            upcomingTasksRemindersProviderService.validateReminderToBeSent.mockResolvedValue(
                validatedData,
            );

            await controller.sendReminder(mockAccount, baseReminderConfig);

            expect(
                upcomingTasksRemindersProviderService.validateReminderToBeSent,
            ).toHaveBeenCalledWith(baseReminderConfig.data, mockConfig);

            expect(upcomingTasksRemindersProviderService.send).toHaveBeenCalledWith(mockAccount, {
                ...baseReminderConfig,
                data: validatedData,
            });
        });

        it('should not send when validation returns null', async () => {
            upcomingTasksRemindersProviderService.validateReminderToBeSent.mockResolvedValue(null);

            await controller.sendReminder(mockAccount, baseReminderConfig);

            expect(upcomingTasksRemindersProviderService.send).not.toHaveBeenCalled();
        });

        it('should catch errors thrown during validation and not throw', async () => {
            const error = new Error('Validation failed');
            upcomingTasksRemindersProviderService.validateReminderToBeSent.mockRejectedValue(error);

            await expect(
                controller.sendReminder(mockAccount, baseReminderConfig),
            ).resolves.toBeUndefined();

            expect(upcomingTasksRemindersProviderService.send).not.toHaveBeenCalled();
        });
    });
});
