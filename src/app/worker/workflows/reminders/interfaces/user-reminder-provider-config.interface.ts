import { User } from 'app/users/entities/user.entity';
import { TaskNotification } from 'app/worker/workflows/grc/types/task-notification.type';
import { RemindersScheduleConfig } from 'app/worker/workflows/reminders/types/reminders-schedule-config.type';
import { FeatureType } from 'commons/enums/feature-toggling/feature.enum';

/**
 * Maps feature types to their corresponding data types
 */
export interface FeatureTypeDataMap {
    [FeatureType.OVERDUE_TASKS_REMINDERS]: TaskNotification[];
    [FeatureType.UPCOMING_TASKS_REMINDERS]: {
        userId: number;
        totalNumberOfTasks: number;
        tasksToIncludeInTheMail: TaskNotification[];
        includeRiskTasks: boolean;
    };
}

export type ReminderFeatures = keyof FeatureTypeDataMap;

export type UserForReminder = {
    id: User['id'];
    email: User['email'];
    firstName: User['firstName'];
    lastName: User['lastName'];
};

/**
 * Generic configuration for user reminders with type-safe data
 */
export type GenericUserReminderProviderConfig = {
    [Feature in ReminderFeatures]: {
        featureType: Feature;
        data: FeatureTypeDataMap[Feature];
        user: UserForReminder;
    } & RemindersScheduleConfig;
};

export type UserReminderProviderConfig = GenericUserReminderProviderConfig[ReminderFeatures];
