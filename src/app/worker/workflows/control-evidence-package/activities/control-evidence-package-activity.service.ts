import { AuditType, ErrorCode, EventCategory, EventType, SocketEvent } from '@drata/enums';
import { BadRequestException, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { CONTROL_EVIDENCE_PACKAGE_ZIP_FILENAME } from 'app/audit-hub/constants/audit-packages.constants';
import { CollectControlEvidenceService } from 'app/audit-hub/evidence-strategies/collect-control-evidence-service.interface';
import { CollectCustomerRequestControlsEvidenceService } from 'app/audit-hub/evidence-strategies/collect-customer-request-controls-evidence.service';
import { CollectSelectControlsEvidenceService } from 'app/audit-hub/evidence-strategies/collect-select-controls-evidence.service';
import { CustomerRequestIdControlIdsMapInput } from 'app/audit-hub/evidence-strategies/interface/customer-request-controls-map.interface';
import { ControlEvidencePackageParameters } from 'app/audit-hub/services/interfaces/control-evidence-package-parameters';
import { CompanyArchiveRepository } from 'app/companies/repositories/company-archive.repository';
import { eventDescriptions } from 'app/events/configs/event-descriptions.config';
import { EventRequestDto } from 'app/events/dtos/event-request.dto';
import { EventsAppService } from 'app/events/events-app.service';
import { Control } from 'app/grc/entities/control.entity';
import { ActivityNs } from 'app/worker/activity-ns.decorator';
import { ControlEvidencePackageEmailParams } from 'app/worker/workflows/control-evidence-package/interface/control-evidence-package-email-params.interface';
import { ControlEvidencePackageEvent } from 'app/worker/workflows/control-evidence-package/interface/control-evidence-package-event.interface';
import { ControlEvidenceJsonUploaderService } from 'app/worker/workflows/control-evidence-package/services/control-evidence-json-uploader.service';
import { ControlEvidenceJsonUploadType } from 'app/worker/workflows/control-evidence-package/services/enum/control-evidence-json-upload-type.enum';
import { UploadedControlEvidenceKeys } from 'app/worker/workflows/control-evidence-package/types/uploaded-control-evidence-keys.type';
import { AuditorFrameworkRepository } from 'auditors/repositories/auditor-framework.repository';
import { AuditorRepository } from 'auditors/repositories/auditor.repository';
import { CollectAllControlEvidenceService } from 'auditors/services/collect-all-control-evidence.service';
import { Account } from 'auth/entities/account.entity';
import { PolloAdapter } from 'commons/adapters/pollo.adapter';
import { EmailConfig } from 'commons/configs/email.config';
import { CompanyArchiveStatus } from 'commons/enums/company-archive-status.enum';
import { EventSource } from 'commons/enums/events/event-source.enum';
import { UploadType } from 'commons/enums/upload-type.enum';
import { NotFoundException } from 'commons/exceptions/not-found.exception';
import {
    abbreviateFilenames,
    removeDuplicatedFiles,
    sanitizeFileName,
} from 'commons/helpers/file.helper';
import { getLanguage } from 'commons/helpers/language.helper';
import { promiseAllSettledInBatches } from 'commons/helpers/promise.helper';
import { encodeHTML } from 'commons/helpers/security.helper';
import { addApostrophe } from 'commons/helpers/string.helper';
import { fullName } from 'commons/helpers/user.helper';
import { EmailService } from 'commons/services/email.service';
import { WorkflowActivityController } from 'commons/services/workflow-activity-controller.service';
import { FileBufferType } from 'commons/types/file-buffer.type';
import { SymlinkType } from 'commons/types/symlink.type';
import config from 'config';
import { format as formatDate } from 'date-fns';
import { Deleter } from 'dependencies/deleter/deleter';
import { Downloader } from 'dependencies/downloader/downloader';
import { Socket } from 'dependencies/socket/socket';
import { ReleaseFilenameAbbreviation } from 'feature-flags/implementations/release-windows-filename-abbreviation-feature-flag';
import { capitalize, isEmpty, isNil } from 'lodash';
import { Response } from 'node-fetch';
import { format } from 'util';

@Injectable()
export class ControlEvidencePackageActivityService extends WorkflowActivityController {
    private readonly BATCH_DELETE = 10;
    private readonly BATCH_DOWNLOAD = 10;

    constructor(
        private readonly controlEvidenceJsonUploaderService: ControlEvidenceJsonUploaderService,
        private readonly downloader: Downloader,
        @InjectRepository(AuditorFrameworkRepository)
        private readonly auditRepository: AuditorFrameworkRepository,
        @InjectRepository(AuditorRepository)
        private readonly auditorRepository: AuditorRepository,
        private readonly socket: Socket,
        private readonly deleter: Deleter,
        private readonly eventsAppService: EventsAppService,
        private readonly collectSelectControlsEvidenceService: CollectSelectControlsEvidenceService,
        private readonly collectCustomerRequestEvidenceService: CollectCustomerRequestControlsEvidenceService,
        private readonly collectAllControlEvidenceService: CollectAllControlEvidenceService,
        private readonly emailConfig: EmailConfig,
        private readonly emailService: EmailService,
        private readonly releaseFilenameAbbreviation: ReleaseFilenameAbbreviation,
    ) {
        super();
    }

    @ActivityNs()
    async getControlsAndRequirementIndexes(event: ControlEvidencePackageEvent): Promise<string> {
        const { account, auditId, controlIds, customerRequestId } = event;
        const audit = await this.auditRepository.getAuditByIdAndAccount(auditId, account.id);
        if (!audit) {
            throw new NotFoundException(ErrorCode.AUDIT_NOT_FOUND);
        }
        const controlEvidencePackageService = this.getControlEvidencePackageService(
            customerRequestId,
            controlIds,
        );
        const controlsAndRequirementIndexes =
            await controlEvidencePackageService.getControlEvidencePackageParameters(audit, {
                controlIds,
                customerRequestId,
            });
        return this.controlEvidenceJsonUploaderService.uploadControlsRequirements(
            account,
            auditId,
            controlsAndRequirementIndexes,
        );
    }

    @ActivityNs()
    async collectControlEvidenceBuffers(
        evidencesS3Key: string,
        event: ControlEvidencePackageEvent,
    ): Promise<UploadedControlEvidenceKeys> {
        this.logger.log(PolloAdapter.acct(`Running collectControlEvidenceBuffers`, event.account));

        // parse s3 JSON into controls and requirements
        const controlsAndRequirementIndexes =
            await this.downloader.getPrivateFileWithBucket<ControlEvidencePackageParameters>(
                config.get('aws.s3.appBucket'),
                evidencesS3Key,
            );

        if (isNil(controlsAndRequirementIndexes)) {
            throw new BadRequestException('parsed evidence json did not have valid data');
        }

        const { account, auditId, company, controlIds, customerRequestId, user } = event;

        const audit = await this.auditRepository.getAuditByIdAndAccount(auditId, account.id);
        if (!audit) {
            throw new NotFoundException(ErrorCode.AUDIT_NOT_FOUND);
        }
        const controlEvidencePackageService = this.getControlEvidencePackageService(
            customerRequestId,
            controlIds,
        );

        const evidenceDataAndBuffer =
            await controlEvidencePackageService.transformEvidenceToFileBuffers(
                audit,
                user,
                account,
                controlsAndRequirementIndexes,
                company,
            );

        const { evidenceFileBuffers, symlinks } = evidenceDataAndBuffer;
        // transform evidence buffers into json
        let evidenceFilesJson = evidenceFileBuffers.map(file => {
            return {
                ...file,
                // Serialize the stream property
                stream: file.stream.toString('base64'),
            };
        });

        const uploadedControlEvidenceKeys: UploadedControlEvidenceKeys = {
            buffersKey: [],
            symlinksKey: undefined,
        };

        const isFilenameAbbreviationEnabled =
            await this.releaseFilenameAbbreviation.isFeatureEnabled(account);

        evidenceFilesJson = removeDuplicatedFiles(evidenceFilesJson);
        if (isFilenameAbbreviationEnabled) {
            abbreviateFilenames(account, evidenceFilesJson, symlinks);
        }

        const evidenceFileKeys =
            await this.controlEvidenceJsonUploaderService.uploadEvidenceJsonFilesBatch(
                account,
                auditId,
                evidenceFilesJson,
                ControlEvidenceJsonUploadType.CONTROL_EVIDENCE,
            );

        uploadedControlEvidenceKeys.buffersKey = evidenceFileKeys;

        // Only upload symlinks separately when filename abbreviation is disabled
        // When abbreviation is enabled, symlinks are handled within the abbreviated manifest
        if (!isEmpty(symlinks) && !isFilenameAbbreviationEnabled) {
            const symLinkFileKeys =
                await this.controlEvidenceJsonUploaderService.uploadEvidenceJsonFilesBatch(
                    account,
                    auditId,
                    symlinks,
                    ControlEvidenceJsonUploadType.SYMLINK,
                );

            uploadedControlEvidenceKeys.symlinksKey = symLinkFileKeys;
        }

        this.logger.log(
            PolloAdapter.acct(`Done transforming control evidence into buffers.`, event.account),
        );

        // upload evidence json files to s3
        return uploadedControlEvidenceKeys;
    }

    @ActivityNs()
    async uploadControlEvidencePackage(
        controlEvidenceKeys: UploadedControlEvidenceKeys,
        event: ControlEvidencePackageEvent,
    ): Promise<string> {
        let symlinksData: SymlinkType[] = [];

        const { account, auditId, controlIds, customerRequestId } = event;

        const audit = await this.auditRepository.getAuditByIdAndAccount(auditId, account.id);
        if (!audit) {
            throw new NotFoundException(ErrorCode.AUDIT_NOT_FOUND);
        }
        const { fulfilled: controlEvidenceFulfilled, rejected: controlEvidenceRejected } =
            await promiseAllSettledInBatches(
                controlEvidenceKeys.buffersKey,
                this.BATCH_DOWNLOAD,
                async key => {
                    return this.downloader.getPrivateFileWithBucket<
                        {
                            stream: string;
                            filename: string;
                            fileType?: string;
                        }[]
                    >(config.get('aws.s3.appBucket'), key);
                },
            );
        const controlEvidenceJson = controlEvidenceFulfilled.flat();
        if (controlEvidenceRejected.count) {
            this.logger.error(
                PolloAdapter.acct(
                    `Failed to download ${controlEvidenceRejected.count} control evidence files`,
                    account,
                ).setIdentifier({
                    reason: controlEvidenceRejected.reasons,
                }),
            );

            throw new Error(`Failed to download ${controlEvidenceRejected.count}`);
        }

        // parse controlEvidenceJSON - transform base64 string to Buffer - get FileBufferType array
        const controlEvidenceBuffers: FileBufferType[] = controlEvidenceJson.map(
            (item: { stream: string; filename: string; fileType?: string }) => {
                const { stream, ...rest } = item;

                return {
                    ...rest,
                    stream: Buffer.from(stream, 'base64'),
                };
            },
        );

        // parse symlinks json into SymlinkType array
        if (!isNil(controlEvidenceKeys.symlinksKey)) {
            const { fulfilled: symlinkFulfilled, rejected: symlinkRejected } =
                await promiseAllSettledInBatches(
                    controlEvidenceKeys.symlinksKey,
                    this.BATCH_DOWNLOAD,
                    async key => {
                        return this.downloader.getPrivateFileWithBucket<SymlinkType[]>(
                            config.get('aws.s3.appBucket'),
                            key,
                        );
                    },
                );
            symlinksData = symlinkFulfilled.flat();

            if (symlinkRejected.count) {
                this.logger.error(
                    PolloAdapter.acct(
                        `Failed to download ${symlinkRejected.count} control evidence files`,
                        account,
                    ).setIdentifier({
                        reason: symlinkRejected.reasons,
                    }),
                );
                throw new Error(`Failed to download ${controlEvidenceRejected.count}`);
            }
        }

        const controlEvidencePackageService = this.getControlEvidencePackageService(
            customerRequestId,
            controlIds,
        );
        const sampleData = await controlEvidencePackageService.getAuditSampleData(audit);
        const isFilenameAbbreviationEnabled =
            await this.releaseFilenameAbbreviation.isFeatureEnabled(account);

        let zipFilename = CONTROL_EVIDENCE_PACKAGE_ZIP_FILENAME;
        if (isFilenameAbbreviationEnabled) {
            const { controls } =
                await controlEvidencePackageService.getControlEvidencePackageParameters(audit, {
                    controlIds,
                    customerRequestId,
                });
            zipFilename = this.getEvidenceZipFilename(account, controls, customerRequestId);
        }

        // upload buffers and symlinks as zip using passthrough
        const uploadedEvidenceZip = await this.controlEvidenceJsonUploaderService.uploadPackage(
            account,
            controlEvidenceBuffers,
            sampleData.platform,
            symlinksData,
            UploadType.CONTROL_EVIDENCE_PACKAGE,
            zipFilename,
        );

        if (isNil(uploadedEvidenceZip?.Key)) {
            throw new Error(`Uploaded evidence did not return a key`);
        }
        return uploadedEvidenceZip?.Key;
    }

    getEvidenceZipFilename(
        account: Account,
        controls: Control[] = [],
        customerRequestId?: number,
    ): string {
        const formattedDate = formatDate(new Date(), 'yyyyMMdd');
        const sanitizedCompanyName = sanitizeFileName(account.companyName);
        // multiple controls package (from customer request)
        if (customerRequestId && controls.length > 1) {
            return `${sanitizedCompanyName}_REQ-${customerRequestId}_Evidence_${formattedDate}.zip`;
        }
        // single control package (from customer request)
        else if (controls.length === 1) {
            const controlCode = controls[0].code;
            return `${sanitizedCompanyName}_${controlCode}_${formattedDate}.zip`;
        }

        // complete control evidence package (include requirements)
        return `${sanitizedCompanyName}_Control_Evidence_${formattedDate}.zip`;
    }

    @ActivityNs()
    async getControlEvidencePackageDownloadLink(
        evidencePackageKey: string,
        event: ControlEvidencePackageEvent,
    ): Promise<Response> {
        const { companyArchiveId, account, customerRequestId } = event;
        const { signedUrl } = await this.downloader.getDownloadUrl(evidencePackageKey);

        if (!isNil(companyArchiveId)) {
            await this.companyArchiveRepository.update(
                { id: companyArchiveId },
                {
                    status: CompanyArchiveStatus.SUCCESS,
                    file: evidencePackageKey,
                },
            );

            return this.socket.sendMessage(
                account.id,
                SocketEvent.GENERATE_CONTROL_EVIDENCE_PACKAGE_COMPLETED,
                {},
            );
        }

        return this.socket.sendMessage(
            account.id,
            SocketEvent.GENERATE_REQUEST_CONTROL_EVIDENCE_PACKAGE_COMPLETED,
            signedUrl,
            customerRequestId,
        );
    }

    @ActivityNs()
    async cleanUpTemporaryFiles(fileKeys: string[], account: Account): Promise<boolean[]> {
        const sanitizedFileKeys = fileKeys.filter(key => !!key?.length);
        const { rejected, fulfilled } = await promiseAllSettledInBatches(
            sanitizedFileKeys,
            this.BATCH_DELETE,
            fileKey => this.deleter.deleteObject(fileKey),
        );
        if (rejected.count) {
            this.logger.error(
                PolloAdapter.acct(
                    `There were ${rejected.count} errors in deleting temporary files`,
                    account,
                )
                    .setContext(this.constructor.name)
                    .setSubContext(this.cleanUpTemporaryFiles.name),
                rejected.reasons.join(),
            );
        }

        return fulfilled;
    }

    @ActivityNs()
    async generateControlEvidencePackageEvent(event: ControlEvidencePackageEvent): Promise<void> {
        const { account, user, auditId, companyArchiveId, customerRequestId, controlIds } = event;

        const eventDto = {
            type: EventType.CONTROL_EVIDENCE_PACKAGE_GENERATED,
            category: EventCategory.AUDIT,
            source: EventSource.APP,
            description: format(
                eventDescriptions[EventType.CONTROL_EVIDENCE_PACKAGE_GENERATED],
                fullName(user),
            ),
            metadata: {
                dto: { auditId, companyArchiveId, customerRequestId, controlIds },
            },
            user,
        } as EventRequestDto;

        await this.eventsAppService.createEvent(account, eventDto);
    }

    @ActivityNs()
    async getControlEvidenceDownloadReadyEmailParams(
        event: ControlEvidencePackageEvent,
    ): Promise<ControlEvidencePackageEmailParams[]> {
        // // only sent this email when generating all control evidence package - no current request evidence package notification
        if (
            this.getSpecificControlEvidencesInRequest(event) ||
            this.getAllControlEvidencesInRequest(event)
        ) {
            return [];
        }

        const { account, auditId, company, user } = event;

        const audit = await this.auditRepository.getAuditByIdAndAccount(auditId, account.id);

        if (!audit) {
            throw new NotFoundException(ErrorCode.AUDIT_NOT_FOUND);
        }

        const auditors = await this.auditorRepository.getAuditorsByAccountAndAudit(
            audit.id,
            account.id,
        );

        const currentProduct = account.getCurrentProduct();

        const totalProducts = company.products?.length ?? 0;

        const hasMultipleWorkspaces = totalProducts > 1;

        // get user language
        let emailLanguage = getLanguage(user.language, account.language);

        const companyName = addApostrophe(encodeHTML(company.name));

        const frameworkName = audit.auditorFrameworkType.label;

        const productName = capitalize(currentProduct.name);
        if (audit.auditType === AuditType.FULL_AUDIT) {
            return auditors.map(auditor => {
                const clientId = auditor.entry.auditorClients[0].id;
                const ctaUrl = `${config.get('url.webApp')}/auditor/client/${clientId}/framework/${
                    audit.id
                }`;
                emailLanguage = getLanguage(auditor.language, account.language);
                return {
                    account,
                    auditType: audit.auditType,
                    recipientEmail: auditor.entry.email,
                    recipientCtaUrl: ctaUrl,
                    emailLanguage,
                    hasMultipleWorkspaces,
                    companyName,
                    frameworkName,
                    productName,
                };
            });
        } else {
            const ctaUrl = `${config.get('url.webApp')}/auditor/admin/framework/${audit.id}`;
            return [
                {
                    account,
                    auditType: audit.auditType,
                    recipientEmail: user.email,
                    recipientCtaUrl: ctaUrl,
                    emailLanguage,
                    hasMultipleWorkspaces,
                    companyName,
                    frameworkName,
                    productName,
                },
            ];
        }
    }

    private getControlEvidencePackageService(
        customerRequestId?: number,
        controlIds?: number[],
    ): CollectControlEvidenceService {
        const params = { customerRequestId, controlIds } as CustomerRequestIdControlIdsMapInput;
        switch (true) {
            case this.getSpecificControlEvidencesInRequest(params):
                return this.collectSelectControlsEvidenceService;
            case this.getAllControlEvidencesInRequest(params):
                return this.collectCustomerRequestEvidenceService;
            default:
                return this.collectAllControlEvidenceService;
        }
    }

    private getSpecificControlEvidencesInRequest(
        params: CustomerRequestIdControlIdsMapInput,
    ): boolean {
        return !isEmpty(params.controlIds) && !isNil(params.customerRequestId);
    }

    private getAllControlEvidencesInRequest(params: CustomerRequestIdControlIdsMapInput): boolean {
        return isEmpty(params.controlIds) && !isNil(params.customerRequestId);
    }

    private get companyArchiveRepository(): CompanyArchiveRepository {
        return this.getCustomTenantRepository(CompanyArchiveRepository);
    }
}
