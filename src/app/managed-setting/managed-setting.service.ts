import { Injectable } from '@nestjs/common';
import { ManagedSetting } from 'app/managed-setting/entities/managed-setting.entity';
import { ManagedSettingRepository } from 'app/managed-setting/repositories/managed-setting.repository';
import { ManagedSettingType } from 'commons/enums/managed-setting/managed-setting-type.enum';
import { AppService } from 'commons/services/app.service';
import config from 'config';
import { PolloMessage } from 'pollo-logger/classes/pollo.message';

@Injectable()
export class ManagedSettingService extends AppService {
    public async getAll(): Promise<Record<number, Record<string, string>>> {
        const settings = await this.managedSettingRepository.find();
        const result = {};
        for (const setting of settings) {
            if (!(setting.workspaceId in result)) {
                result[setting.workspaceId] = {};
            }
            result[setting.workspaceId][setting.type] = setting.value;
        }
        return result;
    }

    public cacheKey(type: ManagedSettingType, workspaceId: number): string {
        const accountId = this._tenancyContext.getAccountId() || 'global';
        const globalPrefix = config.get<string>('cache.globalPrefix') || 'globalstore';
        return `${globalPrefix}:{${accountId}}:managed-setting:${workspaceId}-${type}`;
    }

    public async get(type: ManagedSettingType, workspaceId: number): Promise<string> {
        const cachedValue = await this._cacheService.get<string>(this.cacheKey(type, workspaceId));
        if (cachedValue) {
            return cachedValue;
        }
        const value = await this.getWithDefault(type, workspaceId);
        await this._cacheService.set<string>(this.cacheKey(type, workspaceId), value);
        return value;
    }

    public async getWithDefault(type: ManagedSettingType, workspaceId: number): Promise<string> {
        return this.managedSettingRepository.get(type, workspaceId);
    }

    public async getFromDB(
        type: ManagedSettingType,
        workspaceId: number,
    ): Promise<ManagedSetting | null> {
        return this.managedSettingRepository.findOneBy({ type, workspaceId });
    }

    public async getFromDBOrFail(
        type: ManagedSettingType,
        workspaceId: number,
    ): Promise<ManagedSetting> {
        return this.managedSettingRepository.findOneByOrFail({
            type,
            workspaceId,
        });
    }

    public async set(type: ManagedSettingType, value: string, workspaceId: number): Promise<void> {
        await this.managedSettingRepository.set(type, value, workspaceId);
        await this._cacheService.set(this.cacheKey(type, workspaceId), value);
        this.logger.log(
            PolloMessage.msg('set ManagedSetting')
                .setAccountId(this._tenancyContext.getAccountId() ?? undefined)
                .setMetadata({ type }),
        );
    }

    public async delete(type: ManagedSettingType, workspaceId: number): Promise<void> {
        await this.managedSettingRepository.softDelete({ type, workspaceId });
        await this._cacheService.del(this.cacheKey(type, workspaceId));
        this.logger.log(
            PolloMessage.msg('delete ManagedSetting')
                .setAccountId(this._tenancyContext.getAccountId() ?? undefined)
                .setMetadata({ type }),
        );
    }

    private get managedSettingRepository(): ManagedSettingRepository {
        return this.getCustomTenantRepository(ManagedSettingRepository);
    }
}
