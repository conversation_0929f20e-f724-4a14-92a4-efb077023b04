import { CheckStatus, TestSource } from '@drata/enums';
import { faker } from '@faker-js/faker';
import { Logger } from '@nestjs/common';
import { TestingModule } from '@nestjs/testing';
import NotesServiceMock from 'app/access-review/mocks/service/notes.service.mock';
import { AiExecutionGroupService } from 'app/ai/services/ai-execution-group.service';
import { ConnectionEntity } from 'app/companies/connections/entities/connection.entity';
import { ConnectionsCoreService } from 'app/companies/connections/services/connections-core.service';
import { ConnectionsService } from 'app/companies/connections/services/connections.service';
import { Product } from 'app/companies/products/entities/product.entity';
import { ProductsService } from 'app/companies/products/services/products.service';
import { WorkspacesCoreService } from 'app/companies/products/services/workspaces-core.service';
import { CompaniesCoreService } from 'app/companies/services/companies-core.service';
import { CompaniesService } from 'app/companies/services/companies.service';
import { CreateEventCoreService } from 'app/create-event/create-event-core.service';
import { CreateEventService } from 'app/create-event/create-event.service';
import FeatureFlagServiceMock from 'app/custom-fields/mocks/service/feature-flag.service.mock';
import { EDRService } from 'app/edr/services/edr.service';
import { Event } from 'app/events/entities/event.entity';
import { EventsService } from 'app/events/events.service';
import { MonitoringTestTrendsConstants } from 'app/monitors/constants/monitoring-test-trends.constants';
import { ControlTestInstanceHistory } from 'app/monitors/entities/control-test-instance-history.entity';
import { ControlTestInstance } from 'app/monitors/entities/control-test-instance.entity';
import { MonitorInstanceExclusion } from 'app/monitors/entities/monitor-instance-exclusion.entity';
import { MonitorInstance } from 'app/monitors/entities/monitor-instance.entity';
import { monitorConnectionMock } from 'app/monitors/mocks/repositories/monitor-connection.mock';
import * as monitorServiceHelper from 'app/monitors/monitor-service.helper';
import { ControlTestInstanceHistoryRepository } from 'app/monitors/repositories/control-test-instance-history.repository';
import { ControlTestInstanceRepository } from 'app/monitors/repositories/control-test-instance.repository';
import { MonitorInstanceRepository } from 'app/monitors/repositories/monitor-instance.repository';
import { MonitoringSummaryIndexingService } from 'app/monitors/services/monitoring-summary-indexing.service';
import { MonitorsCoreService } from 'app/monitors/services/monitors-core.service';
import { MonitorsService } from 'app/monitors/services/monitors.service';
import { NotesService } from 'app/notes/services/notes.service';
import { ProviderCatalogService } from 'app/provider-catalog/provider-catalog.service';
import { Account } from 'auth/entities/account.entity';
import {
    getProviderTypeClientTypeMap,
    isClientTypeOfProviderType,
} from 'auth/helpers/provider-type.helper';
import { ClientType } from 'commons/enums/auth/client-type.enum';
import { ProviderType } from 'commons/enums/auth/provider-type.enum';
import { AutopilotTaskType } from 'commons/enums/autopilot/autopilot-task-type.enum';
import { RequestDescriptionType } from 'commons/enums/request-description-type.enum';
import { RunMode } from 'commons/enums/run-mode.enum';
import { NotFoundException } from 'commons/exceptions/not-found.exception';
import { createAppTestingModule } from 'commons/helpers/app-testing-module.helper';
import { getNumericEnumValues } from 'commons/helpers/enum.helper';
import * as monitorHelper from 'commons/helpers/monitor.helper';
import { clientTypeAutopilotTaskTypeMap } from 'commons/helpers/monitor.helper';
import { MockFactory } from 'commons/mocks/factories/mock.factory';
import { MockType } from 'commons/mocks/types/mock.type';
import { WorkspacesBaseService } from 'commons/services/workspaces-base.service';
import { Analytics } from 'dependencies/analytics/analytics';
import { Socket } from 'dependencies/socket/socket';
import { FeatureFlagService } from 'feature-flags/feature-flag.service';
import { get, isEmpty, isNaN, isNil } from 'lodash';
import moment from 'moment';
import { ControlTestTemplateRepository } from 'site-admin/repositories/control-test-template.repository';
import { MonitorTemplateRepository } from 'site-admin/repositories/monitor-template.repository';
import { TenancyContextMock } from 'tenancy/contexts/__mocks__/tenancy.context';
import { TenancyContext } from 'tenancy/contexts/tenancy.context';
import { getNewConnection } from 'tests/utils/connections.helper';
import { useSeeding } from 'typeorm-seeding';

const spyControlTestInstanceRepositorySave = jest.fn();

const mockAnalytics = {
    track: jest.fn(),
    identify: jest.fn(),
    group: jest.fn(),
    flush: jest.fn(),
    userIdPublic: jest.fn(),
};

const spyMonitorInstanceRepositorySave = jest.fn();

const spyGetMonitorTestInstanceByControlTestInstanceId = jest.fn();
const spyAggregateStats = jest.fn(() => ({}));

const tenancyContextMock = TenancyContextMock();

const connectionServiceMock = {
    getConnectionsByProviderType: (providerType: ProviderType) => {
        if (providerType !== ProviderType.INFRASTRUCTURE) {
            const clientTypesByProviderType = getProviderTypeClientTypeMap().get(providerType);
            let idConnection = 1;
            return clientTypesByProviderType.map(clientType => {
                const connection = getNewConnection({
                    providerTypes: [providerType],
                    clientType,
                    id: idConnection,
                });

                idConnection++;

                return connection;
            });
        } else {
            return [];
        }
    },
    getConnectionsByProviderTypes: (providerTypes: ProviderType[]) => {
        const clientTypesByProviderTypes = [];
        for (const providerType of providerTypes) {
            if (providerType !== ProviderType.INFRASTRUCTURE) {
                const clientTypesByProviderType = getProviderTypeClientTypeMap().get(providerType);

                return clientTypesByProviderType.forEach(clientType => {
                    clientTypesByProviderTypes.push(
                        getNewConnection({
                            providerTypes: [providerType],
                            clientType,
                        }),
                    );
                });
            }
        }
        return clientTypesByProviderTypes;
    },
    getConnectionsByClientTypes: (clientTypes: ClientType[]) => {
        const clientTypesByProviderTypes = [];
        for (const clientType of clientTypes) {
            if (isClientTypeOfProviderType(ProviderType.OBSERVABILITY, clientType)) {
                clientTypesByProviderTypes.push(
                    getNewConnection({
                        providerTypes: [ProviderType.OBSERVABILITY],
                        clientType,
                    }),
                );
            }
        }
        return clientTypesByProviderTypes;
    },
    getActiveConnectionsByProductId: () => {
        const clientTypesByProviderTypes = [];
        const clientTypes = [ClientType.AWS];
        for (const clientType of clientTypes) {
            if (isClientTypeOfProviderType(ProviderType.OBSERVABILITY, clientType)) {
                clientTypesByProviderTypes.push(
                    getNewConnection({
                        providerTypes: [ProviderType.OBSERVABILITY],
                        clientType,
                    }),
                );
            }
        }
        return clientTypesByProviderTypes;
    },
};

const productsServiceMock = {
    resolveProductsForConnection: () => {
        let products: Product[] = [];
        const product: Product = new Product();

        product.name = faker.company.name();
        product.description = faker.lorem.sentence();
        product.url = faker.internet.url();

        products = [product];
        return products;
    },
    getProductById: (productId: number) => {
        const product: Product = new Product();

        product.id = productId || 1;
        product.name = faker.company.name();
        product.description = faker.lorem.sentence();
        product.url = faker.internet.url();
        return product;
    },
};

const monitoringSummaryIndexingServiceMock = {
    indexSingleTestResult: () => null,
};

jest.mock('@temporalio/client', () => {
    return {
        Connection: { connect: () => ({}) },
        Client: jest.fn().mockImplementation(() => {
            return {
                schedule: {
                    getHandle: () => ({
                        describe: () => ({
                            scheduleId: 'scheduleId',
                        }),
                        update: () => ({}),
                        delete: () => ({}),
                    }),
                    create: () => ({
                        describe: () => ({
                            scheduleId: 'scheduleId',
                        }),
                    }),
                },
            };
        }),
        ScheduleOverlapPolicy: {
            BUFFER_ONE: 2,
        },
    };
});

let account;
let monitorsService: MonitorsService;
let spyGetAutopilotTaskTypesByProviderType;
let spyGetConnectionsByProviderType;
let spyGetConnectionsByProviderTypes;
let spyFindAutopilotTaskTypesForConnections;
let spyLog;

const mockGetControlTestInstanceForUser = (monitor, service, baseControlTestInstance) => {
    spyGetMonitorTestInstanceByControlTestInstanceId.mockResolvedValueOnce([monitor]);
    jest.spyOn(service, 'getControlTestInstanceByTestId').mockImplementationOnce(() =>
        Promise.resolve(baseControlTestInstance),
    );
    jest.spyOn(service, 'getComplianceCheckExclusionsPersonnel').mockImplementationOnce(() =>
        Promise.resolve([]),
    );
    jest.spyOn(monitorServiceHelper, 'getRequiredPolicies').mockImplementationOnce(() =>
        Promise.resolve(['some_policy']),
    );
};

beforeAll(async () => {
    Logger.overrideLogger(false);

    account = new Account();

    spyGetConnectionsByProviderType = jest.spyOn(
        connectionServiceMock,
        'getConnectionsByProviderType',
    );

    spyGetConnectionsByProviderTypes = jest.spyOn(
        connectionServiceMock,
        'getConnectionsByProviderTypes',
    );

    spyGetConnectionsByProviderTypes = jest.spyOn(
        productsServiceMock,
        'resolveProductsForConnection',
    );

    spyGetConnectionsByProviderTypes = jest.spyOn(productsServiceMock, 'getProductById');

    spyGetAutopilotTaskTypesByProviderType = jest.spyOn(
        monitorHelper,
        'getAutopilotTaskTypesByProviderType',
    );

    spyFindAutopilotTaskTypesForConnections = jest.spyOn(
        monitorServiceHelper,
        'findAutopilotTaskTypesForConnections',
    );
});

afterEach(() => {
    jest.clearAllMocks();
});

beforeEach(async () => {
    const module: TestingModule = await createAppTestingModule({
        providers: [
            MonitorsService,
            { provide: TenancyContext, useValue: tenancyContextMock },
            {
                provide: CreateEventCoreService,
                useValue: MockFactory.getMock(CreateEventService),
            },
            {
                provide: ConnectionsCoreService,
                useValue: connectionServiceMock,
            },
            { provide: CompaniesCoreService, useValue: MockFactory.getMock(CompaniesService) },
            {
                provide: WorkspacesCoreService,
                useValue: productsServiceMock,
            },
            {
                provide: NotesService,
                useValue: NotesServiceMock,
            },
            {
                provide: ConnectionsService,
                useValue: connectionServiceMock,
            },
            {
                provide: CreateEventService,
                useValue: MockFactory.getMock(CreateEventService),
            },
            {
                provide: EventsService,
                useValue: MockFactory.getMock(EventsService),
            },
            {
                provide: Socket,
                useValue: {},
            },
            {
                provide: CompaniesService,
                useValue: MockFactory.getMock(CompaniesService),
            },
            {
                provide: ProductsService,
                useValue: productsServiceMock,
            },
            {
                provide: ControlTestTemplateRepository,
                useValue: MockFactory.getMock(ControlTestTemplateRepository),
            },
            {
                provide: MonitorTemplateRepository,
                useValue: MockFactory.getMock(MonitorTemplateRepository),
            },
            {
                provide: EDRService,
                useValue: MockFactory.getMock(EDRService),
            },
            {
                provide: FeatureFlagService,
                useValue: FeatureFlagServiceMock,
            },
            {
                provide: AiExecutionGroupService,
                useValue: MockFactory.getMock(AiExecutionGroupService),
            },
            {
                provide: ProviderCatalogService,
                useValue: MockFactory.getMock(ProviderCatalogService),
            },
            {
                provide: MonitoringSummaryIndexingService,
                useValue: monitoringSummaryIndexingServiceMock,
            },
            {
                provide: Analytics,
                useValue: mockAnalytics,
            },
            {
                provide: MonitorsCoreService,
                useValue: MockFactory.getMock(MonitorsCoreService),
            },
            {
                provide: WorkspacesBaseService,
                useValue: MockFactory.getMock(WorkspacesBaseService),
            },
        ],
    }).compile();

    monitorsService = await module.resolve<MonitorsService>(MonitorsService);

    tenancyContextMock.getCustomRepository.mockImplementation(repository => {
        if (repository === ControlTestInstanceRepository) {
            return {
                getControlTestInstancesByAutopilotTaskTypesAndCheckStatus: jest.fn(),
                aggregateStats: spyAggregateStats,
                save: spyControlTestInstanceRepositorySave,
            };
        }
        if (repository === MonitorInstanceRepository) {
            return {
                getMonitorTestInstanceByControlTestInstanceId:
                    spyGetMonitorTestInstanceByControlTestInstanceId,
                save: spyMonitorInstanceRepositorySave,
            };
        }
        return { save: jest.fn() };
    });

    spyLog = jest.spyOn(monitorsService, 'log' as any);
});

describe('enableControlTestInstancesByClientType', () => {
    test('if connection entity has MDM providerType the method is not completed, finishes successfully', async () => {
        tenancyContextMock.getCustomRepository.mockReturnValue(() => {
            return { save: () => true };
        });

        await expect(
            monitorsService.enableControlTestInstancesByClientType(
                account,
                getNewConnection({ providerTypes: [ProviderType.MDM] }),
            ),
        ).resolves.not.toThrow();

        scenarioAssertions({
            callsGetAutopilotTaskTypesByProviderType: 0,
            callsGetConnectionsByProviderType: 0,
            callsFindAutopilotTaskTypesForConnections: 0,
            callsLog: 0,
            callsControlTestSave: 0,
            callsMonitorInstanceSave: 0,
        });
    });

    test('if connection entity has VERSION_CONTROL providerType and there are NO controlTestInstances to enable, finishes successfully', async () => {
        tenancyContextMock.getCustomRepository.mockImplementation((repository: any) => {
            if (repository === ControlTestInstanceRepository) {
                return {
                    getControlTestInstancesByAutopilotTaskTypesAndCheckStatus: () => [],
                    save: spyControlTestInstanceRepositorySave,
                };
            }
            return {
                save: spyMonitorInstanceRepositorySave,
            };
        });

        await expect(
            monitorsService.enableControlTestInstancesByClientType(
                account,
                getNewConnection({
                    providerTypes: [ProviderType.VERSION_CONTROL],
                    clientType: ClientType.GITHUB,
                }),
            ),
        ).resolves.not.toThrow();

        scenarioAssertions({
            callsGetAutopilotTaskTypesByProviderType: 1,
            callsGetConnectionsByProviderType: 1,
            callsFindAutopilotTaskTypesForConnections: 1,
            callsLog: 1,
            callsControlTestSave: 0,
            callsMonitorInstanceSave: 0,
        });
    });

    // eslint-disable-next-line max-len
    test("if connection entity has VERSION_CONTROL providerType, there are controlTestInstances which don't have NEEDS_ATTENTION/UPCOMMING checkStatus, finishes successfully", async () => {
        tenancyContextMock.getCustomRepository.mockImplementation((repository: any) => {
            if (repository === ControlTestInstanceRepository) {
                return {
                    getControlTestInstancesByAutopilotTaskTypesAndCheckStatus: () => {
                        return controlTestInstancesGenerator([
                            {
                                checkStatus: CheckStatus.DISABLED,
                                MonitorInstanceTaskType: NaN,
                            },
                            {
                                checkStatus: CheckStatus.TESTING,
                                MonitorInstanceTaskType: NaN,
                            },
                        ]);
                    },
                    save: spyControlTestInstanceRepositorySave,
                };
            }
            return {
                save: spyMonitorInstanceRepositorySave,
            };
        });

        await expect(
            monitorsService.enableControlTestInstancesByClientType(
                account,
                getNewConnection({
                    providerTypes: [ProviderType.VERSION_CONTROL],
                    clientType: ClientType.GITLAB,
                }),
            ),
        ).resolves.not.toThrow();

        scenarioAssertions({
            callsGetAutopilotTaskTypesByProviderType: 1,
            callsGetConnectionsByProviderType: 1,
            callsFindAutopilotTaskTypesForConnections: 1,
            callsLog: 0,
            callsControlTestSave: 0,
            callsMonitorInstanceSave: 0,
        });
    });

    // eslint-disable-next-line max-len
    test("if connection entity has VERSION_CONTROL providerType,there are controlTestInstances to enable which 'dont have monitorInstances related, finishes successfully", async () => {
        tenancyContextMock.getCustomRepository.mockImplementation((repository: any) => {
            if (repository === ControlTestInstanceRepository) {
                return {
                    getControlTestInstancesByAutopilotTaskTypesAndCheckStatus: () => {
                        return controlTestInstancesGenerator([
                            {
                                checkStatus: CheckStatus.UNUSED,
                                MonitorInstanceTaskType: NaN,
                            },
                        ]);
                    },
                    save: spyControlTestInstanceRepositorySave,
                };
            }
            return {
                save: spyMonitorInstanceRepositorySave,
            };
        });

        await expect(
            monitorsService.enableControlTestInstancesByClientType(
                account,
                getNewConnection({
                    providerTypes: [ProviderType.VERSION_CONTROL],
                    clientType: ClientType.AZURE_BOARDS,
                }),
            ),
        ).resolves.not.toThrow();

        scenarioAssertions({
            callsGetAutopilotTaskTypesByProviderType: 1,
            callsGetConnectionsByProviderType: 1,
            callsFindAutopilotTaskTypesForConnections: 1,
            callsLog: 0,
            callsControlTestSave: 0,
            callsMonitorInstanceSave: 0,
        });
    });

    // eslint-disable-next-line max-len
    test('if connection entity has VERSION_CONTROL providerType, there are controlTestInstances which have monitorInstances related but its autopilotTaskType is not implemented, finishes successfully', async () => {
        tenancyContextMock.getCustomRepository.mockImplementation((repository: any) => {
            if (repository === ControlTestInstanceRepository) {
                return {
                    getControlTestInstancesByAutopilotTaskTypesAndCheckStatus: () => {
                        return controlTestInstancesGenerator([
                            {
                                checkStatus: CheckStatus.UNUSED,
                                MonitorInstanceTaskType: 1,
                            },
                        ]);
                    },
                    save: spyControlTestInstanceRepositorySave,
                };
            }
            return {
                save: spyMonitorInstanceRepositorySave,
            };
        });

        await expect(
            monitorsService.enableControlTestInstancesByClientType(
                account,
                getNewConnection({
                    providerTypes: [ProviderType.VERSION_CONTROL],
                    clientType: ClientType.AZURE_BOARDS,
                }),
            ),
        ).resolves.not.toThrow();

        scenarioAssertions({
            callsGetAutopilotTaskTypesByProviderType: 1,
            callsGetConnectionsByProviderType: 1,
            callsFindAutopilotTaskTypesForConnections: 1,
            callsControlTestSave: 1,
            callsMonitorInstanceSave: 1,
            callsLog: 0,
        });
    });

    // eslint-disable-next-line max-len
    test('if connection entity has VERSION_CONTROL providerType, there are controlTestInstances which have monitorInstances related and its autopilotTaskType is implemented, finishes successfully', async () => {
        tenancyContextMock.getCustomRepository.mockImplementation((repository: any) => {
            if (repository === ControlTestInstanceRepository) {
                return {
                    getControlTestInstancesByAutopilotTaskTypesAndCheckStatus: () => {
                        const autopilotTaskTypesForBitbucket = getAutopilotTaskTypesByClientType(
                            ClientType.BITBUCKET,
                        );
                        if (isEmpty(autopilotTaskTypesForBitbucket)) throw new Error('error');
                        return controlTestInstancesGenerator([
                            {
                                checkStatus: CheckStatus.UNUSED,
                                MonitorInstanceTaskType: autopilotTaskTypesForBitbucket[1],
                            },
                        ]);
                    },
                    save: spyControlTestInstanceRepositorySave,
                };
            }
            return {
                save: spyMonitorInstanceRepositorySave,
            };
        });

        await expect(
            monitorsService.enableControlTestInstancesByClientType(
                account,
                getNewConnection({
                    providerTypes: [ProviderType.VERSION_CONTROL],
                    clientType: ClientType.BITBUCKET,
                }),
            ),
        ).resolves.not.toThrow();

        scenarioAssertions({
            callsGetAutopilotTaskTypesByProviderType: 1,
            callsGetConnectionsByProviderType: 1,
            callsFindAutopilotTaskTypesForConnections: 1,
            callsControlTestSave: 1,
            callsMonitorInstanceSave: 1,
            callsLog: 0,
        });
    });

    test('if connection entity has INFRASTRUCTURE providerType and there is a task type excluded, finishes successfully', async () => {
        tenancyContextMock.getCustomRepository.mockImplementation((repository: any) => {
            if (repository === ControlTestInstanceRepository) {
                return {
                    getControlTestInstancesByAutopilotTaskTypesAndCheckStatus: () => {
                        const autopilotTaskTypesForDigitalOcean = getAutopilotTaskTypesByClientType(
                            ClientType.DIGITAL_OCEAN,
                        );
                        if (isEmpty(autopilotTaskTypesForDigitalOcean)) throw new Error('error');
                        return controlTestInstancesGenerator([
                            {
                                checkStatus: CheckStatus.UNUSED,
                                MonitorInstanceTaskType: autopilotTaskTypesForDigitalOcean[1],
                            },
                        ]);
                    },
                    save: spyControlTestInstanceRepositorySave,
                };
            }
            return {
                save: spyMonitorInstanceRepositorySave,
            };
        });

        await expect(
            monitorsService.enableControlTestInstancesByClientType(
                account,
                getNewConnection({
                    providerTypes: [ProviderType.INFRASTRUCTURE],
                    clientType: ClientType.DIGITAL_OCEAN,
                    metadata: { clientKey: '123', clientSecret: null },
                }),
            ),
        ).resolves.not.toThrow();

        scenarioAssertions({
            callsGetAutopilotTaskTypesByProviderType: 1,
            callsGetConnectionsByProviderType: 1,
            callsFindAutopilotTaskTypesForConnections: 1,
            callsControlTestSave: 1,
            callsMonitorInstanceSave: 1,
            callsLog: 0,
        });
    });

    test('if connection entity has INFRASTRUCTURE providerType and there is a dependent test, it disconnects successfully', async () => {
        tenancyContextMock.getCustomRepository.mockImplementation((repository: any) => {
            if (repository === ControlTestInstanceRepository) {
                return {
                    getControlTestInstancesByAutopilotTaskTypesAndCheckStatus: () => {
                        const autopilotTaskTypesForAws = getAutopilotTaskTypesByClientType(
                            ClientType.AWS,
                        );
                        if (isEmpty(autopilotTaskTypesForAws)) throw new Error('error');
                        return controlTestInstancesGenerator([
                            {
                                checkStatus: CheckStatus.UNUSED,
                                MonitorInstanceTaskType: autopilotTaskTypesForAws[1],
                            },
                        ]);
                    },
                    save: spyControlTestInstanceRepositorySave,
                };
            }
            return {
                save: spyMonitorInstanceRepositorySave,
            };
        });

        const testConnection = getNewConnection({
            providerTypes: [ProviderType.INFRASTRUCTURE],
            clientType: ClientType.AWS,
            metadata: { clientKey: '123', clientSecret: null },
        });

        await monitorsService.setControlTestInstancesCheckStatusByActiveConnections(
            account,
            testConnection,
        );

        await expect(
            monitorsService.resolveControlTestConnectionDependencyOnDisconnection(
                account,
                testConnection,
            ),
        ).resolves.not.toThrow();

        const dependencies = monitorHelper.getAutopilotDependencies(testConnection);

        scenarioAssertions({
            callsGetAutopilotTaskTypesByProviderType: 1,
            callsGetConnectionsByProviderType: dependencies.length,
            callsGetConnectionsByProviderTypes: 2,
            callsFindAutopilotTaskTypesForConnections: dependencies.length,
            callsControlTestSave: dependencies.length + 1,
            callsMonitorInstanceSave: dependencies.length + 1,
            callsLog: 0,
        });
    });

    test('if connection entity has TICKETING providerType, there are NO task type implemented for client type, finishes with error', async () => {
        tenancyContextMock.getCustomRepository.mockImplementation((repository: any) => {
            if (repository === ControlTestInstanceRepository) {
                return {
                    getControlTestInstancesByAutopilotTaskTypesAndCheckStatus: () => {
                        const autopilotTaskTypesForDummyClientType =
                            getAutopilotTaskTypesByClientType(0);
                        if (isEmpty(autopilotTaskTypesForDummyClientType)) {
                            throw new Error('error');
                        }
                        return [];
                    },
                    save: spyControlTestInstanceRepositorySave,
                };
            }
            return {
                save: spyMonitorInstanceRepositorySave,
            };
        });

        await expect(
            monitorsService.setControlTestInstancesCheckStatusByActiveConnections(
                account,
                getNewConnection({
                    providerTypes: [ProviderType.TICKETING],
                    clientType: ClientType.ASANA,
                }),
            ),
        ).rejects.toThrow(/^error/);

        scenarioAssertions({
            callsGetAutopilotTaskTypesByProviderType: 1,
            callsGetConnectionsByProviderTypes: 1,
            callsGetConnectionsByProviderType: 0,
            callsFindAutopilotTaskTypesForConnections: 0,
            callsLog: 0,
            callsControlTestSave: 0,
            callsMonitorInstanceSave: 0,
        });
    });
});

describe('setControlTestInstancesCheckStatusByActiveConnections', () => {
    test('if connection entity has MDM providerType the method is not completed but finishes successfully though', async () => {
        tenancyContextMock.getCustomRepository.mockReturnValue(() => {
            return { save: spyMonitorInstanceRepositorySave };
        });

        await expect(
            monitorsService.setControlTestInstancesCheckStatusByActiveConnections(
                account,
                getNewConnection({ providerTypes: [ProviderType.MDM] }),
            ),
        ).resolves.not.toThrow();

        const noExpectedMockCalls = {};
        scenarioAssertions(noExpectedMockCalls);
    });

    // eslint-disable-next-line max-len
    test('if connection entity has VERSION_CONTROL providerType and there are NO controlTestInstances the method is not completed but finishes successfully though', async () => {
        tenancyContextMock.getCustomRepository.mockImplementation((repository: any) => {
            if (repository === ControlTestInstanceRepository) {
                return {
                    getControlTestInstancesByAutopilotTaskTypesAndCheckStatus: () => [],
                    save: spyControlTestInstanceRepositorySave,
                };
            }
            return {
                save: () => false,
            };
        });

        await expect(
            monitorsService.setControlTestInstancesCheckStatusByActiveConnections(
                account,
                getNewConnection({
                    providerTypes: [ProviderType.VERSION_CONTROL],
                    clientType: ClientType.GITHUB,
                }),
            ),
        ).resolves.not.toThrow();

        scenarioAssertions({
            callsGetAutopilotTaskTypesByProviderType: 1,
            callsLog: 1,
            callsGetConnectionsByProviderTypes: 1,
        });
    });

    // eslint-disable-next-line max-len
    test("if connection is version control type and there are controlTestInstances with monitorInstances NULL the method isn't completed and throws an exception", async () => {
        tenancyContextMock.getCustomRepository.mockImplementation((repository: any) => {
            if (repository === ControlTestInstanceRepository) {
                return {
                    getControlTestInstancesByAutopilotTaskTypesAndCheckStatus: () => {
                        return controlTestInstancesGenerator([
                            {
                                checkStatus: CheckStatus.UNUSED,
                                MonitorInstanceTaskType: null,
                            },
                        ]);
                    },
                    save: spyControlTestInstanceRepositorySave,
                };
            }
            return {
                save: spyMonitorInstanceRepositorySave,
            };
        });

        await expect(
            monitorsService.setControlTestInstancesCheckStatusByActiveConnections(
                account,
                getNewConnection({
                    providerTypes: [ProviderType.VERSION_CONTROL],
                    clientType: ClientType.GITLAB,
                }),
            ),
        ).rejects.toThrow("Cannot read properties of null (reading 'find')");

        scenarioAssertions({
            callsGetAutopilotTaskTypesByProviderType: 1,
            callsGetConnectionsByProviderTypes: 1,
        });
    });

    // eslint-disable-next-line max-len
    test('if connection is version control provider type and there are controlTestInstances which have monitorInstances with task type values outside the allowed, this case is 0, the method finalizes successfully', async () => {
        tenancyContextMock.getCustomRepository.mockImplementation((repository: any) => {
            if (repository === ControlTestInstanceRepository) {
                return {
                    getControlTestInstancesByAutopilotTaskTypesAndCheckStatus: () => {
                        return controlTestInstancesGenerator([
                            {
                                checkStatus: CheckStatus.UNUSED,
                                MonitorInstanceTaskType: 0,
                            },
                        ]);
                    },
                    save: spyControlTestInstanceRepositorySave,
                };
            }
            return {
                save: spyMonitorInstanceRepositorySave,
            };
        });

        await expect(
            monitorsService.setControlTestInstancesCheckStatusByActiveConnections(
                account,
                getNewConnection({
                    providerTypes: [ProviderType.VERSION_CONTROL],
                    clientType: ClientType.GITLAB,
                }),
            ),
        ).resolves.not.toThrow();

        scenarioAssertions({
            callsGetAutopilotTaskTypesByProviderType: 1,
            callsGetConnectionsByProviderTypes: 1,
            callsControlTestSave: 1,
            callsMonitorInstanceSave: 1,
        });
    });

    // eslint-disable-next-line max-len
    test('if connection is version control provider type and there are controlTestInstances which have monitorInstances with task type values inside allowed, the method finalizes successfully', async () => {
        tenancyContextMock.getCustomRepository.mockImplementation((repository: any) => {
            if (repository === ControlTestInstanceRepository) {
                return {
                    getControlTestInstancesByAutopilotTaskTypesAndCheckStatus: () => {
                        const autopilotTaskTypesForGitlab = getAutopilotTaskTypesByClientType(
                            ClientType.GITLAB,
                        );
                        if (isEmpty(autopilotTaskTypesForGitlab))
                            throw new Error('There are no task types for GITLAB implemented');
                        return controlTestInstancesGenerator([
                            {
                                checkStatus: CheckStatus.UNUSED,
                                MonitorInstanceTaskType: autopilotTaskTypesForGitlab[1],
                            },
                        ]);
                    },
                    save: spyControlTestInstanceRepositorySave,
                };
            }
            return {
                save: spyMonitorInstanceRepositorySave,
            };
        });

        await expect(
            monitorsService.setControlTestInstancesCheckStatusByActiveConnections(
                account,
                getNewConnection({
                    providerTypes: [ProviderType.VERSION_CONTROL],
                    clientType: ClientType.GITLAB,
                }),
            ),
        ).resolves.not.toThrow();

        scenarioAssertions({
            callsGetAutopilotTaskTypesByProviderType: 1,
            callsGetConnectionsByProviderTypes: 1,
            callsControlTestSave: 1,
            callsMonitorInstanceSave: 1,
        });
    });

    // eslint-disable-next-line max-len
    test('if connection is version control provider type and there are controlTestInstances but there are NO active connections, the method is completed and finalizes successfully', async () => {
        connectionServiceMock.getConnectionsByProviderType = () => [];

        tenancyContextMock.getCustomRepository.mockImplementation((repository: any) => {
            if (repository === ControlTestInstanceRepository) {
                return {
                    getControlTestInstancesByAutopilotTaskTypesAndCheckStatus: () => {
                        return controlTestInstancesGenerator([
                            {
                                checkStatus: CheckStatus.UNUSED,
                                MonitorInstanceTaskType: 2,
                            },
                        ]);
                    },
                    save: spyControlTestInstanceRepositorySave,
                };
            }
            return {
                save: spyMonitorInstanceRepositorySave,
            };
        });

        await expect(
            monitorsService.setControlTestInstancesCheckStatusByActiveConnections(
                account,
                getNewConnection({
                    providerTypes: [ProviderType.VERSION_CONTROL],
                    clientType: ClientType.GITLAB,
                }),
            ),
        ).resolves.not.toThrow();

        scenarioAssertions({
            callsGetAutopilotTaskTypesByProviderType: 1,
            callsGetConnectionsByProviderTypes: 1,
            callsControlTestSave: 1,
            callsMonitorInstanceSave: 1,
        });
    });
});

describe('MonitorsService', () => {
    beforeAll(async () => {
        await useSeeding({
            configName: './src/tests/ormconfig-unit-tests',
        });
    });

    describe('getControlTestInstanceForUser', () => {
        let baseControlTestInstance: ControlTestInstance;
        const monitor = {
            requestDescriptions: JSON.stringify([
                {
                    requestDescriptionType: RequestDescriptionType.CONNECTION,
                    clientType: ClientType.AWS,
                },
                {
                    requestDescriptionType: RequestDescriptionType.CONNECTION,
                    clientType: ClientType.AWS_ORG_UNITS,
                },
            ]),
        };

        beforeEach(() => {
            baseControlTestInstance = Object.assign(new ControlTestInstance(), {
                monitorInstances: [],
                runMode: RunMode.AP1,
                source: TestSource.DRATA,
                recipes: [
                    {
                        recipe: {
                            providers: [
                                {
                                    provider: 'GCP',
                                },
                                {
                                    provider: 'AZURE',
                                },
                            ],
                        },
                    },
                ],
            });
            account = Object.assign(new Account());
            account.setCurrentProduct({ id: 1 } as any as Product);
        });

        it('should get available connections when test is AP1', async () => {
            mockGetControlTestInstanceForUser(monitor, monitorsService, baseControlTestInstance);

            const response = await monitorsService.getControlTestInstanceForUser(account, 1);

            expect(response.availableConnections).toEqual([
                { clientType: 'AWS' },
                { clientType: 'AWS_ORG_UNITS' },
            ]);
        });

        it('should get available connections when test is AP2', async () => {
            baseControlTestInstance.runMode = RunMode.AP2;
            mockGetControlTestInstanceForUser(monitor, monitorsService, baseControlTestInstance);

            const response = await monitorsService.getControlTestInstanceForUser(account, 1);

            expect(response.availableConnections).toEqual([
                { clientType: 'GCP' },
                { clientType: 'AZURE' },
            ]);
        });

        it('should get required policies when policies exist', async () => {
            baseControlTestInstance.checkStatus = CheckStatus.DISABLED;
            baseControlTestInstance.runMode = RunMode.AP2;
            mockGetControlTestInstanceForUser(monitor, monitorsService, baseControlTestInstance);

            const response = await monitorsService.getControlTestInstanceForUser(account, 1);

            expect(response.requiredPolicies).toEqual(['some_policy']);
        });
    });

    describe('getInfrastructureMonitorsForCsvExport', () => {
        const controlTestInstanceRepositoryMock: MockType<ControlTestInstanceRepository> =
            monitorConnectionMock.getCustomRepository(ControlTestInstanceRepository);
        const controlTestInstanceHistoryRepositoryMock: MockType<ControlTestInstanceHistoryRepository> =
            monitorConnectionMock.getCustomRepository(ControlTestInstanceHistoryRepository);

        beforeEach(() => {
            tenancyContextMock.getCustomRepository = monitorConnectionMock.getCustomRepository;
        });

        afterEach(() => {
            tenancyContextMock.getCustomRepository = TenancyContextMock().getCustomRepository;
        });

        describe('Given an account, testId, included type, and null check type', () => {
            let accountA: Account;
            let testId: number;
            let type: 'included' | 'excluded';
            const checkType = null;

            beforeEach(() => {
                accountA = new Account();
                testId = 102;
                type = 'included';
            });

            describe('Given there are no monitor instance exclusions', () => {
                let controlTestInstance: ControlTestInstance;

                beforeEach(() => {
                    controlTestInstance = new ControlTestInstance();
                    controlTestInstance.monitorInstanceExclusions = [];

                    const monitorInstance = new MonitorInstance();
                    monitorInstance.metadata = JSON.stringify([
                        [
                            1,
                            {
                                monitorResult: {
                                    clientType: ClientType.GCP,
                                    clientId: 'some-client-id',
                                    clientAlias: 'some-alias',
                                    fail: [
                                        {
                                            id: 'fail-id-1',
                                            resourceArn: 'some-arn',
                                            accountId: 'account-id',
                                            accountName: 'account-name',
                                            region: 'NA',
                                        },
                                        {
                                            id: 'fail-id-2',
                                            resourceArn: 'some-arn',
                                            accountId: 'account-id',
                                            accountName: 'account-name',
                                            region: 'NA',
                                        },
                                    ],
                                },
                            },
                        ],
                    ]);
                    monitorInstance.url =
                        'https://help.drata.com/en/articles/4776984-test-database-read-i-o-monitored';
                    controlTestInstance.monitorInstances = [monitorInstance];

                    const controlTestInstanceHistory = new ControlTestInstanceHistory();
                    const event = new Event();
                    event.metadata = JSON.stringify({
                        response: { message: 'an error event happened' },
                    });
                    controlTestInstanceHistory.events = [event];

                    controlTestInstanceRepositoryMock.getControlTestInstanceExclusionsByTestId.mockResolvedValue(
                        controlTestInstance,
                    );
                    controlTestInstanceHistoryRepositoryMock.getMostRecentByTestId.mockResolvedValue(
                        controlTestInstanceHistory,
                    );
                    controlTestInstanceRepositoryMock.getControlTestInstanceByTestIdWithAllMonitorInstances.mockResolvedValue(
                        controlTestInstance,
                    );
                });

                it('Should return all data', async () => {
                    const result = await monitorsService.getInfrastructureMonitorsForCsvExport(
                        accountA,
                        testId,
                        type,
                        checkType,
                    );
                    expect(result).toEqual({
                        data: [
                            {
                                accountId: 'account-id',
                                accountName: 'account-name',
                                checkType: null,
                                clientAlias: 'some-alias',
                                clientId: 'some-client-id',
                                clientType: 5,
                                error: 'an error event happened',
                                headers: {
                                    accountId: true,
                                    accountName: true,
                                    arn: true,
                                    organizationalUnitId: false,
                                    region: true,
                                },
                                id: 'fail-id-1',
                                region: 'NA',
                                resourceArn: 'some-arn',
                                url: 'https://help.drata.com/en/articles/4776984-test-database-read-i-o-monitored',
                            },
                            {
                                accountId: 'account-id',
                                accountName: 'account-name',
                                checkType: null,
                                clientAlias: 'some-alias',
                                clientId: 'some-client-id',
                                clientType: 5,
                                error: 'an error event happened',
                                headers: {
                                    accountId: true,
                                    accountName: true,
                                    arn: true,
                                    organizationalUnitId: false,
                                    region: true,
                                },
                                id: 'fail-id-2',
                                region: 'NA',
                                resourceArn: 'some-arn',
                                url: 'https://help.drata.com/en/articles/4776984-test-database-read-i-o-monitored',
                            },
                        ],
                        filename: '',
                    });
                });
            });

            describe('Given that there is a monitor instance exclusion that excludes fail-id-1', () => {
                let controlTestInstance: ControlTestInstance;

                beforeEach(() => {
                    const connection = new ConnectionEntity();
                    connection.clientType = ClientType.GCP;
                    connection.clientId = 'some-client-id';

                    const monitorExclusion = new MonitorInstanceExclusion();
                    monitorExclusion.connection = connection;
                    monitorExclusion.targetName = 'exclusion-target';
                    monitorExclusion.targetId = 'fail-id-1';

                    controlTestInstance = new ControlTestInstance();
                    controlTestInstance.monitorInstanceExclusions = [];

                    const monitorInstance = new MonitorInstance();
                    monitorInstance.metadata = JSON.stringify([
                        [
                            1,
                            {
                                monitorResult: {
                                    clientType: ClientType.GCP,
                                    clientId: 'some-client-id',
                                    clientAlias: 'some-alias',
                                    fail: [
                                        {
                                            id: 'fail-id-1',
                                            resourceArn: 'some-arn',
                                            accountId: 'account-id',
                                            accountName: 'account-name',
                                            region: 'NA',
                                        },
                                        {
                                            id: 'fail-id-2',
                                            resourceArn: 'some-arn',
                                            accountId: 'account-id',
                                            accountName: 'account-name',
                                            region: 'NA',
                                        },
                                    ],
                                },
                            },
                        ],
                    ]);
                    monitorInstance.url =
                        'https://help.drata.com/en/articles/4776984-test-database-read-i-o-monitored';
                    controlTestInstance.monitorInstances = [monitorInstance];

                    controlTestInstance.monitorInstanceExclusions = [monitorExclusion];

                    const controlTestInstanceHistory = new ControlTestInstanceHistory();
                    const event = new Event();
                    event.metadata = JSON.stringify({
                        response: { message: 'an error event happened' },
                    });
                    controlTestInstanceHistory.events = [event];

                    controlTestInstanceRepositoryMock.getControlTestInstanceExclusionsByTestId.mockResolvedValue(
                        controlTestInstance,
                    );
                    controlTestInstanceHistoryRepositoryMock.getMostRecentByTestId.mockResolvedValue(
                        controlTestInstanceHistory,
                    );
                    controlTestInstanceRepositoryMock.getControlTestInstanceByTestIdWithAllMonitorInstances.mockResolvedValue(
                        controlTestInstance,
                    );
                });

                it('Should filter out fail-id-1 because it is excluded', async () => {
                    const result = await monitorsService.getInfrastructureMonitorsForCsvExport(
                        accountA,
                        testId,
                        type,
                        checkType,
                    );
                    expect(result).not.toContain({
                        id: 'fail-id-1',
                        resourceArn: 'some-arn',
                        accountId: 'account-id',
                        accountName: 'account-name',
                        region: 'NA',
                    });

                    expect(result).toEqual({
                        data: [
                            {
                                accountId: 'account-id',
                                accountName: 'account-name',
                                checkType: null,
                                clientAlias: 'some-alias',
                                clientId: 'some-client-id',
                                clientType: 5,
                                error: 'an error event happened',
                                headers: {
                                    accountId: true,
                                    accountName: true,
                                    arn: true,
                                    organizationalUnitId: false,
                                    region: true,
                                },
                                id: 'fail-id-2',
                                region: 'NA',
                                resourceArn: 'some-arn',
                                url: 'https://help.drata.com/en/articles/4776984-test-database-read-i-o-monitored',
                            },
                        ],
                        filename: '',
                    });
                });
            });
        });
    });
});

const getAutopilotTaskTypesByClientType = clientType => {
    const autopilotTaskTypesImplemented = [];
    for (const autopilotTaskType of getNumericEnumValues(AutopilotTaskType)) {
        const clientTypes = get(
            clientTypeAutopilotTaskTypeMap,
            autopilotTaskType,
            [],
        ) as ClientType[];
        if (!isEmpty(clientTypes) && clientTypes.includes(clientType)) {
            autopilotTaskTypesImplemented.push(autopilotTaskType);
        }
    }
    return autopilotTaskTypesImplemented;
};

const scenarioAssertions = ({
    callsGetAutopilotTaskTypesByProviderType = 0,
    callsGetConnectionsByProviderType = 0,
    callsGetConnectionsByProviderTypes = 0,
    callsFindAutopilotTaskTypesForConnections = 0,
    callsLog = 0,
    callsControlTestSave = 0,
    callsMonitorInstanceSave = 0,
}: {
    callsGetAutopilotTaskTypesByProviderType?: number;
    callsGetConnectionsByProviderType?: number;
    callsGetConnectionsByProviderTypes?: number;
    callsFindAutopilotTaskTypesForConnections?: number;
    callsLog?: number;
    callsControlTestSave?: number;
    callsMonitorInstanceSave?: number;
}) => {
    expect(spyGetAutopilotTaskTypesByProviderType).toHaveBeenCalledTimes(
        callsGetAutopilotTaskTypesByProviderType,
    );
    expect(spyGetConnectionsByProviderType).toHaveBeenCalledTimes(
        callsGetConnectionsByProviderType,
    );
    expect(spyGetConnectionsByProviderTypes).toHaveBeenCalledTimes(
        callsGetConnectionsByProviderTypes,
    );
    expect(spyFindAutopilotTaskTypesForConnections).toHaveBeenCalledTimes(
        callsFindAutopilotTaskTypesForConnections,
    );
    expect(spyControlTestInstanceRepositorySave).toHaveBeenCalledTimes(callsControlTestSave);
    expect(spyMonitorInstanceRepositorySave).toHaveBeenCalledTimes(callsMonitorInstanceSave);
    expect(spyLog).toHaveBeenCalledTimes(callsLog);
};

const controlTestInstancesGenerator = (
    data: {
        checkStatus: CheckStatus;
        MonitorInstanceTaskType: number; //null: monitorInstances is null, NaN: monitorInstances is empty, number: monitorInstances has values
    }[],
): ControlTestInstance[] => {
    const controlTestInstances: ControlTestInstance[] = [];
    for (const item of data) {
        const controlTestInstance = new ControlTestInstance();
        controlTestInstance.checkStatus = item.checkStatus;
        controlTestInstance.draft = false;
        if (!isNaN(item.MonitorInstanceTaskType)) {
            if (!isNil(item.MonitorInstanceTaskType)) {
                controlTestInstance.monitorInstances = [
                    {
                        ...new MonitorInstance(),
                        autopilotTaskType: item.MonitorInstanceTaskType,
                    } as unknown as MonitorInstance,
                ];
            } else {
                controlTestInstance.monitorInstances = null;
            }
        }
        controlTestInstances.push(controlTestInstance);
    }
    return controlTestInstances;
};

describe('getTestTrendsOverview', () => {
    let product: Product;
    let testAccount: Account;
    let failedTestsMock: Partial<ControlTestInstance>[];
    let nowDateMock: Date;
    let daysAgoFailedTestsHistoryMock: Partial<ControlTestInstanceHistory>[];
    let avgTimeToRemediationMs: number;
    let failuresResolved: number;
    let timeToRemediationDelta: number;
    let passedCount: number;
    let errorCount: number;
    let ctiRepoMock: jest.Mocked<ControlTestInstanceRepository>;
    let ctihRepoMock: jest.Mocked<ControlTestInstanceHistoryRepository>;

    beforeEach(() => {
        product = Object.assign(new Product(), { id: 1 });
        testAccount = Object.assign(new Account());
        testAccount.setCurrentProduct(product);
        nowDateMock = new Date();
        avgTimeToRemediationMs = ********;
        timeToRemediationDelta = 0;
        failuresResolved = 1;
        passedCount = 120;
        errorCount = 3;
        failedTestsMock = [
            {
                id: 101,
                testId: 101,
                name: 'Failed Test A',
                lastCheck: nowDateMock,
                monitorInstances: [{ monitorInstanceCheckTypes: [{ checkType: 3 }] } as any],
            },
            {
                id: 102,
                testId: 102,
                name: 'Failed Test B',
                lastCheck: nowDateMock,
                monitorInstances: [{ monitorInstanceCheckTypes: [{ checkType: 3 }] } as any],
            },
        ];
        daysAgoFailedTestsHistoryMock = [
            {
                controlTestInstance: {
                    monitorInstances: [{ monitorInstanceCheckTypes: [{ checkType: 3 }] } as any],
                } as any,
            },
        ];
        ctiRepoMock = {
            getFailedControlTestInstances: jest.fn().mockResolvedValue(failedTestsMock),
            getPassedControlTestInstancesCount: jest.fn().mockResolvedValue(passedCount),
            getErrorControlTestInstancesCount: jest.fn().mockResolvedValue(errorCount),
        } as unknown as jest.Mocked<ControlTestInstanceRepository>;
        ctihRepoMock = {
            getFailedControlTestDaysAgo: jest.fn().mockResolvedValue(daysAgoFailedTestsHistoryMock),
            getMostRecentFailure: jest.fn().mockResolvedValue([{ created_at: nowDateMock, id: 1 }]),
            getAverageTimeToRemediationMs: jest.fn().mockResolvedValue({
                avgTimeToRemediationMs,
                failuresResolved,
            }),
        } as unknown as jest.Mocked<ControlTestInstanceHistoryRepository>;
        tenancyContextMock.getCustomRepository.mockImplementation(repository => {
            switch (repository) {
                case ControlTestInstanceRepository:
                    return ctiRepoMock;
                case ControlTestInstanceHistoryRepository:
                    return ctihRepoMock;
                default:
                    return {};
            }
        });
    });

    it('should skip cache value if non DEFAULT_TEST_TRENDS_TIMEFRAME_DAYS is used', async () => {
        const dayRange = 30;
        const fromDate = moment(nowDateMock).subtract(dayRange, 'days').startOf('day').toDate();

        monitorsService['getCacheValueForKey'] = jest.fn();
        monitorsService['setCacheValueForKey'] = jest.fn();

        const result = await monitorsService.getTestTrendsOverview(
            testAccount,
            product.id,
            dayRange,
        );

        expect(result).toEqual({
            window: {
                from: fromDate,
                to: expect.any(Date),
            },
            counts: {
                passing: passedCount,
                failed: 2,
                error: errorCount,
            },
            avgTimeToRemediationMs,
            includedRecoveries: failuresResolved,
            timeToRemediationDelta,
        });

        expect(ctiRepoMock.getFailedControlTestInstances).toHaveBeenCalled();
        expect(ctiRepoMock.getFailedControlTestInstances).toHaveBeenCalledWith(
            expect.objectContaining({ id: product.id }),
        );
        expect(ctiRepoMock.getErrorControlTestInstancesCount).toHaveBeenCalledWith(
            expect.objectContaining({ id: product.id }),
        );
        expect(ctihRepoMock.getAverageTimeToRemediationMs).toHaveBeenCalledWith(
            expect.anything(),
            product.id,
            fromDate,
            MonitoringTestTrendsConstants.DEFAULT_TEST_TRENDS_MAX_LOOK_BEHIND_DAYS,
            expect.any(Array),
        );
        expect(monitorsService['getCacheValueForKey']).not.toHaveBeenCalled();
        expect(monitorsService['setCacheValueForKey']).not.toHaveBeenCalled();
    });

    it('should throw if workspaceId does not match account current product and cannot be found ', async () => {
        const workspaceId = 999;

        const getProductByIdSpy = jest
            .spyOn(monitorsService['workspacesBaseService'], 'getProductById')
            .mockResolvedValue(null);

        await expect(
            monitorsService.getTestTrendsOverview(testAccount, workspaceId),
        ).rejects.toThrow(NotFoundException);

        expect(getProductByIdSpy).toHaveBeenCalledWith(workspaceId);
    });
});
