import {
    CheckResultStatus,
    CheckStatus,
    <PERSON><PERSON>r<PERSON><PERSON>,
    TestSource,
    VulnerabilitySeverity,
    VulnerabilityStatus,
} from '@drata/enums';
import { Injectable, InternalServerErrorException, NotFoundException } from '@nestjs/common';
import { resultExclusionFuzzyMatch } from 'app/autopilot/helper/fuzzy-matching.helper';
import { MonitorInstanceMetadataItem } from 'app/autopilot/types/monitor-instance-metadata-item.type';
import { ConnectionEntity } from 'app/companies/connections/entities/connection.entity';
import { Company } from 'app/companies/entities/company.entity';
import { ControlTestInstanceTestIdComplianceCheckTypes } from 'app/compliance-check-exclusions/maps/control-test-instance-test-id-compliance-check-types.map';
import { ComplianceCheckExclusionRepository } from 'app/compliance-check-exclusions/repositories/compliance-check-exclusion.repository';
import { Event } from 'app/events/entities/event.entity';
import { EventsService } from 'app/events/events.service';
import { TestGraphsType } from 'app/grc/types/test-graphs.type';
import { FindingExclusion } from 'app/iac-scanning/entities/finding-exclusion.entity';
import { CodeRepositorySettingsRepository } from 'app/iac-scanning/repositories/code-repository-settings.repository';
import { FindingExclusionRepository } from 'app/iac-scanning/repositories/finding-exclusion.repository';
import { MonitorsTestsReportsConstants } from 'app/monitors/constants/monitors-tests-reports.constants';
import { ControlTestInstanceHistoryRequestDto } from 'app/monitors/dtos/control-test-instance-history-request.dto';
import { FindingsCsvFactory } from 'app/monitors/dtos/csv/factory/findings-csv.factory';
import { MonitorsRequestDto } from 'app/monitors/dtos/monitors-request.dto';
import { ControlTestInstance } from 'app/monitors/entities/control-test-instance.entity';
import { MonitorInstanceExclusion } from 'app/monitors/entities/monitor-instance-exclusion.entity';
import { MonitorInstance } from 'app/monitors/entities/monitor-instance.entity';
import { MonitorStat } from 'app/monitors/entities/monitor-stat.entity';
import { MonitorFindingsType } from 'app/monitors/enums/monitor-findings-type.enum';
import { getControlTestHistoryByWeekLabels } from 'app/monitors/helpers/control-test.sql.helper';
import { ControlTestInstanceHistoryRepository } from 'app/monitors/repositories/control-test-instance-history.repository';
import { ControlTestInstanceRepository } from 'app/monitors/repositories/control-test-instance.repository';
import { MonitorInstanceExclusionRepository } from 'app/monitors/repositories/monitor-instance-exclusion.repository';
import { MonitorInstanceRepository } from 'app/monitors/repositories/monitor-instance.repository';
import { MonitorStatRepository } from 'app/monitors/repositories/monitor-stat.repository';
import { ControlTestInstanceHistoryType } from 'app/monitors/types/control-test-instance-history.type';
import { MonitorExclusionData } from 'app/monitors/types/monitor-exclusion-data.type';
import { MonitorExclusionsRequestType } from 'app/monitors/types/monitor-exclusions-request.type';
import { MonitorListRequestType } from 'app/monitors/types/monitor-list-request.type';
import { MonitorRequestType } from 'app/monitors/types/monitor-request.type';
import { MonitorTestFailureItem } from 'app/monitors/types/monitor-test-failure-item.type';
import { MonitorTestFailuresRequestType } from 'app/monitors/types/monitor-test-failures-request.type';
import { User } from 'app/users/entities/user.entity';
import { PersonnelRepository } from 'app/users/personnel/repositories/personnel.repository';
import { UserRepository } from 'app/users/repositories/user.repository';
import { VulnerabilityFindingRepository } from 'app/vulnerability/repositories/monitoring/vulnerability-finding.repository';
import { monitorFindingsCsvDownloadWorkflowV1 } from 'app/worker/workflows/monitors/findings/monitor-findings-csv-download.v1.workflow';
import { monitorFindingsZipDownloadWorkflowV1 } from 'app/worker/workflows/monitors/findings/monitor-findings-zip-download.v1.workflow';
import { Account } from 'auth/entities/account.entity';
import { ClientTypeDescription } from 'auth/entities/client-type-description-map.enum';
import { PaginationExclusionRequestDto } from 'autopilot2/dtos/pagination-exclusion-request.dto';
import { ExtractedRequestProps } from 'commons/decorators/get-request-notifier-props.decorator';
import { ClientType } from 'commons/enums/auth/client-type.enum';
import { CheckType } from 'commons/enums/check-type.enum';
import { MonitorStatType } from 'commons/enums/monitors/monitor-stat-type.enum';
import { Priority } from 'commons/enums/priority.enum';
import { ReportInterval } from 'commons/enums/report-interval.enum';
import { RunMode } from 'commons/enums/run-mode.enum';
import { BadRequestException as CommonBadRequestException } from 'commons/exceptions/bad-request.exception';
import { NotFoundException as CommonNotFoundException } from 'commons/exceptions/not-found.exception';
import { applyCursorPaginationToArray } from 'commons/helpers/array-cursor.helper';
import { createCsvBuffer, createZipBuffer } from 'commons/helpers/buffer.helper';
import { injectCompanyNameForControlTestInstances } from 'commons/helpers/control-test-instance.helper';
import { formatDateToReadable } from 'commons/helpers/date.helper';
import { generateMonitorTestReportFileName, sanitizeFileName } from 'commons/helpers/file.helper';
import { getProductId, hasAssociatedProduct } from 'commons/helpers/products.helper';
import { getTemporalClient } from 'commons/helpers/temporal/client';
import { fullName } from 'commons/helpers/user.helper';
import { AppService } from 'commons/services/app.service';
import { WorkspacesBaseService } from 'commons/services/workspaces-base.service';
import { AccountIdType } from 'commons/types/account-id.type';
import { CsvDataSetType } from 'commons/types/csv-data-set.type';
import { CursorPage } from 'commons/types/cursor-page.type';
import { FileBufferType } from 'commons/types/file-buffer.type';
import { PaginationType } from 'commons/types/pagination.type';
import config from 'config';
import { format } from 'date-fns';
import { DownloaderPayloadType } from 'dependencies/downloader/types/downloader-payload.interface';
import { HtmlToPdfConverter } from 'dependencies/html-to-pdf-converter/html-to-pdf-converter';
import { FeatureFlagCategory } from 'feature-flags/enums/feature-flag-category.enum';
import { FeatureFlag } from 'feature-flags/feature-flag.enum';
import { FeatureFlagService } from 'feature-flags/feature-flag.service';
import fs from 'fs';
import hbs from 'hbs';
import { fill, flatten, get, indexOf, isEmpty, isNil, map, uniq } from 'lodash';
import moment from 'moment';
import path from 'path';
import { FindManyOptions, In, Repository } from 'typeorm';

@Injectable()
export class MonitorsCoreService extends AppService {
    private readonly ALL_COMPANY_TEST_IDS = [37, 38, 39, 190];

    constructor(
        private readonly workspacesBaseService: WorkspacesBaseService,
        private readonly featureFlagService: FeatureFlagService,
        private readonly htmlToPdfConverter: HtmlToPdfConverter,
        private readonly eventsService: EventsService,
    ) {
        super();
    }

    /**
     * @deprecated Use getMonitorTestDataForCsvExport
     */
    public async getInfrastructureMonitorsForCsvExport(
        account: Account,
        testId: number,
        type: 'included' | 'excluded',
        checkType?: string | null,
        workspaceId?: number,
    ): Promise<CsvDataSetType> {
        return this.getMonitorTestDataForCsvExport(account, testId, type, checkType, workspaceId);
    }

    public saveExclusions(
        exclusions: MonitorInstanceExclusion[],
    ): Promise<MonitorInstanceExclusion[]> {
        return this.exclusionRepository.save(exclusions);
    }

    public async getMonitorTestDataForCsvExport(
        account: Account,
        testId: number,
        type: 'included' | 'excluded',
        checkType?: string | null,
        workspaceId?: number,
    ): Promise<CsvDataSetType> {
        if (!isNil(checkType) && !this.isValidCheckType(checkType)) {
            throw new CommonBadRequestException(
                `Invalid checkType: ${checkType}`,
                ErrorCode.CONTROL_TEST_INSTANCE_CHECK_TYPE_INVALID,
            );
        }

        const resolvedWorkspaceId = workspaceId ?? getProductId(account) ?? 1;

        // Get control test instance for metadata
        const controlTestInstance =
            await this.controlTestInstanceRepository.getControlTestInstanceByTestIdWithAllMonitorInstances(
                testId,
                resolvedWorkspaceId,
                account,
            );

        const runMode = controlTestInstance.runMode;

        const monitorInstance = controlTestInstance.monitorInstances[0];
        const monitorInstanceMetadata = monitorInstance.getMetadata().values();
        const metadataArray = Array.from(monitorInstanceMetadata);

        const additionalData = {
            testMetadata: {
                testId: testId.toString(),
                testName: controlTestInstance.name,
            },
        };

        let data: any[];

        const monitorExclusionData = await this.getTestMonitorExclusionData(
            account,
            testId,
            resolvedWorkspaceId,
            metadataArray,
            checkType,
        );

        if (type === 'excluded') {
            data = await this.getMonitorTestExclusionsForCsv(
                account,
                testId,
                monitorExclusionData,
                runMode,
                checkType,
                monitorInstance.url,
                metadataArray,
            );

            return {
                data,
                filename: '',
                additionalData,
            };
        } else {
            data = await this.getMonitorTestFindingsForCsv(
                account,
                resolvedWorkspaceId,
                testId,
                monitorExclusionData,
                checkType,
                monitorInstance.url,
                metadataArray,
            );

            return {
                data,
                filename: '',
                additionalData,
            };
        }
    }

    private async getMonitorTestExclusionsForCsv(
        account: Account,
        testId: number,
        monitorExclusionData: MonitorExclusionData,
        runMode: RunMode,
        checkType?: string | null,
        url?: string | null,
        metadataArray?: MonitorInstanceMetadataItem[],
    ) {
        const findings = flatten(
            metadataArray?.map(({ monitorResult }) => monitorResult.fail) ?? [],
        );

        // For AP2 tests sometimes the excluded findings are not in the fail array, they get removed and re-added at the excluded array
        const excludedFindings = flatten(
            metadataArray?.map(({ monitorResult }) => monitorResult.excluded) ?? [],
        );

        const headers = this.getInfrastructureMonitorHeadersForCsv(metadataArray ?? []);
        headers.arn ||=
            findings?.some(f => !!f?.resourceArn) || excludedFindings?.some(f => !!f?.resourceArn);

        if (monitorExclusionData.personnelExclusions.length > 0) {
            const excludedPersonnelIds = monitorExclusionData.personnelExclusions.map(
                (exclusion: any) => exclusion.targetId,
            );

            const excludedPersonnelList =
                await this.personnelRepository.findPersonnelWithUserIdentitiesAndConnections(
                    excludedPersonnelIds,
                );
            return monitorExclusionData.personnelExclusions.map((exclusion: any) => {
                const excludedPersonnel = excludedPersonnelList.find(
                    personnel => personnel.id === Number(exclusion.targetId),
                );
                const personnelConnection = excludedPersonnel?.user?.identities?.[0]?.connection;
                const matchedFinding = findings.find(finding =>
                    resultExclusionFuzzyMatch(finding, exclusion),
                );

                return {
                    testId,
                    clientType: personnelConnection?.clientType ?? null,
                    clientId: personnelConnection?.clientId ?? null,
                    clientAlias: personnelConnection?.clientAlias ?? null,
                    name: exclusion.targetDisplayName,
                    targetId: exclusion.targetId,
                    excludedBy: fullName(exclusion.createdBy),
                    excludedAt: formatDateToReadable(exclusion.createdAt),
                    exclusionReason: exclusion.reason,
                    raw: matchedFinding?.raw ?? null,
                    url,
                    email: excludedPersonnel?.user?.email ?? null,
                    ...exclusion,
                    headers,
                    checkType,
                    type: 'excluded',
                };
            });
        }

        if (monitorExclusionData.controlTestInstanceExclusions?.source === TestSource.ACORN) {
            const acornExclusions =
                await this.findingExclusionRepository.getFindingExclusionsByTestId(testId);

            return acornExclusions.map((exclusion: FindingExclusion) => ({
                ...exclusion,
                isComplianceAsCodeTest: true,
                headers: {},
            }));
        }

        return monitorExclusionData.monitorExclusions.map((exclusion: any) => {
            let matchedFinding = findings.find(finding =>
                resultExclusionFuzzyMatch(finding, exclusion),
            );

            // Use excluded findings as a fallback for AP2 tests
            if (!matchedFinding && runMode === RunMode.AP2) {
                matchedFinding = excludedFindings.find(finding =>
                    resultExclusionFuzzyMatch(finding, exclusion),
                );
            }

            const enrichmentData: any = {};

            if (matchedFinding) {
                enrichmentData.dueAt = matchedFinding.dueAt ?? null;
                enrichmentData.vulnerabilitySeverity =
                    exclusion.vulnerabilitySeverity ??
                    matchedFinding.severity ??
                    matchedFinding.type ??
                    null;
                enrichmentData.vulnerabilityResourceId =
                    exclusion.vulnerabilityResourceId ?? matchedFinding.id ?? null;
                enrichmentData.vulnerabilityDueDate =
                    exclusion.vulnerabilityDueDate ?? matchedFinding.dueAt ?? null;
                enrichmentData.deviceMetadata = matchedFinding.raw ?? null;
                enrichmentData.raw = matchedFinding.raw ?? null;
                enrichmentData.organizationalUnitId = matchedFinding.organizationalUnitId;
                enrichmentData.accountId = matchedFinding.accountId;
                enrichmentData.accountName = matchedFinding.accountName;
                enrichmentData.resourceArn = matchedFinding.resourceArn;
                enrichmentData.connectionClientType = matchedFinding.clientType ?? null;
                enrichmentData.connectionClientId = matchedFinding.clientId ?? null;
                enrichmentData.connectionClientAlias = matchedFinding.clientAlias ?? null;
                enrichmentData.region = matchedFinding.region ?? null;
                enrichmentData.email = matchedFinding.email ?? null;
                enrichmentData.ticketUrl = matchedFinding.url ?? null;
            }

            if (exclusion.connection) {
                enrichmentData.connectionClientType = exclusion.connection.clientType ?? null;
                enrichmentData.connectionClientId = exclusion.connection.clientId ?? null;
                enrichmentData.connectionClientAlias = exclusion.connection.clientAlias ?? null;
            }

            return {
                ...enrichmentData,
                ...exclusion,
                testId,
                name: exclusion.targetName,
                targetId: exclusion.targetId,
                excludedBy: fullName(exclusion.exclusionDesignator),
                excludedAt: formatDateToReadable(exclusion.createdAt),
                exclusionReason: exclusion.exclusionReason,
                url,
                headers,
                checkType,
                type: 'excluded',
            };
        });
    }

    private async getMonitorTestFindingsForCsv(
        account: Account,
        workspaceId: number,
        testId: number,
        monitorExclusionData: MonitorExclusionData,
        checkType?: string | null,
        url?: string | null,
        metadataArray?: MonitorInstanceMetadataItem[],
    ) {
        const headers = this.getInfrastructureMonitorHeadersForCsv(metadataArray ?? []);
        const findings = flatten(
            metadataArray?.map(({ monitorResult }) => monitorResult.fail) ?? [],
        );
        headers.arn ||= findings?.some(f => !!f?.resourceArn);

        const controlTestInstanceExclusions =
            monitorExclusionData?.controlTestInstanceExclusions ??
            (await this.controlTestInstanceRepository.getControlTestInstanceExclusionsByTestId(
                testId,
                workspaceId,
            ));

        const failingItems: any[] = [];

        const monitorExclusionTargetIds = new Set(
            monitorExclusionData?.monitorExclusions.map((exclusion: any) => exclusion.targetId),
        );
        const personnelExclusionTargetIds = new Set(
            (monitorExclusionData?.personnelExclusions ?? []).map(
                (exclusion: any) => exclusion.targetId,
            ),
        );
        const allExclusionTargetIds = new Set([
            ...monitorExclusionTargetIds,
            ...personnelExclusionTargetIds,
        ]);

        const exclusionNames = get(
            controlTestInstanceExclusions,
            'monitorInstanceExclusions',
            [],
        ).map((exclusion: any) => exclusion.name);

        const errorMessage = await this.getFailingMessageForFindingsAndExclusions(
            account,
            testId,
            metadataArray,
        );

        let vulnerabilityFindingsMap: Map<string, any>;
        if (checkType === CheckType[CheckType.OBSERVABILITY]) {
            const allFailItems = flatten(
                metadataArray?.map(({ monitorResult }) => monitorResult.fail) ?? [],
            );
            const failingItemIds = allFailItems.map(item => item.id).filter(Boolean);
            vulnerabilityFindingsMap = await this.getVulnerabilityFindingsByIds(
                account,
                failingItemIds,
            );
        } else {
            vulnerabilityFindingsMap = new Map();
        }

        for (const { monitorResult } of metadataArray ?? []) {
            for (const failItem of monitorResult.fail) {
                if (
                    allExclusionTargetIds.has(failItem.id) ||
                    exclusionNames.some(
                        exclusionName =>
                            failItem.name.localeCompare(exclusionName, undefined, {
                                sensitivity: 'base',
                            }) === 0,
                    )
                ) {
                    continue;
                }

                const vulnerabilityFinding = vulnerabilityFindingsMap.get(failItem.id);
                failingItems.push({
                    testId,
                    clientType: monitorResult.clientType,
                    clientId: monitorResult.clientId,
                    clientAlias: monitorResult.clientAlias,
                    ...failItem,
                    vulnerabilityResourceId:
                        vulnerabilityFinding?.vulnerabilityResourceId ?? failItem.id ?? null,
                    vulnerabilityDueDate: vulnerabilityFinding?.vulnerabilityDueDate ?? null,
                    vulnerabilitySeverity:
                        vulnerabilityFinding?.vulnerabilitySeverity ??
                        failItem.severity ??
                        failItem.type ??
                        null,
                    url,
                    error: errorMessage,
                    headers,
                    checkType,
                    type: 'included',
                });
            }
        }

        return failingItems;
    }

    private async getTestMonitorExclusionData(
        account: Account,
        testId: number,
        workspaceId: number,
        metadataArray?: MonitorInstanceMetadataItem[],
        checkType?: string | null,
    ): Promise<MonitorExclusionData> {
        try {
            const controlTestInstanceExclusions =
                await this.controlTestInstanceRepository.getControlTestInstanceExclusionsByTestId(
                    testId,
                    workspaceId,
                );
            const monitorExclusions = get(
                controlTestInstanceExclusions,
                'monitorInstanceExclusions',
                [],
            );

            const complianceCheckData = ControlTestInstanceTestIdComplianceCheckTypes.get(testId);
            let personnelExclusions: any[] = [];

            if (!isNil(complianceCheckData)) {
                const validPersonnel = await this.personnelRepository.findManyByEmploymentStatuses(
                    complianceCheckData.employmentStatuses,
                );
                personnelExclusions =
                    await this.complianceCheckExclusionRepository.getPersonnelExclusionsForCsvExport(
                        complianceCheckData,
                        validPersonnel,
                    );
            }

            const enrichedMonitorExclusions =
                monitorExclusions.length > 0
                    ? await this.enrichMonitorExclusionsWithAdditionalProperties(
                          account,
                          monitorExclusions,
                          metadataArray,
                          checkType,
                      )
                    : [];

            return {
                monitorExclusions: enrichedMonitorExclusions,
                personnelExclusions,
                controlTestInstanceExclusions,
            };
        } catch (error) {
            this.debug(`Failed to get exclusion monitor data for CSV: ${error.message}`, account);
            return {
                monitorExclusions: [],
                personnelExclusions: [],
                controlTestInstanceExclusions: null,
            };
        }
    }

    private async enrichMonitorExclusionsWithAdditionalProperties(
        account: Account,
        monitorExclusions: any[],
        metadataArray?: MonitorInstanceMetadataItem[],
        checkType?: string | null,
    ): Promise<any[]> {
        if (!metadataArray) {
            return monitorExclusions;
        }
        const findings = flatten(metadataArray.map(({ monitorResult }) => monitorResult.fail));

        let vulnerabilityFindingsMap: Map<string, any>;
        if (checkType === CheckType[CheckType.OBSERVABILITY]) {
            const monitorExclusionTargetIds = monitorExclusions.map(
                (exclusion: any) => exclusion.targetId,
            );
            vulnerabilityFindingsMap = await this.getVulnerabilityFindingsByIds(
                account,
                monitorExclusionTargetIds,
            );
        } else {
            vulnerabilityFindingsMap = new Map();
        }

        return monitorExclusions.map((exclusion: any) => {
            const matchedFinding = findings.find(finding => finding.id === exclusion.targetId);
            const vulnerabilityFinding = vulnerabilityFindingsMap.get(exclusion.targetId) ?? null;
            const enrichmentData: any = {};

            if (exclusion.connection) {
                enrichmentData.clientType = exclusion.connection.clientType ?? null;
                enrichmentData.clientId = exclusion.connection.clientId ?? null;
                enrichmentData.clientAlias = exclusion.connection.clientAlias ?? null;
            }

            if (matchedFinding) {
                enrichmentData.email = matchedFinding.email ?? null;
                enrichmentData.region = matchedFinding.region ?? null;
                enrichmentData.dueAt = matchedFinding.dueAt ?? null;
                enrichmentData.vulnerabilitySeverity =
                    matchedFinding.severity ?? matchedFinding.type ?? null;
            }

            if (vulnerabilityFinding) {
                enrichmentData.vulnerabilityResourceId = vulnerabilityFinding.resourceId ?? null;
                enrichmentData.vulnerabilityDueDate = vulnerabilityFinding.vulnerabilityDueDate
                    ? formatDateToReadable(vulnerabilityFinding.vulnerabilityDueDate)
                    : (matchedFinding.dueAt ?? null);
                enrichmentData.vulnerabilitySeverity =
                    vulnerabilityFinding.severity ?? matchedFinding.type ?? null;
            }

            return Object.keys(enrichmentData).length > 0
                ? { ...exclusion, ...enrichmentData }
                : exclusion;
        });
    }

    /**
     * Fetches vulnerability findings by failing item IDs and returns a Map for efficient lookup
     */
    private async getVulnerabilityFindingsByIds(
        account: Account,
        failingItemIds: string[],
    ): Promise<Map<string, any>> {
        try {
            if (isEmpty(failingItemIds)) {
                return new Map();
            }

            // Fetch vulnerability findings that match the failing item IDs
            const vulnerabilityFindings = await this.vulnerabilityFindingRepository.find({
                where: {
                    id: In(failingItemIds),
                },
                select: ['id', 'findingId', 'severity', 'dueAt', 'status', 'score'],
                relations: {
                    resource: true,
                },
            });

            // Create a Map for efficient lookup by findingId
            const vulnerabilityFindingsMap = new Map();
            vulnerabilityFindings.forEach(finding => {
                vulnerabilityFindingsMap.set(finding.id, {
                    ...finding,
                    resourceId: finding.resource?.resourceId || null,
                    severity: finding.severity ?? VulnerabilitySeverity.NONE,
                    status: VulnerabilityStatus[finding.status] ?? null,
                });
            });

            this.debug(
                `Fetched ${vulnerabilityFindings.length} vulnerability findings for ${failingItemIds.length} failing items`,
                account,
                {
                    vulnerabilityFindingsCount: vulnerabilityFindings.length,
                    failingItemsCount: failingItemIds.length,
                },
            );

            return vulnerabilityFindingsMap;
        } catch (error) {
            this.debug(`Failed to fetch vulnerability findings: ${error.message}`, account, {
                error: error.message,
            });
            return new Map();
        }
    }

    private async getFailingMessageForFindingsAndExclusions(
        account: Account,
        testId: number,
        metadataArray?: MonitorInstanceMetadataItem[],
    ): Promise<string> {
        const workspaceId = getProductId(account);
        if (!workspaceId) {
            return '';
        }

        const controlTestInstanceHistory = await this.getMostRecentFailedHistoryByTestId(
            workspaceId,
            testId,
        );

        const events = controlTestInstanceHistory?.events;
        if (!events) {
            return '';
        }

        const sampleEvent = this.getSampleEventForFindingsAndExclusions(events, metadataArray);

        if ((await this.eventsService.isReadingFromS3(account)) && !isNil(sampleEvent.fileKey)) {
            await this.eventsService.setEventMetadataFromRemote(account, sampleEvent);
        }

        return sampleEvent.getMetadata()?.response?.message ?? '';
    }

    private getSampleEventForFindingsAndExclusions(
        events: Event[],
        metadataArray?: MonitorInstanceMetadataItem[],
    ): Event {
        const failedEvents = events.filter(
            event => event.checkResultStatus === CheckResultStatus.FAILED,
        );

        if (isEmpty(failedEvents)) {
            return events[0];
        }

        const failedClientType = metadataArray?.find(
            metadata => metadata.checkResultStatus === CheckResultStatus.FAILED,
        )?.monitorResult?.clientType;

        if (isNil(failedClientType)) {
            return failedEvents[0];
        }

        return (
            failedEvents.find(
                failedEvent =>
                    failedEvent.getMetadata()?.response?.data?.source ===
                    ClientType[failedClientType],
            ) ?? failedEvents[0]
        );
    }

    public async findControlTestInstances(
        options?: FindManyOptions<ControlTestInstance>,
    ): Promise<ControlTestInstance[]> {
        return this.controlTestInstanceRepository.find(options);
    }

    public async updateMonitorStat(account: Account, testSource: TestSource): Promise<MonitorStat> {
        const product = account.getCurrentProduct();

        if (isNil(product)) {
            throw new CommonNotFoundException(ErrorCode.PRODUCT_NOT_FOUND);
        }

        const monitorStat = await this.monitorStatRepository.findOneOrFail({
            where: {
                product: { id: product.id },
                statType:
                    testSource === TestSource.ACORN
                        ? MonitorStatType.CODE
                        : MonitorStatType.DEPLOYED,
            },
            relations: ['product'],
        });

        const { passed, failed, preAudit, passingPercent } =
            await this.controlTestInstanceRepository.aggregateStats(account, testSource);

        monitorStat.passingPercent = passingPercent;
        monitorStat.passed = passed;
        monitorStat.failed = failed;
        monitorStat.preAudit = preAudit;

        if (testSource === TestSource.ACORN) {
            const { numRepositoriesMonitored, totalRepositories } =
                await this.codeRepositorySettingsRepository.getMonitoredRepositoriesStats();

            monitorStat.numberOfRepositoriesMonitored = numRepositoriesMonitored;
            monitorStat.totalRepositories = totalRepositories;
        }

        return this.monitorStatRepository.save(monitorStat, { reload: false });
    }

    public async enablePolicyBasedControlTestInstances(
        account: Account,
    ): Promise<ControlTestInstance[]> {
        /**
         * We are not required to filter by products so pass an empty account
         */
        const controlTestInstances = await this.getControlTestInstancesByCheckTypeAndCheckStatus(
            account,
            CheckType.POLICY,
            CheckStatus.UNUSED,
        );

        if (controlTestInstances.length === 0) {
            return controlTestInstances;
        }

        for (const controlTestInstance of controlTestInstances) {
            controlTestInstance.checkStatus = CheckStatus.ENABLED;
        }

        return this.controlTestInstanceRepository.save(controlTestInstances);
    }

    public async disableCompanyLevelControlTestInstances(
        account: Account,
        user: User,
    ): Promise<void> {
        const controlTests = await this.controlTestInstanceRepository.find({
            where: {
                testId: In(this.ALL_COMPANY_TEST_IDS),
            },
        });

        for (const controlTest of controlTests) {
            controlTest.disablingUser = user;
            controlTest.checkStatus = CheckStatus.DISABLED;
            controlTest.disabledMessage = config.get('api.policyTestDisabledMessage');
            controlTest.disabledAt = new Date();

            if (!isEmpty(controlTest.monitorInstances)) {
                const disabledMonitorInstances = [];

                for (const monitorInstance of controlTest.monitorInstances) {
                    monitorInstance.enabled = false;
                    disabledMonitorInstances.push(monitorInstance);
                }

                // eslint-disable-next-line no-await-in-loop
                await this.monitorInstanceRepository.save(disabledMonitorInstances, {
                    reload: false,
                });
            }
        }

        await this.controlTestInstanceRepository.save(controlTests, {
            reload: false,
        });

        /**
         * This has to be LAST or we get inaccurate monitor stats
         */
        const uniqueTestSources = uniq(map(controlTests, ct => ct.source));

        const updateMonitorStatsPromises = uniqueTestSources.map(testSource =>
            this.updateMonitorStats(account, testSource),
        );

        await Promise.all(updateMonitorStatsPromises);
    }

    public async enableControlTestInstance(
        account: Account,
        controlTestInstance: ControlTestInstance,
    ): Promise<void> {
        controlTestInstance.disablingUser = null;
        controlTestInstance.disabledMessage = null;
        controlTestInstance.checkStatus = CheckStatus.ENABLED;
        controlTestInstance.disabledAt = null;

        await this.enableMonitorTestsOnControlTestInstance(account, controlTestInstance);
    }

    public async enableMonitorInstances(
        account: Account,
        controlTestInstance: ControlTestInstance,
    ): Promise<void> {
        const enabledMonitorInstances: MonitorInstance[] = [];

        for (const monitorInstance of controlTestInstance.monitorInstances) {
            monitorInstance.enabled = true;
            enabledMonitorInstances.push(monitorInstance);
        }

        await this.monitorInstanceRepository.save(enabledMonitorInstances, {
            reload: false,
        });

        await this.updateMonitorStats(account, controlTestInstance.source);
    }

    public async enableCompanyLevelControlTestInstances(account: Account): Promise<void> {
        const controlTests = await this.controlTestInstanceRepository.find({
            where: {
                testId: In(this.ALL_COMPANY_TEST_IDS),
            },
        });

        for (const controlTest of controlTests) {
            // eslint-disable-next-line no-await-in-loop
            await this.enableControlTestInstance(account, controlTest);
        }
    }

    public async checkForDeprecatedTestExistence(testId: number): Promise<boolean> {
        const controlTestInstance = await this.controlTestInstanceRepository.findOne({
            where: {
                testId,
            },
        });

        return !isNil(controlTestInstance);
    }

    public async getMonitorInstanceByIdWithControlTest(
        monitorInstanceId: number,
    ): Promise<MonitorInstance> {
        return this.monitorInstanceRepository.getMonitorInstanceByIdWithControlTest(
            monitorInstanceId,
        );
    }

    public getMonitorControlWithRecipeByControlIdOrFail(id: number): Promise<ControlTestInstance> {
        return this.controlTestInstanceRepository
            .createQueryBuilder('ControlTestInstance')
            .leftJoinAndSelect('ControlTestInstance.recipes', 'AutopilotRecipeInstance')
            .where('ControlTestInstance.id = :id', { id })
            .andWhere('AutopilotRecipeInstance.current = :current', {
                current: 1,
            })
            .getOneOrFail();
    }

    getTestsCount(account: Account): Promise<number> {
        return this.controlTestInstanceHistoryRepository.getTestsCount(account);
    }

    public getAllMonitorExclusions(
        dto: PaginationExclusionRequestDto,
    ): Promise<PaginationType<MonitorInstanceExclusion>> {
        return this.exclusionRepository.getAllMonitorInstanceExclusion(dto);
    }

    public async listMonitorInstanceExclusionsWithCursor(
        testId: number,
        workspaceId: number,
        request: MonitorExclusionsRequestType,
    ): Promise<CursorPage<MonitorInstanceExclusion>> {
        return this.exclusionRepository.getMonitorInstanceExclusionsWithCursor(
            testId,
            workspaceId,
            request,
        );
    }

    public async enableMonitorTestsOnControlTestInstance(
        account: Account,
        controlTestInstance: ControlTestInstance,
    ): Promise<void> {
        /**
         * This has to be saved FIRST or we get inaccurate monitor stats
         */
        await this.controlTestInstanceRepository.save(controlTestInstance, {
            reload: false,
        });

        if (!isEmpty(controlTestInstance.monitorInstances)) {
            await this.enableMonitorInstances(account, controlTestInstance);
        }
    }

    public async updateMonitorStats(account: Account, testSource: TestSource): Promise<void> {
        /**
         * If there is no associated product we are updating a monitor stat
         * that is account-scoped; ie, Policies - get all the monitor stats
         * and run and update to get all the stats up to date for the account.
         */
        if (!hasAssociatedProduct(account)) {
            const monitorStats = await this.monitorStatRepository.find({
                where: {
                    statType:
                        testSource === TestSource.ACORN
                            ? MonitorStatType.CODE
                            : MonitorStatType.DEPLOYED,
                },
                relations: ['product'],
            });

            for (const monitorStat of monitorStats) {
                // eslint-disable-next-line no-await-in-loop
                await this.updateMonitorStat(
                    account.cloneWithProduct(monitorStat.product),
                    testSource,
                );
            }
        } else {
            await this.updateMonitorStat(account, testSource);
        }
    }

    getControlTestInstanceByProductIdAndTestId(
        productId: number,
        testId: number,
    ): Promise<ControlTestInstance | null> {
        return this.controlTestInstanceRepository.getControlTestInstanceByProductIdAndTestId(
            productId,
            testId,
        );
    }

    public getMonitorExclusionsByDeletedConnection(
        deletedConnection: ConnectionEntity,
    ): Promise<MonitorInstanceExclusion[]> {
        return this.exclusionRepository.find({
            where: {
                deletedAt: deletedConnection.deletedAt,
                connection: {
                    id: deletedConnection.id,
                },
            },
            withDeleted: true,
        });
    }

    async getAllStuckInTestingControlTestInstance(hours: number): Promise<ControlTestInstance[]> {
        return this.controlTestInstanceRepository.getStuckInTestingControlTestInstances(hours);
    }

    public getMonitorExclusionsByTestIdAndConnectionId(
        account: Account,
        testId: number,
        connectionId: number | null,
    ): Promise<MonitorInstanceExclusion[]> {
        return this.exclusionRepository.getMonitorExclusionsByTestIdAndConnectionId(
            account,
            testId,
            connectionId,
        );
    }

    public findByTestId(account: Account, testId: number): Promise<MonitorInstanceExclusion[]> {
        return this.exclusionRepository.findByTestId(account, testId);
    }

    async setControlTestInstancesTestStatus(
        instances: ControlTestInstance[],
        checkStatus: CheckStatus,
    ): Promise<void> {
        instances.forEach((instance, index) => {
            instances[index].checkStatus = checkStatus;
            delete instances[index].controls; // delete this else typeorm will delete the association on disabled controls
        });

        await this.controlTestInstanceRepository.save(instances, {
            reload: false,
        });
    }

    getSupportUser(): Promise<User | null> {
        return this.userRepository.findOneBy({
            email: config.get('api.supportUser.email'),
        });
    }

    public getMonitorInstanceByIdOrFail(id: number): Promise<ControlTestInstance> {
        return this.controlTestInstanceRepository.findOneOrFail({
            where: { id },
        });
    }

    async listControlTestInstancesByTestIds(
        testIds: number[],
        productId: number,
    ): Promise<ControlTestInstance[]> {
        return this.controlTestInstanceRepository.getControlTestInstanceByTestIds(
            testIds,
            productId,
        );
    }

    public async listControlTestInstances(
        account: Account,
        dto: MonitorsRequestDto,
        user?: User,
    ): Promise<PaginationType<TestGraphsType>> {
        const isMonitoringTestInstanceEndpointRefactorEnabled =
            await this.isMonitoringTestInstanceEndpointRefactorEnabled(account);

        if (isMonitoringTestInstanceEndpointRefactorEnabled) {
            return this.listControlTestInstancesV2(account, dto, user);
        }

        const controlTestInstances =
            await this.controlTestInstanceRepository.listControlTestInstances(account, dto, user);

        const company = await this.getCompanyByAccountId(account.id);

        controlTestInstances.data = injectCompanyNameForControlTestInstances(
            controlTestInstances.data,
            company.name,
        );

        const graphs: PaginationType<TestGraphsType> = {
            data: [],
            limit: controlTestInstances.limit,
            page: controlTestInstances.page,
            total: controlTestInstances.total,
        };

        for (const test of controlTestInstances.data) {
            graphs.data.push({
                test,
                graphs: !isNil(dto.reportInterval)
                    ? // eslint-disable-next-line no-await-in-loop
                      await this.getControlTestHistory(account, {
                          testId: test.testId,
                          reportInterval: dto.reportInterval,
                      })
                    : null,
            });
        }

        return graphs;
    }

    async getControlTestInstanceOrFail(
        testId: number,
        workspaceId: number,
        request: MonitorRequestType,
    ): Promise<ControlTestInstance> {
        const controlTestInstance =
            await this.controlTestInstanceRepository.getControlTestInstanceDetailsByTestIdOrFail(
                testId,
                workspaceId,
                request.expand,
            );

        if (controlTestInstance.runMode === RunMode.AP2_SHADOW_TEST) {
            throw new NotFoundException(ErrorCode.ENTITY_NOT_FOUND);
        }

        return controlTestInstance;
    }

    public async listControlTestInstancesV2(
        account: Account,
        dto: MonitorsRequestDto,
        user?: User,
    ): Promise<PaginationType<TestGraphsType>> {
        const controlTestInstances =
            await this.controlTestInstanceRepository.listControlTestInstancesOptimized(
                account,
                dto,
                user,
            );

        const company = await this.getCompanyByAccountId(account.id);

        controlTestInstances.data = injectCompanyNameForControlTestInstances(
            controlTestInstances.data,
            company.name,
        );

        const graphs: PaginationType<TestGraphsType> = {
            data: [],
            limit: controlTestInstances.limit,
            page: controlTestInstances.page,
            total: controlTestInstances.total,
        };

        for (const test of controlTestInstances.data) {
            graphs.data.push({
                test,
                graphs: !isNil(dto.reportInterval)
                    ? // eslint-disable-next-line no-await-in-loop
                      await this.getControlTestHistoryV2(account, {
                          testId: test.testId,
                          reportInterval: dto.reportInterval,
                      })
                    : null,
            });
        }

        return graphs;
    }

    async listControlTestInstancesWithCursor(
        request: MonitorListRequestType,
        account: Account,
        workspaceId: number,
    ): Promise<CursorPage<ControlTestInstance>> {
        const workspace = await this.workspacesBaseService.getProductById(workspaceId);
        if (isNil(workspace)) {
            throw new NotFoundException(`Workspace not found for ID: ${workspaceId}`);
        }

        const controlTestInstances =
            await this.controlTestInstanceRepository.listControlTestInstancesWithCursor(
                request,
                workspaceId,
            );

        const company = await this.getCompanyByAccountId(account.id);

        controlTestInstances.data = injectCompanyNameForControlTestInstances(
            controlTestInstances.data,
            company.name,
        );

        return controlTestInstances;
    }

    async getControlTestFailureResultsWithCursor(
        testId: number,
        workspaceId: number,
        request: MonitorTestFailuresRequestType,
    ): Promise<CursorPage<MonitorTestFailureItem>> {
        const controlTestInstance: ControlTestInstance =
            await this.controlTestInstanceRepository.getControlTestInstanceByTestIdOrFail(
                testId,
                workspaceId,
            );

        // TODO: Performance optimization needed - this method loads ALL failure data into memory
        // Consider normalizing failure data into a separate table for true database-level pagination
        // Current approach is necessary because failure data is stored as JSON in metadata field

        const allFailingItems: MonitorTestFailureItem[] = [];

        const monitorInstance = controlTestInstance.monitorInstances[0];
        const monitorInstanceMetadata = monitorInstance.getMetadata().values();
        const metadataArray = Array.from(monitorInstanceMetadata);

        // Pre-compute exclusion set for better performance
        const exclusionTargetIds = new Set(
            !request.includeExclusions
                ? get(controlTestInstance, 'monitorInstanceExclusions', []).map(
                      (exclusion: any) => exclusion.targetId,
                  )
                : [],
        );

        for (const { monitorResult } of metadataArray) {
            if (!monitorResult || !Array.isArray(monitorResult.fail)) continue;

            for (const failItem of monitorResult.fail) {
                // Skip excluded items early if not including exclusions
                if (!request.includeExclusions && exclusionTargetIds.has(failItem.id)) {
                    continue;
                }

                const failingItem = {
                    id: failItem.id,
                    providerName: this.getProviderName(monitorResult.clientType),
                    clientId: monitorResult.clientId,
                    resourceName: failItem.name,
                    organizationalUnitId: failItem.organizationalUnitId,
                    accountId: failItem.accountId,
                    accountName: failItem.accountName,
                    resourceArn: failItem.resourceArn,
                    region: failItem.region,
                    // Vulnerability/CSPM specific fields
                    vulnerabilityResourceId: failItem.vulnerabilityResourceId ?? null,
                    vulnerabilityDueDate: failItem.vulnerabilityDueDate ?? null,
                    vulnerabilitySeverity:
                        failItem.severity ?? failItem.vulnerabilitySeverity ?? null,
                };

                allFailingItems.push(failingItem);
            }
        }

        const { cursor, size } = request;

        return applyCursorPaginationToArray<MonitorTestFailureItem>(allFailingItems, cursor, size);
    }

    private getProviderName(clientType: any): string {
        return ClientTypeDescription[clientType] || 'Unknown Provider';
    }

    public async deleteMonitorExclusionsByConnection(
        connectionId: number,
        deletedAtTimestamp: Date,
    ): Promise<void> {
        const exclusions = await this.exclusionRepository.find({
            where: { connection: { id: connectionId } },
        });

        if (!isEmpty(exclusions)) {
            for (const exclusion of exclusions) {
                exclusion.deletedAt = deletedAtTimestamp;
            }

            await this.exclusionRepository.save(exclusions);
        }
    }

    /**
     * Generate a PDF document with test description, remediation instructions, and help article link
     * @param account
     * @param testId
     * @returns FileBufferType
     */
    async generateTestRemediationPdf(
        account: Account,
        testId: number,
        workspaceId?: number,
    ): Promise<FileBufferType> {
        if (!(await this.isFindingsEnhancementPhase1Enabled(account))) {
            throw new NotFoundException(ErrorCode.ENDPOINT_NOT_FOUND);
        }

        const controlTestInstance =
            await this.controlTestInstanceRepository.getControlTestInstanceByTestIdWithAllMonitorInstances(
                testId,
                workspaceId ?? getProductId(account) ?? 1,
                account,
            );
        const monitorInstance = controlTestInstance.monitorInstances[0];

        if (!monitorInstance) {
            throw new NotFoundException(ErrorCode.MONITOR_TEST_NOT_FOUND);
        }

        const templateData = {
            testName: controlTestInstance.name,
            testId: testId,
            description:
                monitorInstance.evidenceCollectionDescription?.replace(
                    /%s/g,
                    account.companyName,
                ) ?? controlTestInstance.description,
            remedyDescription: monitorInstance.remedyDescription?.replace(
                /%s/g,
                account.companyName,
            ),
            url: monitorInstance.url,
            priority: Priority[controlTestInstance.priority],
            priorityClass: Priority[controlTestInstance.priority].toLowerCase(),
            status: CheckResultStatus[controlTestInstance.checkResultStatus],
            statusClass: CheckResultStatus[controlTestInstance.checkResultStatus].toLowerCase(),
            lastCheck: controlTestInstance.lastCheck
                ? moment(controlTestInstance.lastCheck).format('MMMM Do YYYY, h:mm:ss a')
                : null,
            generatedDate: moment().format('MMMM Do YYYY, h:mm:ss a'),
        };

        const templatePath = path.join(
            __dirname,
            MonitorsTestsReportsConstants.REMEDIATION_PDF_TEMPLATE,
        );
        const templateSource = await fs.promises.readFile(templatePath, 'utf8');
        const template = hbs.handlebars.compile(templateSource);
        const html = template(templateData);

        const { data: pdfBuffer } = await this.htmlToPdfConverter.convertToPdfBuffer(html, account);
        const fileName = sanitizeFileName(
            `Test_${testId}_Remediation_Guide_moment${format(new Date(), 'MMddyyyy')}.pdf`,
        );

        return {
            stream: pdfBuffer,
            filename: fileName,
        };
    }

    /**
     * Generate a CSV report for a monitoring test
     * @param account Account for which the CSV is being generated
     * @param testId Test ID
     * @param type Type of findings, either included or excluded
     * @param checkType Check type
     * @param workspaceId Workspace ID
     * @returns FileBufferType
     */
    async generateMonitorTestReportCsv(
        account: Account,
        testId: number,
        type: MonitorFindingsType,
        checkType?: string | null,
        workspaceId?: number,
    ): Promise<FileBufferType | null> {
        if (!(await this.isFindingsEnhancementPhase1Enabled(account))) {
            throw new NotFoundException(ErrorCode.ENDPOINT_NOT_FOUND);
        }

        const csvData = await this.getMonitorTestDataForCsvExport(
            account,
            testId,
            type,
            checkType,
            workspaceId,
        );

        if (isEmpty(csvData?.data)) {
            this.debug(`No findings found for CSV export`, account);
            return null;
        }

        // Use the same DTO transformation as the regular CSV endpoints
        const factory = new FindingsCsvFactory();
        const csvDtoType = factory.getResponseDtoType(csvData.data);
        const csvBuffer = createCsvBuffer(csvData.data, csvDtoType);

        if (!csvBuffer) {
            throw new Error('Failed to generate CSV buffer for findings');
        }

        const controlTestInstance =
            await this.controlTestInstanceRepository.getControlTestInstanceByTestIdWithAllMonitorInstances(
                testId,
                workspaceId ?? getProductId(account) ?? 1,
                account,
            );
        const monitorTestReportFileName = generateMonitorTestReportFileName(
            type,
            testId,
            controlTestInstance.name,
        );
        const csvFileName = `${monitorTestReportFileName}.csv`;

        this.debug(
            `Creating CSV with ${csvData.data.length} CSV records for test ${testId}`,
            account,
        );

        return {
            stream: csvBuffer,
            filename: csvFileName,
        };
    }

    /**
     * Generate a ZIP file containing both the remediation PDF and the CSV report
     * @param account Account for which the ZIP is being generated
     * @param testId Test ID
     * @param type Type of findings, either included or excluded
     * @param checkType Check type
     * @param workspaceId Workspace ID
     * @returns FileBufferType
     */
    async generateMonitorTestReportZip(
        account: Account,
        testId: number,
        type: MonitorFindingsType,
        checkType?: string | null,
        workspaceId?: number,
    ): Promise<FileBufferType> {
        if (!(await this.isFindingsEnhancementPhase1Enabled(account))) {
            throw new NotFoundException(ErrorCode.ENDPOINT_NOT_FOUND);
        }

        const pdfFile = await this.generateTestRemediationPdf(account, testId, workspaceId);
        const files: FileBufferType[] = [pdfFile];

        const csvFile = await this.generateMonitorTestReportCsv(
            account,
            testId,
            type,
            checkType,
            workspaceId,
        );

        if (!isNil(csvFile)) {
            files.push(csvFile);
        }

        const controlTestInstance =
            await this.controlTestInstanceRepository.getControlTestInstanceByTestIdWithAllMonitorInstances(
                testId,
                workspaceId ?? getProductId(account) ?? 1,
                account,
            );

        const monitorTestReportFileName = generateMonitorTestReportFileName(
            type,
            testId,
            controlTestInstance.name,
        );

        this.debug(`Creating ZIP with ${files.length} files for test ${testId}`, account);

        const zipBuffer = await createZipBuffer(files);
        const zipFileName = `${monitorTestReportFileName}.zip`;

        return {
            stream: zipBuffer,
            filename: zipFileName,
        };
    }

    /**
     * Start async monitor findings ZIP generation workflow
     * @param account
     * @param user
     * @param testId
     * @param type
     * @param checkType
     * @param requestMetadata
     * @returns workflow ID
     */
    async startFindingsZipGeneration(
        account: Account,
        user: User,
        requestMetadata: ExtractedRequestProps,
        testId: number,
        type: 'included' | 'excluded',
        checkType?: string | null,
        workspaceId?: number,
    ): Promise<DownloaderPayloadType> {
        if (!(await this.isFindingsEnhancementPhase1Enabled(account))) {
            throw new NotFoundException(ErrorCode.ENDPOINT_NOT_FOUND);
        }

        const resolvedWorkspaceId = workspaceId ?? getProductId(account) ?? 1;
        const product = await this.workspacesBaseService.getProductById(resolvedWorkspaceId);

        const temporalClient = await getTemporalClient(account.domain);

        const workflowId = `monitor-findings-zip-${account.id}-${testId}-${type}-${Date.now()}`;

        // @ts-expect-error disabling ts until this is replaced with executeWorkflow
        return temporalClient.startWorkflow(monitorFindingsZipDownloadWorkflowV1, {
            taskQueue: config.get('temporal.taskQueues.temporal-default'),
            args: [
                {
                    account,
                    user,
                    product,
                    testId,
                    type,
                    checkType,
                    requestMetadata: requestMetadata,
                    workspaceId: resolvedWorkspaceId,
                },
            ],
            workflowId,
        });
    }

    /**
     * Start async monitor findings CSV generation workflow
     * @param account
     * @param user
     * @param testId
     * @param type
     * @param checkType
     * @param requestMetadata
     * @returns workflow ID
     */
    async startMonitorTestReportCsvGeneration(
        account: Account,
        user: User,
        requestMetadata: ExtractedRequestProps,
        testId: number,
        type: 'included' | 'excluded',
        checkType?: string | null,
        workspaceId?: number,
    ): Promise<DownloaderPayloadType> {
        if (!(await this.isFindingsEnhancementPhase1Enabled(account))) {
            throw new NotFoundException(ErrorCode.ENDPOINT_NOT_FOUND);
        }

        const resolvedWorkspaceId = workspaceId ?? getProductId(account) ?? 1;
        const product = await this.workspacesBaseService.getProductById(resolvedWorkspaceId);

        const temporalClient = await getTemporalClient(account.domain);

        const workflowId = `monitor-findings-csv-${account.id}-${testId}-${type}-${Date.now()}`;

        // @ts-expect-error disabling ts until this is replaced with executeWorkflow
        return temporalClient.startWorkflow(monitorFindingsCsvDownloadWorkflowV1, {
            taskQueue: config.get('temporal.taskQueues.temporal-default'),
            args: [
                {
                    account,
                    user,
                    product,
                    testId,
                    type,
                    checkType,
                    requestMetadata: requestMetadata,
                    workspaceId: resolvedWorkspaceId,
                },
            ],
            workflowId,
        });
    }

    async enableAllInfrastructureCustomTests(): Promise<void> {
        const customTests = await this.controlTestInstanceRepository.find({
            where: {
                source: TestSource.CUSTOM,
                monitorInstances: [
                    {
                        monitorInstanceCheckTypes: [
                            {
                                checkType: CheckType.INFRASTRUCTURE,
                            },
                        ],
                    },
                ],
            },
        });

        for (const test of customTests) {
            /* eslint-disable no-await-in-loop */
            await this.enableTest(test.id);
        }
    }

    async enableTest(test: number): Promise<void> {
        await this.controlTestInstanceRepository.update(
            {
                id: test,
            },
            {
                checkStatus: CheckStatus.ENABLED,
            },
        );
    }

    private async getControlTestHistory(
        account: Account,
        dto: ControlTestInstanceHistoryRequestDto,
    ): Promise<ControlTestInstanceHistoryType> {
        const { testId, reportInterval } = dto;
        let data = null;

        switch (reportInterval) {
            case ReportInterval.WEEKLY:
                data = this.getControlTestHistoryByWeek(account, testId);
                break;
            case ReportInterval.MONTHLY:
                data = this.getControlTestHistoryByMonth(account, testId);
                break;
            default:
                throw new InternalServerErrorException(
                    `ReportInterval ${ReportInterval[reportInterval]} is not supported`,
                );
        }

        return data;
    }

    private async getControlTestHistoryByWeek(
        account: Account,
        testId: number,
    ): Promise<ControlTestInstanceHistoryType> {
        const weeksPerYear = 52;

        const data: ControlTestInstanceHistoryType = {
            labels: [],
            passed: fill(Array(weeksPerYear), 0),
            failed: fill(Array(weeksPerYear), 0),
            errored: fill(Array(weeksPerYear), 0),
        };

        const labels = getControlTestHistoryByWeekLabels(weeksPerYear);

        const passedResults =
            await this.controlTestInstanceHistoryRepository.getControlTestHistoryByWeek(
                account,
                weeksPerYear,
                CheckResultStatus.PASSED,
                testId,
            );

        const failedResults =
            await this.controlTestInstanceHistoryRepository.getControlTestHistoryByWeek(
                account,
                weeksPerYear,
                CheckResultStatus.FAILED,
                testId,
            );
        const errroResults =
            await this.controlTestInstanceHistoryRepository.getControlTestHistoryByWeek(
                account,
                weeksPerYear,
                CheckResultStatus.ERROR,
                testId,
            );

        for (let index = 1; index <= weeksPerYear; index++) {
            data.labels.push(labels[0][index]);
        }

        if (!isNil(passedResults[0])) {
            for (const result of passedResults) {
                const index = indexOf(data.labels, result.week_of);
                data.passed[index] = Number(result.total) ?? 0;
            }
        }

        if (!isNil(failedResults[0])) {
            for (const result of failedResults) {
                const index = indexOf(data.labels, result.week_of);
                data.failed[index] = Number(result.total) ?? 0;
            }
        }

        if (!isNil(errroResults[0])) {
            for (const result of errroResults) {
                const index = indexOf(data.labels, result.week_of);
                data.errored[index] = Number(result.total) ?? 0;
            }
        }

        return data;
    }

    private async getControlTestHistoryByMonth(
        account: Account,
        testId: number,
    ): Promise<ControlTestInstanceHistoryType> {
        const months = this.getTwelveMonthTrail();

        const data: ControlTestInstanceHistoryType = {
            labels: months.map(el => 'MONTH_' + el),
            passed: [],
            failed: [],
            errored: [],
        };

        const passedResults =
            await this.controlTestInstanceHistoryRepository.getControlTestHistoryByMonth(
                account,
                CheckResultStatus.PASSED,
                testId,
            );

        const failedResults =
            await this.controlTestInstanceHistoryRepository.getControlTestHistoryByMonth(
                account,
                CheckResultStatus.FAILED,
                testId,
            );

        const errorResults =
            await this.controlTestInstanceHistoryRepository.getControlTestHistoryByMonth(
                account,
                CheckResultStatus.ERROR,
                testId,
            );

        if (!isNil(passedResults[0])) {
            const results = passedResults[0];

            for (const month of months) {
                const count = Number(results[month]) ?? 0;
                data.passed.push(count);
            }
        } else {
            data.passed = fill(Array(months.length), 0);
        }

        if (!isNil(failedResults[0])) {
            const results = failedResults[0];

            for (const month of months) {
                const count = Number(results[month]) ?? 0;
                data.failed.push(count);
            }
        } else {
            data.failed = fill(Array(months.length), 0);
        }

        if (!isNil(errorResults[0])) {
            const results = errorResults[0];

            for (const month of months) {
                const count = Number(results[month]) ?? 0;
                data.errored.push(count);
            }
        } else {
            data.errored = fill(Array(months.length), 0);
        }

        return data;
    }

    private async getControlTestHistoryV2(
        account: Account,
        dto: ControlTestInstanceHistoryRequestDto,
    ): Promise<ControlTestInstanceHistoryType> {
        const { testId, reportInterval } = dto;
        let data: ControlTestInstanceHistoryType;

        switch (reportInterval) {
            case ReportInterval.WEEKLY:
                data = await this.getControlTestHistoryByWeekOptimized(account, testId);
                break;
            case ReportInterval.MONTHLY:
                data = await this.getControlTestHistoryByMonthOptimized(account, testId);
                break;
            default:
                throw new InternalServerErrorException(
                    `ReportInterval ${ReportInterval[reportInterval]} is not supported`,
                );
        }

        return data;
    }

    private async getControlTestHistoryByWeekOptimized(
        account: Account,
        testId: number,
    ): Promise<ControlTestInstanceHistoryType> {
        const WEEKS_PER_YEAR = 52;

        try {
            const labels = getControlTestHistoryByWeekLabels(WEEKS_PER_YEAR);

            // Execute database queries in parallel for better performance
            const [passedResults, failedResults, errorResults] = await Promise.all([
                this.controlTestInstanceHistoryRepository.getControlTestHistoryByWeekOptimized(
                    account,
                    WEEKS_PER_YEAR,
                    CheckResultStatus.PASSED,
                    testId,
                ),
                this.controlTestInstanceHistoryRepository.getControlTestHistoryByWeekOptimized(
                    account,
                    WEEKS_PER_YEAR,
                    CheckResultStatus.FAILED,
                    testId,
                ),
                this.controlTestInstanceHistoryRepository.getControlTestHistoryByWeekOptimized(
                    account,
                    WEEKS_PER_YEAR,
                    CheckResultStatus.ERROR,
                    testId,
                ),
            ]);

            // Initialize data structure with proper defaults
            const data: ControlTestInstanceHistoryType = {
                labels: [],
                passed: new Array(WEEKS_PER_YEAR).fill(0),
                failed: new Array(WEEKS_PER_YEAR).fill(0),
                errored: new Array(WEEKS_PER_YEAR).fill(0),
            };

            // Build labels array efficiently
            if (labels?.[0]) {
                for (let index = 1; index <= WEEKS_PER_YEAR; index++) {
                    data.labels.push(labels[0][index]);
                }
            }

            // Create a label-to-index map for O(1) lookups instead of O(n) indexOf calls
            const labelIndexMap = new Map<string, number>();
            data.labels.forEach((label, index) => {
                labelIndexMap.set(label, index);
            });

            // Helper function to process results efficiently
            const processResults = (results: any[], targetArray: number[]): void => {
                if (!results?.length) return;

                for (const result of results) {
                    if (!result?.week_of || result.total === undefined) continue;

                    const index = labelIndexMap.get(result.week_of);
                    if (index !== undefined) {
                        const count = Number(result.total);
                        targetArray[index] = isNaN(count) ? 0 : count;
                    }
                }
            };

            // Process all result types
            processResults(passedResults, data.passed);
            processResults(failedResults, data.failed);
            processResults(errorResults, data.errored);

            return data;
        } catch (error) {
            // Log error and return empty data structure to prevent application crashes
            this.error(error as Error, account, { testId, method: 'getControlTestHistoryByWeek' });

            return {
                labels: [],
                passed: new Array(WEEKS_PER_YEAR).fill(0),
                failed: new Array(WEEKS_PER_YEAR).fill(0),
                errored: new Array(WEEKS_PER_YEAR).fill(0),
            };
        }
    }

    private async getControlTestHistoryByMonthOptimized(
        account: Account,
        testId: number,
    ): Promise<ControlTestInstanceHistoryType> {
        const months = this.getTwelveMonthTrail();

        // Create default data structure factory to avoid duplication
        const createDefaultHistoryData = (): ControlTestInstanceHistoryType => ({
            labels: months.map(month => `MONTH_${month}`),
            passed: new Array(months.length).fill(0),
            failed: new Array(months.length).fill(0),
            errored: new Array(months.length).fill(0),
        });

        try {
            // Define query configurations for better maintainability
            const queryConfigs = [
                { status: CheckResultStatus.PASSED, name: 'passed' },
                { status: CheckResultStatus.FAILED, name: 'failed' },
                { status: CheckResultStatus.ERROR, name: 'errored' },
            ] as const;

            // Execute all database queries in parallel with individual error handling
            const queryPromises = queryConfigs.map(async ({ status, name }) => {
                try {
                    const result =
                        await this.controlTestInstanceHistoryRepository.getControlTestHistoryByMonthOptimized(
                            account,
                            status,
                            testId,
                        );
                    return { status: name, result, error: null };
                } catch (queryError) {
                    this.warn(`Failed to fetch ${name} results for month history`, account, {
                        testId,
                        status,
                        error: queryError,
                    });
                    return { status: name, result: null, error: queryError };
                }
            });

            const queryResults = await Promise.all(queryPromises);

            // Initialize data structure
            const data = createDefaultHistoryData();

            // Enhanced helper function with better validation and error handling
            const processMonthlyResults = (
                queryResult: { status: string; result: any; error: any },
                targetArray: number[],
            ): void => {
                const { status, result, error } = queryResult;

                if (error || !result?.length) {
                    this.debug(`No ${status} data available for monthly history`, account, {
                        testId,
                        hasError: !!error,
                        hasResult: !!result?.length,
                    });
                    return; // Array is already initialized with zeros
                }

                // Validate result structure
                const resultData = result[0];
                if (!resultData || typeof resultData !== 'object') {
                    this.warn(`Invalid ${status} result structure for monthly history`, account, {
                        testId,
                        resultType: typeof resultData,
                    });
                    return;
                }

                // Process each month with enhanced validation
                months.forEach((month, index) => {
                    if (index >= targetArray.length) {
                        this.warn(`Month index ${index} exceeds target array length`, account, {
                            testId,
                        });
                        return;
                    }

                    const rawValue = resultData[month];
                    if (rawValue !== undefined && rawValue !== null) {
                        const count = Number(rawValue);
                        if (!isNaN(count) && count >= 0) {
                            targetArray[index] = Math.floor(count); // Ensure integer values
                        } else {
                            this.debug(
                                `Invalid count value for ${status} in month ${month}`,
                                account,
                                { testId, rawValue, parsedValue: count },
                            );
                        }
                    }
                });
            };

            // Process all result types with their corresponding arrays
            const resultMap = new Map([
                ['passed', data.passed],
                ['failed', data.failed],
                ['errored', data.errored],
            ]);

            queryResults.forEach(queryResult => {
                const targetArray = resultMap.get(queryResult.status);
                if (targetArray) {
                    processMonthlyResults(queryResult, targetArray);
                }
            });

            // Log successful completion with summary
            this.debug('Successfully retrieved monthly control test history', account, {
                testId,
                totalPassed: data.passed.reduce((sum, count) => sum + count, 0),
                totalFailed: data.failed.reduce((sum, count) => sum + count, 0),
                totalErrored: data.errored.reduce((sum, count) => sum + count, 0),
            });

            return data;
        } catch (error) {
            // Log comprehensive error information
            this.error(error as Error, account, {
                testId,
                method: 'getControlTestHistoryByMonth',
                monthsCount: months.length,
                context: 'Failed to retrieve monthly control test history',
            });

            // Return graceful fallback
            return createDefaultHistoryData();
        }
    }

    private getTwelveMonthTrail(): string[] {
        const now = new Date();

        const currentMonth = now.getMonth() + 1;
        const monthsInAYear = 12;

        const months = ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12'];

        for (let index = 0; index < monthsInAYear - currentMonth; index++) {
            months.unshift(months.pop());
        }

        return months;
    }

    private async getControlTestInstancesByCheckTypeAndCheckStatus(
        account: Account,
        checkType: CheckType,
        checkStatus: CheckStatus,
    ): Promise<ControlTestInstance[]> {
        const dto = new MonitorsRequestDto();
        dto.q = null;
        dto.checkResultStatus = null;
        dto.checkResultStatuses = null;
        dto.checkStatus = checkStatus;
        dto.type = checkType;
        dto.page = 1;
        dto.limit = config.get('pagination.limit');
        dto.getAll = true; // Use repository's built-in functionality to fetch all pages

        const result = await this.controlTestInstanceRepository.listControlTestInstances(
            account,
            dto,
        );

        return result?.data || [];
    }

    private isValidCheckType(checkType: string): boolean {
        const isValidKey = Object.keys(CheckType).includes(checkType);
        const isValidValue = Object.values(CheckType).includes(checkType as any);
        return isValidKey || isValidValue;
    }

    private async getMostRecentFailedHistoryByTestId(workspaceId: number, testId: number) {
        return this.controlTestInstanceHistoryRepository.getMostRecentFailedHistoryByTestId(
            workspaceId,
            testId,
        );
    }

    private getInfrastructureMonitorHeadersForCsv(metadata: MonitorInstanceMetadataItem[]): {
        organizationalUnitId: boolean;
        accountId: boolean;
        accountName: boolean;
        region: boolean;
        arn?: boolean;
    } {
        const headers: {
            organizationalUnitId: boolean;
            accountId: boolean;
            accountName: boolean;
            region: boolean;
            arn?: boolean;
        } = {
            organizationalUnitId: false,
            accountId: false,
            accountName: false,
            region: false,
        };

        for (const { monitorResult } of metadata) {
            if (!monitorResult || !Array.isArray(monitorResult.fail)) break;

            for (const item of monitorResult.fail) {
                if (item.organizationalUnitId && !headers.organizationalUnitId) {
                    headers.organizationalUnitId = true;
                }

                if (item.accountId && !headers.accountId) {
                    headers.accountId = true;
                }

                if (item.accountName && !headers.accountName) {
                    headers.accountName = true;
                }

                if (item.region && !headers.region) {
                    headers.region = true;
                }

                if (Object.values(headers).every(Boolean)) break;
            }

            if (Object.values(headers).every(Boolean)) break;
        }

        return headers;
    }

    public async getCompanyByAccountId(accountId: AccountIdType): Promise<Company> {
        return this.companyRepository.findOneOrFail({
            where: { accountId },
            relations: ['actAs'],
        });
    }

    public async isFindingsEnhancementPhase1Enabled(account: Account) {
        const flag = {
            category: FeatureFlagCategory.NONE,
            name: FeatureFlag.RELEASE_FINDINGS_ENHANCEMENT_PHASE_1,
            defaultValue: false,
        };

        return this.featureFlagService.evaluateAsTenant(flag, account);
    }

    private async isMonitoringTestInstanceEndpointRefactorEnabled(account: Account) {
        const flag = {
            category: FeatureFlagCategory.NONE,
            name: FeatureFlag.RELEASE_AP_MONITORING_TEST_INSTANCE_ENDPOINT_REFRACTOR,
            defaultValue: false,
        };

        return this.featureFlagService.evaluateAs(flag, account);
    }

    private get companyRepository(): Repository<Company> {
        return this.getTenantRepository(Company);
    }

    private get controlTestInstanceRepository(): ControlTestInstanceRepository {
        return this.getCustomTenantRepository(ControlTestInstanceRepository);
    }

    private get monitorStatRepository(): MonitorStatRepository {
        return this.getCustomTenantRepository(MonitorStatRepository);
    }

    private get codeRepositorySettingsRepository(): CodeRepositorySettingsRepository {
        return this.getCustomTenantRepository(CodeRepositorySettingsRepository);
    }

    private get monitorInstanceRepository(): MonitorInstanceRepository {
        return this.getCustomTenantRepository(MonitorInstanceRepository);
    }

    private get controlTestInstanceHistoryRepository(): ControlTestInstanceHistoryRepository {
        return this.getCustomTenantRepository(ControlTestInstanceHistoryRepository);
    }

    private get exclusionRepository(): MonitorInstanceExclusionRepository {
        return this.getCustomTenantRepository(MonitorInstanceExclusionRepository);
    }

    private get userRepository(): UserRepository {
        return this.getCustomTenantRepository(UserRepository);
    }

    private get findingExclusionRepository(): FindingExclusionRepository {
        return this.getCustomTenantRepository(FindingExclusionRepository);
    }

    private get personnelRepository(): PersonnelRepository {
        return this.getCustomTenantRepository(PersonnelRepository);
    }

    private get complianceCheckExclusionRepository(): ComplianceCheckExclusionRepository {
        return this.getCustomTenantRepository(ComplianceCheckExclusionRepository);
    }

    private get vulnerabilityFindingRepository(): VulnerabilityFindingRepository {
        return this.getCustomTenantRepository(VulnerabilityFindingRepository);
    }
}
