import { ApiProperty } from '@nestjs/swagger';
import { TestTrendsCountsResponseDto } from 'app/monitors/dtos/stats/test-trends-counts-response.dto';
import { TestTrendsWindowResponseDto } from 'app/monitors/dtos/stats/test-trends-window-response.dto';
import { TestTrendsOverview } from 'app/monitors/types/stats/test-trends-overview-stats.type';
import { ResponseDto } from 'commons/dtos/response.dto';

export class TestTrendsOverviewResponseDto extends ResponseDto {
    @ApiProperty({
        type: () => TestTrendsWindowResponseDto,
        example: { from: '2023-01-01T00:00:00.000Z', to: '2023-01-01T00:00:00.000Z' },
        description: 'Time window for the stats',
    })
    window: TestTrendsWindowResponseDto;

    @ApiProperty({
        type: () => TestTrendsCountsResponseDto,
        example: { error: 3, passing: 120, failed: 120 },
        description: 'Counts for the stats',
    })
    counts: TestTrendsCountsResponseDto;

    @ApiProperty({
        type: 'number',
        required: false,
        example: 86400000,
        nullable: true,
        description: 'Average time to remediation (ms) over the last 7 days',
    })
    avgTimeToRemediationMs?: number | null;

    @ApiProperty({
        type: 'number',
        required: false,
        example: 8.2,
        nullable: true,
        description: 'Percentage change between the current and previous period MTTR',
    })
    timeToRemediationDelta?: number | null;

    @ApiProperty({
        type: 'number',
        example: 120,
        description: 'Number of recoveries included in the stats',
    })
    includedRecoveries: number;

    build(totalTestTrends: TestTrendsOverview): TestTrendsOverviewResponseDto {
        this.window = new TestTrendsWindowResponseDto().build(totalTestTrends.window);
        this.counts = new TestTrendsCountsResponseDto().build(totalTestTrends.counts);
        this.avgTimeToRemediationMs = totalTestTrends.avgTimeToRemediationMs ?? null;
        this.includedRecoveries = totalTestTrends.includedRecoveries;
        this.timeToRemediationDelta = totalTestTrends.timeToRemediationDelta;
        return this.send();
    }
}
