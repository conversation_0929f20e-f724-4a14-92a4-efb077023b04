import { CheckResultStatus } from '@drata/enums';
import { ClientType } from 'commons/enums/auth/client-type.enum';
import { CheckType } from 'commons/enums/check-type.enum';

export class MonitorConstants {
    /**
     * Identified Test IDs belonging to EDR connections
     */
    static readonly EDR_TEST_IDS = [64];

    /**
     * Identified Test IDs belonging to CSPM connections
     */
    static readonly CSPM_TEST_IDS = [208, 209, 210];

    /**
     * Identified Test IDs belonging to Vulnerability connections
     */
    static readonly VULNERABILITY_TEST_IDS = [
        212, 213, 235, 236, 237, 238, 239, 240, 241, 242, 282, 283, 284, 285, 286, 287, 288, 289,
        313, 314,
    ];

    /**
     * Client types that are region agnostic
     */
    static readonly REGION_AGNOSTIC_CLIENT_TYPES = [
        ClientType.AZURE,
        ClientType.AZURE_BOARDS,
        ClientType.AZURE_DEVOPS,
        ClientType.AZURE_ORG_UNITS,
        ClientType.AZURE_REPOS,
        ClientType.GCP,
    ];

    /**
     * Check types that are available for monitor test reports. These are the check types
     * that support monitor test report generation (ZIP files with CSV and PDF, or CSV only)
     */
    static readonly AVAILABLE_MONITOR_TEST_REPORT_TYPES = [
        CheckType.INFRASTRUCTURE,
        CheckType.OBSERVABILITY,
        CheckType.AGENT,
        CheckType.VERSION_CONTROL,
        CheckType.IDENTITY,
        CheckType.TICKETING,
    ];

    /**
     * Check result types that are available for monitor test reports. These are the check result types
     * that support monitor test report generation (ZIP files with CSV and PDF, or CSV only)
     */
    static readonly AVAILABLE_MONITOR_TEST_CHECK_RESULT_STATUSES = [CheckResultStatus.FAILED];
}
