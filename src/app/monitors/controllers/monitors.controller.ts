import { MongoAbility } from '@casl/ability';
import { AsyncActionType, AsyncNotificationType } from '@drata/enums';
import {
    Controller,
    Delete,
    Get,
    HttpStatus,
    Param,
    ParseIntPipe,
    ParseUUIDPipe,
    Post,
    Put,
    Query,
} from '@nestjs/common';
import { Body } from '@nestjs/common/decorators/http/route-params.decorator';
import {
    ApiBadRequestResponse,
    ApiConflictResponse,
    ApiCreatedResponse,
    ApiForbiddenResponse,
    ApiNotFoundResponse,
    ApiOkResponse,
    ApiOperation,
    ApiQuery,
    ApiTags,
} from '@nestjs/swagger';
import { AppController } from 'app/app.controller';
import { IListTestResult } from 'app/document-library/evidence-library/interfaces/list-test-result.interface';
import { ControlTestInstanceEventSummaryCsvDto } from 'app/events/dtos/control-test-instance-event-summary-csv.dto';
import { TestGraphsType } from 'app/grc/types/test-graphs.type';
import { ControlTestInstanceAutopilotRequestDto } from 'app/monitors/dtos/control-test-instance-autopilot-request.dto';
import { ControlTestInstanceDetailsResponseDto } from 'app/monitors/dtos/control-test-instance-details-response.dto';
import { ControlTestInstanceHistoryRequestDto } from 'app/monitors/dtos/control-test-instance-history-request.dto';
import { ControlTestInstanceHistoryResponseDto } from 'app/monitors/dtos/control-test-instance-history-response.dto';
import { ControlTestInstanceResponseDto } from 'app/monitors/dtos/control-test-instance-response.dto';
import { ControlTestsPaginatedResponseDto } from 'app/monitors/dtos/control-tests-paginated-response-dto';
import { CreateCustomTestRequestDto } from 'app/monitors/dtos/create-custom-test-request.dto';
import { CustomTestMetadataResponseDto } from 'app/monitors/dtos/custom-test-metadata-response.dto';
import { CustomTestRecipeRequestDto } from 'app/monitors/dtos/custom-test-recipe-request.dto';
import { CustomTestRequestDto } from 'app/monitors/dtos/custom-test-request.dto';
import { MonitorDownloadResponseDto } from 'app/monitors/dtos/monitor-download-response.dto';
import { MonitorFailedCspmResponseDto } from 'app/monitors/dtos/monitor-failed-csmp-response.dto';
import { MonitorFailedIncludedResponseDto } from 'app/monitors/dtos/monitor-failed-included-response.dto';
import { MonitorFailedInfrastructureResponseDto } from 'app/monitors/dtos/monitor-failed-infrastructure-response.dto';
import { MonitorInstanceDeleteExclusionsDto } from 'app/monitors/dtos/monitor-instance-delete-exclusions-request.dto';
import { MonitorInstanceExclusionsRequestDto } from 'app/monitors/dtos/monitor-instance-exclusions-request.dto';
import { MonitorMetadataAllExclusionsResponseDto } from 'app/monitors/dtos/monitor-instance-metadata-all-exclusions-response.dto';
import { MonitorMetadataExclusionResponseDto } from 'app/monitors/dtos/monitor-instance-metadata-exclusion-response.dto';
import { MonitorListTestResultsResponseDto } from 'app/monitors/dtos/monitor-list-test-results-response.dto';
import { MonitorStatResponseDto } from 'app/monitors/dtos/monitor-stat-response.dto';
import { MonitorStatsRequestDto } from 'app/monitors/dtos/monitor-stats-request.dto';
import { MonitorTestResultsPaginatedRequestDto } from 'app/monitors/dtos/monitor-test-results-paginated.dto';
import { MonitorsDetailsRequestDto } from 'app/monitors/dtos/monitors-details-request.dto';
import { MonitorsRequestDto } from 'app/monitors/dtos/monitors-request.dto';
import { TestTrendsOverviewResponseDto } from 'app/monitors/dtos/stats/test-trends-overview-response.dto';
import { TestTrendsResponseDto } from 'app/monitors/dtos/stats/test-trends-response.dto';
import { ControlTestInstance } from 'app/monitors/entities/control-test-instance.entity';
import { MonitorInstanceExclusion } from 'app/monitors/entities/monitor-instance-exclusion.entity';
import { MonitorStat } from 'app/monitors/entities/monitor-stat.entity';
import { MonitorFindingsType } from 'app/monitors/enums/monitor-findings-type.enum';
import { MonitorsRoute } from 'app/monitors/routes/monitors.routes';
import { MonitorTestService } from 'app/monitors/services/monitor-test.service';
import { MonitorsCoreService } from 'app/monitors/services/monitors-core.service';
import { MonitorsOrchestrationService } from 'app/monitors/services/monitors-orchestration.service';
import { MonitorsService } from 'app/monitors/services/monitors.service';
import { ControlTestInstanceDetails } from 'app/monitors/types/control-test-instance-details.type';
import { ControlTestInstanceHistoryType } from 'app/monitors/types/control-test-instance-history.type';
import { CustomTestsMetadata } from 'app/monitors/types/metadata/custom-tests-metadata.type';
import { TestTrendsOverview } from 'app/monitors/types/stats/test-trends-overview-stats.type';
import { TestTrends } from 'app/monitors/types/stats/test-trends-stats.type';
import { NoteRequestDto } from 'app/notes/dtos/note-request.dto';
import { NoteResponseDto } from 'app/notes/dtos/note-response.dto';
import { NotesPaginatedResponseDto } from 'app/notes/dtos/notes-paginated-response.dto';
import { NotesRequestDto } from 'app/notes/dtos/notes-request.dto';
import { Note } from 'app/notes/entities/note.entity';
import { TestNotesOrchestrationService } from 'app/notes/services/test-notes-orchestration.service';
import { User } from 'app/users/entities/user.entity';
import { Account } from 'auth/entities/account.entity';
import { CheckAbilities } from 'casl/ability.handlers';
import { Action } from 'casl/actions';
import {
    AsyncNotifierStream,
    AsyncNotifierStreamType,
    toAsyncNotifierStream,
} from 'commons/decorators/async-notifier-stream.decorator';
import { CsvReportDownload } from 'commons/decorators/csv-report-download.decorator';
import { Dto } from 'commons/decorators/dto.decorator';
import { FileBufferDownload } from 'commons/decorators/file-buffer-download.decorator';
import { FindingsCsvReportDownload } from 'commons/decorators/findings-csv-report-download.decorator';
import { GetAccount } from 'commons/decorators/get-account.decorator';
import {
    ExtractedRequestProps,
    GetRequestNotifierProps,
} from 'commons/decorators/get-request-notifier-props.decorator';
import { GetUser } from 'commons/decorators/get-user.decorator';
import { HasProductId } from 'commons/decorators/has-productId.decorator';
import { Area, ProductArea } from 'commons/decorators/product-area.decorator';
import { Roles } from 'commons/decorators/roles.decorator';
import { SetWorkspace } from 'commons/decorators/set-workspace.decorator';
import { StatusOnEmpty } from 'commons/decorators/status-on-empty.decorator';
import { AsyncNotifierResponseDto } from 'commons/dtos/async-notifier-response.dto';
import { ExceptionResponseDto } from 'commons/dtos/exception-response.dto';
import { SignedUrlResponseDto } from 'commons/dtos/signed-url-response.dto';
import { AccountEntitlementType } from 'commons/enums/account-entitlement-type.enum';
import { ApiResponse } from 'commons/enums/api-response.enum';
import { ProviderType } from 'commons/enums/auth/provider-type.enum';
import { Role } from 'commons/enums/users/role.enum';
import { Subject } from 'commons/enums/users/subject.enum';
import { IsPositiveIntPipe } from 'commons/pipes/is-positive-int-pipe';
import { CsvDataSetType } from 'commons/types/csv-data-set.type';
import { FileBufferType } from 'commons/types/file-buffer.type';
import { PaginationType } from 'commons/types/pagination.type';
import { DownloaderPayloadType } from 'dependencies/downloader/types/downloader-payload.interface';
import { RequireAccountEntitlement } from 'entitlements/decorators/require-account-entitlement.decorator';
import { FeatureFlagService } from 'feature-flags/feature-flag.service';

@ApiTags('Monitors')
@Controller()
@ProductArea(Area.MONITOR_TEST_DETAILS_UI)
export class MonitorsController extends AppController {
    /**
     *
     * @param monitorsService
     * @param eventService
     */
    constructor(
        private readonly monitorsService: MonitorsService,
        private readonly monitorTestService: MonitorTestService,
        private readonly testNotesOrchestrationService: TestNotesOrchestrationService,
        private readonly monitorsCoreService: MonitorsCoreService,
        private readonly monitorsOrchestrationService: MonitorsOrchestrationService,
        private readonly featureFlagService: FeatureFlagService,
    ) {
        super();
    }

    @ApiOperation({
        description: `List Monitors (a.k.a control test instances) given the provided search terms and filters
        <br><br>
        Allowed Roles: [
            ${Role[Role.TECHGOV]},
            ${Role[Role.ADMIN]},
            ${Role[Role.ACT_AS_READ_ONLY]},
            ${Role[Role.DEVOPS_ENGINEER]}
        ]`,
    })
    @ApiOkResponse({
        type: ControlTestsPaginatedResponseDto,
    })
    @ApiBadRequestResponse({
        description: ApiResponse.BAD_REQUEST,
        type: ExceptionResponseDto,
    })
    @HasProductId()
    @Roles(Role.TECHGOV, Role.ADMIN, Role.ACT_AS_READ_ONLY, Role.DEVOPS_ENGINEER)
    @CheckAbilities((ability: MongoAbility) => ability.can(Action.Read, Subject[Subject.Monitors]))
    @Dto(ControlTestsPaginatedResponseDto)
    @Get(MonitorsRoute.GET_MONITORS)
    listControlTestInstances(
        @GetAccount() account: Account,
        @GetUser() user: User,
        @Query() requestDto: MonitorsRequestDto,
    ): Promise<PaginationType<TestGraphsType>> {
        return this.monitorsCoreService.listControlTestInstances(account, requestDto, user);
    }

    @ApiOperation({
        description: `Get Monitor details (a.k.a control test instances) and its monitor instances
        <br><br>
        Allowed Roles: [
            ${Role[Role.TECHGOV]},
            ${Role[Role.ADMIN]},
            ${Role[Role.ACT_AS_READ_ONLY]},
            ${Role[Role.DEVOPS_ENGINEER]}
        ]`,
    })
    @ApiOkResponse({
        type: ControlTestInstanceDetailsResponseDto,
    })
    @ApiBadRequestResponse({
        description: ApiResponse.BAD_REQUEST,
        type: ExceptionResponseDto,
    })
    @ApiNotFoundResponse({
        description: ApiResponse.NOT_FOUND,
        type: ExceptionResponseDto,
    })
    @HasProductId()
    @Roles(Role.TECHGOV, Role.ADMIN, Role.ACT_AS_READ_ONLY, Role.DEVOPS_ENGINEER)
    @CheckAbilities((ability: MongoAbility) =>
        ability.can(Action.Read, Subject[Subject.ControlTestInstance]),
    )
    @Dto(ControlTestInstanceDetailsResponseDto)
    @Get(MonitorsRoute.GET_MONITOR)
    getControlTestInstance(
        @GetAccount() account: Account,
        @Param('testId', ParseIntPipe) testId: number,
    ): Promise<ControlTestInstanceDetails> {
        return this.monitorsService.getControlTestInstanceForUser(account, testId);
    }

    @ApiOperation({
        description: `Get monitors statistics
        <br><br>
        Allowed Roles: [
            ${Role[Role.TECHGOV]},
            ${Role[Role.ADMIN]},
            ${Role[Role.ACT_AS_READ_ONLY]},
            ${Role[Role.DEVOPS_ENGINEER]}
        ]`,
    })
    @ApiOkResponse({
        type: MonitorStatResponseDto,
    })
    @ApiNotFoundResponse({
        description: ApiResponse.NOT_FOUND,
        type: ExceptionResponseDto,
    })
    @HasProductId()
    @Roles(Role.TECHGOV, Role.ADMIN, Role.ACT_AS_READ_ONLY, Role.DEVOPS_ENGINEER)
    @CheckAbilities((ability: MongoAbility) =>
        ability.can(Action.Read, Subject[Subject.ControlTestInstance]),
    )
    @Dto(MonitorStatResponseDto)
    @Get(MonitorsRoute.GET_MONITOR_STATS)
    getMonitorsStats(
        @GetAccount() account: Account,
        @Query() requestDto: MonitorStatsRequestDto,
    ): Promise<MonitorStat> {
        return this.monitorsService.getMonitorsStats(account, requestDto);
    }

    @ApiOperation({
        description: `Get monitors history
        <br><br>
        Allowed Roles: [
            ${Role[Role.TECHGOV]},
            ${Role[Role.ADMIN]},
            ${Role[Role.ACT_AS_READ_ONLY]},
            ${Role[Role.DEVOPS_ENGINEER]}
        ]`,
    })
    @ApiOkResponse({
        type: ControlTestInstanceHistoryResponseDto,
    })
    @ApiBadRequestResponse({
        description: ApiResponse.BAD_REQUEST,
        type: ExceptionResponseDto,
    })
    @HasProductId()
    @Roles(Role.TECHGOV, Role.ADMIN, Role.ACT_AS_READ_ONLY, Role.DEVOPS_ENGINEER)
    @CheckAbilities((ability: MongoAbility) =>
        ability.can(Action.Read, Subject[Subject.ControlTestInstance]),
    )
    @Dto(ControlTestInstanceHistoryResponseDto)
    @Get(MonitorsRoute.GET_MONITOR_HISTORY)
    getControlTestHistory(
        @GetAccount() account: Account,
        @Query() requestDto: ControlTestInstanceHistoryRequestDto,
    ): Promise<ControlTestInstanceHistoryType> {
        return this.monitorsService.getControlTestHistory(account, requestDto);
    }

    @ApiOperation({
        description: `Update monitor status
        <br><br>
        Allowed Roles: [
            ${Role[Role.TECHGOV]},
            ${Role[Role.ADMIN]},
            ${Role[Role.DEVOPS_ENGINEER]}
        ]`,
    })
    @ApiOkResponse({
        type: ControlTestInstanceResponseDto,
    })
    @ApiBadRequestResponse({
        description: ApiResponse.BAD_REQUEST,
        type: ExceptionResponseDto,
    })
    @ApiNotFoundResponse({
        description: ApiResponse.NOT_FOUND,
        type: ExceptionResponseDto,
    })
    @ApiConflictResponse({
        description: ApiResponse.CONFLICT,
        type: ExceptionResponseDto,
    })
    @HasProductId()
    @Roles(Role.TECHGOV, Role.ADMIN, Role.DEVOPS_ENGINEER)
    @CheckAbilities((ability: MongoAbility) =>
        ability.can(Action.Manage, Subject[Subject.ControlTestInstance]),
    )
    @Dto(ControlTestInstanceResponseDto)
    @Put(MonitorsRoute.PUT_MONITOR_AUTOPILOT)
    controlTestInstanceAutopilotUpdate(
        @GetAccount() account: Account,
        @GetUser() user: User,
        @Param('testId', ParseIntPipe) testId: number,
        @Body() requestDto: ControlTestInstanceAutopilotRequestDto,
    ): Promise<ControlTestInstance> {
        return this.monitorsOrchestrationService.controlTestInstanceAutopilotUpdate(
            account,
            user,
            testId,
            requestDto,
        );
    }

    @ApiOperation({
        description: `Get a monitor exclusion
        <br><br>
        Allowed Roles: [
            ${Role[Role.ADMIN]}
            ${Role[Role.TECHGOV]},
            ${Role[Role.ACT_AS_READ_ONLY]},
            ${Role[Role.DEVOPS_ENGINEER]}
        ]`,
    })
    @ApiOkResponse({ type: MonitorMetadataExclusionResponseDto })
    @ApiBadRequestResponse({
        description: ApiResponse.BAD_REQUEST,
        type: ExceptionResponseDto,
    })
    @ApiNotFoundResponse({
        description: ApiResponse.NOT_FOUND,
        type: ExceptionResponseDto,
    })
    @Roles(Role.ADMIN, Role.TECHGOV, Role.ACT_AS_READ_ONLY, Role.DEVOPS_ENGINEER)
    @CheckAbilities((ability: MongoAbility) =>
        ability.can(Action.Read, Subject[Subject.MonitorInstanceExclusion]),
    )
    @Dto(MonitorMetadataExclusionResponseDto)
    @Get(MonitorsRoute.GET_MONITOR_EXCLUSION)
    getMonitorExclusion(
        @Param('exclusionId', ParseIntPipe) id: number,
    ): Promise<MonitorInstanceExclusion> {
        return this.monitorsService.getMonitorExclusionById(id);
    }

    @HasProductId()
    @Roles(
        Role.TECHGOV,
        Role.ADMIN,
        Role.AUDITOR,
        Role.ACT_AS_READ_ONLY,
        Role.DEVOPS_ENGINEER,
        Role.WORKSPACE_ADMINISTRATOR,
        Role.SERVICE_USER,
        Role.CONTROL_MANAGER,
        Role.PEOPLE_OPS,
    )
    @CheckAbilities((ability: MongoAbility) =>
        ability.can(Action.Read, Subject[Subject.ControlTestInstance]),
    )
    @CsvReportDownload(MonitorDownloadResponseDto)
    @ApiOkResponse({
        description: ApiResponse.CSV_FILE,
    })
    @Get(MonitorsRoute.GET_DOWNLOAD_MONITORS)
    downloadMonitorTests(
        @Query() requestDto: MonitorsRequestDto,
        @GetAccount() account: Account,
        @GetUser() user: User,
    ): Promise<CsvDataSetType> {
        return this.monitorsService.downloadMonitorTests(requestDto, account, user);
    }

    @ApiOperation({
        description: `Create a set of monitor exclusions
        <br><br>
        Allowed Roles: [
            ${Role[Role.ADMIN]}
            ${Role[Role.TECHGOV]},
            ${Role[Role.DEVOPS_ENGINEER]}
        ]`,
    })
    @ApiCreatedResponse({ type: MonitorMetadataAllExclusionsResponseDto })
    @ApiBadRequestResponse({
        description: ApiResponse.BAD_REQUEST,
        type: ExceptionResponseDto,
    })
    @ApiNotFoundResponse({
        description: ApiResponse.NOT_FOUND,
        type: ExceptionResponseDto,
    })
    @ApiBadRequestResponse({
        description: ApiResponse.CONFLICT,
        type: ExceptionResponseDto,
    })
    @HasProductId()
    @Roles(Role.ADMIN, Role.TECHGOV, Role.DEVOPS_ENGINEER)
    @CheckAbilities((ability: MongoAbility) =>
        ability.can(Action.Manage, Subject[Subject.MonitorInstanceExclusion]),
    )
    @Dto(MonitorMetadataAllExclusionsResponseDto)
    @Post(MonitorsRoute.POST_MONITOR_EXCLUSIONS)
    createMonitorExclusion(
        @GetAccount() account: Account,
        @GetUser() user: User,
        @Param('testId', ParseIntPipe) testId: number,
        @Body() requestDto: MonitorInstanceExclusionsRequestDto,
    ): Promise<MonitorInstanceExclusion[]> {
        return this.monitorsService.createMonitorExclusions(account, user, requestDto, testId);
    }

    @ApiOperation({
        description: `Update a set of monitor exclusions
        <br><br>
        Allowed Roles: [
            ${Role[Role.ADMIN]}
            ${Role[Role.TECHGOV]},
            ${Role[Role.DEVOPS_ENGINEER]}
        ]`,
    })
    @ApiOkResponse({ type: MonitorMetadataAllExclusionsResponseDto })
    @ApiBadRequestResponse({
        description: ApiResponse.BAD_REQUEST,
        type: ExceptionResponseDto,
    })
    @ApiNotFoundResponse({
        description: ApiResponse.NOT_FOUND,
        type: ExceptionResponseDto,
    })
    @HasProductId()
    @Roles(Role.ADMIN, Role.TECHGOV, Role.DEVOPS_ENGINEER)
    @CheckAbilities((ability: MongoAbility) =>
        ability.can(Action.Manage, Subject[Subject.MonitorInstanceExclusion]),
    )
    @Dto(MonitorMetadataAllExclusionsResponseDto)
    @Put(MonitorsRoute.PUT_MONITOR_EXCLUSIONS)
    updateMonitorExclusion(
        @GetAccount() account: Account,
        @GetUser() user: User,
        @Param('testId', ParseIntPipe) testId: number,
        @Body() requestDto: MonitorInstanceExclusionsRequestDto,
    ): Promise<MonitorInstanceExclusion[]> {
        return this.monitorsService.updateMonitorExclusions(account, user, requestDto, testId);
    }

    @ApiOperation({
        description: `Delete monitor instance exclusions.
        <br><br>
        Allowed Roles: [
            ${Role[Role.ADMIN]}
            ${Role[Role.TECHGOV]},
            ${Role[Role.DEVOPS_ENGINEER]}
        ]`,
    })
    @ApiOkResponse({
        description: ApiResponse.OK,
        type: MonitorMetadataAllExclusionsResponseDto,
    })
    @ApiBadRequestResponse({
        description: ApiResponse.BAD_REQUEST,
        type: ExceptionResponseDto,
    })
    @ApiNotFoundResponse({
        description: ApiResponse.NOT_FOUND,
        type: ExceptionResponseDto,
    })
    @HasProductId()
    @Roles(Role.ADMIN, Role.TECHGOV, Role.DEVOPS_ENGINEER)
    @CheckAbilities((ability: MongoAbility) =>
        ability.can(Action.Manage, Subject[Subject.MonitorInstanceExclusion]),
    )
    @Dto(MonitorMetadataAllExclusionsResponseDto)
    @Delete(MonitorsRoute.DELETE_MONITOR_EXCLUSIONS)
    deleteMonitorExclusions(
        @GetAccount() account: Account,
        @GetUser() user: User,
        @Body() dto: MonitorInstanceDeleteExclusionsDto,
    ): Promise<MonitorInstanceExclusion[]> {
        return this.monitorsService.deleteMonitorExclusions(account, user, dto);
    }

    @ApiOperation({
        description: `Stop a stuck test from running
        <br><br>
        Allowed Roles: [
            ${Role[Role.TECHGOV]},
            ${Role[Role.ADMIN]}},
            ${Role[Role.DEVOPS_ENGINEER]}
        ]`,
    })
    @ApiOkResponse({
        type: ControlTestInstanceResponseDto,
    })
    @ApiBadRequestResponse({
        description: ApiResponse.BAD_REQUEST,
        type: ExceptionResponseDto,
    })
    @ApiNotFoundResponse({
        description: ApiResponse.NOT_FOUND,
        type: ExceptionResponseDto,
    })
    @HasProductId()
    @Roles(Role.TECHGOV, Role.ADMIN, Role.DEVOPS_ENGINEER)
    @CheckAbilities((ability: MongoAbility) =>
        ability.can(Action.Manage, Subject[Subject.ControlTestInstance]),
    )
    @Dto(ControlTestInstanceResponseDto)
    @Put(MonitorsRoute.PUT_MONITOR_STOP)
    stopControlTestInstance(
        @GetAccount() account: Account,
        @Param('testId', ParseIntPipe) testId: number,
    ): Promise<ControlTestInstance> {
        return this.monitorsService.controlTestInstanceAutopilotStop(account, testId);
    }

    @ApiOperation({
        description: `Get notes for a monitor
        <br><br>
        Allowed Roles: [
            ${Role[Role.TECHGOV]},
            ${Role[Role.ADMIN]},
            ${Role[Role.WORKSPACE_ADMINISTRATOR]},
            ${Role[Role.CONTROL_MANAGER]},
            ${Role[Role.ACT_AS_READ_ONLY]},
            ${Role[Role.DEVOPS_ENGINEER]}
        ]`,
    })
    @ApiOkResponse({
        type: NotesPaginatedResponseDto,
    })
    @ApiBadRequestResponse({
        description: ApiResponse.BAD_REQUEST,
        type: ExceptionResponseDto,
    })
    @ApiNotFoundResponse({
        description: ApiResponse.NOT_FOUND,
        type: ExceptionResponseDto,
    })
    @Roles(
        Role.TECHGOV,
        Role.ADMIN,
        Role.ACT_AS_READ_ONLY,
        Role.WORKSPACE_ADMINISTRATOR,
        Role.CONTROL_MANAGER,
        Role.DEVOPS_ENGINEER,
    )
    @CheckAbilities((ability: MongoAbility) => ability.can(Action.Read, Subject[Subject.Note]))
    @HasProductId()
    @Dto(NotesPaginatedResponseDto)
    @Get(MonitorsRoute.GET_NOTES)
    getNotes(
        @GetAccount() account: Account,
        @GetUser() user: User,
        @Param('testId', ParseIntPipe) testId: number,
        @Query() requestDto: NotesRequestDto,
    ): Promise<PaginationType<Note>> {
        return this.testNotesOrchestrationService.getNotes(requestDto, account, testId);
    }

    @ApiOperation({
        description: `Create a note
        <br><br>
        Allowed Roles: [
            ${Role[Role.TECHGOV]},
            ${Role[Role.ADMIN]},
            ${Role[Role.WORKSPACE_ADMINISTRATOR]},
            ${Role[Role.CONTROL_MANAGER]},
            ${Role[Role.DEVOPS_ENGINEER]}
        ]`,
    })
    @ApiCreatedResponse({
        description: ApiResponse.CREATED,
        type: NoteResponseDto,
    })
    @ApiBadRequestResponse({
        description: ApiResponse.BAD_REQUEST,
        type: ExceptionResponseDto,
    })
    @ApiNotFoundResponse({
        description: ApiResponse.NOT_FOUND,
        type: ExceptionResponseDto,
    })
    @Roles(
        Role.ADMIN,
        Role.TECHGOV,
        Role.WORKSPACE_ADMINISTRATOR,
        Role.CONTROL_MANAGER,
        Role.DEVOPS_ENGINEER,
    )
    @CheckAbilities((ability: MongoAbility) => ability.can(Action.Manage, Subject[Subject.Note]))
    @HasProductId()
    @Dto(NoteResponseDto)
    @Post(MonitorsRoute.POST_CREATE_NOTE)
    createNote(
        @GetAccount() account: Account,
        @GetUser() user: User,
        @Param('testId', ParseIntPipe) testId: number,
        @Body() noteRequestDto: NoteRequestDto,
    ): Promise<Note> {
        return this.testNotesOrchestrationService.createNote(account, user, noteRequestDto, testId);
    }

    @ApiOperation({
        description: `Update a note
        <br><br>
        Allowed Roles: [
            ${Role[Role.TECHGOV]},
            ${Role[Role.ADMIN]},
            ${Role[Role.WORKSPACE_ADMINISTRATOR]},
            ${Role[Role.CONTROL_MANAGER]},
            ${Role[Role.DEVOPS_ENGINEER]}
        ]`,
    })
    @ApiOkResponse({
        type: NoteResponseDto,
    })
    @ApiBadRequestResponse({
        description: ApiResponse.BAD_REQUEST,
        type: ExceptionResponseDto,
    })
    @ApiNotFoundResponse({
        description: ApiResponse.NOT_FOUND,
        type: ExceptionResponseDto,
    })
    @Roles(
        Role.TECHGOV,
        Role.ADMIN,
        Role.WORKSPACE_ADMINISTRATOR,
        Role.CONTROL_MANAGER,
        Role.DEVOPS_ENGINEER,
    )
    @CheckAbilities((ability: MongoAbility) => ability.can(Action.Manage, Subject[Subject.Note]))
    @Dto(NoteResponseDto)
    @Put(MonitorsRoute.PUT_MONITORS_NOTE)
    updateNote(
        @GetAccount() account: Account,
        @GetUser() user: User,
        @Param('noteId', ParseUUIDPipe) noteId: string,
        @Body() requestDto: NoteRequestDto,
    ): Promise<Note> {
        return this.testNotesOrchestrationService.updateNote(account, user, requestDto, noteId);
    }

    @ApiOperation({
        description: `Delete a note
        <br><br>
        Allowed Roles: [
            ${Role[Role.TECHGOV]},
            ${Role[Role.ADMIN]},
            ${Role[Role.WORKSPACE_ADMINISTRATOR]},
            ${Role[Role.CONTROL_MANAGER]},
            ${Role[Role.DEVOPS_ENGINEER]}
        ]`,
    })
    @ApiOkResponse({
        description: ApiResponse.OK,
    })
    @ApiNotFoundResponse({
        description: ApiResponse.NOT_FOUND,
        type: ExceptionResponseDto,
    })
    @ApiQuery({
        name: 'workspaceId',
        type: Number,
        description:
            'ID of the Workspace. If this is not sent, it will default to the global workspace.',
        required: false,
    })
    @Roles(
        Role.TECHGOV,
        Role.ADMIN,
        Role.WORKSPACE_ADMINISTRATOR,
        Role.CONTROL_MANAGER,
        Role.DEVOPS_ENGINEER,
    )
    @CheckAbilities((ability: MongoAbility) => ability.can(Action.Manage, Subject[Subject.Note]))
    @Delete(MonitorsRoute.DELETE_NOTE)
    deleteNote(
        @GetAccount() account: Account,
        @GetUser() user: User,
        @Param('noteId', ParseUUIDPipe) noteId: string,
    ): Promise<void> {
        return this.testNotesOrchestrationService.deleteNote(account, user, noteId);
    }

    @ApiOperation({
        description: `Get test trends statistics
        <br><br>
        Allowed Roles: [
            ${Role[Role.ADMIN]},
            ${Role[Role.TECHGOV]},
            ${Role[Role.ACT_AS_READ_ONLY]},
            ${Role[Role.WORKSPACE_ADMINISTRATOR]},
            ${Role[Role.POLICY_MANAGER]},
            ${Role[Role.DEVOPS_ENGINEER]}
        ]`,
    })
    @ApiOkResponse({
        type: TestTrendsResponseDto,
    })
    @ApiBadRequestResponse({
        description: ApiResponse.BAD_REQUEST,
        type: ExceptionResponseDto,
    })
    @HasProductId()
    @Roles(
        Role.TECHGOV,
        Role.ADMIN,
        Role.ACT_AS_READ_ONLY,
        Role.WORKSPACE_ADMINISTRATOR,
        Role.POLICY_MANAGER,
        Role.DEVOPS_ENGINEER,
    )
    @CheckAbilities(
        (ability: MongoAbility) => ability.can(Action.Read, Subject[Subject.Dashboard]),
        (ability: MongoAbility) => ability.can(Action.Read, Subject[Subject.Policy]),
    )
    @Dto(TestTrendsResponseDto)
    @Get(MonitorsRoute.GET_TEST_TRENDS)
    getTestTrends(@GetAccount() account: Account): Promise<TestTrends> {
        return this.monitorsService.getTestTrends(account);
    }

    @ApiOperation({
        description: `Get test trends overview
        <br><br>
        Allowed Roles: [
            ${Role[Role.ADMIN]},
            ${Role[Role.TECHGOV]},
            ${Role[Role.ACT_AS_READ_ONLY]},
            ${Role[Role.WORKSPACE_ADMINISTRATOR]},
            ${Role[Role.POLICY_MANAGER]},
            ${Role[Role.DEVOPS_ENGINEER]}
        ]`,
    })
    @ApiOkResponse({
        type: TestTrendsOverviewResponseDto,
    })
    @ApiBadRequestResponse({
        description: ApiResponse.BAD_REQUEST,
        type: ExceptionResponseDto,
    })
    @SetWorkspace()
    @Roles(
        Role.TECHGOV,
        Role.ADMIN,
        Role.ACT_AS_READ_ONLY,
        Role.WORKSPACE_ADMINISTRATOR,
        Role.POLICY_MANAGER,
        Role.DEVOPS_ENGINEER,
    )
    @CheckAbilities(
        (ability: MongoAbility) => ability.can(Action.Read, Subject[Subject.Dashboard]),
        (ability: MongoAbility) => ability.can(Action.Read, Subject[Subject.Policy]),
    )
    @Dto(TestTrendsOverviewResponseDto)
    @Get(MonitorsRoute.GET_TEST_TRENDS_OVERVIEW)
    getTestTrendsOverview(
        @GetAccount() account: Account,
        @Param('workspaceId', IsPositiveIntPipe) workspaceId: number,
    ): Promise<TestTrendsOverview> {
        return this.monitorsService.getTestTrendsOverview(account, workspaceId);
    }

    // This implementation is just working for EDR connections, there is fields on metadata that are just for this connection
    @ApiOperation({
        description: `Download failed monitor for EDR report.
        <br><br>
        Allowed Roles: [
            ${Role[Role.TECHGOV]},
            ${Role[Role.ADMIN]},
            ${Role[Role.AUDITOR]},
            ${Role[Role.ACT_AS_READ_ONLY]},
            ${Role[Role.WORKSPACE_ADMINISTRATOR]},
            ${Role[Role.DEVOPS_ENGINEER]}
        ]`,
    })
    @CsvReportDownload(MonitorFailedIncludedResponseDto)
    @Roles(Role.TECHGOV, Role.ADMIN, Role.AUDITOR, Role.ACT_AS_READ_ONLY, Role.DEVOPS_ENGINEER)
    @CheckAbilities((ability: MongoAbility) => ability.can(Action.Read, Subject[Subject.Monitors]))
    @ApiOkResponse({
        description: ApiResponse.CSV_FILE,
    })
    @Get(MonitorsRoute.GET_EDR_REPORT)
    getEdrMonitorFailDetailsReport(
        @GetAccount() account: Account,
        @Param('testId', ParseIntPipe) testId: number,
    ): Promise<CsvDataSetType> {
        return this.monitorsService.getMonitorsForCsvExport(account, testId, ProviderType.EDR);
    }

    // This implementation is just working for CSMP connections, there is fields that are just for this connection
    @ApiOperation({
        description: `Download failed monitor for CSMP report.
        <br><br>
        Allowed Roles: [
            ${Role[Role.TECHGOV]},
            ${Role[Role.ADMIN]},
            ${Role[Role.AUDITOR]},
            ${Role[Role.ACT_AS_READ_ONLY]},
            ${Role[Role.WORKSPACE_ADMINISTRATOR]},
            ${Role[Role.DEVOPS_ENGINEER]}
        ]`,
    })
    @CsvReportDownload(MonitorFailedCspmResponseDto)
    @Roles(Role.TECHGOV, Role.ADMIN, Role.AUDITOR, Role.ACT_AS_READ_ONLY, Role.DEVOPS_ENGINEER)
    @CheckAbilities((ability: MongoAbility) => ability.can(Action.Read, Subject[Subject.Monitors]))
    @ApiOkResponse({
        description: ApiResponse.CSV_FILE,
    })
    @Get(MonitorsRoute.GET_CSPM_REPORT)
    getCspmMonitorFailDetailsReport(
        @GetAccount() account: Account,
        @Param('testId', ParseIntPipe) testId: number,
    ): Promise<CsvDataSetType> {
        return this.monitorsService.getMonitorsForCsvExport(account, testId, ProviderType.CSPM);
    }

    @ApiOperation({
        description: `Download failed infrastructure monitor results.
        <br><br>
        Allowed Roles: [
            ${Role[Role.TECHGOV]},
            ${Role[Role.ADMIN]},
            ${Role[Role.AUDITOR]},
            ${Role[Role.ACT_AS_READ_ONLY]},
            ${Role[Role.WORKSPACE_ADMINISTRATOR]},
            ${Role[Role.DEVOPS_ENGINEER]}
        ]`,
    })
    @CsvReportDownload(MonitorFailedInfrastructureResponseDto)
    @Roles(Role.TECHGOV, Role.ADMIN, Role.AUDITOR, Role.ACT_AS_READ_ONLY, Role.DEVOPS_ENGINEER)
    @CheckAbilities((ability: MongoAbility) => ability.can(Action.Read, Subject[Subject.Monitors]))
    @ApiOkResponse({
        description: ApiResponse.CSV_FILE,
    })
    @Get(MonitorsRoute.GET_INFRASTRUCTURE_MONITOR_REPORT)
    getInfrastructureFailingDetailsReport(
        @GetAccount() account: Account,
        @Param('testId', ParseIntPipe) testId: number,
        @Query('type') type: 'included' | 'excluded',
    ): Promise<CsvDataSetType> {
        return this.monitorsService.getInfrastructureMonitorsForCsvExport(account, testId, type);
    }

    @ApiOperation({
        description: `Download monitor failed results.
        <br><br>
        Allowed Roles: [
            ${Role[Role.TECHGOV]},
            ${Role[Role.ADMIN]},
            ${Role[Role.AUDITOR]},
            ${Role[Role.ACT_AS_READ_ONLY]},
            ${Role[Role.WORKSPACE_ADMINISTRATOR]},
            ${Role[Role.DEVOPS_ENGINEER]}
        ]`,
    })
    @FindingsCsvReportDownload()
    @Roles(Role.TECHGOV, Role.ADMIN, Role.AUDITOR, Role.ACT_AS_READ_ONLY, Role.DEVOPS_ENGINEER)
    @CheckAbilities((ability: MongoAbility) => ability.can(Action.Read, Subject[Subject.Monitors]))
    @Get(MonitorsRoute.GET_MONITOR_FAILED_REPORT)
    @ApiOkResponse({ description: ApiResponse.OK })
    getMonitorFailedResultsReport(
        @GetAccount() account: Account,
        @Param('testId', ParseIntPipe) testId: number,
        @Query('type') type: 'included' | 'excluded',
        @Query('checkType') checkType: string,
    ): Promise<CsvDataSetType> {
        return this.monitorsCoreService.getInfrastructureMonitorsForCsvExport(
            account,
            testId,
            type,
            checkType,
        );
    }

    @ApiOperation({
        description: `Download remediation PDF for infrastructure test.
        <br><br>
        Allowed Roles: [
            ${Role[Role.TECHGOV]},
            ${Role[Role.ADMIN]},
            ${Role[Role.AUDITOR]},
            ${Role[Role.ACT_AS_READ_ONLY]},
            ${Role[Role.WORKSPACE_ADMINISTRATOR]},
            ${Role[Role.DEVOPS_ENGINEER]}
        ]`,
    })
    @Roles(Role.TECHGOV, Role.ADMIN, Role.AUDITOR, Role.ACT_AS_READ_ONLY, Role.DEVOPS_ENGINEER)
    @CheckAbilities((ability: MongoAbility) => ability.can(Action.Read, Subject[Subject.Monitors]))
    @FileBufferDownload('application/pdf')
    @Get(MonitorsRoute.GET_MONITOR_REMEDIATION_PDF)
    @ApiOkResponse({ description: 'PDF file download' })
    async getMonitorRemediationPdf(
        @GetAccount() account: Account,
        @Param('testId', ParseIntPipe) testId: number,
        @Query('workspaceId', ParseIntPipe) workspaceId: number,
    ): Promise<FileBufferType> {
        return this.monitorsCoreService.generateTestRemediationPdf(account, testId, workspaceId);
    }

    @ApiOperation({
        description: `Download ZIP package containing remediation PDF and CSV report for infrastructure test.
        <br><br>
        Allowed Roles: [
            ${Role[Role.TECHGOV]},
            ${Role[Role.ADMIN]},
            ${Role[Role.AUDITOR]},
            ${Role[Role.ACT_AS_READ_ONLY]},
            ${Role[Role.WORKSPACE_ADMINISTRATOR]},
            ${Role[Role.DEVOPS_ENGINEER]}
        ]`,
    })
    @Roles(Role.TECHGOV, Role.ADMIN, Role.AUDITOR, Role.ACT_AS_READ_ONLY, Role.DEVOPS_ENGINEER)
    @CheckAbilities((ability: MongoAbility) => ability.can(Action.Read, Subject[Subject.Monitors]))
    @FileBufferDownload('application/zip')
    @Get(MonitorsRoute.GET_MONITOR_FINDINGS_ZIP)
    @ApiOkResponse({ description: 'ZIP file download' })
    async getMonitorFindingsZip(
        @GetAccount() account: Account,
        @Param('testId', ParseIntPipe) testId: number,
        @Query('type') type: MonitorFindingsType,
        @Query('checkType') checkType: string,
    ): Promise<FileBufferType> {
        return this.monitorsCoreService.generateMonitorTestReportZip(
            account,
            testId,
            type,
            checkType,
        );
    }

    @ApiOperation({
        description: `Generate ZIP package containing remediation PDF and CSV report for infrastructure test.
        <br><br>
        Allowed Roles: [
            ${Role[Role.TECHGOV]},
            ${Role[Role.ADMIN]},
            ${Role[Role.AUDITOR]},
            ${Role[Role.ACT_AS_READ_ONLY]},
            ${Role[Role.WORKSPACE_ADMINISTRATOR]},
            ${Role[Role.DEVOPS_ENGINEER]}
        ]`,
    })
    @AsyncNotifierStream(
        AsyncNotificationType.DOWNLOAD,
        AsyncActionType.MONITOR_FINDINGS_ZIP_GENERATED,
        SignedUrlResponseDto,
        true,
    )
    @Roles(Role.TECHGOV, Role.ADMIN, Role.AUDITOR, Role.ACT_AS_READ_ONLY, Role.DEVOPS_ENGINEER)
    @CheckAbilities((ability: MongoAbility) => ability.can(Action.Read, Subject[Subject.Monitors]))
    @Get(MonitorsRoute.GET_MONITOR_FINDINGS_GENERATE)
    @Dto(AsyncNotifierResponseDto)
    @ApiOkResponse({ type: AsyncNotifierResponseDto })
    generateMonitorFindingsZip(
        @GetAccount() account: Account,
        @GetUser() user: User,
        @GetRequestNotifierProps() requestMetadata: ExtractedRequestProps,
        @Param('testId', ParseIntPipe) testId: number,
        @Query('type') type: 'included' | 'excluded',
        @Query('checkType') checkType: string,
        @Query('workspaceId', ParseIntPipe) workspaceId: number,
    ): AsyncNotifierStreamType<DownloaderPayloadType> {
        return toAsyncNotifierStream(
            this.monitorsCoreService.startFindingsZipGeneration(
                account,
                user,
                requestMetadata,
                testId,
                type,
                checkType,
                workspaceId,
            ),
        );
    }

    @ApiOperation({
        description: `Generate CSV report for monitor test.
        <br><br>
        Allowed Roles: [
            ${Role[Role.TECHGOV]},
            ${Role[Role.ADMIN]},
            ${Role[Role.AUDITOR]},
            ${Role[Role.ACT_AS_READ_ONLY]},
            ${Role[Role.WORKSPACE_ADMINISTRATOR]},
            ${Role[Role.DEVOPS_ENGINEER]}
        ]`,
    })
    @AsyncNotifierStream(
        AsyncNotificationType.DOWNLOAD,
        AsyncActionType.MONITOR_FINDINGS_CSV_GENERATED,
        SignedUrlResponseDto,
        true,
    )
    @Roles(Role.TECHGOV, Role.ADMIN, Role.AUDITOR, Role.ACT_AS_READ_ONLY, Role.DEVOPS_ENGINEER)
    @CheckAbilities((ability: MongoAbility) => ability.can(Action.Read, Subject[Subject.Monitors]))
    @Get(MonitorsRoute.GET_MONITOR_FINDINGS_GENERATE_CSV)
    @Dto(AsyncNotifierResponseDto)
    @ApiOkResponse({ type: AsyncNotifierResponseDto })
    generateMonitorFindingsCsv(
        @GetAccount() account: Account,
        @GetUser() user: User,
        @GetRequestNotifierProps() requestMetadata: ExtractedRequestProps,
        @Param('testId', ParseIntPipe) testId: number,
        @Query('type') type: 'included' | 'excluded',
        @Query('checkType') checkType: string,
        @Query('workspaceId', ParseIntPipe) workspaceId: number,
    ): AsyncNotifierStreamType<DownloaderPayloadType> {
        return toAsyncNotifierStream(
            this.monitorsCoreService.startMonitorTestReportCsvGeneration(
                account,
                user,
                requestMetadata,
                testId,
                type,
                checkType,
                workspaceId,
            ),
        );
    }

    @ApiOperation({
        description: `Update monitor test name and description.
        <br><br>
        Allowed Roles: [
            ${Role[Role.ADMIN]},
            ${Role[Role.TECHGOV]},
            ${Role[Role.WORKSPACE_ADMINISTRATOR]},
            ${Role[Role.CONTROL_MANAGER]},
            ${Role[Role.DEVOPS_ENGINEER]}
        ]`,
    })
    @ApiOkResponse({
        description: ApiResponse.OK,
        type: ControlTestInstanceDetailsResponseDto,
    })
    @HasProductId()
    @Roles(
        Role.TECHGOV,
        Role.ADMIN,
        Role.WORKSPACE_ADMINISTRATOR,
        Role.CONTROL_MANAGER,
        Role.DEVOPS_ENGINEER,
    )
    @CheckAbilities((ability: MongoAbility) => ability.can(Action.Read, Subject[Subject.Monitors]))
    @Dto(ControlTestInstanceDetailsResponseDto)
    @Put(MonitorsRoute.PUT_MONITOR_TEST_DETAILS)
    putMonitorDetails(
        @GetAccount() account: Account,
        @GetUser() user: User,
        @Param('testId', ParseIntPipe) testId: number,
        @Body() requestDto: MonitorsDetailsRequestDto,
    ): Promise<ControlTestInstanceDetails> {
        return this.monitorsService.putMonitorDetails(account, user, testId, requestDto);
    }

    @ApiOperation({
        description: `Revert monitor test name and description to default values.
        <br><br>
        Allowed Roles: [
            ${Role[Role.ADMIN]},
            ${Role[Role.TECHGOV]},
            ${Role[Role.WORKSPACE_ADMINISTRATOR]},
            ${Role[Role.CONTROL_MANAGER]},
            ${Role[Role.DEVOPS_ENGINEER]}
        ]`,
    })
    @HasProductId()
    @Roles(
        Role.TECHGOV,
        Role.ADMIN,
        Role.WORKSPACE_ADMINISTRATOR,
        Role.CONTROL_MANAGER,
        Role.DEVOPS_ENGINEER,
    )
    @CheckAbilities((ability: MongoAbility) => ability.can(Action.Read, Subject[Subject.Monitors]))
    @ApiOkResponse({
        description: ApiResponse.OK,
        type: ControlTestInstanceDetailsResponseDto,
    })
    @Dto(ControlTestInstanceDetailsResponseDto)
    @Put(MonitorsRoute.PUT_REVERT_MONITOR_DETAILS)
    putRevertMonitorDetails(
        @GetAccount() account: Account,
        @Param('testId', ParseIntPipe) testId: number,
    ): Promise<ControlTestInstanceDetails> {
        return this.monitorsService.putRevertMonitorDetails(account, testId);
    }

    @ApiOperation({
        description: `Create a draft test from given test id
        <br><br>
        Allowed Roles: [
            ${Role[Role.ADMIN]},
            ${Role[Role.TECHGOV]},
            ${Role[Role.WORKSPACE_ADMINISTRATOR]},
            ${Role[Role.CONTROL_MANAGER]},
            ${Role[Role.DEVOPS_ENGINEER]}
        ]`,
    })
    @ApiCreatedResponse({
        description: ApiResponse.CREATED,
        type: ControlTestInstanceResponseDto,
    })
    @ApiBadRequestResponse({
        description: ApiResponse.BAD_REQUEST,
        type: ExceptionResponseDto,
    })
    @ApiConflictResponse({
        description: ApiResponse.CONFLICT,
        type: ExceptionResponseDto,
    })
    @ApiNotFoundResponse({
        description: ApiResponse.NOT_FOUND,
        type: ExceptionResponseDto,
    })
    @HasProductId()
    @Roles(
        Role.ADMIN,
        Role.TECHGOV,
        Role.WORKSPACE_ADMINISTRATOR,
        Role.CONTROL_MANAGER,
        Role.DEVOPS_ENGINEER,
    )
    @CheckAbilities((ability: MongoAbility) =>
        ability.can(Action.Manage, Subject[Subject.Monitors]),
    )
    @RequireAccountEntitlement(AccountEntitlementType.CUSTOM_CONNECTIONS_AND_TESTS)
    @Dto(ControlTestInstanceResponseDto)
    @Post(MonitorsRoute.POST_CREATE_DRAFT_TEST)
    createDraftTest(
        @GetAccount() account: Account,
        @GetUser() user: User,
        @Param('testId', ParseIntPipe) testId: number,
    ): Promise<ControlTestInstance> {
        return this.monitorTestService.createDraftTest(account, user, testId);
    }

    @ApiOperation({
        description: `Create a custom test
        <br><br>
        Allowed Roles: [
            ${Role[Role.ADMIN]},
            ${Role[Role.TECHGOV]},
            ${Role[Role.WORKSPACE_ADMINISTRATOR]},
            ${Role[Role.CONTROL_MANAGER]},
            ${Role[Role.DEVOPS_ENGINEER]},
        ]`,
    })
    @ApiCreatedResponse({
        description: ApiResponse.CREATED,
        type: ControlTestInstanceResponseDto,
    })
    @ApiBadRequestResponse({
        description: ApiResponse.BAD_REQUEST,
        type: ExceptionResponseDto,
    })
    @ApiConflictResponse({
        description: ApiResponse.CONFLICT,
        type: ExceptionResponseDto,
    })
    @ApiNotFoundResponse({
        description: ApiResponse.NOT_FOUND,
        type: ExceptionResponseDto,
    })
    @HasProductId()
    @Roles(
        Role.ADMIN,
        Role.TECHGOV,
        Role.WORKSPACE_ADMINISTRATOR,
        Role.CONTROL_MANAGER,
        Role.DEVOPS_ENGINEER,
    )
    @CheckAbilities((ability: MongoAbility) =>
        ability.can(Action.Manage, Subject[Subject.Monitors]),
    )
    @RequireAccountEntitlement(AccountEntitlementType.CUSTOM_CONNECTIONS_AND_TESTS)
    @Dto(ControlTestInstanceResponseDto)
    @Post(MonitorsRoute.POST_CREATE_CUSTOM_TEST)
    createCustomTest(
        @GetAccount() account: Account,
        @GetUser() user: User,
        @Body() requestDto: CreateCustomTestRequestDto,
    ): Promise<ControlTestInstance> {
        return this.monitorTestService.createCustomTest(
            account,
            user,
            requestDto as CustomTestRequestDto,
        );
    }

    @ApiOperation({
        description: `Update draft test
        <br><br>
        Allowed Roles: [
            ${Role[Role.ADMIN]},
            ${Role[Role.TECHGOV]},
            ${Role[Role.WORKSPACE_ADMINISTRATOR]},
            ${Role[Role.CONTROL_MANAGER]},
            ${Role[Role.DEVOPS_ENGINEER]},
        ]`,
    })
    @ApiOkResponse({
        description: ApiResponse.OK,
        type: ControlTestInstanceResponseDto,
    })
    @ApiBadRequestResponse({
        description: ApiResponse.BAD_REQUEST,
        type: ExceptionResponseDto,
    })
    @ApiConflictResponse({
        description: ApiResponse.FORBIDDEN,
        type: ExceptionResponseDto,
    })
    @ApiNotFoundResponse({
        description: ApiResponse.NOT_FOUND,
        type: ExceptionResponseDto,
    })
    @HasProductId()
    @Roles(
        Role.ADMIN,
        Role.TECHGOV,
        Role.WORKSPACE_ADMINISTRATOR,
        Role.CONTROL_MANAGER,
        Role.DEVOPS_ENGINEER,
    )
    @CheckAbilities((ability: MongoAbility) =>
        ability.can(Action.Manage, Subject[Subject.Monitors]),
    )
    @RequireAccountEntitlement(AccountEntitlementType.CUSTOM_CONNECTIONS_AND_TESTS)
    @Dto(ControlTestInstanceResponseDto)
    @Put(MonitorsRoute.PUT_DRAFT_TEST)
    updateDraftTest(
        @GetAccount() account: Account,
        @GetUser() user: User,
        @Param('testId', ParseIntPipe) testId: number,
        @Body() requestDto: CustomTestRecipeRequestDto,
    ): Promise<ControlTestInstance> {
        return this.monitorTestService.updateDraftTest(account, user, testId, requestDto);
    }

    @ApiOperation({
        description: `Delete a draft test
        <br><br>
        Allowed Roles: [
            ${Role[Role.ADMIN]},
            ${Role[Role.TECHGOV]},
            ${Role[Role.WORKSPACE_ADMINISTRATOR]},
            ${Role[Role.CONTROL_MANAGER]},
            ${Role[Role.DEVOPS_ENGINEER]},
        ]`,
    })
    @ApiBadRequestResponse({
        description: ApiResponse.BAD_REQUEST,
        type: ExceptionResponseDto,
    })
    @ApiConflictResponse({
        description: ApiResponse.CONFLICT,
        type: ExceptionResponseDto,
    })
    @ApiNotFoundResponse({
        description: ApiResponse.NOT_FOUND,
        type: ExceptionResponseDto,
    })
    @ApiOkResponse({
        description: ApiResponse.OK,
    })
    @HasProductId()
    @Roles(
        Role.ADMIN,
        Role.TECHGOV,
        Role.WORKSPACE_ADMINISTRATOR,
        Role.CONTROL_MANAGER,
        Role.DEVOPS_ENGINEER,
    )
    @CheckAbilities((ability: MongoAbility) =>
        ability.can(Action.Manage, Subject[Subject.Monitors]),
    )
    @RequireAccountEntitlement(AccountEntitlementType.CUSTOM_CONNECTIONS_AND_TESTS)
    @Dto(ControlTestInstanceResponseDto)
    @StatusOnEmpty(HttpStatus.OK)
    @Delete(MonitorsRoute.DELETE_CUSTOM_TEST)
    deleteDraftTest(
        @GetAccount() account: Account,
        @GetUser() user: User,
        @Param('testId', ParseIntPipe) testId: number,
    ): Promise<void> {
        return this.monitorTestService.deleteCustomTest(account, user, testId);
    }

    @ApiOperation({
        description: `Publish a draft test
        <br><br>
        Allowed Roles: [
            ${Role[Role.ADMIN]},
            ${Role[Role.TECHGOV]},
            ${Role[Role.WORKSPACE_ADMINISTRATOR]},
            ${Role[Role.CONTROL_MANAGER]},
            ${Role[Role.DEVOPS_ENGINEER]},
        ]
        `,
    })
    @ApiBadRequestResponse({
        description: ApiResponse.BAD_REQUEST,
        type: ExceptionResponseDto,
    })
    @ApiConflictResponse({
        description: ApiResponse.CONFLICT,
        type: ExceptionResponseDto,
    })
    @ApiNotFoundResponse({
        description: ApiResponse.NOT_FOUND,
        type: ExceptionResponseDto,
    })
    @ApiOkResponse({
        description: ApiResponse.OK,
        type: ControlTestInstanceDetailsResponseDto,
    })
    @HasProductId()
    @Roles(
        Role.ADMIN,
        Role.TECHGOV,
        Role.WORKSPACE_ADMINISTRATOR,
        Role.CONTROL_MANAGER,
        Role.DEVOPS_ENGINEER,
    )
    @CheckAbilities((ability: MongoAbility) =>
        ability.can(Action.Manage, Subject[Subject.Monitors]),
    )
    @RequireAccountEntitlement(AccountEntitlementType.CUSTOM_CONNECTIONS_AND_TESTS)
    @Dto(ControlTestInstanceDetailsResponseDto)
    @Put(MonitorsRoute.PUT_PUBLISH_DRAFT_TEST)
    publishDraftTest(
        @GetAccount() account: Account,
        @GetUser() user: User,
        @Param('testId', ParseIntPipe) testId: number,
    ): Promise<ControlTestInstanceDetails> {
        return this.monitorTestService.publishDraftTest(account, user, testId);
    }

    @ApiOperation({
        description: `Get draft from a monitor test
        <br><br>
        Allowed Roles: [
            ${Role[Role.ADMIN]},
            ${Role[Role.TECHGOV]},
            ${Role[Role.WORKSPACE_ADMINISTRATOR]},
            ${Role[Role.CONTROL_MANAGER]},
            ${Role[Role.DEVOPS_ENGINEER]},
        ]`,
    })
    @ApiOkResponse({
        description: ApiResponse.OK,
        type: ControlTestInstanceResponseDto,
    })
    @ApiBadRequestResponse({
        description: ApiResponse.BAD_REQUEST,
        type: ExceptionResponseDto,
    })
    @ApiConflictResponse({
        description: ApiResponse.FORBIDDEN,
        type: ExceptionResponseDto,
    })
    @ApiNotFoundResponse({
        description: ApiResponse.NOT_FOUND,
        type: ExceptionResponseDto,
    })
    @HasProductId()
    @Roles(
        Role.ADMIN,
        Role.TECHGOV,
        Role.WORKSPACE_ADMINISTRATOR,
        Role.CONTROL_MANAGER,
        Role.DEVOPS_ENGINEER,
    )
    @CheckAbilities((ability: MongoAbility) =>
        ability.can(Action.Manage, Subject[Subject.Monitors]),
    )
    @RequireAccountEntitlement(AccountEntitlementType.CUSTOM_CONNECTIONS_AND_TESTS)
    @Get(MonitorsRoute.GET_MONITOR_DRAFT_TEST)
    getMonitorDraftTest(
        @GetAccount() account: Account,
        @Param('testId', ParseIntPipe) testId: number,
    ): Promise<ControlTestInstance> {
        return this.monitorTestService.getMonitorDraftTest(account, testId);
    }

    @ApiOperation({
        description: `Get monitor metadata
        <br><br>
        Allowed Roles: [
            ${Role[Role.ADMIN]},
            ${Role[Role.TECHGOV]},
            ${Role[Role.WORKSPACE_ADMINISTRATOR]},
            ${Role[Role.CONTROL_MANAGER]},
            ${Role[Role.ACT_AS_READ_ONLY]},
            ${Role[Role.DEVOPS_ENGINEER]},
        ]`,
    })
    @ApiOkResponse({
        description: ApiResponse.OK,
        type: CustomTestMetadataResponseDto,
    })
    @ApiBadRequestResponse({
        description: ApiResponse.BAD_REQUEST,
        type: ExceptionResponseDto,
    })
    @ApiForbiddenResponse({
        description: ApiResponse.FORBIDDEN,
        type: ExceptionResponseDto,
    })
    @ApiNotFoundResponse({
        description: ApiResponse.NOT_FOUND,
        type: ExceptionResponseDto,
    })
    @HasProductId()
    @Roles(
        Role.ADMIN,
        Role.TECHGOV,
        Role.WORKSPACE_ADMINISTRATOR,
        Role.CONTROL_MANAGER,
        Role.ACT_AS_READ_ONLY,
        Role.DEVOPS_ENGINEER,
    )
    @CheckAbilities((ability: MongoAbility) => ability.can(Action.Read, Subject[Subject.Monitors]))
    @Dto(CustomTestMetadataResponseDto)
    @Get(MonitorsRoute.GET_MONITORS_METADATA)
    getMonitorTestMetadata(@GetAccount() account: Account): Promise<CustomTestsMetadata> {
        return this.monitorTestService.getMonitorTestMetadata(account);
    }

    @ApiOperation({
        description: `Generate Control Test Instance Summary CSV <br><br>
        Allowed Roles: [
            ${Role[Role.ADMIN]},
            ${Role[Role.TECHGOV]},
            ${Role[Role.WORKSPACE_ADMINISTRATOR]},
            ${Role[Role.CONTROL_MANAGER]},
            ${Role[Role.DEVOPS_ENGINEER]},
        ]`,
    })
    @CsvReportDownload(ControlTestInstanceEventSummaryCsvDto)
    @Roles(
        Role.ADMIN,
        Role.TECHGOV,
        Role.WORKSPACE_ADMINISTRATOR,
        Role.CONTROL_MANAGER,
        Role.DEVOPS_ENGINEER,
    )
    @CheckAbilities((ability: MongoAbility) =>
        ability.can(Action.Manage, Subject[Subject.Monitors]),
    )
    @ApiCreatedResponse({
        description: ApiResponse.CSV_FILE,
    })
    @Post(MonitorsRoute.POST_TEST_SUMMARY_CSV)
    generateSummariesCsv(
        @GetAccount() account: Account,
        @Param('testId', ParseIntPipe) testId: number,
    ): Promise<any> {
        return this.monitorsService.generateTestSummaryCsv(account, testId);
    }

    @ApiOperation({
        description: `List test results based on the events by test id.
            <br><br>
            Allowed Roles: [
                ${Role[Role.TECHGOV]},
                ${Role[Role.ADMIN]},
                ${Role[Role.AUDITOR]},
                ${Role[Role.ACT_AS_READ_ONLY]},
                ${Role[Role.WORKSPACE_ADMINISTRATOR]},
                ${Role[Role.CONTROL_MANAGER]}
            ]`,
    })
    @ApiOkResponse({
        type: MonitorListTestResultsResponseDto,
    })
    @ApiBadRequestResponse({
        description: ApiResponse.BAD_REQUEST,
        type: ExceptionResponseDto,
    })
    @Roles(
        Role.TECHGOV,
        Role.ADMIN,
        Role.AUDITOR,
        Role.ACT_AS_READ_ONLY,
        Role.WORKSPACE_ADMINISTRATOR,
        Role.CONTROL_MANAGER,
    )
    @CheckAbilities((ability: MongoAbility) => ability.can(Action.Read, Subject[Subject.Report]))
    @Dto(MonitorListTestResultsResponseDto)
    @Get(MonitorsRoute.GET_MONITORS_TEST_RESULTS)
    listTestResults(
        @GetAccount() account: Account,
        @GetUser() user: User,
        @Param('controlTestInstanceId', ParseIntPipe) controlTestInstanceId: number,
        @Query()
        evidenceLibraryTestResultsDto: MonitorTestResultsPaginatedRequestDto,
    ): Promise<PaginationType<IListTestResult>> {
        return this.monitorsService.listTestResultsFromEvents(
            account,
            user,
            controlTestInstanceId,
            evidenceLibraryTestResultsDto,
        );
    }
}
