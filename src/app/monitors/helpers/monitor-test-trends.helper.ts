import { AccountIdType } from 'commons/types/account-id.type';
import { isNil } from 'lodash';

export function getTestTrendsOverviewCacheKey(
    account: { id: AccountIdType },
    workspaceId: number,
    identifier: string,
) {
    return `monitor-test-trends:overview:${account.id}:${workspaceId}:${identifier}`;
}

/**
 * Calculates the delta (arithmetic difference) between the current and previous period MTTR.
 *
 * @param currentMttr
 * @param previousPeriodMttr
 * @returns
 */
export function calculateMttrDelta(currentMttr: number | null, previousPeriodMttr: number | null) {
    let delta = 0;

    if (previousPeriodMttr === 0) {
        return null;
    }

    if (!isNil(previousPeriodMttr) && !isNil(currentMttr)) {
        delta = ((currentMttr - previousPeriodMttr) / previousPeriodMttr) * 100;
    }

    return delta;
}
