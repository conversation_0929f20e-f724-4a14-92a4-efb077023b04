import {
    calculateMttrD<PERSON><PERSON>,
    getTestTrendsOverviewCacheKey,
} from 'app/monitors/helpers/monitor-test-trends.helper';
import { AccountIdType } from 'commons/types/account-id.type';

describe('monitor-test-trends.helper', () => {
    describe('getTestTrendsOverviewCacheKey', () => {
        it('should generate correct cache key with all parameters', () => {
            // Arrange
            const account = { id: 12345 as unknown as AccountIdType };
            const workspaceId = 67890;
            const identifier = 'test-identifier';

            // Act
            const result = getTestTrendsOverviewCacheKey(account, workspaceId, identifier);

            // Assert
            expect(result).toBe('monitor-test-trends:overview:12345:67890:test-identifier');
        });

        it('should generate cache key with string account id', () => {
            // Arrange
            const account = { id: 'account-123' as AccountIdType };
            const workspaceId = 456;
            const identifier = 'my-test';

            // Act
            const result = getTestTrendsOverviewCacheKey(account, workspaceId, identifier);

            // Assert
            expect(result).toBe('monitor-test-trends:overview:account-123:456:my-test');
        });

        it('should handle zero workspace id', () => {
            // Arrange
            const account = { id: 999 as unknown as AccountIdType };
            const workspaceId = 0;
            const identifier = 'zero-workspace';

            // Act
            const result = getTestTrendsOverviewCacheKey(account, workspaceId, identifier);

            // Assert
            expect(result).toBe('monitor-test-trends:overview:999:0:zero-workspace');
        });

        it('should handle empty identifier', () => {
            // Arrange
            const account = { id: 111 as unknown as AccountIdType };
            const workspaceId = 222;
            const identifier = '';

            // Act
            const result = getTestTrendsOverviewCacheKey(account, workspaceId, identifier);

            // Assert
            expect(result).toBe('monitor-test-trends:overview:111:222:');
        });

        it('should handle special characters in identifier', () => {
            // Arrange
            const account = { id: 555 as unknown as AccountIdType };
            const workspaceId = 777;
            const identifier = 'test-with-special@chars#123';

            // Act
            const result = getTestTrendsOverviewCacheKey(account, workspaceId, identifier);

            // Assert
            expect(result).toBe('monitor-test-trends:overview:555:777:test-with-special@chars#123');
        });
    });

    describe('calculateMttrDelta', () => {
        it('should calculate positive delta when current MTTR is higher', () => {
            // Arrange
            const currentMttr = 120;
            const previousPeriodMttr = 100;

            // Act
            const result = calculateMttrDelta(currentMttr, previousPeriodMttr);

            // Assert
            expect(result).toBe(20); // ((120 - 100) / 100) * 100 = 20%
        });

        it('should calculate negative delta when current MTTR is lower', () => {
            // Arrange
            const currentMttr = 80;
            const previousPeriodMttr = 100;

            // Act
            const result = calculateMttrDelta(currentMttr, previousPeriodMttr);

            // Assert
            expect(result).toBe(-20); // ((80 - 100) / 100) * 100 = -20%
        });

        it('should return zero delta when MTTRs are equal', () => {
            // Arrange
            const currentMttr = 100;
            const previousPeriodMttr = 100;

            // Act
            const result = calculateMttrDelta(currentMttr, previousPeriodMttr);

            // Assert
            expect(result).toBe(0); // ((100 - 100) / 100) * 100 = 0%
        });

        it('should return zero when current MTTR is null', () => {
            // Arrange
            const currentMttr = null;
            const previousPeriodMttr = 100;

            // Act
            const result = calculateMttrDelta(currentMttr, previousPeriodMttr);

            // Assert
            expect(result).toBe(0);
        });

        it('should return zero when previous period MTTR is null', () => {
            // Arrange
            const currentMttr = 120;
            const previousPeriodMttr = null;

            // Act
            const result = calculateMttrDelta(currentMttr, previousPeriodMttr);

            // Assert
            expect(result).toBe(0);
        });

        it('should return zero when both MTTRs are null', () => {
            // Arrange
            const currentMttr = null;
            const previousPeriodMttr = null;

            // Act
            const result = calculateMttrDelta(currentMttr, previousPeriodMttr);

            // Assert
            expect(result).toBe(0);
        });

        it('should return null when previous period MTTR is zero', () => {
            // Arrange
            const currentMttr = 50;
            const previousPeriodMttr = 0;

            // Act
            const result = calculateMttrDelta(currentMttr, previousPeriodMttr);

            // Assert
            expect(result).toBeNull(); // Special case: returns null instead of division by zero
        });

        it('should return null when previous period MTTR is zero and current is null', () => {
            // Arrange
            const currentMttr = null;
            const previousPeriodMttr = 0;

            // Act
            const result = calculateMttrDelta(currentMttr, previousPeriodMttr);

            // Assert
            expect(result).toBeNull(); // Zero check takes precedence over null check
        });

        it('should return null when previous period MTTR is zero and current is zero', () => {
            // Arrange
            const currentMttr = 0;
            const previousPeriodMttr = 0;

            // Act
            const result = calculateMttrDelta(currentMttr, previousPeriodMttr);

            // Assert
            expect(result).toBeNull(); // Zero check takes precedence
        });

        it('should handle zero current MTTR with non-zero previous', () => {
            // Arrange
            const currentMttr = 0;
            const previousPeriodMttr = 100;

            // Act
            const result = calculateMttrDelta(currentMttr, previousPeriodMttr);

            // Assert
            expect(result).toBe(-100); // ((0 - 100) / 100) * 100 = -100%
        });

        it('should handle decimal values correctly', () => {
            // Arrange
            const currentMttr = 75.5;
            const previousPeriodMttr = 50.2;

            // Act
            const result = calculateMttrDelta(currentMttr, previousPeriodMttr);

            // Assert
            expect(result).toBeCloseTo(50.398, 3); // ((75.5 - 50.2) / 50.2) * 100 ≈ 50.398%
        });

        it('should handle large numbers correctly', () => {
            // Arrange
            const currentMttr = 1000000;
            const previousPeriodMttr = 500000;

            // Act
            const result = calculateMttrDelta(currentMttr, previousPeriodMttr);

            // Assert
            expect(result).toBe(100); // ((1000000 - 500000) / 500000) * 100 = 100%
        });

        it('should handle very small numbers correctly', () => {
            // Arrange
            const currentMttr = 0.002;
            const previousPeriodMttr = 0.001;

            // Act
            const result = calculateMttrDelta(currentMttr, previousPeriodMttr);

            // Assert
            expect(result).toBe(100); // ((0.002 - 0.001) / 0.001) * 100 = 100%
        });

        it('should handle negative MTTR values', () => {
            // Arrange
            const currentMttr = -50;
            const previousPeriodMttr = -100;

            // Act
            const result = calculateMttrDelta(currentMttr, previousPeriodMttr);

            // Assert
            expect(result).toBe(-50); // ((-50 - (-100)) / (-100)) * 100 = -50%
        });
    });
});
