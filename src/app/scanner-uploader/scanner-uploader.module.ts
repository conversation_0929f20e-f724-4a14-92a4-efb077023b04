import { SQSClient } from '@aws-sdk/client-sqs';
import { Module } from '@nestjs/common';
import { SqsModule } from '@ssut/nestjs-sqs';
import { SqsConsumerOptions, SqsProducerOptions } from '@ssut/nestjs-sqs/dist/sqs.types';
import { AwsCredentialsFactory } from 'app/apis/factories/aws-credentials.factory';
import { AppModule } from 'app/app.module';
import { ScannerUploaderController } from 'app/scanner-uploader/controllers/scanner-uploader.controller';
import { DocumentScanningUploadRejectedHandler } from 'app/scanner-uploader/observables/handlers/document-scanning-upload-rejected.handler';
import { ScannerUploaderConsumer } from 'app/scanner-uploader/scanner-uploader.consumer';
import { ScanUploaderDependencyService } from 'app/scanner-uploader/services/scan-uploader-dependency.service';
import { ScannerUploaderService } from 'app/scanner-uploader/services/scanner-uploader.service';
import { AccountRepository } from 'auth/repositories/account.repository';
import config from 'config';
import { TypeOrmExtensionsModule } from 'database/typeorm/typeorm.extensions.module';
import { ModuleType, ModuleTypes } from 'commons/decorators/module-type.decorator';

@ModuleType(ModuleTypes.COMMERCE)
@Module({
    imports: [
        TypeOrmExtensionsModule.forGlobalCustomRepository([AccountRepository]),
        SqsModule.registerAsync({
            useFactory: () => {
                const consumers: SqsConsumerOptions[] = [];
                const producers: SqsProducerOptions[] = [];

                if (config.get<boolean>('scannerUploader.sqs.enabled')) {
                    consumers.push({
                        name: config.get('scannerUploader.sqs.name'),
                        queueUrl: config.get('scannerUploader.sqs.url'),
                        region: config.get('scannerUploader.sqs.region'),
                        sqs: new SQSClient({
                            region: config.get('scannerUploader.sqs.region'),
                            apiVersion: config.get('scannerUploader.sqs.apiVersion'),
                            endpoint: config.get('scannerUploader.sqs.url'),
                            credentials: AwsCredentialsFactory.buildCredentials(),
                        }),
                    });
                }
                /**
                 * Remove this consumer after switchover from cloud storage security to av-bridge
                 */
                if (config.get<boolean>('scannerUploader.sqs.queue2Enabled')) {
                    consumers.push({
                        name: config.get('scannerUploader.sqs.nameDeprecated'),
                        queueUrl: config.get('scannerUploader.sqs.urlDeprecated'),
                        region: config.get('scannerUploader.sqs.region'),
                        sqs: new SQSClient({
                            region: config.get('scannerUploader.sqs.region'),
                            apiVersion: config.get('scannerUploader.sqs.apiVersion'),
                            endpoint: config.get('scannerUploader.sqs.urlDeprecated'),
                            credentials: AwsCredentialsFactory.buildCredentials(),
                        }),
                    });
                }
                return {
                    consumers,
                    producers,
                };
            },
        }),
    ],
    providers: [
        ScannerUploaderConsumer,
        ScannerUploaderService,
        ScanUploaderDependencyService,
        DocumentScanningUploadRejectedHandler,
    ],
    exports: [ScannerUploaderService, ScanUploaderDependencyService],
    controllers: [ScannerUploaderController],
})
export class ScannerUploaderModule extends AppModule {}
