import { AgentWorkflowGeneralStepName } from 'app/agent-workflow/enums/agent-workflow-general-step-name.enum';
import { AgentWorkflowStepMessageRef } from 'app/agent-workflow/enums/agent-workflow-step-message-ref.enum';
import { AgentWorkflowStepName } from 'app/agent-workflow/enums/agent-workflow-step-name.enum';
import { AgentWorkflowVendorAssessmentStepName } from 'app/agent-workflow/enums/agent-workflow-vendor-assessment-step-name.enum';
import { AgentWorkflowVendorCriteriaStepName } from 'app/agent-workflow/enums/agent-workflow-vendor-criteria-step-name.enum';
import { AuditTrailStepConfig } from 'app/agent-workflow/types/audit-trail-step-config.type';

/**
 * Mapping of vendor assessment workflow step names to their audit trail configuration.
 * This defines which steps should be included in the audit trail and their display names
 * for Vendor Risk Management workflows.
 *
 * Only steps defined in this mapping will be included in the audit trail response.
 * The display names here override any display names stored in the database.
 */
export const VENDOR_ASSESSMENT_AUDIT_TRAIL_STEP_MAPPING = new Map<
    AgentWorkflowStepName,
    AuditTrailStepConfig
>([
    [
        AgentWorkflowGeneralStepName.PROCESS_START,
        {
            displayName: 'Started security review',
            ref: AgentWorkflowStepMessageRef.SECURITY_REVIEW_ASSESSMENT,
        },
    ],
    [
        AgentWorkflowVendorAssessmentStepName.SAFEBASE_ACCESS_GRANTED,
        {
            displayName: 'Access Granted',
        },
    ],
    [
        AgentWorkflowVendorAssessmentStepName.SAFEBASE_SEND_DOCUMENTS_TO_AI,
        {
            displayName: 'Documents collected',
            ref: AgentWorkflowStepMessageRef.SECURITY_REVIEW_DOCUMENTS,
        },
    ],
    [
        AgentWorkflowVendorAssessmentStepName.RETRIEVE_SECURITY_POSTURE,
        {
            displayName: 'Criteria assessment results',
            ref: AgentWorkflowStepMessageRef.SECURITY_REVIEW_ASSESSMENT,
        },
    ],
    [
        AgentWorkflowVendorAssessmentStepName.SECURITY_QUESTIONNAIRE_SENT,
        {
            displayName: 'Follow up questionnaire',
            ref: AgentWorkflowStepMessageRef.SECURITY_REVIEW_QUESTIONNAIRE,
        },
    ],
]);

/**
 * Mapping of vendor criteria workflow step names to their audit trail configuration.
 * This defines which steps should be included in the audit trail and their display names
 * for Vendor Criteria Creation workflows.
 *
 * Only steps defined in this mapping will be included in the audit trail response.
 * The display names here override any display names stored in the database.
 */
export const VENDOR_CRITERIA_AUDIT_TRAIL_STEP_MAPPING = new Map<
    AgentWorkflowStepName,
    AuditTrailStepConfig
>([
    [
        AgentWorkflowGeneralStepName.PROCESS_START,
        {
            displayName: 'Started criteria creation',
        },
    ],
    [
        AgentWorkflowVendorCriteriaStepName.SEND_CRITERIA_QUESTIONNAIRE_TO_AI,
        {
            displayName: 'Questionnaire sent to AI',
        },
    ],
    [
        AgentWorkflowVendorCriteriaStepName.ANALYZING_CRITERIA_QUESTIONNAIRE,
        {
            displayName: 'Analyzing questionnaire',
        },
    ],
    [
        AgentWorkflowVendorCriteriaStepName.GENERATING_CRITERIA,
        {
            displayName: 'Generating criteria',
        },
    ],
    [
        AgentWorkflowVendorCriteriaStepName.CRITERIA_GENERATION_FINALIZED,
        {
            displayName: 'Criteria generation completed',
        },
    ],
    [
        AgentWorkflowVendorCriteriaStepName.SAVE_GENERATED_CRITERIA,
        {
            displayName: 'Criteria saved',
        },
    ],
    [
        AgentWorkflowVendorCriteriaStepName.RESTORE_DRATA_CRITERIA,
        {
            displayName: 'Restored default criteria',
        },
    ],
    [
        AgentWorkflowGeneralStepName.FINALIZE,
        {
            displayName: 'Workflow completed',
        },
    ],
]);
