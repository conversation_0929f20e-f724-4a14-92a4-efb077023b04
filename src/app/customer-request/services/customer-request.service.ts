import { <PERSON>rror<PERSON><PERSON>, SentByReservedUUIDs } from '@drata/enums';
import {
    BadRequestException,
    Injectable,
    NotFoundException,
    UnauthorizedException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { AuditHubOrchestrationService } from 'app/audit-hub/orchestration/audit-hub-orchestration.service';
import { Product } from 'app/companies/products/entities/product.entity';
import { WorkspacesCoreService } from 'app/companies/products/services/workspaces-core.service';
import { ControlService } from 'app/control/control.service';
import { CreateEventService } from 'app/create-event/create-event.service';
import { CustomerRequestMessageBaseAdapter } from 'app/customer-request/adapters/customer-request-message-base.adapter';
import { DrataUpdateCustomerRequestMessageAdapter } from 'app/customer-request/adapters/drata-update-customer-request-message.adapter';
import { UserUpdateCustomerRequestMessageAdapter } from 'app/customer-request/adapters/user-update-customer-request-message.adapter';
import { CustomerRequestConstants } from 'app/customer-request/classes/customer-request-constants.class';
import { CustomerRequestFileRecord } from 'app/customer-request/classes/customer-request-file-record.class';
import { CustomerRequestFileValidation } from 'app/customer-request/classes/customer-request-file-validation.class';
import { auditHubEmailTypeToFeatureType } from 'app/customer-request/constants/audit-hub-email-type-to-feature-type-map.constant';
import { MAX_AUDIT_REQUEST_OWNERS } from 'app/customer-request/constants/customer-request-max-audit-request-owners';
import { CreateCustomerRequestsFromRequirementsRequestDto } from 'app/customer-request/dtos/create-customer-requests-from-requirements-request.dto';
import { CreateCustomerRequestsRequestDto } from 'app/customer-request/dtos/create-customer-requests-request.dto';
import { CustomerRequestBulkActionRequestDto } from 'app/customer-request/dtos/customer-request-bulk-action-request.dto';
import { CustomerRequestFileValidationRequestDto } from 'app/customer-request/dtos/customer-request-file-validation-request.dto';
import { CustomerRequestListRequestDto } from 'app/customer-request/dtos/customer-request-list-request.dto';
import { DeleteCustomerRequestsRequestDto } from 'app/customer-request/dtos/delete-customer-requests-request.dto';
import { UpdateCustomerRequestDetailsRequestDto } from 'app/customer-request/dtos/update-customer-request-details-request.dto';
import { UpdateCustomerRequestMessageRequestDto } from 'app/customer-request/dtos/update-customer-request-message-request.dto';
import { UpdateCustomerRequestStatusesRequestDto } from 'app/customer-request/dtos/update-customer-request-statuses-request.dto';
import { AuditorFrameworkListView } from 'app/customer-request/entities/auditor-framework-list-view.entity';
import { CustomerRequestMessageFile } from 'app/customer-request/entities/customer-request-message-file.entity';
import { CustomerRequestMessageReferenceDocument } from 'app/customer-request/entities/customer-request-message-reference-document.entity';
import { CustomerRequestMessage } from 'app/customer-request/entities/customer-request-message.entity';
import { CustomerRequest } from 'app/customer-request/entities/customer-request.entity';
import { AuditHubEmailType } from 'app/customer-request/enums/audit-hub-email-type.enum';
import { CustomerRequestDetailsEvent } from 'app/customer-request/enums/customer-request-details-event.enum';
import { CustomerRequestStatus } from 'app/customer-request/enums/customer-request-status.enum';
import { RequestMessageSenderType } from 'app/customer-request/enums/request-message-sender-type.enum';
import { CustomerRequestUpdatedEvent } from 'app/customer-request/events/customer-request-updated.event';
import { CustomerRequestAuditorFacet } from 'app/customer-request/facets/customer-request-auditor-facet.class';
import { CustomerRequestIsOwnedFacet } from 'app/customer-request/facets/customer-request-is-owned-facet.class';
import { CustomerRequestListViewSearchStringFacet } from 'app/customer-request/facets/customer-request-list-view-search-string-facet.class';
import { CustomerRequestListViewUnreadMessagesFacet } from 'app/customer-request/facets/customer-request-list-view-unread-messages-facet.class';
import { CustomerRequestOwnerFacet } from 'app/customer-request/facets/customer-request-owner-facet.class';
import { CustomerRequestStatusFacet } from 'app/customer-request/facets/customer-request-status-facet.class';
import {
    isRequestFieldAvailable,
    mapRequirementsToCustomerRequests,
} from 'app/customer-request/helpers/customer-request-service.helper';
import { CreateCustomerRequestMessageParams } from 'app/customer-request/interface/create-customer-request-message.type';
import { AuditHubMessageDeleted } from 'app/customer-request/observables/events/audit-hub-message-deleted.event';
import { AuditHubMessageEvent } from 'app/customer-request/observables/events/audit-hub-message.event';
import { CustomerRequestCreatedEvent } from 'app/customer-request/observables/events/customer-request-created.event';
import { NewAuditMessageEvent } from 'app/customer-request/observables/events/new-audit-message.event';
import { RequestCommentCreatedEvent } from 'app/customer-request/observables/events/request-comment-created.event';
import { RequestStatusChangedBulkEmailEvent } from 'app/customer-request/observables/events/request-status-changed-bulk.event';
import { RequestStatusChangedEmailEvent } from 'app/customer-request/observables/events/request-status-changed-email.event';
import { RequestStatusChangedEvent } from 'app/customer-request/observables/events/request-status-changed.event';
import { CustomerRequestListViewRepository } from 'app/customer-request/repositories/customer-request-list-view.repository';
import { CustomerRequestRepository } from 'app/customer-request/repositories/customer-request.repository';
import { CustomerRequestDetailsWithFrameworkType } from 'app/customer-request/types/customer-request-details-with-framework.type';
import { CustomerRequestDetailsType } from 'app/customer-request/types/customer-request-details.type';
import { CustomerRequestEvidenceType } from 'app/customer-request/types/customer-request-evidence.type';
import { CustomerRequestEvidencesType } from 'app/customer-request/types/customer-request-evidences.type';
import { CustomerRequestListItemType } from 'app/customer-request/types/customer-request-list-item.type';
import { CustomerRequestMessageType } from 'app/customer-request/types/customer-request-message.type';
import { CustomerRequestStatusChangedType } from 'app/customer-request/types/customer-request-status-changed.type';
import { CustomerRequestType } from 'app/customer-request/types/customer-request.type';
import {
    CustomerRequestsPaginatedType,
    CustomerRequestsType,
} from 'app/customer-request/types/customer-requests.type';
import { CustomerTotalUnreadMessagesType } from 'app/customer-request/types/customer-total-unread-messages.type';
import { NewAuditMesssageEventPayload } from 'app/customer-request/types/new-audit-message-event-payload.type';
import { RequestFieldValidationType } from 'app/customer-request/types/request-field-validation-response.type';
import { RequestStatusChangeEventPayload } from 'app/customer-request/types/request-status-change-event-payload.type';
import { RequestStatusChangedBulkPayload } from 'app/customer-request/types/request-status-changed-bulk-payload.type';
import {
    SendAuditHubEmailEventParams,
    SendAuditHubEventPayload,
} from 'app/customer-request/types/send-audit-hub-email-event-params.type';
import { UserFeature } from 'app/feature-toggling/entities/user-feature.entity';
import { UserFeatureRepository } from 'app/feature-toggling/repositories/user-feature.repository';
import { Framework } from 'app/frameworks/entities/framework.entity';
import { FrameworksCoreService } from 'app/frameworks/services/frameworks-core.service';
import { RequirementsCoreService } from 'app/frameworks/services/requirements-core.service';
import { ControlIsReadyView } from 'app/grc/entities/control-is-ready-view.entity';
import { Control } from 'app/grc/entities/control.entity';
import { FacetRunner } from 'app/grc/facets/facet-runner.class';
import { ControlRepository } from 'app/grc/repositories/control.repository';
import { User } from 'app/users/entities/user.entity';
import { PolicyVersion } from 'app/users/policies/entities/policy-version.entity';
import { UserRepository } from 'app/users/repositories/user.repository';
import { UsersCoreService } from 'app/users/services/users-core.service';
import { RequestGetFieldValidationRequestDto } from 'auditors/dtos/request-get-field-validation-request.dto';
import { FrameworkTypeTags } from 'auditors/entities/framework-type-tags.map';
import { Audit } from 'audits/entities/audit.entity';
import { Account } from 'auth/entities/account.entity';
import { PolloAdapter } from 'commons/adapters/pollo.adapter';
import { csvMaxFileSize } from 'commons/configs/upload.config';
import { TenancyTransaction } from 'commons/decorators/transaction';
import { CustomerRequestFileHeader } from 'commons/enums/customer-request-file-header.enum';
import { FeatureType } from 'commons/enums/feature-toggling/feature.enum';
import { MimeType } from 'commons/enums/mime-type.enum';
import { UploadType } from 'commons/enums/upload-type.enum';
import { Role } from 'commons/enums/users/role.enum';
import { ForbiddenException } from 'commons/exceptions/forbidden.exception';
import { compareLowerCase } from 'commons/helpers/string.helper';
import { hasRole, hasRoleWithDeleted } from 'commons/helpers/user.helper';
import { AppService } from 'commons/services/app.service';
import { UploadedFileType } from 'commons/types/uploaded-file.type';
import config from 'config';
import { Downloader } from 'dependencies/downloader/downloader';
import { DownloaderPayloadType } from 'dependencies/downloader/types/downloader-payload.interface';
import { Uploader } from 'dependencies/uploader/uploader';
import { FeatureFlagService } from 'feature-flags/feature-flag.service';
import { FileValidationService } from 'file-validation/services/file-validation.service';
import { FileValidationBuilder } from 'file-validation/validations/base/file-validation.builder';
import { compact, get, head, isEmpty, isNil, orderBy, remove, uniqBy } from 'lodash';
import { PolloMessage } from 'pollo-logger/classes/pollo.message';
import { In, MoreThan, Not, Repository } from 'typeorm';

@Injectable()
export class CustomerRequestService extends AppService {
    constructor(
        // @AUDIT-REFACTOR: TODO rename to auditRepository
        @InjectRepository(Audit)
        private readonly auditorFrameworkRepository: Repository<Audit>,
        private readonly uploader: Uploader,
        private readonly downloader: Downloader,
        private readonly createEventService: CreateEventService,
        private readonly controlService: ControlService,
        private readonly featureFlagService: FeatureFlagService,
        private readonly frameworksCoreService: FrameworksCoreService,
        private readonly workspacesCoreService: WorkspacesCoreService,
        private readonly requirementsCoreService: RequirementsCoreService,
        private readonly usersCoreService: UsersCoreService,
        private readonly fileValidationService: FileValidationService,
        private readonly auditHubOrchestrationService: AuditHubOrchestrationService,
    ) {
        super();
    }

    async getCustomerRequestFieldValidation(
        account: Account,
        auditorFrameworkId: string,
        requestDto: RequestGetFieldValidationRequestDto,
    ): Promise<RequestFieldValidationType> {
        try {
            let isAvailable = true;
            const { code, title, excludeIds } = requestDto;
            const requests =
                await this.customerRequestRepository.getCustomerRequestBasicInfoByAuditorFrameworkId(
                    auditorFrameworkId,
                    excludeIds,
                );

            if (isEmpty(requests)) {
                return { isAvailable };
            }

            if (code) {
                const requestCodes = requests.map(request => request.code.toLowerCase());
                isAvailable = isRequestFieldAvailable(requestCodes, code);
            } else if (title) {
                const requestTitles = requests.map(request => request.title.toLowerCase());
                isAvailable = isRequestFieldAvailable(requestTitles, title);
            }

            return { isAvailable };
        } catch (error) {
            this.logger.error(
                PolloMessage.msg(error.message)
                    .setError(error)
                    .setDomain(account.domain)
                    .setContext(this.constructor.name)
                    .setSubContext('getCustomerRequestFieldValidation'),
            );
            throw error;
        }
    }

    /**
     * Get a list of customer requests
     * @param account Account
     * @param user User
     * @param requestDto Request DTO
     * @returns Paginated list of customer requests
     */
    async getCustomerRequests(
        account: Account,
        user: User,
        requestDto: CustomerRequestListRequestDto,
    ): Promise<CustomerRequestsType> {
        try {
            await this.validateUserAccessForAuditorFramework(user, requestDto.auditId);

            const userIsAuditor = hasRole(user, [Role.AUDITOR]);
            const totalUnreadMessages =
                await this.customerRequestListViewRepository.getTotalUnreadMessagesByAudit(
                    userIsAuditor,
                    requestDto.auditId,
                );

            const auditorFramework: Audit | null = await this.auditorFrameworkRepository.findOneBy({
                id: requestDto.auditId,
            });

            const listView = await this.getFacetedCustomerRequestFilters(account, user, requestDto);

            if (isNil(auditorFramework)) {
                throw new NotFoundException(
                    `Auditor framework with ID ${requestDto.auditId} was not found`,
                );
            }

            const data: CustomerRequestListItemType[] = [];
            if (!isEmpty(listView?.data)) {
                const enabledControls =
                    await this.controlService.getControlsWithReadyForCustomerRequests();

                // Get owners for all requests
                const requestIds = listView.data.map(item => item.requestId);
                const ownersData =
                    await this.userRepository.getCustomerRequestOwnersByRequestIds(requestIds);

                listView.data.forEach(item => {
                    const evidenceControls: CustomerRequestEvidenceType[] = enabledControls
                        .filter(
                            enabledControl =>
                                item.controlCodes.includes(enabledControl.code) &&
                                enabledControl.products[0]?.id === auditorFramework.productId,
                        )
                        .map(filteredControl => {
                            return {
                                id: filteredControl.id,
                                code: filteredControl.code,
                                name: filteredControl.name,
                                description: filteredControl.description,
                                isReady: filteredControl.controlIsReady.isReady,
                            };
                        });

                    const customerRequest: CustomerRequestListItemType = {
                        id: item.requestId,
                        code: item.requestCode,
                        title: item.requestTitle,
                        description: item.requestDescription,
                        status: item.requestStatus,
                        unreadMessages: userIsAuditor
                            ? item.auditorUnreadMessages
                            : item.customerUnreadMessages,
                        controls: evidenceControls,
                        auditorFrameworkId: item.auditorFrameworkId,
                        owners:
                            ownersData.find(owners => owners.requestId === item.requestId)
                                ?.owners || [],
                    };

                    data.push(customerRequest);
                });
            }

            return {
                totalUnreadMessages,
                requestIds: listView.requestIds,
                requests: {
                    data,
                    page: listView.page,
                    limit: listView.limit,
                    total: listView.total,
                },
            };
        } catch (error) {
            this.logger.error(
                PolloMessage.msg(error.message)
                    .setError(error)
                    .setDomain(account.domain)
                    .setContext(this.constructor.name)
                    .setSubContext('getCustomerRequests'),
            );
            throw error;
        }
    }

    async getFacetedCustomerRequestFilters(
        account: Account,
        user: User,
        requestDto: CustomerRequestListRequestDto,
    ): Promise<CustomerRequestsPaginatedType> {
        const userIsAuditor = hasRole(user, [Role.AUDITOR]);
        const facetRunner = new FacetRunner();

        facetRunner.addFacetsByClass(
            [
                CustomerRequestOwnerFacet,
                CustomerRequestIsOwnedFacet,
                CustomerRequestAuditorFacet,
                CustomerRequestStatusFacet,
            ],
            account,
            this.customerRequestRepository,
            requestDto,
        );

        facetRunner.addFacetsByClass(
            [CustomerRequestListViewUnreadMessagesFacet, CustomerRequestListViewSearchStringFacet],
            account,
            this.customerRequestListViewRepository,
            requestDto,
            userIsAuditor,
        );

        const { include: includeRequestIds, exclude: excludeRequestIds } = await facetRunner.run();

        if (facetRunner.isAnyFacetActivated() && includeRequestIds.length === 0) {
            return {
                data: [],
                page: requestDto.page,
                limit: requestDto.limit,
                total: 0,
                requestIds: [],
            };
        }

        const { data, page, limit, total } =
            await this.customerRequestListViewRepository.listCustomerRequestsByRequestIds(
                includeRequestIds,
                excludeRequestIds,
                requestDto?.page ?? 1,
                requestDto.limit,
            );

        return {
            data,
            page,
            limit,
            total,
            requestIds: includeRequestIds,
        } as CustomerRequestsPaginatedType;
    }

    /**
     * Get total unread messages count for customers
     * @param account Account
     * @returns Total unread messages count
     */
    async getTotalAuditUnreadMessages(account: Account): Promise<CustomerTotalUnreadMessagesType> {
        try {
            const data = await this.auditorFrameworkListViewRepository.find({
                where: { customerUnreadMessages: MoreThan(0) },
            });
            const totalUnreadMessages = data.reduce((acc, current) => {
                return acc + Number(current.customerUnreadMessages);
            }, 0);

            return {
                totalUnreadMessages,
            };
        } catch (error) {
            this.logger.error(
                PolloMessage.msg(error.message)
                    .setError(error)
                    .setDomain(account.domain)
                    .setContext(this.constructor.name)
                    .setSubContext('getTotalAuditUnreadMessages'),
            );
            throw error;
        }
    }

    /**
     * Get customer request details with evidence, messages and files (if any)
     * @param account Account
     * @param user User
     * @param customerRequestId Customer Request ID
     * @returns Customer request details
     */
    async getCustomerRequestDetails(
        account: Account,
        user: User,
        customerRequestId: number,
    ): Promise<CustomerRequestDetailsType> {
        const customerRequest = await this.customerRequestRepository.getRequest(customerRequestId);

        if (isNil(customerRequest)) {
            throw new NotFoundException(
                `Customer request with ID ${customerRequestId} was not found`,
            );
        }

        await this.validateUserAccessForAuditorFramework(user, customerRequest.auditorFrameworkId);

        const auditorFramework = await this.auditorFrameworkRepository.findOne({
            where: { id: customerRequest.auditorFrameworkId },
        });

        if (isNil(auditorFramework)) {
            throw new NotFoundException(
                `Auditor framework with ID ${customerRequest.auditorFrameworkId} was not found`,
            );
        }

        const framework = await this.getFrameworkByAuditorFramework(account, auditorFramework);

        if (isNil(framework)) {
            throw new NotFoundException(
                `There is no framework related to the auditor framework ${customerRequest.auditorFrameworkId}`,
            );
        }

        const messages: CustomerRequestMessageType[] = !isEmpty(
            customerRequest.customerRequestMessages,
        )
            ? await this.formatCustomerMessages(account, customerRequest.customerRequestMessages)
            : [];

        return {
            customerRequest,
            framework,
            messages: orderBy(messages, message => message.sentAt, 'desc'),
            companyName: account.companyName,
        };
    }

    /**
     * @deprecated Use CustomerRequestCoreService.getCustomerRequest
     * Get customer request
     * @param customerRequestId Customer Request ID
     * @returns Customer request
     */
    async getCustomerRequest(customerRequestId: number): Promise<CustomerRequest> {
        const customerRequest =
            await this.customerRequestRepository.getCustomerRequest(customerRequestId);

        if (isNil(customerRequest)) {
            throw new NotFoundException(
                `Customer request with ID ${customerRequestId} was not found`,
            );
        }
        return customerRequest;
    }

    /**
     * Get customer request details with framework
     * @param account Account
     * @param user User
     * @param customerRequestId Customer Request ID
     * @returns Customer request details
     */
    async getCustomerRequestDetailsWithFramework(
        account: Account,
        user: User,
        customerRequestId: number,
    ): Promise<CustomerRequestDetailsWithFrameworkType> {
        const customerRequest = await this.getCustomerRequest(customerRequestId);

        customerRequest.owners =
            await this.userRepository.getCustomerRequestOwners(customerRequestId);

        await this.validateUserAccessForAuditorFramework(user, customerRequest.auditorFrameworkId);

        const auditorFramework = await this.auditorFrameworkRepository.findOne({
            where: { id: customerRequest.auditorFrameworkId },
        });

        if (isNil(auditorFramework)) {
            throw new NotFoundException(
                `Auditor framework with ID ${customerRequest.auditorFrameworkId} was not found`,
            );
        }

        const framework = await this.getFrameworkByAuditorFramework(account, auditorFramework);

        if (isNil(framework)) {
            throw new NotFoundException(
                `There is no framework related to the auditor framework ${customerRequest.auditorFrameworkId}`,
            );
        }

        return {
            customerRequest,
            framework,
        };
    }

    /**
     * Get customer request messages
     * @param account Account
     * @param user User
     * @param customerRequestId Customer Request ID
     * @returns Customer request details
     */
    async getCustomerRequestMessages(
        account: Account,
        user: User,
        customerRequestId: number,
    ): Promise<CustomerRequestMessageType[]> {
        const customerRequest =
            await this.customerRequestRepository.getRequestWithMessages(customerRequestId);

        if (isNil(customerRequest)) {
            throw new NotFoundException(
                `Customer request with ID ${customerRequestId} was not found`,
            );
        }

        await this.validateUserAccessForAuditorFramework(user, customerRequest.auditorFrameworkId);

        const messages: CustomerRequestMessageType[] = !isEmpty(
            customerRequest.customerRequestMessages,
        )
            ? await this.formatCustomerMessages(account, customerRequest.customerRequestMessages)
            : [];

        return orderBy(messages, message => message.sentAt, 'desc');
    }

    /**
     * Get customer request details with evidence, messages and files (if any)
     * @param account Account
     * @param user User
     * @param customerRequestId Customer Request ID
     * @returns Customer request details
     */
    async getCustomerRequestWithControls(
        account: Account,
        user: User,
        customerRequestId: number,
    ): Promise<CustomerRequestEvidencesType> {
        const customerRequest =
            await this.customerRequestRepository.getRequestWithControls(customerRequestId);

        if (isNil(customerRequest)) {
            throw new NotFoundException(
                `Customer request with ID ${customerRequestId} was not found`,
            );
        }

        await Promise.all(
            customerRequest.controls.map(async control => {
                try {
                    await this.mapControlEvidenceItems(control);
                } catch (error) {
                    this.logger.warn(
                        PolloAdapter.acct(
                            `Could not retrieve control with evidence for control with ID ${control.id}`,
                            account,
                        ).setError(error),
                    );
                }
            }),
        );

        await this.validateUserAccessForAuditorFramework(user, customerRequest.auditorFrameworkId);

        return {
            customerRequest,
            companyName: account.companyName,
        };
    }

    async mapControlEvidenceItems(control: Control): Promise<void> {
        const controlIsReady = await this.controlService.getControlIsReady(control.id);
        control.controlIsReady = controlIsReady?.controlIsReady as unknown as ControlIsReadyView;
    }

    /**
     * @deprecated Use CustomerRequestOrchestrationService.createAuditRequestsRequirements
     */
    async createAuditRequestsRequirements(
        account: Account,
        requestDto: CreateCustomerRequestsFromRequirementsRequestDto,
    ): Promise<CustomerRequest[]> {
        try {
            const auditorFramework = await this.auditorFrameworkRepository.findOneBy({
                id: requestDto.auditorFrameworkId,
            });

            if (isNil(auditorFramework)) {
                throw new NotFoundException(
                    `auditor framework with id ${requestDto.auditorFrameworkId} not found`,
                );
            }

            const frameworkTag = FrameworkTypeTags.get(auditorFramework.frameworkType);

            const framework = await this.frameworksCoreService.getEnabledFrameworkByTag(
                frameworkTag,
                auditorFramework.productId,
                auditorFramework.customFrameworkId,
            );

            if (isNil(framework)) {
                throw new NotFoundException(ErrorCode.FRAMEWORK_NOT_FOUND);
            }

            const excludeArchivedRequirements = true;

            const requirementsToTransform =
                await this.requirementsCoreService.getRequirementsByFramework(
                    framework.id,
                    excludeArchivedRequirements,
                );

            if (isEmpty(requirementsToTransform)) {
                throw new NotFoundException(
                    ErrorCode.NO_REQUIREMENTS_FOUND,
                    `requirements for framework with id ${framework.id} not found`,
                );
            }

            const customerRequests = mapRequirementsToCustomerRequests(
                requirementsToTransform,
                auditorFramework.id,
            );

            return await this.customerRequestRepository.save(customerRequests);
        } catch (error) {
            this.logger.error(
                PolloMessage.msg(error.message)
                    .setError(error)
                    .setDomain(account.domain)
                    .setContext(this.constructor.name)
                    .setSubContext('createAuditRequestsRequirements'),
            );
            throw error;
        }
    }

    /**
     * Create customer requests
     * @param account Account
     * @param requestDto Request DTO
     * @returns List of customer requests
     */
    async createCustomerRequests(
        account: Account,
        user: User,
        requestDto: CreateCustomerRequestsRequestDto,
    ): Promise<CustomerRequest[]> {
        try {
            const { requests } = requestDto;

            if (isEmpty(requests)) {
                throw new BadRequestException('requests to create are missing');
            }

            // prevent unauthorized access to the auditor framework
            if (!hasRole(user, [Role.ADMIN])) {
                await Promise.all(
                    requests.map(async request => {
                        await this.auditHubOrchestrationService.validateAuditorAccessToAuditOrFail(
                            user.entryId,
                            request.auditorFrameworkId,
                            account.id,
                        );
                    }),
                );
            }
            const enabledControls = await this.controlService.getControlsWithReady();

            const customerRequests = requests.map(request => {
                const customerRequest = new CustomerRequest();
                customerRequest.code = request.code;
                customerRequest.title = request.title;
                customerRequest.description = request.description;
                customerRequest.auditorFrameworkId = request.auditorFrameworkId;

                if (!isEmpty(request.controlIds)) {
                    const controls = enabledControls.filter(control =>
                        request.controlIds.includes(control.id),
                    );
                    customerRequest.controls = controls;
                }
                customerRequest.status = isEmpty(customerRequest.controls)
                    ? CustomerRequestStatus.OUTSTANDING
                    : CustomerRequestStatus.IN_REVIEW;
                return customerRequest;
            });

            const savedCustomerRequests =
                await this.customerRequestRepository.save(customerRequests);

            const auditorFramework = await this.getAuditorFrameworkById(
                head(customerRequests)?.auditorFrameworkId,
            );

            const frameworkTag = FrameworkTypeTags.get(auditorFramework.frameworkType);
            const framework = await this.frameworksCoreService.getEnabledFrameworkByTag(
                frameworkTag,
                auditorFramework.productId,
                auditorFramework.customFrameworkId,
            );

            if (isNil(framework)) {
                throw new NotFoundException(ErrorCode.FRAMEWORK_NOT_FOUND);
            }

            savedCustomerRequests.forEach(customRequest => {
                this._eventBus.publish(
                    new CustomerRequestCreatedEvent(
                        account,
                        user,
                        !isEmpty(customRequest.controls),
                        requestDto.isBulkUpload,
                        requestDto.isWizardUpload,
                        customRequest.description,
                        customRequest.id.toString(),
                        framework.name,
                    ),
                );
            });

            return savedCustomerRequests;
        } catch (error) {
            this.logger.error(
                PolloMessage.msg(error.message)
                    .setError(error)
                    .setDomain(account.domain)
                    .setContext(this.constructor.name)
                    .setSubContext('createCustomerRequests'),
            );
            throw error;
        }
    }

    async getSelectedCustomerRequests(
        account: Account,
        user: User,
        requestIds: number[],
        requestDto: CustomerRequestBulkActionRequestDto,
    ): Promise<CustomerRequest[]> {
        const { selectAll } = requestDto;

        let customerRequests: CustomerRequest[] = [];
        if (!selectAll) {
            customerRequests = await this.customerRequestRepository.find({
                where: { id: In(requestIds) },
            });

            if (requestIds.length !== customerRequests.length) {
                this.logger.warn(
                    PolloMessage.msg('Some customer requests were not found')
                        .setDomain(account.domain)
                        .setContext(this.constructor.name)
                        .setSubContext('getSelectedCustomerRequests'),
                );
            }

            if (isEmpty(customerRequests)) {
                throw new NotFoundException(
                    `No customer requests were found for IDs: ${requestIds.join()}`,
                );
            }
        } else if (selectAll && !isNil(requestDto.framework)) {
            const { requestIds: customerRequestIds = [] } = await this.getCustomerRequests(
                account,
                user,
                {
                    ...requestDto,
                    status: requestDto.statusFilter,
                    auditId: requestDto.framework,
                    page: 1,
                    limit: 20,
                },
            );
            if (isEmpty(customerRequestIds)) {
                return [];
            }

            customerRequests = await this.customerRequestRepository.find({
                where: { id: In(customerRequestIds) },
            });
        }

        return customerRequests;
    }

    /**
     * Update customer requests status
     * @param account Account
     * @param user User
     * @param requestDto Request DTO
     * @returns Customer Requests
     */
    async updateCustomerRequestStatus(
        account: Account,
        user: User,
        requestDto: UpdateCustomerRequestStatusesRequestDto,
    ): Promise<CustomerRequest[]> {
        const customerRequests = await this.getSelectedCustomerRequests(
            account,
            user,
            requestDto.requestIds,
            requestDto,
        );

        const isValidRole =
            hasRole(user, [Role.ADMIN]) && requestDto.status == CustomerRequestStatus.ACCEPTED;

        if (isValidRole) {
            throw new UnauthorizedException(
                `Admins cannot change the status to ACCEPTED for the selected customer request ids`,
            );
        }

        // Structure for Analytics Event
        const requestStatusMap: CustomerRequestStatusChangedType[] = [];
        customerRequests.forEach(customerRequest => {
            requestStatusMap.push({
                statusFrom: customerRequest.status,
                statusTo: requestDto.status,
                id: customerRequest.id.toString(),
                requestDescription: customerRequest.description,
                frameworkId: customerRequest.auditorFrameworkId,
                title: customerRequest.title,
            });
            customerRequest.status = requestDto.status;
        });

        const updatedRequests = await this.customerRequestRepository.save(customerRequests);

        // GetRequestFramework for analytics event since all request are from the same fw
        const auditorFramework = await this.getAuditorFrameworkById(
            head(customerRequests)?.auditorFrameworkId,
        );

        const framework = await this.getFrameworkByAuditorFramework(account, auditorFramework);
        requestStatusMap.forEach(request => {
            this._eventBus.publish(
                new RequestStatusChangedEvent(
                    account,
                    user,
                    request.statusFrom,
                    request.statusTo,
                    request.requestDescription,
                    request.id,
                    request.title,
                    framework.name,
                ),
            );
        });
        const customerRequestsTotal = customerRequests?.length;
        if (!isNil(requestDto.message)) {
            const customerRequestPromises = updatedRequests
                .filter((_val, index) => !isNil(requestStatusMap[index]))
                .map((request, index) => {
                    const requestStatus = requestStatusMap[index];

                    const params: CreateCustomerRequestMessageParams = {
                        account,
                        user,
                        sentBy: user.entryId,
                        customerRequestId: request.id,
                        requestDto: { message: requestDto.message },
                        files: [],
                        referenceDocuments: undefined,
                        ...(customerRequestsTotal === 1
                            ? {
                                  sendEmail: true,
                                  emailType: AuditHubEmailType.STATUS_CHANGE,
                                  emailPayload: requestStatus,
                              }
                            : { sendEmail: false }),
                    };

                    return this.createCustomerRequestMessage(params);
                });
            await Promise.all(customerRequestPromises);
        }

        const product = await this.workspacesCoreService.getProductById(auditorFramework.productId);
        const hasMultipleProducts = await this.workspacesCoreService.hasMultipleProducts(account);
        const productName = get(product, 'name', '');

        if (customerRequestsTotal > 1) {
            if (hasRole(user, [Role.AUDITOR])) {
                const adminsAndWorkspaceManagersWithFeatureOn =
                    await this.getAdminsAndWorkspaceManagersWithFeatureOn(
                        account,
                        FeatureType.REQUEST_STATUS_CHANGE,
                        auditorFramework.productId,
                    );
                adminsAndWorkspaceManagersWithFeatureOn.forEach(userWithFeatureOn => {
                    this.sendAuditHubEmailEvent({
                        emailType: AuditHubEmailType.BULK_STATUS_CHANGE,
                        account,
                        sender: user,
                        receiver: userWithFeatureOn.user,
                        hasMultipleProducts,
                        eventPayload: {
                            isRecieverAuditor: false,
                            auditorFrameworkId: auditorFramework.id,
                            totalRequestsUpdateCount: customerRequests.length,
                            frameworkName: framework.name,
                            productName,
                        },
                    });
                });
            } else {
                const auditorUsers = await Promise.all(
                    auditorFramework.auditorFrameworkAuditors.map(async auditorMapItem =>
                        this.usersCoreService.getUserByEntryId(
                            auditorMapItem.auditorClient.entry.id,
                        ),
                    ),
                );

                auditorUsers.forEach(auditorUser => {
                    const auditorMapItem = auditorFramework.auditorFrameworkAuditors.find(
                        auditorFrameworkAuditor =>
                            auditorFrameworkAuditor.auditorClient.entry.id === auditorUser.entryId,
                    );
                    this.sendAuditHubEmailEvent({
                        emailType: AuditHubEmailType.BULK_STATUS_CHANGE,
                        account,
                        sender: user,
                        receiver: auditorUser,
                        hasMultipleProducts,
                        eventPayload: {
                            isRecieverAuditor: true,
                            auditorFrameworkId: auditorFramework.id,
                            totalRequestsUpdateCount: customerRequests.length,
                            clientId: auditorMapItem.auditorClient.id,
                            frameworkName: framework.name,
                            productName,
                        },
                    });
                });
            }
        }
        return updatedRequests;
    }

    /**
     * Create customer request message
     * @param params CreateCustomerRequestMessageParams
     * @returns Customer request message
     */
    async createCustomerRequestMessage(
        params: CreateCustomerRequestMessageParams,
    ): Promise<CustomerRequestMessageType> {
        const {
            account,
            user,
            sentBy,
            customerRequestId,
            requestDto,
            files,
            sendEmail,
            policyVersionsForEvent,
            libraryDocumentVersions,
            externalEvidences,
            referenceDocuments,
        } = params;

        try {
            const customerRequestMessage = new CustomerRequestMessage();
            customerRequestMessage.message = requestDto.message;
            customerRequestMessage.sentBy = sentBy;

            const customerRequest = await this.customerRequestRepository.findOneBy({
                id: customerRequestId,
            });

            if (isNil(customerRequest)) {
                throw new NotFoundException(
                    `Customer request with ID ${customerRequestId} was not found`,
                );
            }

            customerRequestMessage.customerRequest = customerRequest;

            const newCustomerRequestMessage =
                await this.customerRequestMessageRepository.save(customerRequestMessage);

            let customerRequestMessageFiles: CustomerRequestMessageFile[] = [];
            const auditorFrameworkPromise = this.getAuditorFrameworkById(
                customerRequest.auditorFrameworkId,
            );
            if (Array.isArray(files) && files.length > 0) {
                const results = await Promise.allSettled(
                    files.map(file =>
                        this.createCustomerRequestFile(account, newCustomerRequestMessage.id, file),
                    ),
                );
                customerRequestMessageFiles = results
                    .filter(
                        (r): r is PromiseFulfilledResult<CustomerRequestMessageFile> =>
                            r.status === 'fulfilled' && !isNil(r.value),
                    )
                    .map(r => r.value);

                newCustomerRequestMessage.customerRequestMessageFiles = customerRequestMessageFiles;
            }

            const policyVersionsPromise =
                Array.isArray(policyVersionsForEvent) && policyVersionsForEvent.length > 0
                    ? this.policyVersionRepository.findByIds(policyVersionsForEvent.map(p => p.id))
                    : Promise.resolve<PolicyVersion[]>([]);

            const referenceDocsPromise =
                Array.isArray(referenceDocuments) && referenceDocuments.length > 0
                    ? Promise.all(
                          referenceDocuments.map(refDoc => {
                              const referenceDocument =
                                  new CustomerRequestMessageReferenceDocument();
                              referenceDocument.documentType = refDoc.documentType;
                              referenceDocument.documentName = refDoc.documentName;
                              referenceDocument.customerRequestMessage = newCustomerRequestMessage;
                              return this.customerRequestMessageReferenceDocumentRepository.save(
                                  referenceDocument,
                              );
                          }),
                      )
                    : Promise.resolve<CustomerRequestMessageReferenceDocument[]>([]);

            if (Array.isArray(libraryDocumentVersions) && libraryDocumentVersions.length > 0) {
                newCustomerRequestMessage.libraryDocumentVersions = libraryDocumentVersions;
            }

            if (Array.isArray(externalEvidences) && externalEvidences.length > 0) {
                newCustomerRequestMessage.externalEvidences = externalEvidences;
            }

            const [policyVersions, createdReferenceDocuments] = await Promise.all([
                policyVersionsPromise,
                referenceDocsPromise,
            ]);

            if (policyVersions.length > 0) {
                newCustomerRequestMessage.policyVersions = policyVersions;
            }
            if (createdReferenceDocuments.length > 0) {
                newCustomerRequestMessage.referenceDocuments = createdReferenceDocuments;
            }

            let adaptedMessage: CustomerRequestMessageType;
            if (newCustomerRequestMessage.sentBy === SentByReservedUUIDs.AUDIT_HUB_DRATA_UPDATE) {
                const adapter = new DrataUpdateCustomerRequestMessageAdapter(
                    newCustomerRequestMessage,
                );
                adaptedMessage = adapter.adapt();
            } else {
                const adapter = new UserUpdateCustomerRequestMessageAdapter(
                    user,
                    newCustomerRequestMessage,
                );
                adaptedMessage = adapter.adapt();
            }

            if (sendEmail) {
                const emailType = params.emailType;
                const emailPayload = params?.emailPayload;
                // Offload email computation and event publication outside the request's critical path
                this.sendNewAuditMessageEmails(
                    user,
                    account,
                    newCustomerRequestMessage,
                    emailType,
                    emailPayload,
                ).catch(err => {
                    this.logger.warn(
                        PolloMessage.msg('sendNewAuditMessageEmails failed')
                            .setError(err)
                            .setDomain(account.domain)
                            .setContext(this.constructor.name)
                            .setSubContext('createCustomerRequestMessage'),
                    );
                });
            }

            if (!adaptedMessage) {
                throw new Error('Failed to format customer request message');
            }

            this.publishCreateMessageEventsInBackground(
                account,
                user,
                customerRequest,
                newCustomerRequestMessage,
                adaptedMessage,
                customerRequestMessageFiles,
                auditorFrameworkPromise,
            ).catch(err => {
                this.logger.warn(
                    PolloMessage.msg('publishCreateMessageEventsInBackground failed')
                        .setError(err)
                        .setDomain(account.domain)
                        .setContext(this.constructor.name)
                        .setSubContext('createCustomerRequestMessage'),
                );
            });

            return adaptedMessage;
        } catch (error) {
            this.logger.error(
                PolloMessage.msg(error.message)
                    .setError(error)
                    .setDomain(account.domain)
                    .setContext(this.constructor.name)
                    .setSubContext('createCustomerRequestMessage'),
            );
            throw error;
        }
    }

    async updateCustomerRequestDetails(
        account: Account,
        user: User,
        customerRequestId: number,
        requestDto: UpdateCustomerRequestDetailsRequestDto,
    ): Promise<CustomerRequest> {
        try {
            const customerRequest = await this.customerRequestRepository.findOne({
                where: { id: customerRequestId },
            });

            if (isNil(customerRequest)) {
                throw new NotFoundException(
                    `customerRequest with id ${customerRequestId} not found`,
                );
            }
            const { title, description, ownerIds } = requestDto;

            if (ownerIds.length > MAX_AUDIT_REQUEST_OWNERS) {
                throw new BadRequestException(
                    `Users cannot add more than ${MAX_AUDIT_REQUEST_OWNERS} owners to a request`,
                );
            }

            const auditOwners = await this.userRepository.getUsersByIdsAndRoles(ownerIds, [
                Role.ADMIN,
                Role.AUDITOR,
                Role.SERVICE_USER,
            ]);

            if (!isEmpty(ownerIds) && isEmpty(auditOwners)) {
                throw new BadRequestException('All provided owner IDs are invalid');
            }

            customerRequest.title = title;
            customerRequest.description = description;
            customerRequest.owners = auditOwners;

            const customerRequestUpdated =
                await this.customerRequestRepository.save(customerRequest);

            const auditorFramework = await this.getAuditorFrameworkById(
                customerRequestUpdated.auditorFrameworkId,
            );

            const product = await this.workspacesCoreService.getProductById(
                auditorFramework.productId,
            );

            const hasMultipleProducts =
                await this.workspacesCoreService.hasMultipleProducts(account);

            this._eventBus.publish(
                new CustomerRequestUpdatedEvent(
                    account,
                    user,
                    requestDto,
                    customerRequestUpdated,
                    auditorFramework,
                    product,
                    CustomerRequestDetailsEvent.EDITED,
                    hasMultipleProducts,
                ),
            );
            return customerRequest;
        } catch (error) {
            this.logger.error(
                PolloMessage.msg('Could not update customer request details')
                    .setError(error)
                    .setDomain(account.domain)
                    .setContext(this.constructor.name)
                    .setSubContext('createCustomerRequestMessage'),
            );
            throw error;
        }
    }

    /**
     * Update customer request message
     * @param account Account
     * @param user User
     * @param customerRequestMessageId Customer request message ID
     * @param requestDto Request DTO
     * @param files List of files
     * @returns Customer request message
     */
    async updateCustomerRequestMessage(
        account: Account,
        user: User,
        customerRequestMessageId: number,
        requestDto: UpdateCustomerRequestMessageRequestDto,
        files: UploadedFileType[],
    ): Promise<CustomerRequestMessageType> {
        try {
            let customerRequestMessage = await this.customerRequestMessageRepository.findOne({
                where: { id: customerRequestMessageId },
                relations: ['customerRequestMessageFiles'],
            });

            if (isNil(customerRequestMessage)) {
                throw new NotFoundException(
                    `Customer request message with ID ${customerRequestMessageId} was not found`,
                );
            }

            if (
                customerRequestMessage.sentBy != user.entryId ||
                customerRequestMessage.sentBy === SentByReservedUUIDs.AUDIT_HUB_DRATA_UPDATE
            ) {
                throw new UnauthorizedException(
                    `User ${user.entryId} is not able to edit message with ID ${customerRequestMessageId}`,
                );
            }

            customerRequestMessage.message = requestDto.message;
            customerRequestMessage =
                await this.customerRequestMessageRepository.save(customerRequestMessage);

            if (!isEmpty(requestDto.filesToDelete)) {
                await this.customerRequestMessageFileRepository.softDelete(
                    requestDto.filesToDelete,
                );
                customerRequestMessage.customerRequestMessageFiles = remove(
                    customerRequestMessage.customerRequestMessageFiles,
                    file => !requestDto.filesToDelete.includes(file.id),
                );
            }

            if (!isEmpty(files)) {
                const customerRequestFiles: CustomerRequestMessageFile[] = [];
                for (const file of files) {
                    // eslint-disable-next-line no-await-in-loop
                    const newFile = await this.createCustomerRequestFile(
                        account,
                        customerRequestMessageId,
                        file,
                    );
                    if (!isNil(newFile)) {
                        customerRequestFiles.push(newFile);
                    }
                }
                customerRequestMessage.customerRequestMessageFiles.push(...customerRequestFiles);
            }

            // Handle reference documents - if empty, delete existing ones
            if (requestDto.referenceDocuments !== undefined) {
                if (isEmpty(requestDto.referenceDocuments)) {
                    // Delete all existing reference documents for this message
                    await this.customerRequestMessageReferenceDocumentRepository.softDelete({
                        customerRequestMessage: { id: customerRequestMessageId },
                    });
                } else {
                    // First delete existing reference documents
                    await this.customerRequestMessageReferenceDocumentRepository.softDelete({
                        customerRequestMessage: { id: customerRequestMessageId },
                    });

                    // Then create new reference documents
                    const createdReferenceDocuments = await Promise.all(
                        requestDto.referenceDocuments.map(async refDoc => {
                            const referenceDocument = new CustomerRequestMessageReferenceDocument();
                            referenceDocument.documentType = refDoc.documentType;
                            referenceDocument.documentName = refDoc.documentName;
                            referenceDocument.customerRequestMessage = customerRequestMessage;

                            return this.customerRequestMessageReferenceDocumentRepository.save(
                                referenceDocument,
                            );
                        }),
                    );
                    customerRequestMessage.referenceDocuments = createdReferenceDocuments;
                }
            }

            const messages = await this.formatCustomerMessages(account, [customerRequestMessage]);

            const message = head(messages);
            if (!message) {
                throw new Error('Failed to format customer request message');
            }

            return message;
        } catch (error) {
            this.logger.error(
                PolloMessage.msg(error.message)
                    .setError(error)
                    .setDomain(account.domain)
                    .setContext(this.constructor.name)
                    .setSubContext('updateCustomerRequestMessage'),
            );
            throw error;
        }
    }

    /**
     * Delete customer request message
     * @param account Account
     * @param user User
     * @param customerRequestMessageId Customer request message ID
     */
    async deleteCustomerRequestMessage(
        account: Account,
        user: User,
        customerRequestMessageId: number,
    ): Promise<void> {
        try {
            const customerRequestMessage = await this.customerRequestMessageRepository.findOne({
                where: { id: customerRequestMessageId },
                relations: ['customerRequest', 'customerRequestMessageFiles', 'referenceDocuments'],
            });

            if (isNil(customerRequestMessage)) {
                throw new NotFoundException(
                    `Customer request message with ID ${customerRequestMessageId} was not found`,
                );
            }

            if (
                customerRequestMessage.sentBy != user.entryId &&
                customerRequestMessage.sentBy === SentByReservedUUIDs.AUDIT_HUB_DRATA_UPDATE &&
                !hasRole(user, [Role.ADMIN])
            ) {
                throw new UnauthorizedException(
                    `User ${user.entryId} is not able to delete message with ID ${customerRequestMessage.id}`,
                );
            }

            if (!isEmpty(customerRequestMessage.customerRequestMessageFiles)) {
                await this.customerRequestMessageFileRepository.softDelete(
                    customerRequestMessage.customerRequestMessageFiles.map(file => file.id),
                );
            }

            if (!isEmpty(customerRequestMessage.referenceDocuments)) {
                await this.customerRequestMessageReferenceDocumentRepository.softDelete(
                    customerRequestMessage.referenceDocuments.map(refDoc => refDoc.id),
                );
            }

            await this.customerRequestMessageRepository.softDelete(customerRequestMessage.id);

            this.publishAuditHubMessageDeletedEvent(account, user, customerRequestMessage).catch(
                err => {
                    this.logger.warn(
                        PolloMessage.msg('publishAuditHubMessageDeletedEvent failed')
                            .setError(err)
                            .setDomain(account.domain)
                            .setContext(this.constructor.name)
                            .setSubContext('deleteCustomerRequestMessage'),
                    );
                },
            );
        } catch (error) {
            this.logger.error(
                PolloMessage.msg(error.message)
                    .setError(error)
                    .setDomain(account.domain)
                    .setContext(this.constructor.name)
                    .setSubContext('deleteCustomerRequestMessage'),
            );
            throw error;
        }
    }

    /**
     * Update read by status for customer request messages
     * @param {Account} account
     * @param {User} user
     * @param {number} customerRequestId
     */
    async toggleReadCustomerRequestNotifications(
        account: Account,
        user: User,
        customerRequestId: number,
        messageId: number,
    ): Promise<CustomerRequestMessageType> {
        try {
            if (isNil(customerRequestId) || customerRequestId === 0) {
                throw new BadRequestException(`Invalid customer request ID`);
            }

            if (isNil(messageId) || messageId === 0) {
                throw new BadRequestException(`Invalid message request ID`);
            }

            const customerRequest = await this.customerRequestRepository.findOneBy({
                id: customerRequestId,
            });

            if (isNil(customerRequest)) {
                throw new NotFoundException(
                    `Customer request with ID ${customerRequestId} was not found`,
                );
            }

            const customerRequestMessage = await this.customerRequestMessageRepository.findOne({
                where: {
                    customerRequest: {
                        id: customerRequestId,
                    },
                    id: messageId,
                    sentBy: Not(user.entryId),
                },
                relations: ['customerRequestMessageFiles'],
            });

            if (isNil(customerRequestMessage)) {
                throw new NotFoundException(ErrorCode.REQUEST_MESSAGE_NOT_FOUND);
            }

            const userIsAuditor = hasRole(user, [Role.AUDITOR]);

            let authors: User[] = [];
            if (!userIsAuditor) {
                authors = await this.userRepository.findManyByEntryIdWithDeleted(
                    customerRequestMessage.sentBy,
                );
            }

            if (customerRequestMessage?.readAt) {
                customerRequestMessage.readAt = null;
                customerRequestMessage.readBy = null;
            } else {
                const shouldUpdateReadByAuditor =
                    userIsAuditor && customerRequestMessage.sentBy !== user.entryId;

                const author = authors.find(a => a.entryId === customerRequestMessage.sentBy);
                const authorIsAuditor = !isNil(author)
                    ? hasRoleWithDeleted(author, [Role.AUDITOR])
                    : false;

                const shouldUpdateReadByAdminOrTechGov = !userIsAuditor && authorIsAuditor;

                if (shouldUpdateReadByAuditor || shouldUpdateReadByAdminOrTechGov) {
                    customerRequestMessage.readBy = user.entryId;
                    customerRequestMessage.readAt = new Date();
                }
            }

            const updatedMessage =
                await this.customerRequestMessageRepository.save(customerRequestMessage);

            const messages = await this.formatCustomerMessages(account, [updatedMessage]);
            return messages[0];
        } catch (error) {
            this.logger.error(
                PolloMessage.msg(error.message)
                    .setError(error)
                    .setDomain(account.domain)
                    .setContext(this.constructor.name)
                    .setSubContext('createCustomerRequestFile'),
            );
            throw error;
        }
    }

    /**
     * Create customer request message file
     * @param account Account
     * @param customerRequestMessageId Customer request message ID
     * @param file File
     * @returns Customer request file
     */
    async createCustomerRequestFile(
        account: Account,
        customerRequestMessageId: number,
        file: UploadedFileType,
    ): Promise<CustomerRequestMessageFile> {
        try {
            if (isNil(file?.buffer)) {
                throw new BadRequestException(`Buffer cannot be ${file?.buffer}`);
            }

            const uploadedFile = await this.uploader.uploadPrivateFile(
                file,
                UploadType.CUSTOMER_REQUEST,
                account.id,
            );

            const customerRequestMessageRef = new CustomerRequestMessage();
            customerRequestMessageRef.id = customerRequestMessageId;

            const customerRequestMessageFile = new CustomerRequestMessageFile();
            customerRequestMessageFile.name = uploadedFile.fileName;
            customerRequestMessageFile.file = uploadedFile.key;
            customerRequestMessageFile.customerRequestMessage = customerRequestMessageRef;

            return await this.customerRequestMessageFileRepository.save(customerRequestMessageFile);
        } catch (error) {
            this.logger.error(
                PolloMessage.msg(error.message)
                    .setError(error)
                    .setDomain(account.domain)
                    .setContext(this.constructor.name)
                    .setSubContext('createCustomerRequestFile'),
            );
            throw error;
        }
    }

    /**
     * Provide template url
     * @return string url
     */
    getRequestsFileTemplate(): string {
        return CustomerRequestConstants.REQUESTS_TEMPLATE_URL;
    }

    /**
     * Validate file containing customer requests
     * @param account Account
     * @param file File metadata
     * @param requestDto Request DTO
     * @returns Validation data, such as validation status, requests, and issues (if any)
     */
    async validateRequestsFile(
        account: Account,
        file: UploadedFileType,
        requestDto: CustomerRequestFileValidationRequestDto,
    ): Promise<CustomerRequestFileValidation> {
        try {
            if (isNil(file?.buffer)) {
                throw new BadRequestException('File buffer cannot be null');
            }

            const customerRequestValidationResults = new CustomerRequestFileValidation();

            const auditorFramework = await this.auditorFrameworkRepository.findOneBy({
                id: requestDto.auditorFrameworkId,
            });

            if (isNil(auditorFramework)) {
                throw new NotFoundException(ErrorCode.ENTITY_NOT_FOUND);
            }

            const existingRequests =
                await this.customerRequestRepository.getRequestsByAuditorFramework(
                    requestDto.auditorFrameworkId,
                );
            const workspaceIdForAuditorFramework = auditorFramework.productId;
            const product = new Product();
            product.id = workspaceIdForAuditorFramework;
            account.setCurrentProduct(product);

            const enabledControls = await this.controlRepository.getEnabledControls(account);

            const maxLengthMap = new Map<string, number>([
                [CustomerRequestFileHeader.REQUEST_ID, config.get('validation.maxVarcharText')],
                [CustomerRequestFileHeader.TITLE, config.get('validation.maxVarcharText')],
                [CustomerRequestFileHeader.DESCRIPTION, config.get('validation.maxLongText')],
            ]);

            const requiredHeaders = Object.values(CustomerRequestFileHeader);

            const fileValidationBuilder = new FileValidationBuilder()
                .setInvalidFileExtensionValidation(file.mimetype, [MimeType.CSV_FILE])
                .setInvalidFileSizeValidation(file.size, csvMaxFileSize)
                .setMissingColumnsValidation(requiredHeaders)
                .setNoRecordsValidation()
                .setEmptyRowValidation()
                .setDuplicatedCustomerRequestsValidation(existingRequests)
                .setDuplicatedCustomerRequestTitlesValidation(existingRequests)
                .setInvalidDataValidation(CustomerRequestConstants.INVALID_CHARS_REGEX)
                .setMissingRequiredValuesValidation(CustomerRequestConstants.REQUIRED_HEADERS)
                .setDuplicatedRequestIdsValidation()
                .setDuplicatedTitles()
                .setMaxLengthExceededValidation(maxLengthMap)
                .setControlNotEnabledValidation(
                    enabledControls,
                    CustomerRequestConstants.CONTROL_SEPARATORS,
                );

            const fileValidationResults =
                await this.fileValidationService.validateCsvFile<CustomerRequestFileRecord>(
                    account.domain,
                    file,
                    fileValidationBuilder,
                    CustomerRequestFileRecord,
                );

            if (!isEmpty(fileValidationResults.validData)) {
                const customerRequests: CustomerRequestType[] = fileValidationResults.validData.map(
                    data => {
                        const controlCodes = data[CustomerRequestFileHeader.CONTROLS].split(
                            CustomerRequestConstants.CONTROL_SEPARATORS,
                        );

                        const controlIds = compact(
                            controlCodes.map(
                                code =>
                                    enabledControls.find(control =>
                                        compareLowerCase(control.code, code.trim()),
                                    )?.id,
                            ),
                        );

                        return {
                            code: data[CustomerRequestFileHeader.REQUEST_ID],
                            title: data[CustomerRequestFileHeader.TITLE],
                            description: data[CustomerRequestFileHeader.DESCRIPTION],
                            status: CustomerRequestStatus.OUTSTANDING,
                            auditorFrameworkId: requestDto.auditorFrameworkId,
                            controlIds,
                        };
                    },
                );

                customerRequestValidationResults.setRequests(customerRequests);
            }

            if (!isEmpty(fileValidationResults.issues)) {
                customerRequestValidationResults.setIssues(fileValidationResults.issues);
            }

            return customerRequestValidationResults;
        } catch (error) {
            this.logger.error(
                PolloMessage.msg(error.message)
                    .setError(error)
                    .setDomain(account.domain)
                    .setContext(this.constructor.name)
                    .setSubContext('validateRequestsFile'),
            );
            throw error;
        }
    }

    /**
     * Assign controls to existing audit request
     * @param account Account
     * @param requestId Audit Request ID
     * @param controlIds List of control IDs
     */
    async assignControlsToAuditRequest(
        account: Account,
        requestId: number,
        controlIds: number[],
    ): Promise<void> {
        try {
            const enabledControls = await this.controlService.getControlsWithReady();

            const customerRequest = await this.customerRequestRepository.findOne({
                where: { id: requestId },
                relations: ['controls'],
            });

            if (isNil(customerRequest)) {
                throw new NotFoundException(`Customer request with ID ${requestId} does not exist`);
            }

            const newControls = enabledControls.filter(enabledControl =>
                controlIds.includes(enabledControl.id),
            );
            customerRequest.controls = uniqBy([...customerRequest.controls, ...newControls], 'id');

            await this.customerRequestRepository.save(customerRequest, {
                reload: false,
            });
        } catch (error) {
            this.logger.error(
                PolloMessage.msg(error.message)
                    .setError(error)
                    .setDomain(account.domain)
                    .setContext(this.constructor.name)
                    .setSubContext('assignControlsToAuditRequest'),
            );
            throw error;
        }
    }

    /**
     * Format list of customer request messages
     * @param account Account
     * @param customerRequestMessages Customer request message
     * @returns Formatted list of messages
     */
    private async formatCustomerMessages(
        account: Account,
        customerRequestMessages: CustomerRequestMessage[],
    ): Promise<CustomerRequestMessageType[]> {
        try {
            const messages: CustomerRequestMessageType[] = [];

            if (!isEmpty(customerRequestMessages)) {
                for (const customerRequestMessage of customerRequestMessages) {
                    let message;
                    let requestMessageAdapter: CustomerRequestMessageBaseAdapter;
                    if (
                        customerRequestMessage.sentBy === SentByReservedUUIDs.AUDIT_HUB_DRATA_UPDATE
                    ) {
                        requestMessageAdapter = new DrataUpdateCustomerRequestMessageAdapter(
                            customerRequestMessage,
                        );
                        message = (
                            requestMessageAdapter as DrataUpdateCustomerRequestMessageAdapter
                        ).adapt();
                    } else {
                        const user =
                            // eslint-disable-next-line no-await-in-loop
                            await this.userRepository.findOneByEntryIdOrFailWithDeleted(
                                customerRequestMessage.sentBy,
                                true,
                            );
                        requestMessageAdapter = new UserUpdateCustomerRequestMessageAdapter(
                            user,
                            customerRequestMessage,
                        );
                        message = (
                            requestMessageAdapter as UserUpdateCustomerRequestMessageAdapter
                        ).adapt();
                    }

                    if (!isNil(message)) {
                        messages.push(message);
                    }
                }
            }

            return messages;
        } catch (error) {
            this.logger.error(
                PolloMessage.msg(error.message)
                    .setError(error)
                    .setDomain(account.domain)
                    .setContext(this.constructor.name)
                    .setSubContext('formatCustomerMessages'),
            );
            return [];
        }
    }

    /**
     *
     * @param account
     * @param file
     * @returns
     */
    async getRequestFileSignedUrl(account: Account, id: number): Promise<DownloaderPayloadType> {
        try {
            const messageFile = await this.customerRequestMessageFileRepository.findOneBy({
                id,
            });
            return await this.downloader.getDownloadUrl(messageFile.file);
        } catch (error) {
            this.logger.error(
                PolloMessage.msg(error.message)
                    .setError(error)
                    .setDomain(account.domain)
                    .setContext(this.constructor.name)
                    .setSubContext('getRequestFileSignedUrl'),
            );
            return null;
        }
    }

    /**
     * Send email indicating a new message has been added to a request
     * @param user User
     * @param account Account
     * @param message Message
     */
    private async sendNewAuditMessageEmails(
        user: User,
        account: Account,
        message: CustomerRequestMessage,
        emailType: AuditHubEmailType,
        emailPayload?: object,
    ): Promise<void> {
        try {
            const auditorFramework = await this.getAuditorFrameworkById(
                message.customerRequest.auditorFrameworkId,
            );

            const frameworkTag = FrameworkTypeTags.get(auditorFramework.frameworkType);
            const framework = await this.frameworksCoreService.getEnabledFrameworkByTag(
                frameworkTag,
                auditorFramework.productId,
                auditorFramework.customFrameworkId,
            );

            const product = await this.workspacesCoreService.getProductById(
                auditorFramework.productId,
            );

            const productName = get(product, 'name', '');

            const hasMultipleProducts =
                await this.workspacesCoreService.hasMultipleProducts(account);

            if (!isEmpty(auditorFramework.auditorFrameworkAuditors)) {
                if (hasRole(user, [Role.AUDITOR])) {
                    const adminsAndWorkspaceManagersWithFeatureOn =
                        await this.getAdminsAndWorkspaceManagersWithFeatureOn(
                            account,
                            auditHubEmailTypeToFeatureType[emailType],
                            auditorFramework.productId,
                        );
                    for (const userWithFeatureOn of adminsAndWorkspaceManagersWithFeatureOn) {
                        const payload = this.emailEventPayloadBuilder({
                            emailType,
                            message,
                            framework,
                            productName,
                            ...emailPayload,
                            isRecieverAuditor: false,
                            clientId: '',
                        });
                        this.sendAuditHubEmailEvent({
                            emailType,
                            account,
                            sender: user,
                            receiver: userWithFeatureOn.user,
                            eventPayload: payload,
                            hasMultipleProducts,
                        });
                    }
                } else {
                    for (const auditorMapItem of auditorFramework.auditorFrameworkAuditors) {
                        const auditorUser =
                            // eslint-disable-next-line no-await-in-loop
                            await this.usersCoreService.getUserByEntryId(
                                auditorMapItem.auditorClient.entry.id,
                            );

                        const payload = this.emailEventPayloadBuilder({
                            emailType,
                            message,
                            framework,
                            productName,
                            ...emailPayload,
                            isRecieverAuditor: true,
                            clientId: auditorMapItem.auditorClient.id,
                            client: auditorMapItem.auditorClient,
                        });
                        this.sendAuditHubEmailEvent({
                            emailType,
                            account,
                            sender: user,
                            receiver: auditorUser,
                            eventPayload: payload,
                            hasMultipleProducts,
                        });
                    }
                }
            }
        } catch (error) {
            this.logger.error(
                PolloMessage.msg(error.message)
                    .setError(error)
                    .setDomain(account.domain)
                    .setContext(this.constructor.name)
                    .setSubContext('sendNewAuditMessageEmails'),
            );
            throw error;
        }
    }

    private emailEventPayloadBuilder(
        params: Partial<SendAuditHubEventPayload>,
    ):
        | NewAuditMesssageEventPayload
        | RequestStatusChangeEventPayload
        | RequestStatusChangedBulkPayload {
        const {
            emailType,
            message,
            framework,
            productName,
            statusFrom,
            statusTo,
            totalRequestsUpdateCount,
            auditorFrameworkId,
            isRecieverAuditor,
            clientId,
            client,
        } = params;
        switch (emailType) {
            case AuditHubEmailType.MESSAGE_REQUEST:
                return {
                    message,
                    framework,
                    productName,
                    client,
                } as NewAuditMesssageEventPayload;

            case AuditHubEmailType.STATUS_CHANGE:
                return {
                    statusFrom,
                    statusTo,
                    message,
                    framework,
                    productName,
                    client,
                } as RequestStatusChangeEventPayload;

            case AuditHubEmailType.BULK_STATUS_CHANGE:
                return {
                    isRecieverAuditor,
                    totalRequestsUpdateCount,
                    auditorFrameworkId,
                    clientId,
                } as RequestStatusChangedBulkPayload;
        }
    }

    private sendAuditHubEmailEvent(params: SendAuditHubEmailEventParams): void {
        const { emailType, account, sender, receiver, hasMultipleProducts } = params;
        switch (emailType) {
            case AuditHubEmailType.MESSAGE_REQUEST:
                {
                    const eventPayload = params?.eventPayload;

                    const { message, framework, productName, client } =
                        eventPayload as NewAuditMesssageEventPayload;
                    this._eventBus.publish(
                        new NewAuditMessageEvent(
                            account,
                            sender,
                            receiver,
                            message,
                            framework,
                            productName,
                            hasMultipleProducts,
                            client,
                        ),
                    );
                }
                break;
            case AuditHubEmailType.STATUS_CHANGE:
                {
                    const eventPayload = params?.eventPayload;

                    if (isNil(eventPayload)) {
                        return;
                    }
                    const { message, framework, productName, statusFrom, statusTo, client } =
                        eventPayload as RequestStatusChangeEventPayload;

                    const isRecieverAuditor = hasRole(receiver, [Role.AUDITOR]) && !isNil(client);

                    this._eventBus.publish(
                        new RequestStatusChangedEmailEvent(
                            account,
                            sender,
                            receiver,
                            statusFrom,
                            statusTo,
                            framework,
                            productName,
                            hasMultipleProducts,
                            message,
                            { isRecieverAuditor, clientId: client?.id },
                        ),
                    );
                }
                break;
            case AuditHubEmailType.BULK_STATUS_CHANGE:
                {
                    const eventPayload = params?.eventPayload;

                    if (isNil(eventPayload)) {
                        return;
                    }
                    const payload = eventPayload as RequestStatusChangedBulkPayload;

                    this._eventBus.publish(
                        new RequestStatusChangedBulkEmailEvent(
                            account,
                            sender,
                            receiver,
                            payload.totalRequestsUpdateCount,
                            payload.auditorFrameworkId,
                            payload.frameworkName,
                            payload.productName,
                            hasMultipleProducts,
                            {
                                isRecieverAuditor: payload.isRecieverAuditor,
                                clientId: payload.isRecieverAuditor ? payload.clientId : '',
                            },
                        ),
                    );
                }
                break;
            default:
                break;
        }
    }

    /**
     * Get UserFeatures.User with role of admin/workspace manager and has the feature on
     * @param user User
     * @param account Account
     * @param message Message
     */
    private async getAdminsAndWorkspaceManagersWithFeatureOn(
        account: Account,
        featureType: FeatureType,
        workspaceId: number,
    ): Promise<UserFeature[]> {
        if (isNil(this.userRepository)) {
            throw new NotFoundException(`User Repository was not found for account ${account.id}`);
        }

        if (isNil(this.userFeatureRepository)) {
            throw new NotFoundException(
                `User Feature Repository was not found for account ${account.id}`,
            );
        }
        let nextPage;
        let users: User[] = [];
        do {
            // eslint-disable-next-line no-await-in-loop
            const usersPage = await this.userRepository.getUsersByWorkspace({
                page: 1,
                limit: 50,
                workspaceId,
                roles: [Role.ADMIN, Role.WORKSPACE_ADMINISTRATOR],
            });
            nextPage = usersPage.nextPage;
            users = users.concat(usersPage.data);
        } while (nextPage);

        if (users.length) {
            const usersIds = users.map(u => u.id);
            return this.userFeatureRepository.getUserFeatures(usersIds, featureType);
        } else {
            return [];
        }
    }

    /**
     * @deprecated Use CustomerRequestCoreService.validateUserAccessForAuditorFramework
     * Validate if user should be able to get auditor framework data
     * @param user User
     * @param auditorFrameworkId Auditor framework ID
     */
    async validateUserAccessForAuditorFramework(
        user: User,
        auditorFrameworkId: string,
    ): Promise<void> {
        if (hasRole(user, [Role.AUDITOR])) {
            const auditorFramework = await this.getAuditorFrameworkById(auditorFrameworkId);

            if (isNil(auditorFramework)) {
                throw new NotFoundException(
                    `Auditor framework with ID ${auditorFrameworkId} was not found`,
                );
            }

            const auditorUsers = auditorFramework.auditorFrameworkAuditors
                .filter(item => !isNil(item.auditorClient))
                .map(item => item.auditorClient.entry.id);

            if (!isEmpty(auditorUsers) && !auditorUsers.includes(user.entryId)) {
                throw new ForbiddenException(
                    `${user.email} is not allowed to get data for auditor framework: ${auditorFrameworkId}`,
                    ErrorCode.AUDITOR_CLIENT_FORBIDDEN,
                );
            }
        }
    }

    /**
     * Delete customer request
     * @param account Account
     * @param customerRequestId Customer request ID
     */
    async deleteCustomerRequest(account: Account, customerRequestId: number): Promise<void> {
        try {
            const customerRequest =
                await this.customerRequestRepository.getRequest(customerRequestId);

            await this.customerRequestMessageRepository.softDelete({
                customerRequest: { id: customerRequestId },
            });

            if (isNil(customerRequest)) {
                throw new NotFoundException(
                    `Customer request with ID ${customerRequestId} was not found`,
                );
            }
            await this.customerRequestRepository.softDelete(customerRequestId);
        } catch (error) {
            this.logger.error(
                PolloMessage.msg(error.message)
                    .setError(error)
                    .setDomain(account.domain)
                    .setContext(this.constructor.name)
                    .setSubContext('deleteCustomerRequest'),
            );
            throw error;
        }
    }

    /**
     *
     * @param account
     * @param requestIdList
     */
    @TenancyTransaction()
    async deleteCustomerRequests(
        account: Account,
        user: User,
        requestDto: DeleteCustomerRequestsRequestDto,
    ): Promise<void> {
        try {
            const customerRequests = await this.getSelectedCustomerRequests(
                account,
                user,
                requestDto.requestIdList,
                requestDto,
            );
            if (isEmpty(customerRequests)) {
                return;
            }
            await this.customerRequestMessageRepository.softDelete({
                customerRequest: { id: In(customerRequests.map(request => request.id)) },
            });
            await this.customerRequestRepository.softDelete({
                id: In(customerRequests.map(request => request.id)),
            });
        } catch (error) {
            this.logger.error(
                PolloMessage.msg(error.message)
                    .setError(error)
                    .setDomain(account.domain)
                    .setContext(this.constructor.name)
                    .setSubContext('deleteCustomerRequest'),
            );
            throw error;
        }
    }

    /**
     * @deprecated Use CustomerRequestCoreService.getAuditorFrameworkById
     * Get auditor framework by ID
     * @param auditorFrameworkId Auditor Framework ID
     * @returns Auditor Framework
     */
    // @AUDIT-REFACTOR: TODO rename to getAuditById and auditId
    getAuditorFrameworkById(auditorFrameworkId: string): Promise<Audit> {
        // @AUDIT-REFACTOR: TODO rename to auditRepository and auditId
        return this.auditorFrameworkRepository.findOne({
            where: {
                id: auditorFrameworkId,
            },
            join: {
                alias: 'auditorFramework',
                leftJoinAndSelect: {
                    auditorFrameworkAuditors: 'auditorFramework.auditorFrameworkAuditors',
                    auditorClient: 'auditorFrameworkAuditors.auditorClient',
                    entry: 'auditorClient.entry',
                },
            },
        });
    }

    /**
     * Get framework entity object corresponding to the given auditor framework
     * @param account Account
     * @param auditorFramework Auditor Framework
     * @returns Framework
     */
    private async getFrameworkByAuditorFramework(
        account: Account,
        // @AUDIT-REFACTOR: TODO
        auditorFramework: Audit,
    ): Promise<Framework> {
        const frameworkTag = FrameworkTypeTags.get(auditorFramework.frameworkType);
        return this.frameworksCoreService.getEnabledFrameworkByTag(
            frameworkTag,
            auditorFramework.productId,
            auditorFramework.customFrameworkId,
        );
    }

    /**
     * Publish events for create message in background to avoid blocking response
     */
    private async publishCreateMessageEventsInBackground(
        account: Account,
        user: User,
        customerRequest: CustomerRequest,
        newCustomerRequestMessage: CustomerRequestMessage,
        adaptedMessage: CustomerRequestMessageType,
        customerRequestMessageFiles: CustomerRequestMessageFile[],
        auditorFrameworkPromise: Promise<Audit>,
    ): Promise<void> {
        try {
            const auditorFramework = await auditorFrameworkPromise;
            const framework = await this.getFrameworkByAuditorFramework(account, auditorFramework);

            this._eventBus.publish(
                new AuditHubMessageEvent(account, adaptedMessage, customerRequest.id),
            );

            this._eventBus.publish(
                new RequestCommentCreatedEvent(
                    account,
                    user,
                    customerRequest.description ?? '',
                    customerRequest.id,
                    customerRequest.title,
                    hasRole(user, [Role.AUDITOR])
                        ? RequestMessageSenderType.AUDITOR
                        : RequestMessageSenderType.CUSTOMER,
                    newCustomerRequestMessage.message,
                    framework.name,
                    customerRequestMessageFiles?.map(file => file.name) ?? [],
                ),
            );
        } catch (error) {
            this.logger.error(
                PolloMessage.msg('Failed to publish create message events in background')
                    .setError(error)
                    .setDomain(account.domain)
                    .setContext(this.constructor.name)
                    .setSubContext('publishCreateMessageEventsInBackground'),
            );
            throw error;
        }
    }

    private async publishAuditHubMessageDeletedEvent(
        account: Account,
        user: User,
        customerRequestMessage: CustomerRequestMessage,
    ): Promise<void> {
        const auditorFramework = await this.auditorFrameworkRepository.findOneBy({
            id: customerRequestMessage.customerRequest.auditorFrameworkId,
        });

        const product = await this.workspacesCoreService.getProductById(auditorFramework.productId);
        const framework = await this.getFrameworkByAuditorFramework(account, auditorFramework);

        this._eventBus.publish(
            new AuditHubMessageDeleted(
                account,
                user,
                customerRequestMessage,
                framework,
                product,
                new Date(),
            ),
        );
    }

    private get auditorFrameworkListViewRepository(): Repository<AuditorFrameworkListView> {
        return this.getTenantRepository(AuditorFrameworkListView);
    }
    private get customerRequestMessageRepository(): Repository<CustomerRequestMessage> {
        return this.getTenantRepository(CustomerRequestMessage);
    }
    private get customerRequestMessageFileRepository(): Repository<CustomerRequestMessageFile> {
        return this.getTenantRepository(CustomerRequestMessageFile);
    }
    private get customerRequestMessageReferenceDocumentRepository(): Repository<CustomerRequestMessageReferenceDocument> {
        return this.getTenantRepository(CustomerRequestMessageReferenceDocument);
    }
    private get policyVersionRepository(): Repository<PolicyVersion> {
        return this.getTenantRepository(PolicyVersion);
    }
    private get customerRequestRepository(): CustomerRequestRepository {
        return this.getCustomTenantRepository(CustomerRequestRepository);
    }
    private get customerRequestListViewRepository(): CustomerRequestListViewRepository {
        return this.getCustomTenantRepository(CustomerRequestListViewRepository);
    }
    private get userFeatureRepository(): UserFeatureRepository {
        return this.getCustomTenantRepository(UserFeatureRepository);
    }
    private get userRepository(): UserRepository {
        return this.getCustomTenantRepository(UserRepository);
    }
    private get controlRepository(): ControlRepository {
        return this.getCustomTenantRepository(ControlRepository);
    }
}
