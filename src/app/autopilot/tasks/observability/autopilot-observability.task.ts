/* eslint-disable no-await-in-loop */
import { ApiDataStorage } from 'app/apis/classes/api-data-storage/api-data-storage.class';
import { DatabaseCluster } from 'app/apis/classes/api-data-storage/database-cluster.class';
import { Database } from 'app/apis/classes/api-data-storage/database.class';
import { Queue } from 'app/apis/classes/api-data-storage/queue.class';
import { Table } from 'app/apis/classes/api-data-storage/table.class';
import { Alarm } from 'app/apis/classes/api-data/alarm.class';
import { ApiResponse } from 'app/apis/classes/api/api-response.class';
import { IInfrastructureServices } from 'app/apis/interfaces/infrastructure-services.interface';
import { IObservabilityServices } from 'app/apis/interfaces/observability-services.interface';
import { Data } from 'app/autopilot/classes/data.class';
import { AutopilotTask } from 'app/autopilot/tasks/autopilot.task';
import { AutopilotInfrastructureMonitorTask } from 'app/autopilot/tasks/infrastructure/autopilot-infrastructure-monitor.task';
import { IsEmptyResolver } from 'app/autopilot/tasks/resolvers/is-empty.resolver';
import { ConnectionEntity } from 'app/companies/connections/entities/connection.entity';
import { ControlTestInstanceHistory } from 'app/monitors/entities/control-test-instance-history.entity';
import { Account } from 'auth/entities/account.entity';
import { ApiDataSource } from 'commons/enums/api-data-source.enum';
import { ClientType } from 'commons/enums/auth/client-type.enum';
import { AutopilotMetricNamespace as MetricNamespace } from 'commons/enums/autopilot/autopilot-metric-namespace.enum';
import { AutopilotMetric as Metric } from 'commons/enums/autopilot/autopilot-metric.enum';
import { AutopilotTaskType as TaskType } from 'commons/enums/autopilot/autopilot-task-type.enum';
import { LogIdentifierRecordType } from 'commons/enums/log-identifier-record-type.enum';
import { getAutopilotDependencies } from 'commons/helpers/monitor.helper';
import { forEachTokenPage } from 'commons/helpers/pagination/pagination.helper';
import { TokenPagination } from 'commons/helpers/pagination/token.pagination';
import { isEmpty } from 'lodash';

export class AutopilotObservabilityTask extends AutopilotInfrastructureMonitorTask {
    protected observabilityConnections: ConnectionEntity[];

    /**
     *
     * @param context
     * @param account
     * @param taskId
     * @param testId
     * @param controlTestInstanceHistory
     * @param connection
     */
    constructor(
        context: AutopilotTask<any>,
        account: Account,
        protected readonly taskId: TaskType,
        testId: number,
        controlTestInstanceHistory: ControlTestInstanceHistory,
        connection: ConnectionEntity,
        metric: Metric,
        namespace: MetricNamespace,
        observabilityConnections?: ConnectionEntity[],
    ) {
        super(context, account, taskId, testId, controlTestInstanceHistory);

        this.connection = connection;
        this.metric = metric;
        this.namespace = namespace;
        this.resolver = new IsEmptyResolver<Data>();
        this.observabilityConnections = observabilityConnections;
    }

    /**
     *
     */
    async run(): Promise<void> {
        await this.do(async () => {
            try {
                let infrastructureApi: IInfrastructureServices;
                try {
                    infrastructureApi = await this.context.external.get(
                        this.connection,
                        this.account,
                    );
                } catch (error) {
                    error.message =
                        `Loading Infrastructure API (${ClientType[this.connection.clientType]}) ` +
                        `failed: ${error.message}`;
                    return await Promise.reject(error);
                }

                let observabilityApis: IObservabilityServices[] = [];
                const observabilityClientTypes = (this.observabilityConnections ?? []).map(
                    connection => ClientType[connection.clientType],
                );

                try {
                    const dependencies = getAutopilotDependencies(this.connection);
                    const clientSupportsDependenciesForAPTaskType = dependencies.some(
                        dependency => dependency.taskType === this.taskId,
                    );
                    if (clientSupportsDependenciesForAPTaskType) {
                        observabilityApis = await Promise.all(
                            (this.observabilityConnections ?? []).map(async connection =>
                                this.context.external.get<IObservabilityServices>(
                                    connection,
                                    this.account,
                                ),
                            ),
                        );
                    }
                    this.log(
                        `Loaded ${observabilityApis.length} observability providers for ${
                            ClientType[this.connection.clientType]
                        } task ${this.taskId}`,
                        { observabilityClientTypes },
                    );
                } catch (error) {
                    error.message =
                        `Failure loading Observability APIs ` +
                        `${JSON.stringify(observabilityClientTypes)}: ${error.message}`;
                    return await Promise.reject(error);
                }

                const regions = await infrastructureApi.getRegions();

                for (const region of regions) {
                    // Force refresh credentials at the start of each region to ensure fresh credentials
                    if (infrastructureApi.forceRefreshCredentials) {
                        await infrastructureApi.forceRefreshCredentials();
                    }

                    if (this.namespace === MetricNamespace.DATABASE) {
                        this.log(`Verifying Database Clusters for region`, {
                            region,
                            namespace: MetricNamespace[MetricNamespace.DATABASE_CLUSTER],
                        });
                        await this.verifyDatabaseClusters(
                            region,
                            infrastructureApi,
                            MetricNamespace.DATABASE_CLUSTER,
                            this.metric,
                            observabilityApis,
                        );
                        this.log(`Done Verifying Database Clusters for region`, {
                            region,
                            namespace: MetricNamespace[MetricNamespace.DATABASE_CLUSTER],
                        });

                        this.log(`Verifying Databases for region`, {
                            region,
                            namespace: MetricNamespace[MetricNamespace.DATABASE],
                        });
                        await this.verifyDatabases(
                            region,
                            infrastructureApi,
                            this.namespace,
                            this.metric,
                            observabilityApis,
                        );
                        this.log(`Done Verifying Databases for region`, {
                            region,
                            namespace: MetricNamespace[MetricNamespace.DATABASE],
                        });
                    }

                    if (this.namespace === MetricNamespace.QUEUE) {
                        await this.verifyQueues(
                            region,
                            infrastructureApi,
                            this.namespace,
                            this.metric,
                            observabilityApis,
                        );
                    }

                    if (this.namespace === MetricNamespace.CLOUD_DATA) {
                        await this.verifyCloudData(
                            region,
                            infrastructureApi,
                            this.namespace,
                            this.metric,
                            observabilityApis,
                        );
                    }
                }
            } catch (error) {
                this.onCaughtError(error);
            }

            await this.resolve();
        });
    }

    /**
     *
     * @param region
     * @param infrastructureApi
     * @param metric
     * @returns
     */
    protected async verifyDatabaseClusters(
        region: string,
        infrastructureApi: IInfrastructureServices,
        namespace: MetricNamespace,
        metric: Metric,
        observabilityApis?: IObservabilityServices[],
    ): Promise<void> {
        /**
         * We need to verify if the monitor has been set for ALL instances
         */
        const alarms = await this.getMetricsAlarms(
            [infrastructureApi, ...observabilityApis],
            region,
            metric,
            namespace,
        );

        /**
         * If the alarms have been set for all instances we are done
         */
        if (!isEmpty(alarms)) {
            this.rectify({
                region,
                alarms,
                instance: this.getRegion(region),
            });
        } else {
            this.log(
                `[VerifyDatabaseClusters]: No Metrics Alarms found. Fetching Database Clusters for region`,
                {
                    region,
                    namespace: MetricNamespace[namespace],
                },
            );
            await forEachTokenPage(
                (nextPageToken: string) => {
                    return infrastructureApi.getDatabasesClusters(
                        region,
                        TokenPagination.page(nextPageToken),
                    );
                },
                async (clusters: DatabaseCluster[], response: ApiResponse<any>) => {
                    this.source = response.source;
                    this.logTaskProgress(
                        this.connection,
                        LogIdentifierRecordType.DB_CLUSTER,
                        clusters.length,
                    );
                    await this.rectifyInstances({
                        region,
                        apis: [infrastructureApi, ...observabilityApis],
                        instances: clusters,
                        metric,
                        namespace,
                        isNotImplemented: false,
                        source: this.source,
                    });
                },
            );
        }
    }

    /**
     *
     * @param region
     * @param infrastructureApi
     * @param namespace
     * @returns
     */
    protected async verifyDatabases(
        region: string,
        infrastructureApi: IInfrastructureServices,
        namespace: MetricNamespace,
        metric: Metric,
        observabilityApis?: IObservabilityServices[],
    ): Promise<void> {
        /**
         * We need to verify if the monitor has been set for ALL instances
         */
        const alarms = await this.getMetricsAlarms(
            [infrastructureApi, ...observabilityApis],
            region,
            metric,
            namespace,
        );

        /**
         * If the alarms have been set for all instances we are done
         */
        if (!isEmpty(alarms)) {
            this.rectify({
                region,
                alarms: alarms,
                instance: this.getRegion(region),
            });
        } else {
            this.log(`[VerifyDatabases]: No Alarms found. Fetching Databases for region`, {
                region,
                namespace: MetricNamespace[namespace],
            });
            await forEachTokenPage(
                (nextPageToken: string) => {
                    return infrastructureApi.getDatabases(
                        region,
                        TokenPagination.page(nextPageToken),
                    );
                },
                async (databases: Database[], response: ApiResponse<any>) => {
                    this.source = response.source;
                    this.logTaskProgress(
                        this.connection,
                        LogIdentifierRecordType.DATABASE,
                        databases.length,
                    );
                    await this.rectifyInstances({
                        region,
                        apis: [infrastructureApi, ...observabilityApis],
                        instances: databases,
                        metric,
                        namespace,
                        isNotImplemented: false,
                        source: this.source,
                    });
                },
            );
        }
    }

    /**
     *
     * @param region
     * @param infrastructureApi
     * @param namespace
     * @param metric
     * @returns
     */
    protected async verifyQueues(
        region: string,
        infrastructureApi: IInfrastructureServices,
        namespace: MetricNamespace,
        metric: Metric,
        observabilityApis?: IObservabilityServices[],
    ): Promise<void> {
        await forEachTokenPage(
            (nextPageToken: string) => {
                return infrastructureApi.getQueues(region, TokenPagination.page(nextPageToken));
            },
            async (queues: Queue[], response: ApiResponse<any>) => {
                this.source = response.source;
                this.logTaskProgress(this.connection, LogIdentifierRecordType.QUEUE, queues.length);
                await this.rectifyInstances({
                    region,
                    apis: [infrastructureApi, ...observabilityApis],
                    instances: queues,
                    metric,
                    namespace,
                    isNotImplemented: response.isNotImplemented,
                    source: this.source,
                });
            },
        );
    }

    /**
     *
     * @param region
     * @param infrastructureApi
     * @param namespace
     * @param metric
     * @returns
     */
    protected async verifyCloudData(
        region: string,
        infrastructureApi: IInfrastructureServices,
        namespace: MetricNamespace,
        metric: Metric,
        observabilityApis?: IObservabilityServices[],
    ): Promise<void> {
        /**
         * We need to verify if the monitor has been set for ALL instances
         */
        const alarms = await this.getMetricsAlarms(
            [infrastructureApi, ...observabilityApis],
            region,
            Metric.CLOUD_DATA_CLUSTER_FREE_STORAGE,
            MetricNamespace.CLOUD_DATA_CLUSTER,
        );

        /**
         * If the alarms have been set for all instances we are done
         */
        if (!isEmpty(alarms)) {
            this.rectify({
                region,
                alarms: alarms,
                instance: this.getRegion(region),
            });
        } else {
            await forEachTokenPage(
                (nextPageToken: string) => {
                    return infrastructureApi.getCloudData(
                        region,
                        TokenPagination.page(nextPageToken),
                    );
                },
                async (tables: Table[], response: ApiResponse<any>) => {
                    this.source = response.source;
                    this.logTaskProgress(
                        this.connection,
                        LogIdentifierRecordType.CLOUD_DATA_TABLE,
                        tables.length,
                    );
                    if (!response.isNotImplemented) {
                        await this.rectifyInstances({
                            region,
                            apis: [infrastructureApi, ...observabilityApis],
                            instances: tables,
                            metric,
                            namespace,
                            isNotImplemented: false,
                            source: this.source,
                        });
                    }
                },
            );
        }
    }

    /**
     *
     * @param region
     * @param api
     * @param instances
     * @param metric
     * @param namespace
     * @returns
     */
    protected async rectifyInstances({
        region,
        apis,
        instances,
        metric,
        namespace,
        isNotImplemented,
        source = null,
    }: {
        region: string;
        apis: (IInfrastructureServices | IObservabilityServices)[];
        instances: ApiDataStorage[];
        metric: Metric;
        namespace: MetricNamespace;
        isNotImplemented?: boolean;
        source?: ApiDataSource;
    }): Promise<void> {
        for (const instance of instances) {
            if (instance.isAutoScaling) {
                // Skipping Cloud Free Storage tests because the database is auto scaling
                continue;
            }
            const alarms = await this.getMetricsAlarms(apis, region, metric, namespace, instance);

            this.rectify({
                region,
                alarms,
                instance,
                subInstance: null,
                source,
                isNotImplemented,
            });
        }
    }

    /**
     *
     * @param apis
     * @param region
     * @param metric
     * @param namespace
     * @param instance
     * @returns
     */
    protected async getMetricsAlarms(
        apis: (IInfrastructureServices | IObservabilityServices)[],
        region: string,
        metric: Metric,
        namespace: MetricNamespace,
        instance: ApiDataStorage | null = null,
    ): Promise<Alarm[]> {
        const alarms: Alarm[] = [];

        for (const api of apis) {
            const alarmResponse = await api.getMetricAlarms(
                region,
                metric,
                namespace,
                instance?.dimension,
                null,
                instance?.engine,
                instance,
            );

            alarms.push(...alarmResponse.data);
        }
        this.logTaskProgress(this.connection, LogIdentifierRecordType.METRICS_ALARM, alarms.length);

        if (isEmpty(alarms)) {
            this.log(`No metric alarms were found`, {
                region,
                metric: Metric[metric],
                namespace: MetricNamespace[namespace],
                instance,
            });
        } else {
            this.log(`Loaded ${alarms.length} metric alarms`, {
                region,
                metric: Metric[metric],
                namespace: MetricNamespace[namespace],
                instance,
                alarms,
            });
        }

        return alarms;
    }
}
