/* eslint-disable no-await-in-loop */
import { ApiDataStorage } from 'app/apis/classes/api-data-storage/api-data-storage.class';
import { DatabaseCluster } from 'app/apis/classes/api-data-storage/database-cluster.class';
import { Database } from 'app/apis/classes/api-data-storage/database.class';
import { Table } from 'app/apis/classes/api-data-storage/table.class';
import { Alarm } from 'app/apis/classes/api-data/alarm.class';
import { ApiResponse } from 'app/apis/classes/api/api-response.class';
import { RegionPage } from 'app/apis/classes/aws/api-endpoint-item/region-page.class';
import { IInfrastructureServices } from 'app/apis/interfaces/infrastructure-services.interface';
import { IObservabilityServices } from 'app/apis/interfaces/observability-services.interface';
import { apiDataByRegion } from 'app/autopilot/helper/api-data-by-region.helper';
import { AutopilotObservabilityTask } from 'app/autopilot/tasks/observability/autopilot-observability.task';
import { ApiDataSource } from 'commons/enums/api-data-source.enum';
import { ClientType } from 'commons/enums/auth/client-type.enum';
import { AutopilotMetricNamespace as MetricNamespace } from 'commons/enums/autopilot/autopilot-metric-namespace.enum';
import { AutopilotMetric as Metric } from 'commons/enums/autopilot/autopilot-metric.enum';
import { LogIdentifierRecordType } from 'commons/enums/log-identifier-record-type.enum';
import { getAutopilotDependencies } from 'commons/helpers/monitor.helper';
import { forEachTokenPage } from 'commons/helpers/pagination/pagination.helper';
import { TokenPagination } from 'commons/helpers/pagination/token.pagination';
import { getConcurrentBatchSize } from 'commons/helpers/parallelize/aws-org-units-parallelization.helper';
import { batchIteration } from 'commons/helpers/performance/batch-iteration.helper';
import { isEmpty, isNil } from 'lodash';

export class AutopilotObservabilityParallelizedTask extends AutopilotObservabilityTask {
    private regions: string[];
    private regionBatchSize: number;

    /**
     *
     */
    async run(): Promise<void> {
        await this.do(async () => {
            try {
                let infrastructureApi: IInfrastructureServices;
                try {
                    infrastructureApi = await this.context.external.get(
                        this.connection,
                        this.account,
                    );
                } catch (error) {
                    error.message =
                        `Loading Infrastructure API (${ClientType[this.connection.clientType]}) ` +
                        `failed: ${error.message}`;
                    return await Promise.reject(error);
                }

                let observabilityApis: IObservabilityServices[] = [];
                const observabilityClientTypes = (this.observabilityConnections ?? []).map(
                    connection => ClientType[connection.clientType],
                );

                try {
                    const dependencies = getAutopilotDependencies(this.connection);
                    const clientSupportsDependenciesForAPTaskType = dependencies.some(
                        dependency => dependency.taskType === this.taskId,
                    );
                    if (clientSupportsDependenciesForAPTaskType) {
                        observabilityApis = await Promise.all(
                            (this.observabilityConnections ?? []).map(async connection =>
                                this.context.external.get<IObservabilityServices>(
                                    connection,
                                    this.account,
                                ),
                            ),
                        );
                    }
                    this.log(
                        `Loaded ${observabilityApis.length} observability providers for ${
                            ClientType[this.connection.clientType]
                        } task ${this.taskId}`,
                        { observabilityClientTypes },
                    );
                } catch (error) {
                    error.message =
                        `Failure loading Observability APIs ` +
                        `${JSON.stringify(observabilityClientTypes)}: ${error.message}`;
                    return await Promise.reject(error);
                }

                this.regions = await infrastructureApi.getRegions();

                this.regionBatchSize = getConcurrentBatchSize(
                    this.connection,
                    'aws.orgUnits.regionsSubAccountsBatchSize',
                    this.regions.length,
                );

                this.log('concurrentBatchSize', {
                    concurrentBatchSize: this.regionBatchSize,
                });

                if (this.namespace === MetricNamespace.DATABASE) {
                    await this.verifyDatabaseClusters(
                        null,
                        infrastructureApi,
                        MetricNamespace.DATABASE_CLUSTER,
                        this.metric,
                        observabilityApis,
                    );

                    await this.verifyDatabases(
                        null,
                        infrastructureApi,
                        this.namespace,
                        this.metric,
                        observabilityApis,
                    );
                }

                if (this.namespace === MetricNamespace.QUEUE) {
                    await this.verifyQueues(
                        null,
                        infrastructureApi,
                        this.namespace,
                        this.metric,
                        observabilityApis,
                    );
                }

                if (this.namespace === MetricNamespace.CLOUD_DATA) {
                    await this.verifyCloudData(
                        null,
                        infrastructureApi,
                        this.namespace,
                        this.metric,
                        observabilityApis,
                    );
                }
            } catch (error) {
                this.onCaughtError(error);
            }

            await this.resolve();
        });
    }

    /**
     *
     * @param region
     * @param infrastructureApi
     * @param metric
     * @returns
     */
    protected async verifyDatabaseClusters(
        _region: string,
        infrastructureApi: IInfrastructureServices,
        namespace: MetricNamespace,
        metric: Metric,
        observabilityApis?: IObservabilityServices[],
    ): Promise<void> {
        const regionMetricAlarmsMap = new Map<string, Alarm[]>();
        const rectifiedAlarmRegionsSet = new Set<string>();

        await batchIteration(
            this.regions,
            async region => {
                const alarms = await this.getMetricsAlarms(
                    [infrastructureApi, ...observabilityApis],
                    region,
                    metric,
                    namespace,
                );

                regionMetricAlarmsMap.set(region, alarms);
            },
            this.regionBatchSize,
        );

        await forEachTokenPage(
            (nextPageToken: string) => {
                return infrastructureApi.getDatabasesClusters(
                    null,
                    TokenPagination.page(nextPageToken),
                );
            },
            async (clusters: DatabaseCluster[], response: ApiResponse<any>) => {
                // Force refresh credentials at the start of each page to ensure fresh credentials
                if (infrastructureApi.forceRefreshCredentials) {
                    await infrastructureApi.forceRefreshCredentials();
                }

                const clustersByRegion = apiDataByRegion(clusters, this.regions);

                for await (const region of this.regions) {
                    /**
                     * We need to verify if the monitor has been set for ALL instances
                     */
                    const alarms = regionMetricAlarmsMap.get(region);

                    /**
                     * If the alarms have been set for all instances we are done
                     */
                    if (!isEmpty(alarms)) {
                        const hasBeenRectified = rectifiedAlarmRegionsSet.has(region);

                        if (!hasBeenRectified) {
                            this.rectify({
                                region,
                                alarms,
                                instance: this.getRegion(region),
                            });
                            rectifiedAlarmRegionsSet.add(region);
                        }
                    } else {
                        const regionClusters = clustersByRegion[region] ?? [];
                        this.source = response.source;
                        this.logTaskProgress(
                            this.connection,
                            LogIdentifierRecordType.DB_CLUSTER,
                            regionClusters.length,
                        );

                        await this.rectifyInstances({
                            region,
                            apis: [infrastructureApi, ...observabilityApis],
                            instances: regionClusters,
                            metric,
                            namespace,
                            isNotImplemented: false,
                            source: this.source,
                        });
                    }
                }
            },
        );
    }

    /**
     *
     * @param region
     * @param infrastructureApi
     * @param namespace
     * @returns
     */
    protected async verifyDatabases(
        _region: string,
        infrastructureApi: IInfrastructureServices,
        namespace: MetricNamespace,
        metric: Metric,
        observabilityApis?: IObservabilityServices[],
    ): Promise<void> {
        const regionMetricAlarmsMap = new Map<string, Alarm[]>();
        const rectifiedAlarmRegionsSet = new Set<string>();

        await batchIteration(
            this.regions,
            async region => {
                const alarms = await this.getMetricsAlarms(
                    [infrastructureApi, ...observabilityApis],
                    region,
                    metric,
                    namespace,
                );

                regionMetricAlarmsMap.set(region, alarms);
            },
            this.regionBatchSize,
        );

        await forEachTokenPage(
            (nextPageToken: string) => {
                return infrastructureApi.getDatabases(null, TokenPagination.page(nextPageToken));
            },
            async (databases: Database[], response: ApiResponse<any>) => {
                // Force refresh credentials at the start of each page to ensure fresh credentials
                if (infrastructureApi.forceRefreshCredentials) {
                    await infrastructureApi.forceRefreshCredentials();
                }

                const databasesByRegion = apiDataByRegion(databases, this.regions);

                for await (const region of this.regions) {
                    /**
                     * We need to verify if the monitor has been set for ALL instances
                     */
                    const alarms = regionMetricAlarmsMap.get(region);

                    /**
                     * If the alarms have been set for all instances we are done
                     */
                    if (!isEmpty(alarms)) {
                        const hasBeenRectified = rectifiedAlarmRegionsSet.has(region);

                        if (!hasBeenRectified) {
                            this.rectify({
                                region,
                                alarms,
                                instance: this.getRegion(region),
                            });
                            rectifiedAlarmRegionsSet.add(region);
                        }
                    } else {
                        const regionDatabases = databasesByRegion[region] ?? [];
                        this.source = response.source;
                        this.logTaskProgress(
                            this.connection,
                            LogIdentifierRecordType.DATABASE,
                            regionDatabases.length,
                        );

                        await this.rectifyInstances({
                            region,
                            apis: [infrastructureApi, ...observabilityApis],
                            instances: regionDatabases,
                            metric,
                            namespace,
                            isNotImplemented: false,
                            source: this.source,
                        });
                    }
                }
            },
        );
    }

    /**
     *
     * @param region
     * @param infrastructureApi
     * @param namespace
     * @param metric
     * @returns
     */
    protected async verifyCloudData(
        _region: string,
        infrastructureApi: IInfrastructureServices,
        namespace: MetricNamespace,
        metric: Metric,
        observabilityApis?: IObservabilityServices[],
    ): Promise<void> {
        const regionMetricAlarmsMap = new Map<string, Alarm[]>();
        const rectifiedAlarmRegionsSet = new Set<string>();

        await batchIteration(
            this.regions,
            async region => {
                const alarms = await this.getMetricsAlarms(
                    [infrastructureApi, ...observabilityApis],
                    region,
                    Metric.CLOUD_DATA_CLUSTER_FREE_STORAGE,
                    MetricNamespace.CLOUD_DATA_CLUSTER,
                );

                regionMetricAlarmsMap.set(region, alarms);
            },
            this.regionBatchSize,
        );

        await forEachTokenPage(
            (nextPageToken: string) => {
                return infrastructureApi.getCloudData(null, TokenPagination.page(nextPageToken));
            },
            async (tables: Table[], response: ApiResponse<any>) => {
                // Force refresh credentials at the start of each page to ensure fresh credentials
                if (infrastructureApi.forceRefreshCredentials) {
                    await infrastructureApi.forceRefreshCredentials();
                }

                const tablesByRegion = apiDataByRegion(tables, this.regions);

                for await (const region of this.regions) {
                    /**
                     * We need to verify if the monitor has been set for ALL instances
                     */
                    const alarms = regionMetricAlarmsMap.get(region);

                    /**
                     * If the alarms have been set for all instances we are done
                     */
                    if (!isEmpty(alarms)) {
                        const hasBeenRectified = rectifiedAlarmRegionsSet.has(region);

                        if (!hasBeenRectified) {
                            this.rectify({
                                region,
                                alarms,
                                instance: this.getRegion(region),
                            });
                            rectifiedAlarmRegionsSet.add(region);
                        }
                    } else {
                        const regionTables = tablesByRegion[region] ?? [];
                        this.source = response.source;
                        this.logTaskProgress(
                            this.connection,
                            LogIdentifierRecordType.CLOUD_DATA_TABLE,
                            regionTables.length,
                        );

                        if (!response.isNotImplemented) {
                            await this.rectifyInstances({
                                region,
                                apis: [infrastructureApi, ...observabilityApis],
                                instances: regionTables,
                                metric,
                                namespace,
                                isNotImplemented: false,
                                source: this.source,
                            });
                        }
                    }
                }
            },
        );
    }

    /**
     *
     * @param region
     * @param api
     * @param instances
     * @param metric
     * @param namespace
     * @returns
     */
    protected async rectifyInstances({
        region,
        apis,
        instances,
        metric,
        namespace,
        isNotImplemented,
        source = null,
    }: {
        region: string;
        apis: (IInfrastructureServices | IObservabilityServices)[];
        instances: ApiDataStorage[];
        metric: Metric;
        namespace: MetricNamespace;
        isNotImplemented?: boolean;
        source?: ApiDataSource;
    }): Promise<void> {
        for (const instance of instances) {
            if (instance.isAutoScaling) {
                // Skipping Cloud Free Storage tests because the database is auto scaling
                continue;
            }

            const strRegion = isNil(region)
                ? RegionPage.stringify(
                      instance.accountId,
                      instance.region,
                      instance.accountName,
                      instance.organizationalUnitName,
                      instance.resourceOrganizationPath,
                      instance.organizationalUnitId,
                  )
                : region;

            const alarms = await this.getMetricsAlarms(
                apis,
                strRegion,
                metric,
                namespace,
                instance,
            );

            this.rectify({
                region: strRegion,
                alarms,
                instance,
                subInstance: null,
                source,
                isNotImplemented,
            });
        }
    }
}
