import { ApiDataStorage } from 'app/apis/classes/api-data-storage/api-data-storage.class';
import { IInfrastructureServices } from 'app/apis/interfaces/infrastructure-services.interface';
import { Data } from 'app/autopilot/classes/data.class';
import { TaskMessage as Message } from 'app/autopilot/classes/task-message.class';
import { MonitorDataType } from 'app/autopilot/enums/monitor-data-type.enum';
import { AutopilotTask } from 'app/autopilot/tasks/autopilot.task';
import { AutopilotInfrastructureTask } from 'app/autopilot/tasks/infrastructure/autopilot-infrastructure.task';
import { IsEmptyResolver } from 'app/autopilot/tasks/resolvers/is-empty.resolver';
import { ConnectionEntity } from 'app/companies/connections/entities/connection.entity';
import { ControlTestInstanceHistory } from 'app/monitors/entities/control-test-instance-history.entity';
import { Account } from 'auth/entities/account.entity';
import { ClientType } from 'commons/enums/auth/client-type.enum';
import { AutopilotTaskType as TaskType } from 'commons/enums/autopilot/autopilot-task-type.enum';
import { isEmpty } from 'lodash';

export class AutopilotInfrastructureDataBackupsTask extends AutopilotInfrastructureTask {
    /**
     *
     * @param context
     * @param account
     * @param taskId
     * @param testId
     * @param controlTestInstanceHistory
     * @param connection
     */
    constructor(
        context: AutopilotTask<AutopilotInfrastructureTask>,
        account: Account,
        taskId: TaskType,
        testId: number,
        controlTestInstanceHistory: ControlTestInstanceHistory,
        connection: ConnectionEntity,
    ) {
        super(context, account, taskId, testId, controlTestInstanceHistory);
        this.connection = connection;
        this.resolver = new IsEmptyResolver<Data>();
    }

    /**
     *
     */
    async run(): Promise<void> {
        await this.do(async () => {
            try {
                const api = await this.context.external.get<IInfrastructureServices>(
                    this.connection,
                    this.account,
                );

                const regions = await api.getRegions();

                for (const region of regions) {
                    // Force refresh credentials at the start of each region to ensure fresh credentials
                    if (api.forceRefreshCredentials) {
                        // eslint-disable-next-line no-await-in-loop
                        await api.forceRefreshCredentials();
                    }

                    /**
                     * We have to get the clusters first - as we might be required to pass the nodes
                     * that are covered under the cluster configuration ... so be careful ...
                     */
                    // eslint-disable-next-line no-await-in-loop
                    await this.verifyDatabaseClusters(api, region);
                    // eslint-disable-next-line no-await-in-loop
                    await this.verifyDatabases(api, region);

                    this.verifyEmptyEvidence();
                }
            } catch (error) {
                this.onCaughtError(error);
            }

            await this.resolve();
        });
    }

    /**
     *
     */
    protected getFailMessage(): string {
        return Message.dataBackedUp();
    }

    /**
     *
     */
    protected getMonitorDataType(): MonitorDataType {
        return MonitorDataType.LIST;
    }

    /**
     *
     * @param data
     */
    protected getRectification(data: ApiDataStorage): boolean {
        return data.isBackedUp || !isEmpty(data.exclusion);
    }

    /**
     * verify if passing array is empty to add a message
     */
    protected verifyEmptyEvidence(): void {
        if (
            this.connection.clientType === ClientType.AZURE &&
            isEmpty(this.results.fail) &&
            isEmpty(this.results.pass)
        ) {
            this.results.pass.push({
                results: 'All managed databases are backed up by default within Azure',
            });
        }
    }
}
