/* eslint-disable no-await-in-loop */
import { isEmpty, isNil } from '@nestjs/common/utils/shared.utils';
import { Rule } from 'app/apis/classes/api-data/rule.class';
import { Waffable } from 'app/apis/classes/api-data/waffable-data.class';
import { ApiResponse } from 'app/apis/classes/api/api-response.class';
import { IInfrastructureServices } from 'app/apis/interfaces/infrastructure-services.interface';
import { Api } from 'app/apis/providers/api';
import { Data } from 'app/autopilot/classes/data.class';
import { TaskMessage as Message } from 'app/autopilot/classes/task-message.class';
import { MonitorDataType } from 'app/autopilot/enums/monitor-data-type.enum';
import { createApiGatewayArn } from 'app/autopilot/helper/arn.helper';
import { parseRegionString } from 'app/autopilot/helper/parse-region-string';
import { AutopilotAccountTask as AccountTask } from 'app/autopilot/tasks/autopilot-account.task';
import { AutopilotTask } from 'app/autopilot/tasks/autopilot.task';
import { NoFailureResolver } from 'app/autopilot/tasks/resolvers/no-failure.resolver';
import { ConnectionEntity } from 'app/companies/connections/entities/connection.entity';
import { ControlTestInstanceHistory } from 'app/monitors/entities/control-test-instance-history.entity';
import { Account } from 'auth/entities/account.entity';
import { AutopilotTaskType as TaskType } from 'commons/enums/autopilot/autopilot-task-type.enum';
import { LogIdentifierRecordType } from 'commons/enums/log-identifier-record-type.enum';
import { forEachTokenPage } from 'commons/helpers/pagination/pagination.helper';
import { TokenPagination } from 'commons/helpers/pagination/token.pagination';
import { PolloMessage } from 'pollo-logger/classes/pollo.message';

type RuleResources = {
    rule: Rule;
    resources: string[];
};

type RegionalRules = {
    region: string;
    resources: Waffable[];
    ruleResources: RuleResources[];
    isNotImplemented: boolean;
};
export class AutopilotInfrastructureHasWafTask extends AccountTask<any> {
    /**
     *
     */
    private rules: RegionalRules[] = [];

    /**
     *
     * @param context
     * @param account
     * @param taskId
     * @param testId
     * @param controlTestInstanceHistory
     * @param connection
     */
    constructor(
        context: AutopilotTask<any>,
        account: Account,
        taskId: TaskType,
        testId: number,
        controlTestInstanceHistory: ControlTestInstanceHistory,
        connection: ConnectionEntity,
    ) {
        super(context, account, taskId, testId, controlTestInstanceHistory);

        this.connection = connection;
        this.resolver = new NoFailureResolver<Data>();
    }

    /**
     *
     */
    async run(): Promise<void> {
        await this.do(async () => {
            try {
                const api = await this.context.external.get<IInfrastructureServices>(
                    this.connection,
                    this.account,
                );

                const regions = await api.getRegions(false);

                for (const region of regions) {
                    // Force refresh credentials at the start of each region to ensure fresh credentials
                    if (api.forceRefreshCredentials) {
                        await api.forceRefreshCredentials();
                    }

                    await forEachTokenPage(
                        (nextPageToken: string) => {
                            return api.getWAFResources(region, TokenPagination.page(nextPageToken));
                        },
                        async (resources: Waffable[], response: ApiResponse<any>) => {
                            /**
                             * https://drata.atlassian.net/browse/ENG-32512
                             */
                            this.context.logger.log(
                                PolloMessage.msg(
                                    'running autopilot-infrastructure-has-waf.task:pipe',
                                )
                                    .setContext(this.constructor.name)
                                    .setSubContext(this.run.name)
                                    .setDomain(this.account.domain)
                                    .setIdentifier({
                                        resources,
                                        resourcesCount: resources.length,
                                        response,
                                    }),
                            );
                            this.source = response.source;
                            this.logTaskProgress(
                                this.connection,
                                LogIdentifierRecordType.RULE_WAF_RESOURCE,
                                resources.length,
                            );
                            await this.onGotResources(api, region, resources, response);
                        },
                    );
                }

                await forEachTokenPage(
                    (nextPageToken: string) => {
                        return api.getGlobalWAFResources(TokenPagination.page(nextPageToken));
                    },
                    async (resources: Waffable[], response: ApiResponse<any>) => {
                        this.source = response.source;
                        this.logTaskProgress(
                            this.connection,
                            LogIdentifierRecordType.GLOBAL_WAF_RESOURCE,
                            resources.length,
                        );

                        await this.onGotResources(
                            api,
                            api.getGlobalRegion(response),
                            resources,
                            response,
                        );
                    },
                );

                this.onComplete(api);
            } catch (error) {
                /**
                 * https://drata.atlassian.net/browse/ENG-32512
                 */
                this.logErrorLevelBasedOnSeverity(
                    error,
                    PolloMessage.msg('error running autopilot-infrastructure-has-waf.task')
                        .setContext(this.constructor.name)
                        .setSubContext(this.run.name)
                        .setDomain(this.account.domain)
                        .setError(error),
                );
                this.onCaughtError(error);
            }

            await this.resolve();
        });
    }

    /**
     *
     */
    protected getFailMessage(): string {
        return Message.hasWaf();
    }

    /**
     *
     */
    protected getMonitorDataType(): MonitorDataType {
        return MonitorDataType.LIST;
    }

    /**
     *
     * @param api
     * @param region
     * @param resources
     * @param response
     * @returns
     */
    private async onGotResources(
        api: IInfrastructureServices,
        region: string,
        resources: Waffable[],
        response: ApiResponse<any>,
    ): Promise<void> {
        if (!isEmpty(resources)) {
            const regionalRule = this.rules.find((regional: RegionalRules) => {
                return regional.region === region;
            });

            if (!isNil(regionalRule)) {
                regionalRule.resources = regionalRule.resources.concat(resources);
            } else {
                this.rules.push({
                    region,
                    resources,
                    ruleResources: [],
                    isNotImplemented: response.isNotImplemented,
                });
            }

            await this.verifyAclsV2(api, region);
        }
    }

    /**
     *
     * @param api
     * @param region
     */
    private async verifyAclsV2(api: IInfrastructureServices, region: string): Promise<void> {
        await forEachTokenPage(
            (nextPageToken: string) => {
                return api.getWebApplicationFirewallAclsV2(
                    region,
                    TokenPagination.page(nextPageToken),
                );
            },
            async (rules: Rule[], response: ApiResponse<any>) => {
                /**
                 * https://drata.atlassian.net/browse/ENG-32512
                 */
                this.context.logger.log(
                    PolloMessage.msg('getting acls rules')
                        .setContext(this.constructor.name)
                        .setSubContext(this.verifyAclsV2.name)
                        .setDomain(this.account.domain)
                        .setIdentifier({
                            region,
                            rules,
                        }),
                );
                this.source = response.source;
                this.logTaskProgress(this.connection, LogIdentifierRecordType.ACL, rules.length);
                return this.onGotRules(api, region, rules);
            },
        );
    }

    /**
     *
     * @param api
     * @param region
     * @param rules
     * @returns
     */
    private async onGotRules(
        api: IInfrastructureServices,
        region: string,
        rules: Rule[],
    ): Promise<void> {
        for (const rule of rules) {
            await forEachTokenPage(
                // eslint-disable-next-line @typescript-eslint/no-unused-vars
                (nextPageToken: string) => {
                    return api.getResourcesByWAF(region, rule);
                },
                async (resources: Waffable[], response: ApiResponse<any>) => {
                    this.source = response.source;
                    this.logTaskProgress(
                        this.connection,
                        LogIdentifierRecordType.RULE_WAF_RESOURCE,
                        resources.length,
                    );
                    /**
                     * https://drata.atlassian.net/browse/ENG-32512
                     */
                    this.context.logger.log(
                        PolloMessage.msg('getting resources by waf')
                            .setContext(this.constructor.name)
                            .setSubContext(this.run.name)
                            .setDomain(this.account.domain)
                            .setIdentifier({
                                region,
                                rule,
                                resources,
                                source: this.source,
                                response: response.raw,
                                data: response.data,
                            }),
                    );

                    const regionalRule = this.rules.find((regional: RegionalRules) => {
                        return regional.region === region;
                    });

                    if (!isNil(regionalRule)) {
                        regionalRule.ruleResources.push({
                            rule,
                            resources: resources.map((resource: Waffable) => {
                                return resource.id;
                            }),
                        });
                    }
                },
            );
        }
    }

    /**
     *
     * @param api
     */
    private onComplete(api: IInfrastructureServices): void {
        for (const rule of this.rules) {
            /**
             * Verify if the rule is implemented
             */
            if (!rule.isNotImplemented) {
                /**
                 * Does the rule have any resources?
                 */
                if (!isEmpty(rule.resources)) {
                    /**
                     * The rule has resources - verify that the resource is contained within
                     * one of the rule resources and that it is attached to an ACL here.
                     */
                    for (const resource of rule.resources) {
                        let displayName = null;
                        if (api instanceof Api) {
                            displayName = this.toClazz(api).toWafDisplay(rule, resource);
                        }
                        const isAPIGateway = this.isAPIGateway(resource.dimension);
                        const { region } = parseRegionString(rule.region);
                        const data = {
                            id: resource.id,
                            name: resource.name,
                            displayName,
                            description: resource.description,
                            isAPIGateway,
                            dimension: resource.dimension,
                            region,
                            accountId: resource.accountId,
                            accountName: resource.accountName,
                            organizationalUnitId: resource.organizationalUnitId,
                            organizationalUnitName: resource.organizationalUnitName,
                            resourceOrganizationPath: resource.resourceOrganizationPath,
                            raw: null,
                            resourceArn: isAPIGateway
                                ? createApiGatewayArn(
                                      region,
                                      resource.id,
                                      this.connection.clientType,
                                  )
                                : resource.raw.ARN ||
                                  resource.raw.LoadBalancerArn ||
                                  resource.raw.arn,
                        };

                        const ruleResources = this.getRuleResources(rule, resource);

                        /**
                         * If the ruleResources is null - the resource is not covered
                         */
                        if (!isNil(ruleResources)) {
                            data.raw = ruleResources;
                        }

                        /**
                         * We may get the WAF info from the provider
                         */
                        if (resource.hasWaf) {
                            data.raw = resource.raw;
                        }

                        if (!isNil(data.raw)) {
                            this.results.pass.push(data);
                        } else {
                            this.results.fail.push(data);
                        }
                    }
                }
            }
        }
    }

    /**
     *
     * @param rule
     * @param resource
     */
    private getRuleResources(rule: RegionalRules, resource: Rule): any {
        for (const ruleResource of rule.ruleResources) {
            if (ruleResource.resources.includes(resource.dimension)) {
                return ruleResource;
            }
        }

        return null;
    }

    private isAPIGateway(dimension: string): boolean {
        return !dimension.includes('arn:aws');
    }
}
