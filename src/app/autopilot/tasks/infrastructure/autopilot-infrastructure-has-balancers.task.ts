import { <PERSON>ad<PERSON>alancer } from 'app/apis/classes/api-data/load-balancer.class';
import { ApiData } from 'app/apis/classes/api/api-data.class';
import { ApiResponse } from 'app/apis/classes/api/api-response.class';
import { IInfrastructureServices } from 'app/apis/interfaces/infrastructure-services.interface';
import { Data } from 'app/autopilot/classes/data.class';
import { TaskMessage as Message } from 'app/autopilot/classes/task-message.class';
import { MonitorDataType } from 'app/autopilot/enums/monitor-data-type.enum';
import { createClassicLoadBalancerArn } from 'app/autopilot/helper/elb-v1-arn.helper';
import { parseRegionString } from 'app/autopilot/helper/parse-region-string';
import { AutopilotAccountTask as AccountTask } from 'app/autopilot/tasks/autopilot-account.task';
import { AutopilotTask } from 'app/autopilot/tasks/autopilot.task';
import { NoFailureResolver } from 'app/autopilot/tasks/resolvers/no-failure.resolver';
import { ConnectionEntity } from 'app/companies/connections/entities/connection.entity';
import { ControlTestInstanceHistory } from 'app/monitors/entities/control-test-instance-history.entity';
import { Account } from 'auth/entities/account.entity';
import { isJSON } from 'class-validator';
import { AutopilotTaskType as TaskType } from 'commons/enums/autopilot/autopilot-task-type.enum';
import { LogIdentifierRecordType } from 'commons/enums/log-identifier-record-type.enum';
import { resolveRegionDisplay } from 'commons/helpers/autopilot-test.helper';
import { forEachTokenPage } from 'commons/helpers/pagination/pagination.helper';
import { TokenPagination } from 'commons/helpers/pagination/token.pagination';
import { get, isEmpty, isNil } from 'lodash';

type RegionLoadBalancers = {
    region: string;
    balancers: LoadBalancer[];
    isNotImplemented: boolean;
};
export class AutopilotInfrastructureHasBalancersTask extends AccountTask<any> {
    /**
     *
     */
    private balancers: RegionLoadBalancers[] = [];

    /**
     *
     * @param context
     * @param account
     * @param taskId
     * @param testId
     * @param controlTestInstanceHistory
     * @param connection
     */
    constructor(
        context: AutopilotTask<any>,
        account: Account,
        taskId: TaskType,
        testId: number,
        controlTestInstanceHistory: ControlTestInstanceHistory,
        connection: ConnectionEntity,
    ) {
        super(context, account, taskId, testId, controlTestInstanceHistory);

        this.connection = connection;
        this.resolver = new NoFailureResolver<Data>();
    }

    /**
     *
     */
    async run(): Promise<void> {
        await this.do(async () => {
            try {
                const api = await this.context.external.get<IInfrastructureServices>(
                    this.connection,
                    this.account,
                );

                const regions = await api.getRegions();

                for (const region of regions) {
                    // Force refresh credentials at the start of each region to ensure fresh credentials
                    if (api.forceRefreshCredentials) {
                        // eslint-disable-next-line no-await-in-loop
                        await api.forceRefreshCredentials();
                    }

                    /**
                     * Check if we have load balancer resources.
                     */
                    // eslint-disable-next-line no-await-in-loop
                    if (await api.hasLoadBalancerResources(region)) {
                        // eslint-disable-next-line no-await-in-loop
                        await this.getLoadBalancersV1(region, api);
                        // eslint-disable-next-line no-await-in-loop
                        await this.getLoadBalancersV2(region, api);
                    }
                }

                this.onComplete();
            } catch (error) {
                this.onCaughtError(error);
            }

            await this.resolve();
        });
    }

    /**
     *
     */
    protected getFailMessage(): string {
        return Message.hasBalancers();
    }

    /**
     *
     */
    protected getMonitorDataType(): MonitorDataType {
        return MonitorDataType.LIST;
    }

    /**
     *
     * @param region
     * @param api
     */
    private async getLoadBalancersV1(region: string, api: any): Promise<void> {
        await forEachTokenPage(
            (nextPageToken: string) => {
                return api.getLoadBalancersV1(region, TokenPagination.page(nextPageToken));
            },
            async (data: LoadBalancer[], response: ApiResponse<any>) => {
                this.logTaskProgress(
                    this.connection,
                    LogIdentifierRecordType.LOAD_BALANCER,
                    data.length,
                );
                return this.onGotLoadBalancers(region, data, response);
            },
        );
    }

    /**
     *
     * @param region
     * @param api
     */
    private async getLoadBalancersV2(region: string, api: any): Promise<void> {
        await forEachTokenPage(
            (nextPageToken: string) => {
                return api.getLoadBalancersV2(region, TokenPagination.page(nextPageToken));
            },
            async (data: LoadBalancer[], response: ApiResponse<any>) => {
                this.logTaskProgress(
                    this.connection,
                    LogIdentifierRecordType.LOAD_BALANCER,
                    data.length,
                );
                return this.onGotLoadBalancers(region, data, response);
            },
        );
    }

    /**
     *
     * @param data
     * @param response
     */
    private onGotLoadBalancers(region: string, data: LoadBalancer[], response: ApiResponse<any>) {
        this.source = response.source;

        const loadBalancers = data.map((loadBalancer: LoadBalancer) => {
            const resourceArn =
                loadBalancer.type === 'classic'
                    ? createClassicLoadBalancerArn(
                          { ...loadBalancer.raw, region, accountId: loadBalancer.accountId },
                          this.connection.clientType,
                      )
                    : loadBalancer.raw?.LoadBalancerArn;
            return {
                id: loadBalancer.id,
                name: loadBalancer.name,
                displayName: ApiData.getDisplayName(loadBalancer),
                description: loadBalancer.description,
                region: loadBalancer.region,
                availabilityZones: loadBalancer.availabilityZones,
                accountId: loadBalancer.accountId,
                accountName: loadBalancer.accountName,
                organizationalUnitId: loadBalancer.organizationalUnitId,
                organizationalUnitName: loadBalancer.organizationalUnitName,
                resourceOrganizationPath: loadBalancer.resourceOrganizationPath,
                raw: loadBalancer.raw,
                ...(resourceArn && { resourceArn }),
            };
        });

        const regionBalancer = this.balancers.find((regional: RegionLoadBalancers) => {
            return regional.region === region;
        });

        if (!isNil(regionBalancer)) {
            regionBalancer.balancers = regionBalancer.balancers.concat(loadBalancers);
        } else {
            this.balancers.push({
                region,
                balancers: loadBalancers,
                isNotImplemented: response.isNotImplemented,
            });
        }

        return Promise.resolve();
    }

    /**
     *
     */
    private onComplete(): void {
        for (const balancer of this.balancers) {
            /**
             * Check if the load balancer is implemented
             */
            if (!balancer.isNotImplemented) {
                /**
                 * We have a region with resources and no balancers
                 */
                if (isEmpty(balancer.balancers)) {
                    const {
                        id,
                        name,
                        description,
                        displayName,
                        raw,
                        region,
                        accountName,
                        organizationalUnitName,
                        resourceOrganizationPath,
                        organizationalUnitId,
                    } = this.parseBalancerRegion(balancer.region);

                    this.results.fail.push({
                        id,
                        name,
                        description,
                        region,
                        displayName,
                        accountId: this.connection.clientId,
                        raw,
                        accountName,
                        organizationalUnitName,
                        resourceOrganizationPath,
                        organizationalUnitId,
                    });
                } else {
                    for (const loadBalancer of balancer.balancers) {
                        let regionDisplay = resolveRegionDisplay(loadBalancer.region);
                        if (isNil(regionDisplay)) {
                            regionDisplay = loadBalancer.region;
                        }

                        /**
                         * Get the availablity zones
                         */
                        const zones = get(loadBalancer, 'availabilityZones', []);

                        if (zones.length > 1) {
                            /**
                             * Pass with multi AZ
                             */
                            this.results.pass.push({
                                id: loadBalancer.id,
                                name: loadBalancer.name,
                                displayName: ApiData.getDisplayName(loadBalancer),
                                description: loadBalancer.description,
                                region: parseRegionString(loadBalancer.region).region,
                                accountName: loadBalancer.accountName,
                                organizationalUnitName: loadBalancer.organizationalUnitName,
                                resourceOrganizationPath: loadBalancer.resourceOrganizationPath,
                                organizationalUnitId: loadBalancer.organizationalUnitId,
                                raw: loadBalancer.raw,
                                resourceArn: loadBalancer.resourceArn,
                            });
                        } else {
                            /**
                             * Fail with only one or no zones
                             */
                            this.results.fail.push({
                                id: loadBalancer.id,
                                name: loadBalancer.name,
                                displayName: ApiData.getDisplayName(loadBalancer),
                                description: loadBalancer.description,
                                region: parseRegionString(loadBalancer.region).region,
                                accountId: loadBalancer.accountId,
                                accountName: loadBalancer.accountName,
                                organizationalUnitName: loadBalancer.organizationalUnitName,
                                resourceOrganizationPath: loadBalancer.resourceOrganizationPath,
                                organizationalUnitId: loadBalancer.organizationalUnitId,
                                raw: loadBalancer.raw,
                                resourceArn: loadBalancer.resourceArn,
                            });
                        }
                    }
                }
            }
        }
    }

    private parseBalancerRegion(balancerRegion: string): Data {
        const {
            region,
            accountName,
            organizationalUnitName,
            resourceOrganizationPath,
            organizationalUnitId,
        } = parseRegionString(balancerRegion);

        let displayName = region;

        if (isJSON(balancerRegion)) {
            displayName = ApiData.getDisplayName({
                id: balancerRegion,
                name: region,
                raw: region,
                region,
                accountName,
                organizationalUnitName,
                resourceOrganizationPath,
                organizationalUnitId,
            });
        }

        return {
            id: balancerRegion,
            name: region,
            description: balancerRegion,
            region,
            displayName: displayName,
            raw: balancerRegion,
            accountName,
            organizationalUnitName,
            resourceOrganizationPath,
            organizationalUnitId,
        };
    }
}
