/* eslint-disable no-await-in-loop */
import { ApiDataStorage } from 'app/apis/classes/api-data-storage/api-data-storage.class';
import { Cache } from 'app/apis/classes/api-data-storage/cache.class';
import { DatabaseCluster } from 'app/apis/classes/api-data-storage/database-cluster.class';
import { Database } from 'app/apis/classes/api-data-storage/database.class';
import { ApiData } from 'app/apis/classes/api/api-data.class';
import { ApiResponse } from 'app/apis/classes/api/api-response.class';
import { IInfrastructureServices } from 'app/apis/interfaces/infrastructure-services.interface';
import { AwsConstants } from 'app/apis/services/aws/aws.constants';
import { createESDomainArn } from 'app/autopilot/helper/arn.helper';
import { AutopilotAccountTask } from 'app/autopilot/tasks/autopilot-account.task';
import { ApiDataSource } from 'commons/enums/api-data-source.enum';
import { LogIdentifierRecordType } from 'commons/enums/log-identifier-record-type.enum';
import { forEachTokenPage } from 'commons/helpers/pagination/pagination.helper';
import { TokenPagination } from 'commons/helpers/pagination/token.pagination';
import config from 'config';
import { has, isNil } from 'lodash';

export abstract class AutopilotInfrastructureTask extends AutopilotAccountTask<any> {
    /**
     *
     */
    protected clusters = [];

    /**
     *
     */
    async run(): Promise<void> {
        await this.do(async () => {
            try {
                const api = await this.context.external.get<IInfrastructureServices>(
                    this.connection,
                    this.account,
                );

                const regions = await api.getRegions();

                for (const region of regions) {
                    // Force refresh credentials at the start of each region to ensure fresh credentials
                    // This helps with long-running tasks that process many regions
                    if (api.forceRefreshCredentials) {
                        await api.forceRefreshCredentials();
                    }

                    /**
                     * Check the database clusters first
                     */
                    await this.verifyDatabaseClusters(api, region);
                    await this.verifyDatabases(api, region);
                    await this.verifyCloudData(api, region);
                    await this.verifyIndexers(api, region);

                    // Emulated local resources do not support this resource type
                    if (!config.get('aws.localAws.enabled')) {
                        await this.verifyCaches(api, region);
                    }
                }
            } catch (error) {
                this.onCaughtError(error);
            }

            await this.resolve();
        });
    }

    /**
     *
     * @param api
     * @param region
     */
    protected async verifyDatabaseClusters(
        api: IInfrastructureServices,
        region: string,
    ): Promise<void> {
        await forEachTokenPage(
            (nextPageToken: string) => {
                return api.getDatabasesClusters(region, TokenPagination.page(nextPageToken));
            },
            async (data: DatabaseCluster[], response: ApiResponse<any>) => {
                this.source = response.source;
                this.logTaskProgress(
                    this.connection,
                    LogIdentifierRecordType.DB_CLUSTER,
                    data.length,
                );
                for (const databaseCluster of data) {
                    this.rectify({
                        data: databaseCluster,
                        isNotImplemented: response.isNotImplemented,
                        source: this.source,
                    });
                }
            },
        );
    }

    /**
     *
     * @param api
     * @param region
     */
    protected async verifyDatabases(api: IInfrastructureServices, region: string): Promise<void> {
        await forEachTokenPage(
            (nextPageToken: string) => {
                return api.getDatabases(region, TokenPagination.page(nextPageToken));
            },
            async (data: Database[], response: ApiResponse<any>) => {
                this.source = response.source;
                this.logTaskProgress(
                    this.connection,
                    LogIdentifierRecordType.DATABASE,
                    data.length,
                );

                for (const database of data) {
                    this.rectify({
                        data: database,
                        isNotImplemented: response.isNotImplemented,
                        source: this.source,
                    });
                }
            },
        );
    }

    /**
     * Cloud data is encrypted, multi az, and backed up on GCP and AWS -
     * no need to verify this for now.
     *
     * @param api
     * @param region
     */
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    protected async verifyCloudData(api: any, region: string): Promise<void> {
        // eslint-disable-next-line @typescript-eslint/no-empty-function
    }

    /**
     *
     * @param api
     * @param region
     */
    protected async verifyCaches(api: any, region: string): Promise<void> {
        await forEachTokenPage(
            (nextPageToken: string) => {
                return api.getCachesV1(region, TokenPagination.page(nextPageToken));
            },
            async (data: Cache[], response: ApiResponse<any>) => {
                this.logTaskProgress(this.connection, LogIdentifierRecordType.CACHE, data.length);
                await this.handlePipe(data, response);
            },
        );

        await forEachTokenPage(
            (nextPageToken: string) => {
                return api.getCachesV2(region, TokenPagination.page(nextPageToken));
            },
            async (data: Cache[], response: ApiResponse<any>) => {
                this.logTaskProgress(this.connection, LogIdentifierRecordType.CACHE, data.length);
                await this.handlePipe(data, response);
            },
        );
    }

    /**
     *
     * @param data
     * @param response
     */
    private handlePipe(data: Cache[], response: ApiResponse<any>) {
        this.source = response.source;

        for (const cache of data) {
            this.rectify({
                data: cache,
                isNotImplemented: response.isNotImplemented,
                source: this.source,
            });
        }

        return Promise.resolve();
    }

    /**
     *
     * @param api
     * @param region
     */
    protected async verifyIndexers(api: any, region: string): Promise<void> {
        const response = await api.getIndexers(region);

        this.source = response.source;
        const indexers = response.data;
        this.logTaskProgress(this.connection, LogIdentifierRecordType.INDEXER, indexers.length);

        for (const indexer of indexers) {
            this.rectify({ data: indexer, isNotImplemented: false });
        }
    }

    /**
     *
     * @param data
     * @returns
     */
    protected isCluster(
        // eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types
        data: any,
    ): boolean {
        return has(data, 'nodes') && !isNil(data.nodes);
    }

    /**
     *
     * @param data
     * @returns
     */
    protected isClustered(
        // eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types
        data: any,
    ): boolean {
        return has(data, 'clusterId') && !isNil(data.clusterId);
    }

    /**
     *
     * @param {ApiData} instance
     * @returns
     */
    private getResourceArn(instance: ApiData): string | null {
        return instance.resourceType === AwsConstants.ELASTICSEARCH
            ? createESDomainArn(
                  instance.region,
                  instance.accountId,
                  instance.name,
                  this.connection.clientType,
              )
            : instance.raw?.ARN ||
                  instance.raw?.TableArn ||
                  instance.raw?.DBClusterArn ||
                  instance.raw?.DBInstanceArn;
    }

    /**
     *
     * @param param0
     */
    protected rectify({
        data,
        isNotImplemented,
        source = null,
    }: {
        data: ApiDataStorage;
        isNotImplemented: boolean;
        source?: ApiDataSource;
    }): void {
        /**
         * Sanity check if this is implemented
         */
        if (isNotImplemented) {
            return;
        }
        /**
         * Check the rectification - this also checks API exclusions
         */
        const resourceArn = this.getResourceArn(data);
        if (!this.getRectification(data)) {
            /**
             * We might have a clustered instance
             */
            let fail = true;

            /**
             * This is a fail - but we are required to check if this instance is a member of a
             * passing cluster - so check if the cluster id is in the cluster id array ...
             */
            if (this.isClustered(data)) {
                /**
                 * Check the cluster id since this is a clustered instance - if it is in the success
                 * array then there is no need to fail it - as it is covered within the cluster configs.
                 */
                if (this.clusters.includes(data.clusterId)) {
                    fail = false;
                }
            }

            /**
             * We will need to remove this as time permits
             */
            const isAws =
                source === ApiDataSource.AWS ||
                source === ApiDataSource.AWS_ORG_UNITS ||
                source === ApiDataSource.AWS_GOV_CLOUD;

            if (isAws && this.isCluster(data)) {
                /**
                 * We don't want clusters to be failed instances (for AWS only)
                 */
                fail = false;
            }

            /**
             * Check the flag - we might have a clustered instance.
             */
            if (fail) {
                this.results.fail.push({
                    id: data.id,
                    name: data.name,
                    displayName: ApiData.getDisplayName(data),
                    description: data.description,
                    region: data.region,
                    resourceType: data.resourceType,
                    accountId: data.accountId,
                    accountName: data.accountName,
                    organizationalUnitId: data.organizationalUnitId,
                    organizationalUnitName: data.organizationalUnitName,
                    resourceOrganizationPath: data.resourceOrganizationPath,
                    raw: data.raw,
                    ...(resourceArn && { resourceArn }),
                });
            }
        } else {
            /**
             * If this instance is a pass - check if it is a cluster
             */
            if (this.isCluster(data)) {
                /**
                 * This instance is a cluster - so we are required to save it in order to
                 * verify potential instances that may fail but are members of a passing cluster.
                 */
                this.clusters.push(data.id);
            }

            this.results.pass.push({
                id: data.id,
                name: data.name,
                displayName: ApiData.getDisplayName(data),
                description: data.description,
                region: data.region,
                resourceType: data.resourceType,
                accountId: data.accountId,
                accountName: data.accountName,
                organizationalUnitId: data.organizationalUnitId,
                organizationalUnitName: data.organizationalUnitName,
                resourceOrganizationPath: data.resourceOrganizationPath,
                raw: data.raw,
                ...(resourceArn && { resourceArn }),
            });
        }
    }

    /**
     *
     * @param data
     */
    protected abstract getRectification(data: ApiDataStorage): boolean;
}
