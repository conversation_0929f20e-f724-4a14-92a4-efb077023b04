/* eslint-disable no-await-in-loop */
import { ApiData } from 'app/apis/classes/api/api-data.class';
import { IInfrastructureServices } from 'app/apis/interfaces/infrastructure-services.interface';
import { Data } from 'app/autopilot/classes/data.class';
import { TaskMessage as Message } from 'app/autopilot/classes/task-message.class';
import { MonitorDataType } from 'app/autopilot/enums/monitor-data-type.enum';
import { parseRegionString } from 'app/autopilot/helper/parse-region-string';
import { AutopilotAccountTask as Task } from 'app/autopilot/tasks/autopilot-account.task';
import { AutopilotTask } from 'app/autopilot/tasks/autopilot.task';
import { HasOneResolver } from 'app/autopilot/tasks/resolvers/has-one.resolver';
import { ConnectionEntity } from 'app/companies/connections/entities/connection.entity';
import { ControlTestInstanceHistory } from 'app/monitors/entities/control-test-instance-history.entity';
import { Account } from 'auth/entities/account.entity';
import { AutopilotTaskType as TaskType } from 'commons/enums/autopilot/autopilot-task-type.enum';
import { LogIdentifierRecordType } from 'commons/enums/log-identifier-record-type.enum';
import { forEachTokenPage } from 'commons/helpers/pagination/pagination.helper';
import { TokenPagination } from 'commons/helpers/pagination/token.pagination';
import { isEmpty } from 'lodash';
import { PolloMessage } from 'pollo-logger/classes/pollo.message';

export class AutopilotInfrastructureStorageWafTask extends Task<any> {
    /**
     *
     */
    protected static MAX_RESULTS = 50;

    /**
     *
     * @param context
     * @param account
     * @param taskId
     * @param testId
     * @param controlTestInstanceHistory
     * @param connection
     */
    constructor(
        context: AutopilotTask<any>,
        account: Account,
        taskId: TaskType,
        testId: number,
        controlTestInstanceHistory: ControlTestInstanceHistory,
        connection: ConnectionEntity,
    ) {
        super(context, account, taskId, testId, controlTestInstanceHistory);

        this.connection = connection;
        this.resolver = new HasOneResolver<Data>();
    }

    /**
     *
     */
    async run(): Promise<any> {
        await this.do(async () => {
            try {
                const api = await this.context.external.get<IInfrastructureServices>(
                    this.connection,
                    this.account,
                );

                const regions = await api.getRegions(false);

                let allBuckets = [];
                await forEachTokenPage(
                    (nextPageToken: string) => {
                        return api.getBuckets(null as any, TokenPagination.page(nextPageToken));
                    },
                    async (buckets: any) => {
                        allBuckets = allBuckets.concat(buckets);
                    },
                );

                /**
                 * There are no buckets - so this test is not implemented
                 */
                const testNotImplemented = isEmpty(allBuckets);

                if (testNotImplemented) {
                    this.hasOneResolver().setNotImplemented(true);

                    /**
                     * https://drata.atlassian.net/browse/ENG-5950
                     */
                    const clientId = await api.getClientId();

                    this.results.pass.push({
                        id: clientId,
                        name: clientId,
                        raw: clientId,
                        request: null,
                        region: clientId,
                    });

                    await this.resolve();

                    return;
                }

                for (const region of regions) {
                    // Force refresh credentials at the start of each region to ensure fresh credentials
                    // This helps with long-running tasks that process many buckets across regions
                    if (api.forceRefreshCredentials) {
                        await api.forceRefreshCredentials();
                    }

                    if (!isEmpty(allBuckets)) {
                        await forEachTokenPage(
                            (nextPageToken: string) => {
                                return api.getStorageFirewallAcls(
                                    region,
                                    TokenPagination.page(
                                        nextPageToken,
                                        AutopilotInfrastructureStorageWafTask.MAX_RESULTS,
                                    ),
                                );
                            },
                            async (acls: any[]) => {
                                this.logTaskProgress(
                                    this.connection,
                                    LogIdentifierRecordType.ACL,
                                    acls.length,
                                );

                                const {
                                    region: regionStr,
                                    organizationalUnitId,
                                    organizationalUnitName,
                                    accountName,
                                    resourceOrganizationPath,
                                } = parseRegionString(region);

                                for (const acl of acls) {
                                    /**
                                     * https://drata.atlassian.net/browse/ENG-32512
                                     */
                                    this.context.logger.log(
                                        PolloMessage.msg(
                                            'running autopilot-infrastructure-storage-waf.task',
                                        )
                                            .setContext(this.constructor.name)
                                            .setSubContext(this.run.name)
                                            .setDomain(this.account.domain)
                                            .setIdentifier({
                                                alcs: acls.length,
                                                acl,
                                                region,
                                            }),
                                    );
                                    if (
                                        acl.isEnabled &&
                                        acl.storageMonitoringIsEnabled &&
                                        acl.hasEvent
                                    ) {
                                        this.results.pass.push({
                                            id: region,
                                            name: regionStr,
                                            displayName: ApiData.getRegionalName(acl),
                                            raw: acl,
                                            request: null,
                                            accountId: acl.accountId,
                                            region: regionStr,
                                            organizationalUnitId,
                                            organizationalUnitName,
                                            accountName,
                                            resourceOrganizationPath,
                                        });
                                    } else {
                                        this.results.fail.push({
                                            id: region,
                                            name: regionStr,
                                            displayName: ApiData.getRegionalName(acl),
                                            raw: acl,
                                            request: null,
                                            accountId: acl.accountId,
                                            region: regionStr,
                                            organizationalUnitId,
                                            organizationalUnitName,
                                            accountName,
                                            resourceOrganizationPath,
                                        });
                                    }
                                }
                            },
                        );
                    }
                }
            } catch (error) {
                /**
                 * https://drata.atlassian.net/browse/ENG-32512
                 */
                this.logErrorLevelBasedOnSeverity(
                    error,
                    PolloMessage.msg('autopilot-infrastructure-storage-waf.task failed')
                        .setContext(this.constructor.name)
                        .setSubContext(this.run.name)
                        .setDomain(this.account.domain)
                        .setError(error),
                );
                this.onCaughtError(error);
            }

            if (!isEmpty(this.results.pass)) {
                this.results.fail = [];
            }

            await this.resolve();
        });
    }

    /**
     *
     */
    protected getFailMessage(): string {
        return Message.storageWaf();
    }

    /**
     *
     */
    protected getMonitorDataType(): MonitorDataType {
        return MonitorDataType.LIST;
    }
}
