/* eslint-disable no-await-in-loop */
import { Container } from 'app/apis/classes/api-data-storage/container.class';
import { Alarm } from 'app/apis/classes/api-data/alarm.class';
import { Cluster } from 'app/apis/classes/api-data/cluster.class';
import { Instance } from 'app/apis/classes/api-data/instance.class';
import { ApiData } from 'app/apis/classes/api/api-data.class';
import { ApiResponse } from 'app/apis/classes/api/api-response.class';
import { IInfrastructureServices } from 'app/apis/interfaces/infrastructure-services.interface';
import { IObservabilityServices } from 'app/apis/interfaces/observability-services.interface';
import { Data } from 'app/autopilot/classes/data.class';
import { AutopilotTask } from 'app/autopilot/tasks/autopilot.task';
import { AutopilotInfrastructureMonitorTask } from 'app/autopilot/tasks/infrastructure/autopilot-infrastructure-monitor.task';
import { IsEmptyResolver } from 'app/autopilot/tasks/resolvers/is-empty.resolver';
import { ConnectionEntity } from 'app/companies/connections/entities/connection.entity';
import { ControlTestInstanceHistory } from 'app/monitors/entities/control-test-instance-history.entity';
import { Account } from 'auth/entities/account.entity';
import { ClientType } from 'commons/enums/auth/client-type.enum';
import { AutopilotMetricNamespace as MetricNamespace } from 'commons/enums/autopilot/autopilot-metric-namespace.enum';
import { AutopilotMetric as Metric } from 'commons/enums/autopilot/autopilot-metric.enum';
import { AutopilotTaskType as TaskType } from 'commons/enums/autopilot/autopilot-task-type.enum';
import { LogIdentifierRecordType } from 'commons/enums/log-identifier-record-type.enum';
import { getMemoryUsageString, memorySizeOf } from 'commons/helpers/memory.helper';
import { getAutopilotDependencies } from 'commons/helpers/monitor.helper';
import { forEachTokenPage } from 'commons/helpers/pagination/pagination.helper';
import { TokenPagination } from 'commons/helpers/pagination/token.pagination';
import { concat, isArray, isEmpty, isNil, isObject } from 'lodash';

export class AutopilotInfrastructureInstanceMonitorTask extends AutopilotInfrastructureMonitorTask {
    private observabilityConnections: ConnectionEntity[];

    /**
     *
     * @param context
     * @param account
     * @param taskId
     * @param testId
     * @param controlTestInstanceHistory
     * @param connection
     * @param observabilityConnections
     */
    constructor(
        context: AutopilotTask<any>,
        account: Account,
        private readonly taskId: TaskType,
        testId: number,
        controlTestInstanceHistory: ControlTestInstanceHistory,
        connection: ConnectionEntity,
        observabilityConnections?: ConnectionEntity[],
    ) {
        super(context, account, taskId, testId, controlTestInstanceHistory);

        this.connection = connection;
        this.metric = Metric.CPU;
        this.namespace = MetricNamespace.INSTANCE;
        this.resolver = new IsEmptyResolver<Data>();
        this.observabilityConnections = observabilityConnections;
    }

    /**
     *
     */
    async run(): Promise<void> {
        await this.do(async () => {
            try {
                let infrastructureApi;
                try {
                    infrastructureApi = await this.context.external.get<IInfrastructureServices>(
                        this.connection,
                        this.account,
                    );
                } catch (error) {
                    error.message =
                        `Loading Infrastructure API (${ClientType[this.connection.clientType]}) ` +
                        `failed: ${error.message}`;
                    return await Promise.reject(error);
                }

                let observabilityApis: IObservabilityServices[] = [];
                const observabilityClientTypes = (this.observabilityConnections ?? []).map(
                    connection => ClientType[connection.clientType],
                );

                try {
                    const dependencies = getAutopilotDependencies(this.connection);
                    const clientSupportsDependenciesForAPTaskType = dependencies.some(
                        dependency => dependency.taskType === this.taskId,
                    );
                    if (clientSupportsDependenciesForAPTaskType) {
                        observabilityApis = await Promise.all(
                            (this.observabilityConnections ?? []).map(async connection =>
                                this.context.external.get<IObservabilityServices>(
                                    connection,
                                    this.account,
                                ),
                            ),
                        );
                    }
                    this.log(
                        `Loaded ${observabilityApis.length} observability providers for ${
                            ClientType[this.connection.clientType]
                        }, task ${this.taskId}`,
                        { observabilityClientTypes },
                    );
                } catch (error) {
                    error.message =
                        `Failure loading Observability APIs ` +
                        `${JSON.stringify(observabilityClientTypes)}: ${error.message}`;
                    return await Promise.reject(error);
                }

                const regions = await infrastructureApi.getRegions();

                for (const region of regions) {
                    // Force refresh credentials at the start of each region to ensure fresh credentials
                    // This helps with long-running tasks that process many regions
                    if (infrastructureApi.forceRefreshCredentials) {
                        await infrastructureApi.forceRefreshCredentials();
                    }

                    await this.verifyContainers(
                        region,
                        infrastructureApi,
                        MetricNamespace.CONTAINER_INSTANCE,
                    );

                    await this.verifyClusters(
                        region,
                        infrastructureApi,
                        MetricNamespace.CLUSTER_INSTANCE,
                        observabilityApis,
                    );

                    await this.verifyInstances(
                        region,
                        infrastructureApi,
                        MetricNamespace.INSTANCE,
                        observabilityApis,
                    );
                }
            } catch (error) {
                this.onCaughtError(error);
            }

            await this.resolve();
        });
    }

    /**
     *
     * @param region
     * @param api
     * @param namespace
     * @returns
     */
    private async verifyContainers(
        region: string,
        api: IInfrastructureServices,
        namespace: MetricNamespace,
    ): Promise<void> {
        await forEachTokenPage(
            (nextPageToken: string) => {
                return api.getContainers(region, TokenPagination.page(nextPageToken));
            },
            async (containers: Container[], response: ApiResponse<any>) => {
                this.source = response.source;
                this.logTaskProgress(
                    this.connection,
                    LogIdentifierRecordType.CONTAINER,
                    containers.length,
                );
                this.logMemoryStats('VerifyContainers - containers', response);
                for (const container of containers) {
                    if (isEmpty(container.nodes)) {
                        /**
                         * for simple containers that don't include 'nodes' and 'groups'
                         */
                        const alarms = await api.getMetricAlarms(
                            region,
                            Metric.CPU,
                            namespace,
                            container.name,
                            null,
                            null,
                        );

                        this.logTaskProgress(
                            this.connection,
                            LogIdentifierRecordType.METRICS_ALARM,
                            alarms?.data?.length,
                        );

                        this.logMemoryStats('VerifyContainers - alarms', alarms);

                        this.rectify({
                            region,
                            alarms: alarms.data,
                            instance: container,
                            subInstance: null,
                            source: this.source,
                        });

                        this.logMemoryStats('VerifyContainers - rectify', this.results);
                    } else {
                        for (const node of container.nodes) {
                            for (const group of node.groups) {
                                const alarms = await api.getMetricAlarms(
                                    region,
                                    Metric.CPU,
                                    namespace,
                                    group,
                                    null,
                                    null,
                                );

                                this.logMemoryStats('VerifyContainers - alarms nodes', alarms);

                                this.rectify({
                                    region,
                                    alarms: alarms.data,
                                    instance: container,
                                    subInstance: null,
                                    source: this.source,
                                });

                                this.logMemoryStats(
                                    'VerifyContainers - rectify nodes',
                                    this.results,
                                );
                            }
                        }
                    }
                }
            },
        );
    }

    /**
     *
     * @param region
     * @param api
     * @param namespace
     */
    private async verifyClusters(
        region: string,
        api: IInfrastructureServices,
        namespace: MetricNamespace,
        observabilityApis: IObservabilityServices[],
    ): Promise<void> {
        const allApis = [api, ...observabilityApis];

        await forEachTokenPage(
            (nextPageToken: string) => {
                return api.getClusters(region, TokenPagination.page(nextPageToken));
            },
            async (clusters: Cluster[], response: ApiResponse<any>) => {
                this.source = response.source;
                this.logTaskProgress(
                    this.connection,
                    LogIdentifierRecordType.DB_CLUSTER,
                    clusters.length,
                );

                this.logMemoryStats('verifyClusters - clusters', response);

                for (const cluster of clusters) {
                    /**
                     * See if this cluster as a whole is monitored
                     */
                    const alarms = await this.getMetricAlarms(
                        allApis,
                        region,
                        Metric.CPU,
                        namespace,
                        cluster,
                    );

                    this.logMemoryStats('verifyClusters - alarms', alarms);

                    /**
                     * If the cluster is not monitored as a whole - then we have to go to the service
                     * level and verify if the monitors are set there on a case-by-case basis ...
                     */
                    if (isEmpty(alarms)) {
                        await this.verifyClusterServices(
                            region,
                            cluster,
                            api,
                            namespace,
                            observabilityApis,
                        );
                    } else {
                        /**
                         * This entire cluster is being monitored - so rectify
                         */
                        this.rectify({
                            region,
                            alarms,
                            instance: cluster,
                            subInstance: null,
                            source: this.source,
                            isNotImplemented: false,
                        });

                        this.logMemoryStats('verifyClusters - rectify', this.results);
                    }
                }
            },
        );
    }

    /**
     *
     * @param region
     * @param cluster
     * @param api
     * @param namespace
     */
    private async verifyClusterServices(
        region: string,
        cluster: Cluster,
        api: IInfrastructureServices,
        namespace: MetricNamespace,
        observabilityApis: IObservabilityServices[],
    ): Promise<void> {
        const allApis = [api, ...observabilityApis];

        await forEachTokenPage(
            (nextPageToken: string) => {
                return api.getClusterServices(region, cluster, TokenPagination.page(nextPageToken));
            },
            async (services: any[], response: ApiResponse<any>) => {
                this.source = response.source;

                if (isEmpty(services)) {
                    /**
                     * Workaround for Azure Kubernetes
                     */
                    this.rectify({ region, alarms: [], instance: cluster });
                } else {
                    this.logMemoryStats('verifyClusterServices - services', response);
                    this.logTaskProgress(
                        this.connection,
                        LogIdentifierRecordType.CLUSTER_SERVICE,
                        services.length,
                    );
                    for (const service of services) {
                        const alarms = await this.getMetricAlarms(
                            allApis,
                            region,
                            Metric.CPU,
                            namespace,
                            cluster,
                            service,
                        );

                        this.logMemoryStats('verifyClusterServices - alarms', alarms);

                        this.rectify({
                            region,
                            alarms,
                            instance: cluster,
                            subInstance: service,
                            source: this.source,
                        });
                        this.logMemoryStats('verifyClusterServices - rectify', this.results);
                    }
                }
            },
        );
    }

    /**
     *
     * @param region
     * @param api
     * @param namespace
     * @returns
     */
    private async verifyInstances(
        region: string,
        api: IInfrastructureServices,
        namespace: MetricNamespace,
        observabilityApis: IObservabilityServices[],
    ): Promise<void> {
        const allApis = [api, ...observabilityApis];

        /**
         * We need to verify if the monitor has been set for ALL instances
         */
        let alarms = await this.getMetricAlarms(allApis, region, Metric.CPU, namespace);

        /**
         * If the alarms have been set for all instances we are done
         */
        if (!isEmpty(alarms)) {
            this.rectify({
                region,
                alarms,
                instance: this.getRegion(region),
            });

            this.logMemoryStats('verifyInstances - rectify', this.results);
        } else {
            /**
             * Check the instances here
             */
            await forEachTokenPage(
                (nextPageToken: string) => {
                    return api.getInstances(region, TokenPagination.page(nextPageToken));
                },
                async (instances: Instance[], response: ApiResponse<any>) => {
                    this.source = response.source;
                    this.logTaskProgress(
                        this.connection,
                        LogIdentifierRecordType.EC2_INSTANCE,
                        instances.length,
                    );

                    this.logMemoryStats('verifyInstances - instances', response);
                    for (const instance of instances) {
                        let alarmsData = [];

                        /**
                         * Try and get the monitor via the autoscaling group here
                         */
                        if (!isNil(instance.groupName)) {
                            /**
                             * Get the autoscaling group alarms
                             */
                            alarms = await this.getMetricAlarms(
                                allApis,
                                region,
                                Metric.CPU,
                                MetricNamespace.AUTOSCALING_GROUP,
                                instance,
                            );

                            this.logMemoryStats('verifyInstances - alarms', alarms);

                            alarmsData = concat(alarmsData, alarms);
                        }

                        /**
                         * Do a dry run to see if this instance will fail after the
                         * cluster alarms are fetched to see if we need another call.
                         */
                        if (this.dryRunForFail(alarmsData, instance)) {
                            /**
                             * Check for instance alarm
                             */
                            alarms = await this.getMetricAlarms(
                                allApis,
                                region,
                                Metric.CPU,
                                namespace,
                                instance,
                            );

                            this.logMemoryStats('verifyInstances dry run - alarms', alarms);

                            alarmsData = concat(alarmsData, alarms);
                        }

                        this.rectify({
                            region,
                            alarms: alarmsData,
                            instance,
                            subInstance: null,
                            source: this.source,
                        });

                        this.logMemoryStats('verifyInstances - rectify', this.results);
                    }
                },
            );
        }
    }

    private async getMetricAlarms(
        apis: (IInfrastructureServices | IObservabilityServices)[],
        region: string,
        metric: Metric,
        namespace: MetricNamespace,
        instance: ApiData | null = null,
        subInstance: ApiData | null = null,
    ): Promise<Alarm[]> {
        const alarms: Alarm[] = [];

        for (const api of apis) {
            const { data: alarmsFromApi } = await api.getMetricAlarms(
                region,
                metric,
                namespace,
                namespace === MetricNamespace.AUTOSCALING_GROUP
                    ? (instance as Instance)?.groupName
                    : instance?.dimension,
                subInstance?.name,
                null,
                instance,
                subInstance,
            );
            alarms.push(...alarmsFromApi);
        }
        this.logTaskProgress(this.connection, LogIdentifierRecordType.METRICS_ALARM, alarms.length);
        return alarms;
    }

    private logMemoryStats(objectName: string, obj: any): void {
        if (!obj) {
            return;
        }

        if (isObject(obj) && isNil(obj)) {
            return;
        }

        if (isArray(obj) && isEmpty(obj)) {
            return;
        }

        try {
            this.log(`Memory used on: ${objectName}`, {
                currentMemoryUsed: getMemoryUsageString(),
                memoryUsedInObject: memorySizeOf(obj),
            });
        } catch (e) {
            // just in case
        }
    }
}
