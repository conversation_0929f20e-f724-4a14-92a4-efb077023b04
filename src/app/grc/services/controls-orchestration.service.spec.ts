import { ApprovalStatus } from '@drata/enums';
import { EventBus } from '@nestjs/cqrs';
import { TestingModule } from '@nestjs/testing';
import { ApprovalsCoreService } from 'app/approvals/v2/approvals-core/services/approvals-core.service';
import { ConnectionsCoreService } from 'app/companies/connections/services/connections-core.service';
import { WorkspacesCoreService } from 'app/companies/products/services/workspaces-core.service';
import { DocumentLibraryCoreService } from 'app/document-library/services/document-library-core.service';
import { ExternalEvidenceCoreService } from 'app/document-library/services/external-evidence-core.service';
import { FrameworksCoreService } from 'app/frameworks/services/frameworks-core.service';
import { GrcCoreService } from 'app/grc/core/services/grc-core.service';
import { listControlsMapping, mockControlRequestDto } from 'app/grc/mocks/control.mock';
import { ControlFlagsViewRepository } from 'app/grc/repositories/control-flags-view.repository';
import { ControlRepository } from 'app/grc/repositories/control.repository';
import { ControlsOrchestrationService } from 'app/grc/services/controls-orchestration.service';
import { MonitorsOrchestrationService } from 'app/monitors/services/monitors-orchestration.service';
import { NotificationsCoreService } from 'app/notifications/services/notifications-core.service';
import { User } from 'app/users/entities/user.entity';
import { PoliciesCoreService } from 'app/users/policies/services/policies-core.service';
import { UsersCoreService } from 'app/users/services/users-core.service';
import { Account } from 'auth/entities/account.entity';
import { createAppTestingModule } from 'commons/helpers/app-testing-module.helper';
import { MockFactory } from 'commons/mocks/factories/mock.factory';
import { MockType } from 'commons/mocks/types/mock.type';
import { Downloader } from 'dependencies/downloader/downloader';
import { FeatureFlagService } from 'feature-flags/feature-flag.service';
import { TenancyContextMock } from 'tenancy/contexts/__mocks__/tenancy.context';
import { TenancyContext } from 'tenancy/contexts/tenancy.context';

const eventBusMock: Partial<EventBus> = {
    publish: jest.fn().mockResolvedValue(true),
};

describe('ControlsOrchestrationService', () => {
    let controlsOrchestrationService: ControlsOrchestrationService;
    let grcCoreService: MockType<GrcCoreService>;
    let controlRepository: MockType<ControlRepository>;
    let controlFlagsViewRepository: MockType<ControlFlagsViewRepository>;
    let featureFlagService: MockType<FeatureFlagService>;
    let workspacesCoreService: MockType<WorkspacesCoreService>;

    const accountForControlIssues = new Account();
    accountForControlIssues.id = '********-1234-1234-1234-********9012';
    accountForControlIssues.companyName = 'Test Company';

    beforeEach(async () => {
        grcCoreService = {
            getIssuesByControlIds: jest.fn(),
            getBulkedCurrentControlApproval: jest.fn(),
        };

        featureFlagService = {
            evaluate: jest.fn(),
            evaluateAsTenant: jest.fn(),
        };

        workspacesCoreService = {
            getPrimaryProduct: jest.fn().mockResolvedValue({ id: 1 }),
        };

        const module: TestingModule = await createAppTestingModule({
            providers: [
                ControlsOrchestrationService,
                {
                    provide: GrcCoreService,
                    useValue: grcCoreService,
                },
                {
                    provide: ExternalEvidenceCoreService,
                    useValue: MockFactory.getMock(ExternalEvidenceCoreService),
                },
                {
                    provide: Downloader,
                    useValue: {},
                },
                {
                    provide: DocumentLibraryCoreService,
                    useValue: MockFactory.getMock(DocumentLibraryCoreService),
                },
                {
                    provide: PoliciesCoreService,
                    useValue: MockFactory.getMock(PoliciesCoreService),
                },
                {
                    provide: WorkspacesCoreService,
                    useValue: workspacesCoreService,
                },
                {
                    provide: FrameworksCoreService,
                    useValue: {
                        getFrameworksEnabledByProduct: jest.fn().mockResolvedValue([]),
                    },
                },
                {
                    provide: ConnectionsCoreService,
                    useValue: {},
                },
                {
                    provide: MonitorsOrchestrationService,
                    useValue: {},
                },
                {
                    provide: ApprovalsCoreService,
                    useValue: {},
                },
                {
                    provide: NotificationsCoreService,
                    useValue: {},
                },
                {
                    provide: UsersCoreService,
                    useValue: MockFactory.getMock(UsersCoreService),
                },
                {
                    provide: FeatureFlagService,
                    useValue: featureFlagService,
                },
                {
                    provide: EventBus,
                    useValue: eventBusMock,
                },
            ],
        }).compile();

        controlsOrchestrationService = module.get<ControlsOrchestrationService>(
            ControlsOrchestrationService,
        );

        const tenancyContext = module.get<ReturnType<typeof TenancyContextMock>>(TenancyContext);

        controlRepository = {
            getControlsFromIdsGroupBy: jest.fn(),
        } as any;

        controlFlagsViewRepository = {
            getControlFlags: jest.fn(),
        } as any;

        tenancyContext.getCustomRepository.mockImplementation((repository): any => {
            switch (repository) {
                case ControlRepository:
                    return controlRepository;
                case ControlFlagsViewRepository:
                    return controlFlagsViewRepository;
                default:
                    return {} as any;
            }
        });
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    describe('getControlFindings', () => {
        it('should return empty object when controlIds array is empty', async () => {
            const result = await controlsOrchestrationService.getControlFindings(
                [],
                accountForControlIssues,
            );

            expect(result).toEqual({});
        });

        it('should calculate control issues stats correctly with no approval needed', async () => {
            const controlIds = [1, 2];

            // Mock issues stats by control
            grcCoreService.getIssuesByControlIds?.mockResolvedValueOnce([
                {
                    controlId: 1,
                    policies: { notReady: [1, 2] }, // 2 not ready policies
                    evidence: {
                        external: { notReady: [1] }, // 1 not ready external evidence
                        library: { notReady: [2, 3] }, // 2 not ready library evidence
                    },
                    tests: { notReady: [1] }, // 1 not ready test
                },
                {
                    controlId: 2,
                    policies: { notReady: [] }, // 0 not ready policies
                    evidence: {
                        external: { notReady: [] }, // 0 not ready external evidence
                        library: { notReady: [4] }, // 1 not ready library evidence
                    },
                    tests: { notReady: [] }, // 0 not ready tests
                },
            ]);

            // Mock no approval needed for both controls
            grcCoreService.getBulkedCurrentControlApproval?.mockResolvedValueOnce([null, null]);

            const result = await controlsOrchestrationService.getControlFindings(
                controlIds,
                accountForControlIssues,
            );

            expect(result).toEqual({
                1: {
                    policies: {
                        count: 2,
                        unapprovedPoliciesIds: [1, 2],
                    },
                    evidence: 3, // 1 external + 2 library
                    tests: 1,
                    total: 6, // 2 + 3 + 1
                    approvals: 0,
                },
                2: {
                    policies: {
                        count: 0,
                        unapprovedPoliciesIds: [],
                    },
                    evidence: 1, // 0 external + 1 library
                    tests: 0,
                    total: 1, // 0 + 1 + 0
                    approvals: 0,
                },
            });

            expect(grcCoreService.getIssuesByControlIds).toHaveBeenCalledTimes(1);
            expect(grcCoreService.getIssuesByControlIds).toHaveBeenCalledWith(
                controlIds,
                accountForControlIssues,
            );
            expect(grcCoreService.getBulkedCurrentControlApproval).toHaveBeenCalledTimes(1);
        });

        it('should include approval count when approval is needed', async () => {
            const controlIds = [1];

            grcCoreService.getIssuesByControlIds?.mockResolvedValueOnce([
                {
                    controlId: 1,
                    policies: { notReady: [1] },
                    evidence: {
                        external: { notReady: [] },
                        library: { notReady: [] },
                    },
                    tests: { notReady: [] },
                },
            ]);

            // Mock approval needed (status != APPROVED)
            grcCoreService.getBulkedCurrentControlApproval?.mockResolvedValueOnce([
                {
                    controlId: 1,
                    approval: { id: 1, status: ApprovalStatus.READY_FOR_REVIEWS },
                },
            ]);

            const result = await controlsOrchestrationService.getControlFindings(
                controlIds,
                accountForControlIssues,
            );

            expect(result).toEqual({
                1: {
                    policies: {
                        count: 1,
                        unapprovedPoliciesIds: [1],
                    },
                    evidence: 0,
                    tests: 0,
                    total: 2, // 1 policy + 1 approval
                    approvals: 1,
                },
            });
        });

        it('should not include approval count when approval is approved', async () => {
            const controlIds = [1];

            grcCoreService.getIssuesByControlIds?.mockResolvedValueOnce([
                {
                    controlId: 1,
                    policies: { notReady: [] },
                    evidence: {
                        external: { notReady: [] },
                        library: { notReady: [] },
                    },
                    tests: { notReady: [] },
                },
            ]);

            // Mock approval exists but is approved
            grcCoreService.getBulkedCurrentControlApproval?.mockResolvedValueOnce([
                {
                    controlId: 1,
                    approval: { id: 1, status: ApprovalStatus.APPROVED },
                },
            ]);

            const result = await controlsOrchestrationService.getControlFindings(
                controlIds,
                accountForControlIssues,
            );

            expect(result).toEqual({
                1: {
                    policies: {
                        count: 0,
                        unapprovedPoliciesIds: [],
                    },
                    evidence: 0,
                    tests: 0,
                    total: 0,
                    approvals: 0,
                },
            });
        });

        it('should include approval count when approval status is INITIALIZED', async () => {
            const controlIds = [1];

            grcCoreService.getIssuesByControlIds?.mockResolvedValueOnce([
                {
                    controlId: 1,
                    policies: { notReady: [] },
                    evidence: {
                        external: { notReady: [] },
                        library: { notReady: [] },
                    },
                    tests: { notReady: [] },
                },
            ]);

            // Mock approval with INITIALIZED status
            grcCoreService.getBulkedCurrentControlApproval?.mockResolvedValueOnce([
                {
                    controlId: 1,
                    approval: { id: 1, status: ApprovalStatus.INITIALIZED },
                },
            ]);

            const result = await controlsOrchestrationService.getControlFindings(
                controlIds,
                accountForControlIssues,
            );

            expect(result).toEqual({
                1: {
                    policies: {
                        count: 0,
                        unapprovedPoliciesIds: [],
                    },
                    evidence: 0,
                    tests: 0,
                    total: 1, // 1 approval
                    approvals: 1,
                },
            });
        });

        it('should include approval count when approval status is PREPARE_FOR_REVIEWS', async () => {
            const controlIds = [1];

            grcCoreService.getIssuesByControlIds?.mockResolvedValueOnce([
                {
                    controlId: 1,
                    policies: { notReady: [1, 2] },
                    evidence: {
                        external: { notReady: [] },
                        library: { notReady: [] },
                    },
                    tests: { notReady: [] },
                },
            ]);

            // Mock approval with PREPARE_FOR_REVIEWS status
            grcCoreService.getBulkedCurrentControlApproval?.mockResolvedValueOnce([
                {
                    controlId: 1,
                    approval: { id: 1, status: ApprovalStatus.PREPARE_FOR_REVIEWS },
                },
            ]);

            const result = await controlsOrchestrationService.getControlFindings(
                controlIds,
                accountForControlIssues,
            );

            expect(result).toEqual({
                1: {
                    policies: {
                        count: 2,
                        unapprovedPoliciesIds: [1, 2],
                    },
                    evidence: 0,
                    tests: 0,
                    total: 3, // 2 policies + 1 approval
                    approvals: 1,
                },
            });
        });

        it('should include approval count when approval status is CHANGES_REQUESTED', async () => {
            const controlIds = [1];

            grcCoreService.getIssuesByControlIds?.mockResolvedValueOnce([
                {
                    controlId: 1,
                    policies: { notReady: [] },
                    evidence: {
                        external: { notReady: [1] },
                        library: { notReady: [] },
                    },
                    tests: { notReady: [1, 2] },
                },
            ]);

            // Mock approval with CHANGES_REQUESTED status
            grcCoreService.getBulkedCurrentControlApproval?.mockResolvedValueOnce([
                {
                    controlId: 1,
                    approval: { id: 1, status: ApprovalStatus.CHANGES_REQUESTED },
                },
            ]);

            const result = await controlsOrchestrationService.getControlFindings(
                controlIds,
                accountForControlIssues,
            );

            expect(result).toEqual({
                1: {
                    policies: {
                        count: 0,
                        unapprovedPoliciesIds: [],
                    },
                    evidence: 1, // 1 external evidence
                    tests: 2,
                    total: 4, // 1 evidence + 2 tests + 1 approval
                    approvals: 1,
                },
            });
        });
    });

    describe('listControlsGroupBy', () => {
        const account = new Account();
        account.id = '********-1234-1234-1234-********9012';
        account.companyName = 'Test Company';

        const mockUser = new User();
        mockUser.id = 1;

        let mockGetPaginatedControlIds: jest.SpyInstance;
        let mockGetControlFindings: jest.SpyInstance;

        beforeEach(() => {
            jest.spyOn(featureFlagService, 'evaluateAsTenant').mockResolvedValue(true);
            mockGetPaginatedControlIds = jest.spyOn(
                controlsOrchestrationService as any,
                'getPaginatedControlIds',
            );
            mockGetControlFindings = jest.spyOn(
                controlsOrchestrationService as any,
                'getControlFindings',
            );

            // Mock the controlRepository and controlFlagsViewRepository
            controlRepository.getControlsFromIdsGroupBy = jest.fn();
            controlFlagsViewRepository.getControlFlags = jest.fn();

            // Mock workspacesCoreService
            workspacesCoreService.getPrimaryProduct = jest.fn().mockResolvedValue({ id: 1 });
        });

        afterEach(() => {
            jest.clearAllMocks();
        });

        it('should return paginated controls without issues when no controls found', async () => {
            mockGetPaginatedControlIds.mockResolvedValueOnce([[], 0]);
            controlRepository?.getControlsFromIdsGroupBy?.mockResolvedValueOnce([]);

            const result = await controlsOrchestrationService.listControlsGroupBy(
                mockControlRequestDto,
                account,
                mockUser,
            );

            expect(result).toEqual({
                data: [],
                page: 1,
                limit: 10,
                total: 0,
            });

            expect(mockGetControlFindings).not.toHaveBeenCalled();
            expect(controlRepository.getControlsFromIdsGroupBy).toHaveBeenCalledWith(
                [],
                'name',
                'ASC',
            );
        });

        it('should return controls with issues field populated when controls are not ready', async () => {
            const { controlIds, mockControls, mockControlFlags, mockIssuesMapping } =
                listControlsMapping;

            mockGetPaginatedControlIds.mockResolvedValueOnce([controlIds, 2]);
            controlRepository.getControlsFromIdsGroupBy?.mockResolvedValueOnce(mockControls);
            controlFlagsViewRepository.getControlFlags?.mockResolvedValueOnce(mockControlFlags);
            mockGetControlFindings.mockResolvedValueOnce(mockIssuesMapping);

            const result = await controlsOrchestrationService.listControlsGroupBy(
                mockControlRequestDto,
                account,
                mockUser,
            );

            expect(result.data).toHaveLength(2);

            // First control (not ready) should have issues
            expect(result.data[0]).toMatchObject({
                control: expect.objectContaining({
                    id: 1,
                    name: 'Control 1',
                    description: 'Test control Test Company description', // %s replaced
                }),
                hasEvidence: true,
                hasPolicy: true,
                hasOwner: true,
                isMonitored: false,
                isReady: false,
                hasTicket: false,
                issues: {
                    policies: 2,
                    evidence: 1,
                    tests: 1,
                    total: 4,
                    approvals: 0,
                },
            });

            // Second control (ready) should have issues but they should be empty/zero
            expect(result.data[1]).toMatchObject({
                control: expect.objectContaining({
                    id: 2,
                    name: 'Control 2',
                    description: 'Another test control Test Company description', // %s replaced
                }),
                hasEvidence: false,
                hasPolicy: false,
                hasOwner: false,
                isMonitored: false,
                isReady: true,
                hasTicket: false,
                issues: {
                    policies: 0,
                    evidence: 0,
                    tests: 0,
                    total: 0,
                    approvals: 0,
                },
            });
        });

        it('should not populate issues field when getControlFindings returns empty mapping', async () => {
            const controlIds = [1];
            const mockControls = [
                {
                    ...listControlsMapping.mockControls[0],
                },
            ];

            const mockControlFlags = [
                {
                    ...listControlsMapping.mockControlFlags[0],
                },
            ];

            mockGetPaginatedControlIds.mockResolvedValueOnce([controlIds, 1]);
            controlRepository?.getControlsFromIdsGroupBy?.mockResolvedValueOnce(mockControls);
            controlFlagsViewRepository?.getControlFlags?.mockResolvedValueOnce(mockControlFlags);
            mockGetControlFindings.mockResolvedValueOnce({}); // Empty mapping

            const result = await controlsOrchestrationService.listControlsGroupBy(
                mockControlRequestDto,
                account,
                mockUser,
            );

            expect(result.data).toHaveLength(1);
            expect(result.data[0]).not.toHaveProperty('issues');
            expect(mockGetControlFindings).toHaveBeenCalledWith(controlIds, account);
        });

        it('should handle company name replacement in control descriptions', async () => {
            const controlIds = [1];
            const mockControls = [
                {
                    id: 1,
                    name: 'Control 1',
                    code: 'CTRL-001',
                    description: '%s must implement security controls for %s systems',
                    frameworkTags: [],
                    topics: [],
                },
            ];

            const mockControlFlags = [
                {
                    controlId: 1,
                    hasEvidence: true,
                    hasPolicy: true,
                    hasOwner: true,
                    isMonitored: false,
                    isReady: true,
                    hasTicket: false,
                    frameworkTags: [],
                    topics: [],
                },
            ];

            mockGetPaginatedControlIds.mockResolvedValueOnce([controlIds, 1]);
            controlRepository?.getControlsFromIdsGroupBy?.mockResolvedValueOnce(mockControls);
            controlFlagsViewRepository?.getControlFlags?.mockResolvedValueOnce(mockControlFlags);
            mockGetControlFindings.mockResolvedValueOnce({});

            const result = await controlsOrchestrationService.listControlsGroupBy(
                mockControlRequestDto,
                account,
                mockUser,
            );

            expect(result.data[0].control.description).toBe(
                'Test Company must implement security controls for Test Company systems',
            );
        });

        it('should not include issues field when getControlIssuesFeature is false', async () => {
            jest.spyOn(featureFlagService, 'evaluateAsTenant').mockResolvedValue(false);
            const controlIds = [1];
            const mockControls = [
                {
                    id: 1,
                    name: 'Test Control',
                    description: 'Test Description',
                    isReady: false,
                    frameworkTags: [],
                    topics: [],
                },
            ];
            const mockControlFlags = [
                {
                    controlId: 1,
                    hasEvidence: true,
                    hasPolicy: true,
                    hasOwner: true,
                    isMonitored: false,
                    isReady: false,
                    hasTicket: false,
                    frameworkTags: [],
                    topics: [],
                },
            ];

            mockGetPaginatedControlIds.mockResolvedValueOnce([controlIds, 1]);
            controlRepository?.getControlsFromIdsGroupBy?.mockResolvedValueOnce(mockControls);
            controlFlagsViewRepository?.getControlFlags?.mockResolvedValueOnce(mockControlFlags);
            mockGetControlFindings.mockResolvedValueOnce({});

            const result = await controlsOrchestrationService.listControlsGroupBy(
                mockControlRequestDto,
                account,
                mockUser,
            );

            expect(result.data).toHaveLength(1);
            expect(result.data[0].control).not.toHaveProperty('issues');
            expect(mockGetControlFindings).not.toHaveBeenCalled();
        });
    });
});
