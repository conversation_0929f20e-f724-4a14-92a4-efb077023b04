import { ApprovalStatus, ErrorCode } from '@drata/enums';
import {
    BadRequestException,
    ConflictException,
    Injectable,
    NotFoundException,
} from '@nestjs/common';
import { ControlApprovalCreatedEvent } from 'app/analytics/observables/events/control-approval-created';
import { ControlApprovalRemovedEvent } from 'app/analytics/observables/events/control-approval-removed';
import ControlApprovalDetail from 'app/approvals/v1/classes/control-approval-detail.class';
import { BulkDeleteControlsApprovalsRequestDto } from 'app/approvals/v1/dtos/approvals/bulk-delete-controls-approvals-request.dto';
import { BulkDeleteControlsApprovalsResponseDto } from 'app/approvals/v1/dtos/approvals/bulk-delete-controls-approvals-response.dto';
import { BulkUpsertApprovalsRequestDto } from 'app/approvals/v1/dtos/approvals/bulk-upsert-approvals-request.dto';
import { UpdateApprovalRequestDto } from 'app/approvals/v1/dtos/approvals/update-approval-request.dto';
import { UpdateApprovalsAttachedToEvidenceRequestDto } from 'app/approvals/v1/dtos/approvals/update-approvals-attached-to-evidence-request.dto';
import { ApprovalReview } from 'app/approvals/v1/entities/approval-review.entity';
import { ApprovalStatusHistory } from 'app/approvals/v1/entities/approval-status-history.entity';
import { Approval } from 'app/approvals/v1/entities/approval.entity';
import { getCurrentAndNextControlApprovalIds } from 'app/approvals/v1/helpers/control-approval.helper';
import { ApprovalsReviewService } from 'app/approvals/v1/services/approvals-review.service';
import { ApprovalsStatusHistoryService } from 'app/approvals/v1/services/approvals-status-history.service';
import { ApprovalsService } from 'app/approvals/v1/services/approvals.service';
import { Product } from 'app/companies/products/entities/product.entity';
import { WorkspacesCoreService } from 'app/companies/products/services/workspaces-core.service';
import { ControlService } from 'app/control/control.service';
import { LibraryDocumentControlsMap } from 'app/document-library/entities/library-document-controls-map.entity';
import { EventsService } from 'app/events/events.service';
import { ControlApprovalStatusChangeEvent } from 'app/events/observables/events/control-approval-status-change.event';
import { UserFeature } from 'app/feature-toggling/entities/user-feature.entity';
import { UserFeatureRepository } from 'app/feature-toggling/repositories/user-feature.repository';
import { FrameworksCoreService } from 'app/frameworks/services/frameworks-core.service';
import { GrcCoreService } from 'app/grc/core/services/grc-core.service';
import { ControlsApprovalsRequestDto } from 'app/grc/dtos/controls-approvals-request.dto';
import { Control } from 'app/grc/entities/control.entity';
import { ApproveControlApprovalEmailEvent } from 'app/grc/observables/events/approve-control-approval-email-event';
import { ControlApprovalBulkStateChangeEmailEvent } from 'app/grc/observables/events/control-approval-bulk-state-change-email-event';
import { ControlApprovalStageChangePolicyEmailEvent } from 'app/grc/observables/events/control-approval-stage-change-policy-email-event';
import { ControlApprovalStateChangeEmailEvent } from 'app/grc/observables/events/control-approval-state-change-email-event';
import { ControlsReindexEvent } from 'app/grc/observables/events/controls-reindex.event';
import { RequestChangesControlApprovalEmailEvent } from 'app/grc/observables/events/request-changes-control-approval-email-event';
import { RequiredControlApprovalEmailEvent } from 'app/grc/observables/events/required-control-approval-email-event';
import { ControlRepository } from 'app/grc/repositories/control.repository';
import { ControlApprovalsReviewService } from 'app/grc/services/control-approvals-review.service';
import { ControlReadinessEvent } from 'app/grc/workflow-services/events/control-readiness.workflow.event';
import { NotificationsCoreService } from 'app/notifications/services/notifications-core.service';
import { TasksCoreService } from 'app/tasks/core/services/tasks-core.service';
import { User } from 'app/users/entities/user.entity';
import { Account } from 'auth/entities/account.entity';
import { CacheBusterWithPrefix } from 'cache/cache.decorator';
import { PolloAdapter } from 'commons/adapters/pollo.adapter';
import { TenancyTransaction } from 'commons/decorators/transaction';
import { ApprovalStatusChangeSource } from 'commons/enums/approvals/v1/approval-status-change-source.enum';
import { Caches } from 'commons/enums/cache.enum';
import { FeatureType } from 'commons/enums/feature-toggling/feature.enum';
import { UpcomingTaskType } from 'commons/enums/upcoming-task-type-enum';
import { Action } from 'commons/enums/users/action.enum';
import { Subject } from 'commons/enums/users/subject.enum';
import { ForbiddenException } from 'commons/exceptions/forbidden.exception';
import { asyncForEach } from 'commons/helpers/array.helper';
import { isControlApprover } from 'commons/helpers/control.helper';
import { getProductId } from 'commons/helpers/products.helper';
import { capitalize } from 'commons/helpers/string.helper';
import { fullName, hasUserPermission } from 'commons/helpers/user.helper';
import { AppService } from 'commons/services/app.service';
import { WorkspacesBaseService } from 'commons/services/workspaces-base.service';
import { difference, get, isArray, isEmpty, isNil, omit, union } from 'lodash';
import { Repository } from 'typeorm';
@Injectable()
export class ControlApprovalsService extends AppService {
    constructor(
        private readonly approvalService: ApprovalsService,
        private readonly controlApprovalReviewService: ControlApprovalsReviewService,
        private readonly approvalsReviewService: ApprovalsReviewService,
        private readonly eventService: EventsService,
        private readonly workspacesCoreService: WorkspacesCoreService,
        private readonly workspaceBaseService: WorkspacesBaseService,
        private readonly grcCoreService: GrcCoreService,
        private readonly controlService: ControlService,
        private readonly notificationsCoreService: NotificationsCoreService,
        private readonly tasksCoreService: TasksCoreService,
        private readonly frameworksCoreService: FrameworksCoreService,
        private readonly approvalsStatusHistoryService: ApprovalsStatusHistoryService,
    ) {
        super();
    }

    async createCurrentControlApproval(
        controlId: number,
        currentUser: User,
        account: Account,
        product?: Product,
    ): Promise<Approval> {
        const { control, isReady } = await this.controlService.findControlWithEvidenceById(
            account,
            currentUser,
            controlId,
        );

        if (!control) {
            throw new NotFoundException('Control not found');
        }
        // TODO: add function to set other approvals from the same control to current false/0

        const existingApproval = await this.approvalService.getApprovalsByControlId(
            controlId,
            true,
        );
        const approval = existingApproval[0] || null;

        if (!isEmpty(approval)) {
            return approval;
        }

        const newApproval = this.approvalService.createApproval({
            current: true,
            controls: [control],
            createdBy: currentUser,
            updatedBy: currentUser,
        });

        try {
            // use product if it's set, otherwise use the account's current product. currentProduct in account might be null
            const productId = product?.id ?? getProductId(account);

            // Handle case where productId might be null. This shouldn't happen unless data is corrupted
            if (!productId) {
                this.logger.warn(
                    PolloAdapter.acct(`No product ID available for control ${controlId}`, account)
                        .setContext(this.constructor.name)
                        .setSubContext(this.createCurrentControlApproval.name),
                );
                return newApproval;
            }

            const productFrameworks =
                await this.frameworksCoreService.getFrameworksEnabledByProduct(productId);

            // This is for analytics events
            this._eventBus.publish(
                new ControlApprovalCreatedEvent(
                    account,
                    currentUser,
                    newApproval,
                    productFrameworks,
                    product?.name ?? '',
                    control,
                    isReady,
                ),
            );
        } catch (error) {
            this.logger.error(
                PolloAdapter.acct(
                    `Something went wrong creating approval for ${controlId}`,
                    account,
                )
                    .setError(error)
                    .setContext(this.constructor.name)
                    .setSubContext(this.createCurrentControlApproval.name),
            );
            // Return the approval even if analytics event fails
        }
        const savedApproval = await this.approvalService.saveApproval(newApproval);

        // Insert in approval status history when the approval is created
        await this.insertApprovalStatusHistory(savedApproval, currentUser);

        return savedApproval;
    }

    private async insertApprovalStatusHistory(savedApproval: Approval, user: User) {
        if (!isNil(savedApproval)) {
            const approvalStatusHistoryEntity = new ApprovalStatusHistory();
            approvalStatusHistoryEntity.approval = savedApproval;
            approvalStatusHistoryEntity.createdBy = user;
            approvalStatusHistoryEntity.status = savedApproval.status;

            await this.approvalsStatusHistoryService.saveApprovalStatusHistory(
                approvalStatusHistoryEntity,
            );
        }
    }

    @CacheBusterWithPrefix<Approval[]>({
        stores: [Caches.LIST_CONTROLS, Caches.FRAMEWORK, Caches.REQUIREMENTS],
    })
    async bulkUpsertControlApprovals(
        dto: BulkUpsertApprovalsRequestDto,
        currentUser: User,
        account: Account,
        skipNotifications: boolean = false,
    ): Promise<Approval[]> {
        const { controlIds, deadlineDate, approverIds, status } = dto;
        const promises = controlIds.map(async controlId => {
            try {
                let xProduct: Product | null = null;
                if (isNil(account.getCurrentProduct())) {
                    // xProductId is not injected, manually lookup using control id
                    xProduct = await this.workspacesCoreService.getProductByControl(controlId);
                }

                const existingApproval = await this.listControlApprovals(
                    controlId,
                    true,
                    false,
                    account,
                );
                let approval = existingApproval[0] || null;
                if (isEmpty(approval)) {
                    approval = await this.createCurrentControlApproval(
                        controlId,
                        currentUser,
                        account,
                        xProduct ?? undefined,
                    );
                }

                return await this.updateControlApproval(
                    controlId,
                    approval.id,
                    { deadlineDate, approverIds, status },
                    account,
                    currentUser,
                    xProduct ?? undefined,
                    skipNotifications,
                );
            } catch (error) {
                this.logger.error(
                    PolloAdapter.acct(
                        `Something went wrong bulk creating approval for ${controlId}`,
                        account,
                    )
                        .setError(error)
                        .setContext(this.constructor.name)
                        .setSubContext(this.bulkUpsertControlApprovals.name),
                );
                return null;
            }
        });

        const upserted = await Promise.all(promises);

        return upserted.filter(upsert => !isEmpty(upsert));
    }

    async bulkDeleteControlsApprovals(
        account: Account,
        requestDto: BulkDeleteControlsApprovalsRequestDto,
        user: User,
    ): Promise<BulkDeleteControlsApprovalsResponseDto> {
        const { controlIds } = requestDto;
        const response = {
            error: false,
            deleted: true,
        } as BulkDeleteControlsApprovalsResponseDto;

        await Promise.allSettled(
            controlIds.map(async controlId => {
                try {
                    const { id: approvalId } =
                        await this.approvalService.getCurrentApprovalByControlId(controlId);

                    const isDeleted = await this.deleteControlApproval(
                        account,
                        user,
                        controlId,
                        approvalId,
                    );

                    if (!response.error) {
                        response.error = !isDeleted;
                    }

                    response.deleted = !response.error;

                    !isDeleted ??
                        this.logger.error(
                            PolloAdapter.acct(
                                `Something went wrong removing approval for the control [${controlId}]`,
                                account,
                            )
                                .setContext(this.constructor.name)
                                .setSubContext(this.bulkDeleteControlsApprovals.name),
                        );
                } catch (error) {
                    response.error = true;
                    this.logger.error(
                        PolloAdapter.acct(
                            `Something went wrong removing approval for the control [${controlId}]`,
                            account,
                        )
                            .setContext(this.constructor.name)
                            .setSubContext(this.bulkDeleteControlsApprovals.name),
                    );
                }
            }),
        );

        return response;
    }

    @CacheBusterWithPrefix<Approval>({
        stores: [Caches.LIST_CONTROLS, Caches.FRAMEWORK, Caches.REQUIREMENTS],
    })
    async updateControlApproval(
        controlId: number,
        approvalId: number,
        dto: UpdateApprovalRequestDto,
        account: Account,
        user: User,
        xProduct?: Product,
        skipNotifications: boolean = false,
    ): Promise<Approval> {
        const control = await this.controlService.getControlByIdWithOwnersAndApprovers(controlId);
        if (!control) {
            throw new NotFoundException('Control not found');
        }

        // If it's Control Manager with restricted view, only allow updating if user is approver
        if (
            !hasUserPermission(user, Action.READ, Subject.ViewAllControls) &&
            hasUserPermission(user, Action.READ, Subject.Control) &&
            !isControlApprover(control, user)
        ) {
            throw new ForbiddenException(
                `Restricted view enabled for ${user.email}`,
                ErrorCode.RESTRICTED_VIEW_ENABLED,
            );
        }

        const approval = await this.approvalService.getApprovalById(approvalId);

        approval.updatedBy = user;
        let newApproval: Approval;
        const oldApproval = { ...approval } as Approval;
        let reviews = [...approval.approvalReviews];
        // Update approvals when the approver ids is present
        if (isArray(dto.approverIds)) {
            const currentApprovalReviewsUserId = approval.approvalReviews.map(
                review => review.user.id,
            );
            const approvalReviewsUserIdsToCreate = difference(
                dto.approverIds,
                currentApprovalReviewsUserId,
            );
            const newReviews = await this.controlApprovalReviewService.createControlApprovalReview(
                account,
                user,
                controlId,
                approvalId,
                approvalReviewsUserIdsToCreate,
            );

            if (approval.status === ApprovalStatus.READY_FOR_REVIEWS && !skipNotifications) {
                await this.sendEmailAndNotificationsToNewReviewers(
                    newReviews,
                    account,
                    control,
                    user,
                    dto.deadlineDate,
                    xProduct ?? undefined,
                );
            }

            reviews = [...reviews, ...newReviews];

            const approvalReviewsUserIdsToRemove = difference(
                currentApprovalReviewsUserId,
                dto.approverIds,
            );

            const reviewIdsToDelete = approvalReviewsUserIdsToRemove.map(
                reviewUserId => approval.approvalReviews.find(aR => aR.user.id === reviewUserId).id,
            );
            await Promise.all(
                reviewIdsToDelete.map(reviewId =>
                    this.controlApprovalReviewService.deleteControlApprovalReview(
                        account,
                        user,
                        controlId,
                        approvalId,
                        reviewId,
                    ),
                ),
            );
            reviews = reviews.filter(review => !reviewIdsToDelete.includes(review.id));

            const reviewUsers = reviews.map(review => review.user);
            if (reviewUsers.length > 1) {
                const product = xProduct ?? account.getCurrentProduct();

                if (!isNil(product)) {
                    const productFrameworks =
                        await this.frameworksCoreService.getFrameworksEnabledByProduct(product.id);

                    const { isReady } = await this.controlService.findControlWithEvidenceById(
                        account,
                        user,
                        controlId,
                    );

                    this._eventBus.publish(
                        new ControlApprovalCreatedEvent(
                            account,
                            user,
                            approval,
                            productFrameworks,
                            account.companyName,
                            control,
                            isReady,
                        ),
                    );
                }
            }
        }
        newApproval = await this.approvalService.saveApproval({
            ...approval,
            ...{
                status: dto.status,
                deadlineDate: dto.deadlineDate,
                approvalReviews: reviews,
            },
        });

        // Only insert in approval status history when the status has changed
        if (oldApproval.status !== newApproval.status) {
            await this.insertApprovalStatusHistory(newApproval, user);
        }

        if (
            dto.status === ApprovalStatus.CHANGES_REQUESTED &&
            approval.status === ApprovalStatus.APPROVED
        ) {
            // Here we keep the old record as history
            await this.updateNextApprovalOnRequestChanges(controlId, newApproval, user, account);
        } else if (
            approval.status === ApprovalStatus.APPROVED ||
            dto.status == ApprovalStatus.APPROVED
        ) {
            /*
                When an approval is approved an upcoming approval
                is created, used by the cron jobs to replace this
                record when the date expires
            */
            [newApproval] = await this.cloneControlApproval(
                control.id,
                user,
                dto.nextDeadlineDate,
                account,
            );
        }

        const nextDeadlineSkipped = isNil(dto.nextDeadlineDate);

        await this.publishEventTracking(
            account,
            user,
            nextDeadlineSkipped,
            control,
            oldApproval,
            newApproval,
        );

        const workspaceId = account.getCurrentProduct()?.id;
        if (!isNil(workspaceId)) {
            this._eventBus.publish(
                new ControlReadinessEvent(account, control.id, workspaceId, user),
            );

            this._eventBus.publish(new ControlsReindexEvent(account, workspaceId, [controlId]));
        }

        await this.sendAlerts(
            control,
            approvalId,
            dto,
            oldApproval.status,
            account,
            user,
            xProduct,
        );

        return newApproval;
    }

    async listControlApprovals(
        controlId: number,
        current: boolean,
        next: boolean,
        account: Account,
    ): Promise<ControlApprovalDetail[]> {
        const control = await this.controlService.getControlById(controlId);
        if (!control) {
            throw new NotFoundException('Control not found');
        }
        let approvals: ControlApprovalDetail[] = [];
        if (current || next) {
            const [currentApprovalId, nextApprovalId] = await getCurrentAndNextControlApprovalIds(
                controlId,
                account,
                this._tenancyContext.getConnection(),
            );
            if (current && !isNil(currentApprovalId)) {
                try {
                    const currentApproval =
                        await this.approvalService.getApprovalById(currentApprovalId);

                    const lastApprovalStatusHistory =
                        await this.approvalsStatusHistoryService.getLastApprovalStatusHistoryByApprovalId(
                            currentApproval.id,
                        );

                    let currentControlApproval: ControlApprovalDetail;
                    if (!isNil(lastApprovalStatusHistory)) {
                        currentControlApproval = new ControlApprovalDetail(
                            currentApproval,
                            lastApprovalStatusHistory,
                        );
                    } else {
                        currentControlApproval = new ControlApprovalDetail(currentApproval);
                    }

                    approvals.push(currentControlApproval);
                } catch (error) {
                    this.logger.log(
                        PolloAdapter.acct(`Not current approval for control ${controlId}`, account)
                            .setContext(this.constructor.name)
                            .setSubContext(this.listControlApprovals.name),
                    );
                }
            }
            if (next && !isNil(nextApprovalId)) {
                try {
                    const nextApproval = await this.approvalService.getApprovalById(nextApprovalId);
                    approvals.push(nextApproval);
                } catch (error) {
                    this.logger.log(
                        PolloAdapter.acct(`Not next approval for control ${controlId}`, account)
                            .setContext(this.constructor.name)
                            .setSubContext(this.listControlApprovals.name),
                    );
                }
            }
        } else {
            approvals = await this.approvalService.getApprovalsByControlId(controlId, current);
        }

        const getApprovalWithLastReview = async (approval: Approval) => {
            approval.approvalReviews = [];
            const review = await this.approvalsReviewService.getLastReviewFromApproval(approval.id);
            // if not null we push it
            if (review !== undefined) approval.approvalReviews.push(review);
            return approval;
        };
        return Promise.all(
            approvals.map(async approval => {
                return getApprovalWithLastReview(approval);
            }),
        );
    }

    async getControlApproval(controlId: number, approvalId: number): Promise<Approval> {
        const control = await this.controlService.getControlById(controlId);
        if (!control) {
            throw new NotFoundException('Control not found');
        }
        return this.approvalService.getApprovalById(approvalId);
    }

    @CacheBusterWithPrefix<boolean>({
        stores: [Caches.LIST_CONTROLS, Caches.FRAMEWORK, Caches.REQUIREMENTS],
    })
    async deleteControlApproval(
        account: Account,
        user: User,
        controlId: number,
        approvalId: number,
    ): Promise<boolean> {
        const { control, isReady } = await this.controlService.findControlWithEvidenceById(
            account,
            user,
            controlId,
        );
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        const [_, nextApprovalId] = await getCurrentAndNextControlApprovalIds(
            controlId,
            account,
            this._tenancyContext.getConnection(),
        );

        const approval = await this.approvalService.getApprovalById(approvalId);

        const removed = await this.approvalService.deleteApproval(approvalId);
        if (nextApprovalId) {
            await this.approvalService.deleteApproval(nextApprovalId);
        }

        if (removed) {
            this.eventService.removeControlApproval(account, user, approval, control);
        } else {
            throw new ConflictException();
        }

        const productId = getProductId(account);
        const product = !isNil(productId)
            ? await this.workspaceBaseService.getProductById(productId)
            : await this.workspacesCoreService.getProductByControl(controlId);

        // Handle case where productId might be null
        if (!product) {
            this.logger.error(
                PolloAdapter.acct(`No product ID available for approval update`, account)
                    .setContext(this.constructor.name)
                    .setSubContext(this.updateControlApproval.name),
            );
            throw new BadRequestException('No product ID available for approval update');
        }

        const productFrameworks = await this.frameworksCoreService.getFrameworksEnabledByProduct(
            product.id,
        );

        const workspaceId = product.id;
        if (!isNil(workspaceId)) {
            this._eventBus.publish(
                new ControlReadinessEvent(account, control.id, workspaceId, user),
            );
        }

        // This is for analytics events
        this._eventBus.publish(
            new ControlApprovalRemovedEvent(
                account,
                user,
                approval,
                productFrameworks,
                product?.name ?? '',
                control,
                isReady,
            ),
        );
        return removed;
    }

    private async sendAlerts(
        control: Control,
        approvalId: number,
        dto: UpdateApprovalRequestDto,
        oldStatus: ApprovalStatus,
        account: Account,
        user: User,
        xProduct?: Product,
    ): Promise<void> {
        if (dto.status === oldStatus) {
            return;
        }

        const currentProduct = xProduct ?? account.getCurrentProduct();
        if (isNil(currentProduct)) {
            // This shouldn't happen unless there's corrupted data
            this.logger.warn(
                PolloAdapter.acct('Unable to send alerts: No current product found', account)
                    .setContext(this.constructor.name)
                    .setSubContext(this.sendAlerts.name),
            );
            return;
        }

        const productName = get(currentProduct, 'name', '');
        const hasMultipleProducts = await this.workspacesCoreService.hasMultipleProducts(account);
        const approvalReviews = await this.approvalsReviewService.getApprovalReviews(approvalId);
        switch (dto.status) {
            case ApprovalStatus.READY_FOR_REVIEWS:
                // Get approvals to send Email
                const approvalsToSendEmail = await this.getUsersWithControlApprovalFeatureOn(
                    approvalReviews.map(users => users.user.id),
                );
                await Promise.allSettled(
                    approvalsToSendEmail.map(async receiver => {
                        return this.sendRequiredApprovalEmail(
                            control,
                            productName,
                            hasMultipleProducts,
                            account,
                            user,
                            receiver.user,
                            dto.deadlineDate,
                        );
                    }),
                );

                // Get approvals email to send personnel notification on SLACK
                const approvalsEmail = approvalReviews.map(users => users.user.email);
                await this.notificationsCoreService.sendControlApprovalNotification(
                    account,
                    user,
                    control,
                    dto.status,
                    approvalsEmail,
                );
                break;
            case ApprovalStatus.CHANGES_REQUESTED:
                // Get owners to send Email
                const ownersToSendEmail = await this.getUsersWithControlApprovalFeatureOn(
                    control.owners?.map(owner => owner.id),
                );
                await Promise.allSettled(
                    ownersToSendEmail.map(async receiver => {
                        return this.sendRequestChangesEmail(
                            control,
                            productName,
                            hasMultipleProducts,
                            account,
                            user,
                            receiver.user,
                        );
                    }),
                );
                // Get owners email to send personnel notification on SLACK
                const ownersEmail = control.owners?.map(owner => owner.email);
                await this.notificationsCoreService.sendControlApprovalNotification(
                    account,
                    user,
                    control,
                    dto.status,
                    ownersEmail,
                );
                break;
            case ApprovalStatus.APPROVED:
                let ownersAndApprovals = control.owners;
                await asyncForEach(approvalReviews, async review => {
                    ownersAndApprovals.push(review.user);
                });
                // Filter duplicates
                ownersAndApprovals = [...new Set(ownersAndApprovals)];
                // Remove the approver who made the approval
                ownersAndApprovals = ownersAndApprovals.filter(
                    ownerApproval => ownerApproval.id !== user.id,
                );
                const userToSendEmail = await this.getUsersWithControlApprovalFeatureOn(
                    ownersAndApprovals.map(ownerApproval => ownerApproval.id),
                );
                await Promise.allSettled(
                    userToSendEmail.map(async receiver => {
                        return this.sendApproveEmail(
                            control,
                            productName,
                            hasMultipleProducts,
                            account,
                            user,
                            receiver.user,
                        );
                    }),
                );
                // Get owners and approvals email to send personnel notification on SLACK
                const ownersAndApprovalsEmail = ownersAndApprovals.map(
                    ownerApproval => ownerApproval.email,
                );
                await this.notificationsCoreService.sendControlApprovalNotification(
                    account,
                    user,
                    control,
                    dto.status,
                    ownersAndApprovalsEmail,
                );
                break;
        }
    }

    /**
     * @deprecated Use ControlsOrchestrationService.getUsersWithControlApprovalFeatureOn
     *
     * @param userIds
     * @returns
     */
    async getUsersWithControlApprovalFeatureOn(userIds: number[]): Promise<UserFeature[]> {
        if (userIds.length == 0) {
            return [];
        }
        return this.userFeatureRepository.getUserFeatures(
            userIds,
            FeatureType.CONTROL_APPROVAL_NOTIFICATION,
        );
    }

    async sendRequiredApprovalEmail(
        control: Control,
        productName: string,
        hasMultipleProducts: boolean,
        account: Account,
        user: User,
        receiver: User,
        deadline: Date,
    ): Promise<void> {
        this._eventBus.publish(
            new RequiredControlApprovalEmailEvent(
                account,
                user,
                control,
                receiver,
                productName,
                hasMultipleProducts,
                deadline,
            ),
        );
    }

    async sendRequestChangesEmail(
        control: Control,
        productName: string,
        hasMultipleProducts: boolean,
        account: Account,
        user: User,
        receiver: User,
    ): Promise<void> {
        this._eventBus.publish(
            new RequestChangesControlApprovalEmailEvent(
                account,
                user,
                control,
                receiver,
                productName,
                hasMultipleProducts,
            ),
        );
    }

    async sendApproveEmail(
        control: Control,
        productName: string,
        hasMultipleProducts: boolean,
        account: Account,
        user: User,
        receiver: User,
    ): Promise<void> {
        this._eventBus.publish(
            new ApproveControlApprovalEmailEvent(
                account,
                user,
                control,
                receiver,
                productName,
                hasMultipleProducts,
            ),
        );
    }

    async publishEventTracking(
        account: Account,
        user: User,
        skipNextDeadline: boolean,
        control: Control,
        oldApproval: Approval,
        newApproval: Approval,
    ): Promise<void> {
        if (oldApproval.status === ApprovalStatus.INITIALIZED) {
            this.eventService.setUpControlApprovalEvent(account, user, control, newApproval);
        } else if (
            newApproval?.status === ApprovalStatus.READY_FOR_REVIEWS &&
            oldApproval?.status === ApprovalStatus.PREPARE_FOR_REVIEWS
        ) {
            const reviews = await this.approvalsReviewService.getApprovalReviews(newApproval?.id);

            const task = await this.tasksCoreService.getTaskDetailsByIdAndType({
                id: control.id,
                type: UpcomingTaskType.CONTROL_APPROVALS,
            });

            this.eventService.sentControlApprovalToApproversEvent(
                account,
                user,
                skipNextDeadline,
                control,
                newApproval,
                oldApproval,
                reviews,
                task,
            );
        } else if (
            newApproval?.status !== ApprovalStatus.CHANGES_REQUESTED &&
            oldApproval.status === ApprovalStatus.APPROVED
        ) {
            this.eventService.editControlApprovalEvent(
                account,
                user,
                skipNextDeadline,
                control,
                newApproval,
                oldApproval,
            );
        }
    }

    async cloneControlApproval(
        controlId: number,
        user: User,
        deadlineDate: Date,
        account: Account,
    ): Promise<[Approval, Approval]> {
        if (isNil(controlId)) {
            throw new BadRequestException('control id not sent to: cloneControlApproval');
        }
        const control = await this.controlService.getControlById(controlId);
        if (isNil(control)) {
            throw new NotFoundException(`Control with the ID ${controlId} not found`);
        }
        const [currentApprovalId, nextApprovalId] = await getCurrentAndNextControlApprovalIds(
            controlId,
            account,
            this._tenancyContext.getConnection(),
        );

        if (isNil(currentApprovalId)) {
            throw new NotFoundException(`Control ${controlId} has no current approval assign`);
        }

        const currentApproval = await this.approvalService.getApprovalById(currentApprovalId);
        if (!nextApprovalId) {
            const nextApproval = await this.approvalService.saveApproval(
                this.approvalService.createApproval({
                    deadlineDate,
                    controls: [control],
                    createdBy: user,
                    status: ApprovalStatus.READY_FOR_REVIEWS,
                }),
            );

            // Insert in approval status history when the next approval is created
            await this.insertApprovalStatusHistory(nextApproval, user);

            return [currentApproval, nextApproval];
        } else {
            const nextApproval = await this.approvalService.getApprovalById(nextApprovalId);

            nextApproval.deadlineDate = deadlineDate || nextApproval.deadlineDate;

            const updatedApproval = await this.approvalService.saveApproval(nextApproval);

            return [currentApproval, updatedApproval];
        }
    }

    async updateNextApprovalOnRequestChanges(
        controlId: number,
        currentApproval: Approval,
        updatedBy: User,
        account: Account,
    ): Promise<void> {
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        const [_, nextApprovalId] = await getCurrentAndNextControlApprovalIds(
            controlId,
            account,
            this._tenancyContext.getConnection(),
        );

        if (isNil(nextApprovalId)) {
            throw new NotFoundException(`The control [${controlId}] has no next approval`);
        }

        // validate here that you have something othrwise you are introuble
        await this.approvalService.saveApproval({
            ...currentApproval,
            updatedBy,
            current: false,
        });

        const nextApproval = await this.approvalService.getApprovalById(nextApprovalId);

        await Promise.all(
            currentApproval.approvalReviews.map(async approvalReview =>
                this.approvalsReviewService.saveApprovalReview({
                    ...omit(approvalReview, ['id']),
                    approval: nextApproval,
                }),
            ),
        );
        const savedApproval = await this.approvalService.saveApproval({
            updatedBy,
            id: nextApproval.id,
            current: true,
            status: ApprovalStatus.CHANGES_REQUESTED,
        });

        // Insert in approval status history when the next approval is marked as the new approval an it's status is changed to changes requested
        await this.insertApprovalStatusHistory(savedApproval, updatedBy);
    }

    async sendEmailAndNotificationsToNewReviewers(
        approvalReviews: ApprovalReview[],
        account: Account,
        control: Control,
        user: User,
        dateline: Date,
        xProduct?: Product,
    ): Promise<void> {
        if (approvalReviews.length === 0) {
            return;
        }
        // xProduct takes precedence because account.getCurrrentProduct might return null
        const productName = xProduct?.name ?? account.getCurrentProduct().name;
        const hasMultipleProducts = await this.workspacesCoreService.hasMultipleProducts(account);
        const message = `${capitalize(fullName(user))} submitted ${control.code} for review.`;
        const buttonText = `View ${control.code}`;

        // get approvals with Feature ON to send Email
        const approvalsToSendEmail = await this.getUsersWithControlApprovalFeatureOn(
            approvalReviews.map(users => users.user.id),
        );
        await Promise.allSettled(
            approvalsToSendEmail.map(async receiver => {
                return this.sendRequiredApprovalEmail(
                    control,
                    productName,
                    hasMultipleProducts,
                    account,
                    user,
                    receiver.user,
                    dateline,
                );
            }),
        );

        // send personnel notification on SLACK
        await Promise.allSettled(
            approvalReviews.map(async users => {
                return this.notificationsCoreService.sendControlApprovalDirectNotification(
                    account,
                    control,
                    message,
                    buttonText,
                    users.user.email,
                );
            }),
        );
    }

    async updateControlApprovalsAttachedToEvidence(
        account: Account,
        user: User,
        evidenceId: number,
        updateApprovalsAttachedToEvidenceRequestDto: UpdateApprovalsAttachedToEvidenceRequestDto,
    ): Promise<void> {
        try {
            this.validateApprovalControlStatus(updateApprovalsAttachedToEvidenceRequestDto);

            const unlinkedControlIds = updateApprovalsAttachedToEvidenceRequestDto.controlIds;

            const linkedControlIds = await this.getEvidenceControlsIds(evidenceId);
            const modifiedControlIds = union(linkedControlIds, unlinkedControlIds);

            const controlApprovalsToChangeTheStatus = await this.findControlsFromApprovalStatus(
                modifiedControlIds,
                [
                    ApprovalStatus.PREPARE_FOR_REVIEWS,
                    ApprovalStatus.READY_FOR_REVIEWS,
                    ApprovalStatus.APPROVED,
                ],
            );

            const newApprovalStatus = updateApprovalsAttachedToEvidenceRequestDto.newApprovalStatus;
            await this.updateControlApprovalsStatus(
                account,
                user,
                controlApprovalsToChangeTheStatus,
                newApprovalStatus,
            );
        } catch (error) {
            this.logger.log(
                PolloAdapter.acct('Something went wrong updating control approval status.', account)
                    .setError(error)
                    .setContext(this.constructor.name)
                    .setSubContext(this.updateControlApprovalsAttachedToEvidence.name),
            );
        }
    }

    private validateApprovalControlStatus(
        updateApprovalsAttachedToEvidenceRequestDto: UpdateApprovalsAttachedToEvidenceRequestDto,
    ): void {
        if (
            isEmpty(updateApprovalsAttachedToEvidenceRequestDto.controlIds) ||
            isNil(updateApprovalsAttachedToEvidenceRequestDto.newApprovalStatus)
        ) {
            throw new BadRequestException('Control ids and new approval status are required');
        }
    }

    async updateControlApprovalsApproved(
        account: Account,
        user: User,
        newControlsApprovalStatus: ApprovalStatus,
        evidenceControlIds: number[],
        newControlIds: number[] = [],
    ): Promise<void> {
        // Filter to get only the controlIds that are being added/removed
        try {
            const modifiedControlIds = newControlIds
                .filter(id => !evidenceControlIds.includes(id))
                .concat(evidenceControlIds.filter(id => !newControlIds.includes(id)));

            const controlApproved = await this.findControlsFromApprovalStatus(
                modifiedControlIds.length > 0 ? modifiedControlIds : evidenceControlIds,
                [ApprovalStatus.APPROVED],
            );

            await this.updateControlApprovalsStatus(
                account,
                user,
                controlApproved,
                newControlsApprovalStatus,
            );
        } catch (error) {
            this.logger.log(
                PolloAdapter.acct('Something went wrong updating control approval status.', account)
                    .setError(error)
                    .setContext(this.constructor.name)
                    .setSubContext(this.updateControlApprovalsApproved.name),
            );
        }
    }

    @TenancyTransaction()
    private async updateControlApprovalsStatus(
        account: Account,
        user: User,
        controls: Control[],
        newControlsApprovalStatus: ApprovalStatus,
    ): Promise<void> {
        type controlApprovalDetailToPublish = {
            control: Control;
            newApproval: Approval;
            oldApproval: Approval;
        };
        const controlApprovalsDetailToPublish: controlApprovalDetailToPublish[] = [];

        if (controls.length !== 0) {
            await Promise.all(
                controls.map(async control => {
                    try {
                        const oldApproval = {
                            ...control.approvals[0],
                        } as Approval;

                        const newApproval = await this.approvalService.saveApproval({
                            ...control.approvals[0],
                            ...{ status: newControlsApprovalStatus },
                        });

                        //Insert in approval status history when the approval status changes to a new status
                        await this.insertApprovalStatusHistory(newApproval, user);

                        controlApprovalsDetailToPublish.push({
                            control,
                            newApproval,
                            oldApproval,
                        });
                    } catch (error) {
                        this.logger.log(
                            PolloAdapter.acct(
                                'Something went wrong updating the approval status.',
                                account,
                            )
                                .setError(error)
                                .setContext(this.constructor.name)
                                .setSubContext(this.updateControlApprovalsStatus.name),
                        );
                        //this is needed for the transaction rollback if something fails
                        throw error;
                    }
                }),
            );

            this.publishControlApprovalStatusChangeEvent(
                controlApprovalsDetailToPublish,
                account,
                user,
            );

            await this.controlApprovalStateChangeEmailAndNotifications(account, user, controls);
        }
    }

    private publishControlApprovalStatusChangeEvent(
        controlApprovalsDetailToPublish: {
            control: Control;
            newApproval: Approval;
            oldApproval: Approval;
        }[],
        account: Account,
        user: User,
    ) {
        for (const controlApprovalDetailToPublish of controlApprovalsDetailToPublish) {
            this._eventBus.publish(
                new ControlApprovalStatusChangeEvent(
                    account,
                    user,
                    controlApprovalDetailToPublish.control,
                    ApprovalStatusChangeSource.EVIDENCE,
                    controlApprovalDetailToPublish.newApproval,
                    controlApprovalDetailToPublish.oldApproval,
                ),
            );
        }
    }

    async controlApprovalStateChangeEmailAndNotifications(
        account: Account,
        user: User,
        controlApproved: Control[],
    ): Promise<void> {
        const currentProduct = account.getCurrentProduct();
        let product: Product | null;

        const retrieveControlByProductId =
            isNil(currentProduct) && !isEmpty(controlApproved) && !isNil(controlApproved[0]);

        if (retrieveControlByProductId) {
            product = await this.workspacesCoreService.getProductByControl(
                Number(controlApproved[0].id),
            );
        } else {
            product = await this.workspacesCoreService.getProductById(currentProduct.id);
        }

        const productName = get(product, 'name', '');
        const hasMultipleProducts = await this.workspacesCoreService.hasMultipleProducts(account);
        if (controlApproved.length === 1) {
            // Send Email
            const ownersToSendEmail = await this.getUsersWithControlApprovalFeatureOn(
                controlApproved[0].owners.map(owner => owner.id),
            );
            await Promise.all(
                ownersToSendEmail.map(async receiver => {
                    await this.sendControlApprovalStateChangeEmail(
                        account,
                        user,
                        receiver.user,
                        controlApproved[0],
                        productName,
                        hasMultipleProducts,
                    );
                }),
            );

            // Send Notification (Slack, MS Teams)
            const ownersEmail = controlApproved[0].owners.map(owner => owner.email);
            await this.notificationsCoreService.sendControlApprovalUpdateEvidenceNotification(
                account,
                ownersEmail,
                controlApproved[0],
            );
        } else {
            const owners = [];
            await Promise.all(
                controlApproved.map(async control => {
                    control.owners.forEach(userOwner => owners.push(userOwner));
                }),
            );

            // Send Email
            const ownersToSendEmail = await this.getUsersWithControlApprovalFeatureOn(
                owners.map(owner => owner.id),
            );
            await Promise.all(
                ownersToSendEmail.map(async receiver => {
                    return this.sendControlApprovalBulkStateChangeEmail(
                        account,
                        user,
                        receiver.user,
                        productName,
                        hasMultipleProducts,
                    );
                }),
            );

            // Send Notification (Slack, MS Teams)
            const ownersEmail = owners.map(owner => owner.email);
            await this.notificationsCoreService.sendControlApprovalUpdateEvidenceNotification(
                account,
                ownersEmail,
            );
        }
    }

    async sendControlApprovalStateChangeEmail(
        account: Account,
        sender: User,
        receiver: User,
        control: Control,
        productName: string,
        hasMultipleProducts: boolean,
    ): Promise<void> {
        this._eventBus.publish(
            new ControlApprovalStateChangeEmailEvent(
                account,
                sender,
                control,
                receiver,
                productName,
                hasMultipleProducts,
            ),
        );
    }

    async sendControlApprovalBulkStateChangeEmail(
        account: Account,
        sender: User,
        receiver: User,
        productName: string,
        hasMultipleProducts: boolean,
    ): Promise<void> {
        this._eventBus.publish(
            new ControlApprovalBulkStateChangeEmailEvent(
                account,
                sender,
                receiver,
                productName,
                hasMultipleProducts,
            ),
        );
    }

    async getControlsWithApprovalsByOwners(
        requestDto: ControlsApprovalsRequestDto,
    ): Promise<Control[]> {
        return this.grcCoreService.getFilteredControlsWithApprovalsByOwners(
            requestDto.controlIds,
            requestDto.ownerIds,
        );
    }

    /**
     * @deprecated Use ControlsOrchestrationService.updateControlApprovalsApprovedInPolicy
     *
     * @param account
     * @param user
     * @param controlIds
     * @returns
     */
    async updateControlApprovalsApprovedInPolicy(
        account: Account,
        user: User,
        controlIds: number[],
    ): Promise<void> {
        if (isEmpty(controlIds)) {
            return;
        }
        const controlsApproved = await this.findControlsFromApprovalStatus(controlIds, [
            ApprovalStatus.APPROVED,
        ]);

        if (isEmpty(controlsApproved)) {
            return;
        }
        await Promise.allSettled(
            controlsApproved.map(async control => {
                const oldApproval = { ...control.approvals[0] } as Approval;
                // Update status
                const newApproval = await this.approvalService.saveApproval({
                    ...control.approvals[0],
                    ...{ status: ApprovalStatus.PREPARE_FOR_REVIEWS },
                });

                //Insert in approval status history when the approval status changes to a new status
                await this.insertApprovalStatusHistory(newApproval, user);

                // Event Tracking
                await this._eventBus.publish(
                    new ControlApprovalStatusChangeEvent(
                        account,
                        user,
                        control,
                        ApprovalStatusChangeSource.POLICY,
                        newApproval,
                        oldApproval,
                    ),
                );

                // Emails and Notifications
                return this.controlApprovalStageChangePolicyEmailAndNotifications(
                    account,
                    user,
                    control,
                );
            }),
        );
    }

    private async controlApprovalStageChangePolicyEmailAndNotifications(
        account: Account,
        user: User,
        controlApproved: Control,
    ): Promise<void> {
        // Send Email
        const ownersToSendEmail = await this.getUsersWithControlApprovalFeatureOn(
            controlApproved.owners.map(owner => owner.id),
        );
        await Promise.allSettled(
            ownersToSendEmail.map(async receiver => {
                return this._eventBus.publish(
                    new ControlApprovalStageChangePolicyEmailEvent(
                        account,
                        user,
                        controlApproved,
                        receiver.user,
                    ),
                );
            }),
        );

        // Send Notification (Slack, MS Teams)
        const emailsToNotify = controlApproved.owners.map(owner => owner.email);
        await this.notificationsCoreService.sendControlApprovalUpdatedPolicyNotification(
            account,
            emailsToNotify,
            controlApproved,
        );
    }

    private async getEvidenceControlsIds(evidenceId: number): Promise<Array<number>> {
        const evidenceMap = await this.documentLibraryControlsMapRepository.find({
            where: { libraryDocument: { id: evidenceId } },
        });
        if (isNil(evidenceMap) || isEmpty(evidenceMap)) {
            return [];
        }
        return evidenceMap.map(evidence => evidence.control.id);
    }

    /**
     * @deprecated Use ControlsOrchestrationService.findControlsFromApprovalStatus
     *
     * @param controlIds
     * @param approvalStatuses
     * @returns
     */
    private async findControlsFromApprovalStatus(
        controlIds: number[],
        approvalStatuses: ApprovalStatus[] = [],
    ): Promise<Control[]> {
        return this.controlRepository.findControlsFromCurrentApprovalStatuses(
            controlIds,
            approvalStatuses,
        );
    }

    private get userFeatureRepository(): UserFeatureRepository {
        return this.getCustomTenantRepository(UserFeatureRepository);
    }
    private get documentLibraryControlsMapRepository(): Repository<LibraryDocumentControlsMap> {
        return this.getTenantRepository(LibraryDocumentControlsMap);
    }
    private get controlRepository(): ControlRepository {
        return this.getCustomTenantRepository(ControlRepository);
    }
}
