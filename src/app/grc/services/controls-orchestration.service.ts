import { ApprovalStatus, ErrorCode, FrameworkTag } from '@drata/enums';
import {
    BadRequestException,
    Injectable,
    InternalServerErrorException,
    NotFoundException,
    UnprocessableEntityException,
} from '@nestjs/common';
import { ControlFilteredByEvidenceEvent } from 'app/analytics/observables/events/control-filtered-by-evidence.event';
import { ControlFilteredByPolicyEvent } from 'app/analytics/observables/events/control-filtered-by-policy-event';
import { ControlOwnerDeletedEvent } from 'app/analytics/observables/events/control-owner-deleted';
import { PolicyAssociatedToControlEvent } from 'app/analytics/observables/events/policy-associated-to-control-event';
import { PolicyUnassociatedToControlEvent } from 'app/analytics/observables/events/policy-unassocciated-to-control-event';
import { ApprovalStatusHistory } from 'app/approvals/v1/entities/approval-status-history.entity';
import { Approval } from 'app/approvals/v1/entities/approval.entity';
import { ApprovalsCoreService } from 'app/approvals/v2/approvals-core/services/approvals-core.service';
import { ConnectionFeatureClientTypes } from 'app/companies/connections/maps/connection-feature-client-types.map';
import { ConnectionsCoreService } from 'app/companies/connections/services/connections-core.service';
import { FileNames } from 'app/companies/helpers/file-names.helper';
import { Product } from 'app/companies/products/entities/product.entity';
import { ProductRepository } from 'app/companies/products/repositories/product.repository';
import { WorkspacesCoreService } from 'app/companies/products/services/workspaces-core.service';
import { ControlAttachedToPolicyWorkflowTriggerEvent } from 'app/custom-workflows/custom-workflows-common/triggers/events/control-attached-to-policy-trigger.workflow.event';
import { ControlOwnerUpdatedWorkflowTriggerEvent } from 'app/custom-workflows/custom-workflows-common/triggers/events/control-owner-updated-trigger.worklow';
import { ControlUnattachedFromPolicyWorkflowTriggerEvent } from 'app/custom-workflows/custom-workflows-common/triggers/events/control-unattached-from-policy-trigger.workflow.event';
import { AuditHubLinkedPolicyVersionsEvidenceEvent } from 'app/customer-request/observables/events/audit-hub-uploaded-evidence/audit-hub-linked-policy-versions-evidence-event';
import { LibraryDocument } from 'app/document-library/entities/library-document.entity';
import { DocumentLibraryCoreService } from 'app/document-library/services/document-library-core.service';
import { ExternalEvidenceCoreService } from 'app/document-library/services/external-evidence-core.service';
import { ControlApprovalStatusChangeEvent } from 'app/events/observables/events/control-approval-status-change.event';
import { UserFeature } from 'app/feature-toggling/entities/user-feature.entity';
import { UserFeatureRepository } from 'app/feature-toggling/repositories/user-feature.repository';
import { reportDataTransformHelper } from 'app/frameworks/helpers/framework.helper';
import { FrameworkRepository } from 'app/frameworks/repositories/framework.repository';
import { FrameworksCoreService } from 'app/frameworks/services/frameworks-core.service';
import { FrameworkTagType } from 'app/frameworks/types/framework-tag.type';
import { ISO27001AnnexAArray } from 'app/frameworks/types/iso-27001-annex-a-array';
import {
    ISO270012022AnnexAArray,
    ISO270012022ISMSArray,
} from 'app/frameworks/types/requirement-categories/iso27001-2022-category.enum';
import { GrcCoreService } from 'app/grc/core/services/grc-core.service';
import { ControlEvidenceRequestDto } from 'app/grc/dtos/control-evidence-request.dto';
import { ControlOwnerBulkRequestDto } from 'app/grc/dtos/control-owner-bulk-request.dto';
import { ControlPolicyRequestDto } from 'app/grc/dtos/control-policy-request.dto';
import { ControlReportsRequestDto } from 'app/grc/dtos/control-reports-request.dto';
import { ControlsRequestDto } from 'app/grc/dtos/controls-request.dto';
import { ControlIsReadyView } from 'app/grc/entities/control-is-ready-view.entity';
import { Control } from 'app/grc/entities/control.entity';
import { ExternalEvidence } from 'app/grc/entities/external-evidence.entity';
import { ApprovalFacet } from 'app/grc/facets/control/approval-facet.class';
import { ApproverFacet } from 'app/grc/facets/control/approver-facet.class';
import { ControlFacet } from 'app/grc/facets/control/control-facet.class';
import { IsArchivedFacet } from 'app/grc/facets/control/is-archived-facet.class';
import { IsReadyFacet } from 'app/grc/facets/control/is-ready-facet.class';
import { OwnerFacet } from 'app/grc/facets/control/owner-facet.class';
import { SearchFacet } from 'app/grc/facets/control/search-facet.class';
import { TaskOwnerFacet } from 'app/grc/facets/control/task-owner-facet.class';
import { TestFacet } from 'app/grc/facets/control/test-facet.class';
import { TicketFacet } from 'app/grc/facets/control/ticket-facet.class';
import { WorskpaceFacet } from 'app/grc/facets/control/workspace-facet.class';
import { FacetRunner } from 'app/grc/facets/facet-runner.class';
import { IControlIssuesStats } from 'app/grc/interfaces/control-issues-stats.interface';
import { ControlApprovalStageChangePolicyEmailEvent } from 'app/grc/observables/events/control-approval-stage-change-policy-email-event';
import { ControlFlagsViewRepository } from 'app/grc/repositories/control-flags-view.repository';
import { ControlIsReadyViewRepository } from 'app/grc/repositories/control-is-ready-view.repository';
import { ControlTicketViewRepository } from 'app/grc/repositories/control-ticket-view.repository';
import { ControlRepository } from 'app/grc/repositories/control.repository';
import { AccountControlRequestType } from 'app/grc/types/account-control-request.type';
import { ControlListWithFlags } from 'app/grc/types/control-list.type';
import { ControlOverviewType } from 'app/grc/types/control-overview.type';
import { ControlOwnersPutRequestType } from 'app/grc/types/control-owners-put-request.type';
import { ControlOwnersRequestType } from 'app/grc/types/control-owners-request.type';
import { ControlReadinessEvent } from 'app/grc/workflow-services/events/control-readiness.workflow.event';
import { MonitorsOrchestrationService } from 'app/monitors/services/monitors-orchestration.service';
import { NotificationsCoreService } from 'app/notifications/services/notifications-core.service';
import { User } from 'app/users/entities/user.entity';
import { Policy } from 'app/users/policies/entities/policy.entity';
import { PoliciesCoreService } from 'app/users/policies/services/policies-core.service';
import { PolicyForEvent } from 'app/users/policies/types/policy-event.type';
import { UsersCoreService } from 'app/users/services/users-core.service';
import { getAuditorFrameworkTimePeriodForEvidence } from 'auditors/helpers/auditor.helper';
import { Audit } from 'audits/entities/audit.entity';
import { Account } from 'auth/entities/account.entity';
import { CacheWithPrefix } from 'cache/cache.decorator';
import { PolloAdapter } from 'commons/adapters/pollo.adapter';
import { ApprovalStatusChangeSource } from 'commons/enums/approvals/v1/approval-status-change-source.enum';
import { Caches } from 'commons/enums/cache.enum';
import { ConnectionFeature } from 'commons/enums/connection-feature.enum';
import { FeatureType } from 'commons/enums/feature-toggling/feature.enum';
import { LibraryDocumentVersionType } from 'commons/enums/library-document-version-type.enum';
import { Role } from 'commons/enums/users/role.enum';
import { asyncForEach } from 'commons/helpers/array.helper';
import {
    getCSVCustomStream,
    getCSVStream,
    getStreamsFromFiles,
    mapFilesToPrefix,
} from 'commons/helpers/buffer.helper';
import { sanitizeFileName } from 'commons/helpers/file.helper';
import { getAutopilotTaskTypesByProviderType } from 'commons/helpers/monitor.helper';
import { getStreamsFromPolicyVersionsFiles } from 'commons/helpers/policy.helper';
import {
    accountConnectionProviderTypes,
    getProductId,
    hasAssociatedProduct,
} from 'commons/helpers/products.helper';
import { hasRole } from 'commons/helpers/user.helper';
import { AppService } from 'commons/services/app.service';
import { CsvDataSetType } from 'commons/types/csv-data-set.type';
import { CursorPage } from 'commons/types/cursor-page.type';
import { FileBufferType } from 'commons/types/file-buffer.type';
import { PaginationType } from 'commons/types/pagination.type';
import config from 'config';
import { Downloader } from 'dependencies/downloader/downloader';
import { FeatureFlagCategory } from 'feature-flags/enums/feature-flag-category.enum';
import { FeatureFlag } from 'feature-flags/feature-flag.enum';
import { FeatureFlagService } from 'feature-flags/feature-flag.service';
import { concat, get, isEmpty, isNil, isUndefined } from 'lodash';
import moment from 'moment';
import { PolloMessage } from 'pollo-logger/classes/pollo.message';
import { tenantWrapper } from 'tenancy/contexts/tenant-wrapper';
import { In, Repository } from 'typeorm';

@Injectable()
export class ControlsOrchestrationService extends AppService {
    constructor(
        private readonly grcCoreService: GrcCoreService,
        private readonly externalEvidenceCoreService: ExternalEvidenceCoreService,
        private readonly downloader: Downloader,
        private readonly documentLibraryCoreService: DocumentLibraryCoreService,
        private readonly policiesCoreService: PoliciesCoreService,
        private readonly workspacesCoreService: WorkspacesCoreService,
        private readonly frameworksCoreService: FrameworksCoreService,
        private readonly connectionsCoreService: ConnectionsCoreService,
        private readonly monitorsOrchestrationService: MonitorsOrchestrationService,
        private readonly approvalsCoreService: ApprovalsCoreService,
        private readonly notificationsCoreService: NotificationsCoreService,
        private readonly usersCoreService: UsersCoreService,
        private readonly featureFlagService: FeatureFlagService,
    ) {
        super();
    }

    @CacheWithPrefix<PaginationType<ControlListWithFlags>>(null, {
        store: Caches.LIST_CONTROLS,
        useArgs: 1,
        ttl: config.get('cache.ttl.hour'),
    })
    async listControlsGroupBy(
        requestDto: ControlsRequestDto,
        account: Account,
        user?: User,
    ): Promise<PaginationType<ControlListWithFlags>> {
        const { page, limit } = requestDto;
        if (!isNil(requestDto.hasTicket)) {
            const clientTypes = ConnectionFeatureClientTypes.get(
                ConnectionFeature.TICKETING_TICKET_MANAGEMENT,
            );

            const ticketingWriteConnectionIds =
                await this.connectionsCoreService.getConnectionIdsWithWriteAccessByClientTypes(
                    clientTypes,
                );

            if (!isEmpty(ticketingWriteConnectionIds)) {
                requestDto.connectionIds = ticketingWriteConnectionIds;
            }
        }
        if (requestDto.hasEvidence) {
            this._eventBus.publish(new ControlFilteredByEvidenceEvent(account, user));
        }
        if (requestDto.hasPolicy) {
            this._eventBus.publish(new ControlFilteredByPolicyEvent(account, user));
        }

        /**
         * Defaults to the primary product ID if no product ID is supplied in the dto
         * or extracted from the account. Without a supplied product ID, controls will be
         * returned without frameworkTags. This was done to account for a gap in our public api
         */

        let productId = getProductId(account);
        // productId should only be supplied in requestDto via public api
        if (requestDto?.workspaceId) {
            const product = await this.workspacesCoreService.getProductById(
                requestDto?.workspaceId,
            );

            if (isNil(product)) {
                throw new NotFoundException(
                    `Workspace not found for ID: ${requestDto?.workspaceId}`,
                );
            }

            productId = requestDto?.workspaceId;
        } else if (isNil(productId)) {
            productId = (await this.workspacesCoreService.getPrimaryProduct()).id ?? null;
        }

        const productFrameworksEnabled =
            await this.frameworksCoreService.getFrameworksEnabledByProduct(productId);

        const activeFrameworks = productFrameworksEnabled.map(item => item.framework);

        if (!isEmpty(requestDto.libraryDocumentId)) {
            const controlIdsEvidence = await this.documentLibraryCoreService.getEvidenceControlsIds(
                requestDto.libraryDocumentId,
            );
            if (!isEmpty(controlIdsEvidence)) {
                if (!isEmpty(requestDto.excludeIds)) {
                    requestDto.excludeIds = requestDto.excludeIds.concat(controlIdsEvidence);
                } else {
                    requestDto.excludeIds = controlIdsEvidence;
                }
            }
        }

        const [controlIds, total] = await this.getPaginatedControlIds(account, requestDto);

        const controls = await this.controlRepository.getControlsFromIdsGroupBy(
            controlIds,
            requestDto.sort,
            requestDto.sortDir,
        );

        const foundControlIds = controls.map(c => c.id);

        const controlFlags = !isEmpty(foundControlIds)
            ? await this.controlFlagsViewRepository.getControlFlags(foundControlIds)
            : [];

        let data: ControlListWithFlags[] = [];

        if (controlIds.length > 0) {
            for (const control of controls) {
                control.description = control.description.replace(/%s/g, account.companyName);

                const controlFlagSet = controlFlags.find(flags => flags.controlId == control.id);

                if (!isEmpty(activeFrameworks)) {
                    const frameworkTags = controlFlagSet.frameworkTags.reduce((acc, tag) => {
                        if (tag !== FrameworkTag.CUSTOM) {
                            // Incorporating the filter condition inside reduce
                            const frameworkTagData = activeFrameworks.find(
                                framework => framework.tag === tag,
                            );

                            if (!isUndefined(frameworkTagData)) {
                                // Only add to acc if frameworkTagData is defined
                                acc.push({
                                    label: frameworkTagData.pill,
                                    color: frameworkTagData.color,
                                    bgColor: frameworkTagData.bgColor,
                                });
                            }
                        }
                        return acc;
                    }, []);

                    const customFrameworkPill: FrameworkTagType[] = activeFrameworks
                        .filter(framework =>
                            controlFlagSet.customFrameworkSlugs.includes(framework.slug),
                        )
                        .map(framework => ({
                            label: framework.pill,
                            color: framework.color,
                            bgColor: framework.bgColor,
                        }));

                    control.frameworkTags = concat(frameworkTags, customFrameworkPill);
                } else {
                    control.frameworkTags = [];
                }

                control.topics = controlFlagSet.topics;

                data.push({
                    control,
                    hasEvidence: controlFlagSet.hasEvidence,
                    hasPolicy: controlFlagSet.hasPolicy,
                    hasOwner: controlFlagSet.hasOwner,
                    isMonitored: controlFlagSet.isMonitored,
                    isReady: controlFlagSet.isReady,
                    hasTicket: controlFlagSet.hasTicket,
                });
            }

            const getControlIssuesFeature = await this.featureFlagService.evaluateAsTenant(
                {
                    name: FeatureFlag.RELEASE_CONTROL_ACTION_PANEL,
                    category: FeatureFlagCategory.NONE,
                    defaultValue: false,
                },
                account,
            );

            if (getControlIssuesFeature) {
                const notReadyControls = data
                    ?.filter(control => !control?.isReady)
                    .map(control => control?.control?.id);
                const issuesMapping = await this.getControlFindings(notReadyControls, account);

                if (!isEmpty(issuesMapping)) {
                    data = data.map((control: ControlListWithFlags) => {
                        const findings = issuesMapping[String(control.control.id)];
                        control.issues = findings;
                        return control;
                    });
                }
            }
        }
        return {
            data,
            page,
            limit,
            total,
        };
    }

    async getPaginatedAccountControls(
        requestDto: AccountControlRequestType,
    ): Promise<PaginationType<ControlOverviewType>> {
        const { page, limit, accountId } = requestDto;

        /**
         * Let's create an account entity with partial data
         * This is needed to avoid querying the database for the account data
         * since we only need the account id, company name and current product.
         * The account entity was already loaded and the current product was
         * extracted from it.
         */
        const account = new Account();
        account.id = accountId;
        account.companyName = requestDto.companyName;
        const currentProduct = new Product();
        currentProduct.id = requestDto.workspaceId;
        account.setCurrentProduct(currentProduct);

        const [controlIds, total] = await this.getPaginatedControlIds(
            account,
            requestDto as unknown as ControlsRequestDto,
        );

        const controls = await this.controlRepository.getLightweightControlsFromIdsGroupBy(
            controlIds,
            requestDto.sort,
            requestDto.sortDir,
        );

        const data: ControlOverviewType[] = [];

        if (controlIds.length > 0) {
            const mutatedControls = controls.map(control => ({
                ...control,
                description: control.description.replace(/%s/g, requestDto.companyName),
            }));
            data.push(...mutatedControls);
        }

        return {
            data,
            page,
            limit,
            total,
        };
    }

    /**
     *
     * This method is in charge of retrieving all the issues related to a control that makes it as not ready
     *
     * @param controlIds
     * @param account
     * @returns
     */
    async getControlFindings(
        controlIds: number[],
        account: Account,
    ): Promise<Record<number, IControlIssuesStats>> {
        const controlIssueMapping: Record<number, IControlIssuesStats> = {};
        if (isEmpty(controlIds)) {
            return {};
        }

        const [issuesStatsByControl, approvalsByControl] = await Promise.all([
            this.grcCoreService.getIssuesByControlIds(controlIds, account),
            this.grcCoreService.getBulkedCurrentControlApproval(controlIds, account),
        ]);

        issuesStatsByControl.map(issueStat => {
            const { policies, evidence, tests, controlId } = issueStat;

            const approvalSchema = approvalsByControl.find(
                eachApproval => eachApproval?.controlId === controlId,
            );

            const statusAprovalSchema = get(approvalSchema, 'approval.status', null);

            const isApprovalNeeded =
                !isEmpty(approvalSchema) && statusAprovalSchema !== ApprovalStatus.APPROVED;
            const policyCount = policies?.notReady?.length;
            const evidenceCount =
                evidence?.external?.notReady?.length + evidence?.library?.notReady?.length;
            const testsCount = tests?.notReady?.length;

            const stat: IControlIssuesStats = {
                approvals: 0,
                evidence: evidenceCount,
                policies: {
                    count: policyCount,
                    unapprovedPoliciesIds: policies?.notReady,
                },
                tests: testsCount,
                total: policyCount + evidenceCount + testsCount,
            };

            if (isApprovalNeeded) {
                stat.approvals += 1;
                stat.total += 1;
            }

            controlIssueMapping[controlId] = stat;
        });

        return controlIssueMapping;
    }

    public async getPaginatedControlIds(
        account: Account,
        requestDto: ControlsRequestDto,
    ): Promise<[number[], number]> {
        const facetRunner = this.makeFacetRunner(account, requestDto);

        const facetResponse = await facetRunner.run();

        // if facets activated and no data then early return
        if (facetRunner.isAnyFacetActivated() && facetResponse.include.length === 0) {
            return [[], facetResponse.include.length];
        }

        const page = await this.controlRepository.getPageIds(
            facetResponse.include,
            facetResponse.exclude,
            requestDto.page,
            requestDto.limit,
            requestDto.sort,
            requestDto.sortDir,
        );

        return [page.data, page.total ?? 0];
    }

    public async getAllControlsForReport(
        account: Account,
        productId?: number | null,
        requestDto?: ControlsRequestDto | null,
    ): Promise<CsvDataSetType> {
        if (isNil(productId)) {
            productId = await this.workspacesCoreService.getPrimaryProductId();
        }

        let productFrameworks =
            await this.frameworksCoreService.getFrameworksEnabledByProduct(productId);

        if (!isNil(requestDto?.frameworkSlug)) {
            productFrameworks = !isEmpty(
                productFrameworks.filter(f => f.framework.slug == requestDto?.frameworkSlug),
            )
                ? productFrameworks.filter(f => f.framework.slug == requestDto?.frameworkSlug)
                : productFrameworks;
        }

        const productFrameworkIds = productFrameworks.map(framework => framework.frameworkId);
        let filteredControlIds = [];
        if (!isNil(requestDto)) {
            filteredControlIds = await this.getControlIds(account, requestDto);
        }

        const controls = await this.controlRepository.getDownloadAllControlsUnordered(
            productId,
            filteredControlIds,
        );

        controls.sort((a, b) =>
            new Intl.Collator(undefined, { numeric: true, sensitivity: 'base' }).compare(
                a.code,
                b.code,
            ),
        );

        if (isEmpty(controls)) {
            throw new NotFoundException(ErrorCode.NO_CONTROLS_FOUND);
        }

        const isReadyMap = await this.controlReadyRepository.find();

        const frameworks =
            await this.frameworkRepository.getFrameworksWithPositiveInScopeControlCounts(
                productFrameworkIds,
            );

        const data = [];

        for (const control of controls) {
            const ctrl = reportDataTransformHelper(
                control,
                frameworks,
                account,
                isReadyMap,
                ISO27001AnnexAArray,
                ISO270012022ISMSArray,
                ISO270012022AnnexAArray,
            );
            data.push(ctrl);
        }
        const filename = `Controls-${moment().format('MMDDYYYY')}`;
        return {
            data,
            filename,
        };
    }
    /**
     *
     * @param id Id of the control for which to retrieve associated evidence.
     * @param account The account on which the operation is being executed
     * @param audit An associated audit to the control for which we are requesting the evidence.
     * @returns An array of buffers for each of the types of evidence attached to the control.
     *
     * **WARNING This method might return undefined values in the middle of the array**
     *
     * The types of evidence returned by this method are the following:
     * - Policies attached to the control;
     * - Evidence Library files attached to the control;
     * - External Evidence files attached to the control;
     *
     * Previously the return type of this function was returning a arrays of arrays but due to the
     * casting made internally and the forced returned types this was not caught. This was fixed.
     */
    public async getEvidence(
        id: number,
        account: Account,
        audit?: Audit,
        onlyEvidence = false,
        onlyPolicy = false,
    ): Promise<FileBufferType[]> {
        const evidenceBufferPromiseResults = await Promise.allSettled([
            onlyEvidence ? null : this.getPolicyVersionFile(id, account),
            onlyPolicy ? null : this.getEvidenceLibraryFiles(id, account, audit),
            onlyPolicy ? null : this.getExternalFiles(id, account),
        ]);

        const fulfilledEvidenceBufferPromises = evidenceBufferPromiseResults.filter(
            ({ status }) => status === 'fulfilled',
        ) as PromiseFulfilledResult<FileBufferType[]>[];

        return fulfilledEvidenceBufferPromises.map(({ value }) => value).flat();
    }

    public async getControlIds(
        account: Account,
        requestDto: ControlsRequestDto,
    ): Promise<number[]> {
        const facetResponse = await this.makeFacetRunner(account, requestDto).run();
        return facetResponse.include;
    }

    public makeFacetRunner(account: Account, requestDto: ControlsRequestDto): FacetRunner {
        const controlFacet = new ControlFacet(account, this.controlRepository, requestDto);

        const isArchivedFacet = new IsArchivedFacet(account, this.controlRepository, requestDto);

        const productId = getProductId(account) ?? requestDto?.workspaceId;

        const ownerFacet = new OwnerFacet(account, this.controlRepository, requestDto);

        const isReadyFacet = new IsReadyFacet(
            account,
            this.controlIsReadyViewRepository,
            requestDto,
        );

        const searchFacet = new SearchFacet(account, this.controlRepository, requestDto);

        const testFacet = new TestFacet(account, this.controlRepository, requestDto);

        const ticketFacet = new TicketFacet(account, this.controlTicketViewRepository, requestDto);

        const approvalFacet = new ApprovalFacet(account, this.controlRepository, requestDto);

        const approverFacet = new ApproverFacet(account, this.controlRepository, requestDto);

        const taskOwnerFacet = new TaskOwnerFacet(account, this.controlRepository, requestDto);

        const workspaceFacet = new WorskpaceFacet(account, this.controlRepository, productId);

        const facetRunner = new FacetRunner();

        facetRunner.addFacet(controlFacet);
        facetRunner.addFacet(isArchivedFacet);
        facetRunner.addFacet(ownerFacet);
        facetRunner.addFacet(isReadyFacet);
        facetRunner.addFacet(searchFacet);
        facetRunner.addFacet(testFacet);
        facetRunner.addFacet(ticketFacet);
        facetRunner.addFacet(approvalFacet);
        facetRunner.addFacet(approverFacet);
        facetRunner.addFacet(taskOwnerFacet);
        facetRunner.addFacet(workspaceFacet);
        facetRunner.addExcludeIds(requestDto.excludeIds ?? []);

        return facetRunner;
    }

    async enablePreExistingAccountConnectionControlTests(account: Account): Promise<void> {
        if (!hasAssociatedProduct(account)) {
            return;
        }

        for (const providerType of accountConnectionProviderTypes()) {
            const connections =
                // eslint-disable-next-line no-await-in-loop
                await this.connectionsCoreService.getConnectionsByProviderType(
                    providerType,
                    account,
                );

            const providerTypeTasks = getAutopilotTaskTypesByProviderType(providerType);

            for (const accountConnection of connections) {
                // eslint-disable-next-line no-await-in-loop
                await this.monitorsOrchestrationService.enableControlTestInstancesByClientTypeForProduct(
                    account,
                    providerTypeTasks,
                    accountConnection,
                );
            }
        }
    }

    async putPoliciesBulk(
        account: Account,
        user: User,
        controlIds: number[],
        requestDto: ControlPolicyRequestDto,
    ): Promise<void> {
        const { policyIds, updateDisabledControls } = requestDto;

        try {
            const [controls, readiness, requestedPolicies] = await Promise.all([
                this.controlRepository.getControlsWithPoliciesDetails(
                    controlIds,
                    updateDisabledControls,
                ),
                this.controlReadyRepository.find({
                    where: {
                        controlId: In(controlIds),
                    },
                }),
                this.policiesCoreService.getPoliciesForEventByPolicyIds(policyIds),
            ]);

            controls.sort((a, b) => a.id - b.id);
            readiness.sort((a, b) => a.controlId - b.controlId);

            const controlPromises: Promise<Control>[] = [];
            for (let i = 0; i < controls.length; i++) {
                controlPromises.push(
                    this.associatePoliciesToControl(
                        controls[i],
                        requestedPolicies,
                        account,
                        user,
                        false,
                        readiness[i].isReady,
                    ),
                );
            }

            await Promise.allSettled(controlPromises);
        } catch (error) {
            this.logger.error(
                PolloAdapter.acct(`Error while updating Policy Control mappings`, account)
                    .setIdentifier({
                        controls: controlIds,
                    })
                    .setError(error),
            );
            throw error;
        }
    }

    async deletePoliciesBulk(
        account: Account,
        user: User,
        controlIds: number[],
        requestDto: ControlPolicyRequestDto,
    ): Promise<void> {
        const { policyIds, updateDisabledControls } = requestDto;

        try {
            const [controls, readiness] = await Promise.all([
                this.controlRepository.getControlsWithPoliciesDetails(
                    controlIds,
                    updateDisabledControls,
                ),
                this.controlReadyRepository.find({
                    where: {
                        controlId: In(controlIds),
                    },
                }),
            ]);
            controls.sort((a, b) => a.id - b.id);
            readiness.sort((a, b) => a.controlId - b.controlId);

            const controlPromises: Promise<void>[] = [];
            for (let i = 0; i < controls.length; i++) {
                controlPromises.push(
                    this.unmapPoliciesFromControl(
                        controls[i],
                        policyIds,
                        account,
                        user,
                        readiness[i].isReady,
                    ),
                );
            }

            await Promise.allSettled(controlPromises);
        } catch (error) {
            this.logger.error(
                PolloAdapter.acct(
                    `Could not delete the mapping between policy for control with ids ${controlIds.join(
                        ',',
                    )}`,
                    account,
                ).setError(error),
            );
        }
    }

    async updateControlApprovalsApprovedInPolicy(
        account: Account,
        user: User,
        controlIds: number[],
    ): Promise<void> {
        if (isEmpty(controlIds)) {
            return;
        }
        const controlsApproved = await this.findControlsFromApprovalStatus(controlIds, [
            ApprovalStatus.APPROVED,
        ]);

        if (isEmpty(controlsApproved)) {
            return;
        }

        await asyncForEach(controlsApproved, async control => {
            const oldApproval = { ...control.approvals[0] } as Approval;
            // Update status
            const newApproval = await this.approvalsCoreService.saveApprovalV1({
                ...control.approvals[0],
                ...{ status: ApprovalStatus.PREPARE_FOR_REVIEWS },
            });

            //Insert in approval status history when the approval status changes to a new status
            await this.insertApprovalStatusHistory(newApproval, user);

            // Event Tracking
            await this._eventBus.publish(
                new ControlApprovalStatusChangeEvent(
                    account,
                    user,
                    control,
                    ApprovalStatusChangeSource.POLICY,
                    newApproval,
                    oldApproval,
                ),
            );

            // Emails and Notifications
            await this.controlApprovalStageChangePolicyEmailAndNotifications(
                account,
                user,
                control,
            );
        });
    }

    async getUsersWithControlApprovalFeatureOn(userIds: number[]): Promise<UserFeature[]> {
        if (userIds.length == 0) {
            return [];
        }
        return this.userFeatureRepository.getUserFeatures(
            userIds,
            FeatureType.CONTROL_APPROVAL_NOTIFICATION,
        );
    }

    getControlOwnersWithCursor(
        controlId: number,
        controlOwnerRequestType: ControlOwnersRequestType,
    ): Promise<CursorPage<User>> {
        return this.usersCoreService.getControlOwnersWithCursor(controlOwnerRequestType, controlId);
    }

    async deleteControlOwner(
        account: Account,
        user: User,
        workspaceIdSpecified: number,
        controlId: number,
        ownerId: number,
    ): Promise<void> {
        await this.controlRepository.findOneOrFail({
            where: [{ id: controlId, owners: { id: ownerId } }],
        });
        await this.bulkDeleteControlOwners(
            account,
            user,
            {
                controlIds: [controlId],
                ownerIds: [ownerId],
            },
            workspaceIdSpecified,
        );
    }

    async bulkDeleteControlOwners(
        account: Account,
        requestUser: User,
        requestDto: ControlOwnerBulkRequestDto,
        workspaceIdSpecified?: number,
    ): Promise<void> {
        const controlUserMap = (
            await this.controlRepository.getControlOwnersIntersection({
                page: 1,
                limit: null,
                controlIds: requestDto.controlIds,
            })
        ).data;

        const usersToDelete = controlUserMap.filter(foundUser => {
            return requestDto.ownerIds.includes(foundUser.id);
        });

        await Promise.all(
            usersToDelete.map(async user => {
                const fullUser = await this.usersCoreService.getUserByIdWithRelations(user.id, [
                    'controls',
                ]);

                const controlsRemoved = [];
                const controlsToKeep = [];

                fullUser.controls.forEach(control => {
                    if (requestDto.controlIds.includes(control.id)) {
                        controlsRemoved.push(control);
                    } else {
                        controlsToKeep.push(control);
                    }
                });

                fullUser.controls = controlsToKeep;
                await this.usersCoreService.saveUser(fullUser, account.id);

                const controlsRemovedWithProduct = await Promise.all(
                    controlsRemoved.map((control: Control) => this.addProductsToControl(control)),
                );

                this._eventBus.publish(
                    new ControlOwnerDeletedEvent(
                        account,
                        requestUser,
                        controlsRemovedWithProduct,
                        fullUser,
                    ),
                );
            }),
        );

        const userIdsToDelete = usersToDelete.map(user => user.id);

        const workspaceId = requestDto.workspaceId ?? workspaceIdSpecified;

        if (!isNil(workspaceId)) {
            requestDto.controlIds.forEach(id => {
                // Trigger for Custom Workflow
                this._eventBus.publish(
                    new ControlOwnerUpdatedWorkflowTriggerEvent(
                        account,
                        requestUser,
                        userIdsToDelete,
                        id,
                        workspaceId,
                    ),
                );
            });
        } else {
            this.logger.warn(
                PolloMessage.msg('Unable to get workspace Id ')
                    .setIdentifier({ reason: 'Unable to get workspace Id' })
                    .setDomain(account.domain)
                    .setContext(this.constructor.name)
                    .setSubContext('bulkDeleteControlOwners'),
            );
        }
    }

    async addProductsToControl(control: Control): Promise<Control> {
        const currentProductOfControl = await this.workspacesCoreService.getCurrentProductOfControl(
            control.id,
        );

        control.products = [currentProductOfControl];
        return control;
    }

    async modifyControlOwners(
        controlId: number,
        controlOwnersPutRequestType: ControlOwnersPutRequestType,
    ): Promise<User[]> {
        const ownersToAdd = await this.usersCoreService.getUsersByIds(
            controlOwnersPutRequestType.ownerUserIds,
        );

        if (ownersToAdd.length !== controlOwnersPutRequestType.ownerUserIds.length) {
            throw new BadRequestException('One or more of the supplied Owner IDs does not exist');
        }

        await this.putAllControlOwnerHelper(controlId, ownersToAdd);

        return ownersToAdd;
    }

    private async putAllControlOwnerHelper(
        controlId: number,
        ownersToAdd: User[],
    ): Promise<Control> {
        try {
            const control = await this.controlRepository.findOneOrFail({
                where: { id: controlId },
                relations: ['owners'],
            });

            ownersToAdd.forEach(newOwner => {
                if (
                    !hasRole(newOwner, [
                        Role.ADMIN,
                        Role.TECHGOV,
                        Role.WORKSPACE_ADMINISTRATOR,
                        Role.CONTROL_MANAGER,
                    ])
                ) {
                    throw new BadRequestException(
                        'User must be ADMIN, TECHGOV, CONTROL_MANAGER or WORKSPACE ADMIN',
                    );
                }
            });

            control.owners = ownersToAdd;

            await this.controlRepository.save(control);
            return control;
        } catch (error) {
            if (error instanceof BadRequestException) {
                // if the owner addition fails, rethrow the error instance
                throw error;
            } else {
                // catch everything else as 500
                throw new InternalServerErrorException(error);
            }
        }
    }

    async createControlOwner(controlId: number, ownerId: number): Promise<User> {
        const controlUserMap = (
            await this.controlRepository.getControlOwnersIntersection({
                page: 1,
                limit: undefined,
                controlIds: [controlId],
            })
        ).data;

        const ownerIds = controlUserMap.map(controlOwner => controlOwner.id);

        if (ownerIds.includes(ownerId)) {
            throw new BadRequestException(
                `Owner with id: ${ownerId} is already mapped to control: ${controlId}`,
            );
        }

        ownerIds.push(ownerId);
        const allControlOwners = await this.usersCoreService.getUsersByIds(ownerIds);
        await this.putAllControlOwnerHelper(controlId, allControlOwners);

        const addedOwner = allControlOwners.find(owner => owner.id === ownerId);

        if (!addedOwner) {
            throw new UnprocessableEntityException(`Owner with ID ${ownerId} not found`);
        }

        return addedOwner;
    }

    public async getEvidenceLibrary(
        controlId: number,
        account: Account,
        audit: Audit,
    ): Promise<LibraryDocument[]> {
        let evidenceData: LibraryDocument[] = [];

        if (isNil(audit)) {
            const { data } = await this.documentLibraryCoreService.getControlEvidence(
                account,
                controlId,
                {
                    page: 1,
                    excludeSourceless: true,
                },
            );
            evidenceData = data;
        } else {
            evidenceData = await this.documentLibraryCoreService.getControlEvidenceWithinATimeFrame(
                controlId,
                {
                    page: 1,
                },
                getAuditorFrameworkTimePeriodForEvidence(audit),
            );
        }
        return evidenceData;
    }

    public async getExternalEvidence(controlId: number): Promise<ExternalEvidence[]> {
        let externalEvidence = (
            await this.externalEvidenceCoreService.getControlEvidence(controlId, {
                page: 1,
                excludeIds: null,
            } as unknown as ControlEvidenceRequestDto)
        ).data;
        externalEvidence = externalEvidence.map(evidence => {
            return {
                ...evidence,
                description: evidence.description ?? '',
            } as ExternalEvidence;
        });
        return externalEvidence;
    }

    private async controlApprovalStageChangePolicyEmailAndNotifications(
        account: Account,
        user: User,
        controlApproved: Control,
    ): Promise<void> {
        // Send Email
        const ownersToSendEmail = await this.getUsersWithControlApprovalFeatureOn(
            controlApproved.owners.map(owner => owner.id),
        );
        await asyncForEach(ownersToSendEmail, async receiver => {
            await this._eventBus.publish(
                new ControlApprovalStageChangePolicyEmailEvent(
                    account,
                    user,
                    controlApproved,
                    receiver.user,
                ),
            );
        });

        // Send Notification (Slack, MS Teams)
        const emailsToNotify = controlApproved.owners.map(owner => owner.email);

        await this.notificationsCoreService.sendControlApprovalUpdatedPolicyNotification(
            account,
            emailsToNotify,
            controlApproved,
        );
    }

    private async insertApprovalStatusHistory(savedApproval: Approval, user: User) {
        if (!isNil(savedApproval)) {
            const approvalStatusHistoryEntity = new ApprovalStatusHistory();
            approvalStatusHistoryEntity.approval = savedApproval;
            approvalStatusHistoryEntity.createdBy = user;
            approvalStatusHistoryEntity.status = savedApproval.status;

            await this.approvalsCoreService.saveApprovalStatusHistory(approvalStatusHistoryEntity);
        }
    }

    private async findControlsFromApprovalStatus(
        controlIds: number[],
        approvalStatuses: ApprovalStatus[] = [],
    ): Promise<Control[]> {
        return this.controlRepository.findControlsFromCurrentApprovalStatuses(
            controlIds,
            approvalStatuses,
        );
    }

    private async associatePoliciesToControl(
        control: Control,
        policies: PolicyForEvent[],
        account: Account,
        user: User,
        isBeforeReady: boolean,
        omitPoliciesWithoutVersion: boolean,
        customerRequestId?: number,
    ): Promise<Control> {
        control.description = control.description.replace(/%s/g, account.companyName);

        const existingControlPolicyIds = control.policies.map(policy => policy.id);

        const newPolicies = policies.filter(
            policy => existingControlPolicyIds.indexOf(policy.id) === -1,
        );
        const newPolicyIds = newPolicies.map(policy => policy.id);

        /** Typeorm maps these by IDs, there is no need to pull the entire policy object
         * since the PolicyForEvent object overlaps enough
         * */
        control.policies.push(...(newPolicies as Policy[]));

        await this.controlRepository.save(control, { reload: false });
        const after = await this.controlReadyRepository.findOneBy({
            controlId: control.id,
        });

        const currentProduct = await this.productRepository.getProductByControlId(control.id);
        this._eventBus.publish(
            new PolicyAssociatedToControlEvent(
                account,
                user,
                newPolicies,
                control,
                isBeforeReady,
                after.isReady,
                currentProduct,
            ),
        );

        if (!isEmpty(newPolicyIds) && !isNil(currentProduct)) {
            // Trigger for Custom Workflow
            this._eventBus.publish(
                new ControlAttachedToPolicyWorkflowTriggerEvent(
                    account,
                    user,
                    newPolicyIds,
                    control.id,
                    currentProduct.id,
                ),
            );
            try {
                this._eventBus.publish(
                    new ControlReadinessEvent(account, control.id, currentProduct.id, user),
                );
            } catch (error) {
                this.logger.error(
                    PolloAdapter.acct(
                        `Could not emit the control readiness audit event for control with id: ${control.id}`,
                        account,
                    )
                        .setContext(this.constructor.name)
                        .setSubContext(this.associatePoliciesToControl.name)
                        .setError(error),
                );
            }
        }

        if (!isNil(customerRequestId)) {
            const policyVersions = newPolicies
                .map(newPolicy =>
                    newPolicy.versions
                        .filter(policyVersion => policyVersion.current)
                        .map(policyVersion => policyVersion),
                )
                .flat();
            this._eventBus.publish(
                new AuditHubLinkedPolicyVersionsEvidenceEvent(
                    account,
                    user,
                    customerRequestId,
                    control.code,
                    control.name,
                    policyVersions,
                ),
            );
        }

        if (omitPoliciesWithoutVersion) {
            control.policies = control.policies.filter(policy => !isEmpty(policy.versions));
        }

        return control;
    }

    private async unmapPoliciesFromControl(
        control: Control,
        policyIds: number[],
        account: Account,
        user: User,
        beforeIsReady: boolean,
    ): Promise<void> {
        control.description = control.description.replace(/%s/g, account.companyName);
        const deletedPolicyIds = control.policies
            .filter(policy => policyIds.indexOf(policy.id) !== -1)
            .map(p => p.id);

        const filteredPolicies = control.policies.filter(
            //filter out any policy who is
            policy => policyIds.indexOf(policy.id) === -1,
        );

        control.policies = filteredPolicies;

        await this.controlRepository.save(control);

        const [controlForEvent, existingPoliciesToDelete, after] = await Promise.all([
            this.controlRepository.getControlDataForEvent(control.id),
            this.policiesCoreService.getPoliciesWithCurrentPublishedVersionForEventByIds(
                deletedPolicyIds,
            ),
            this.controlReadyRepository.findOneBy({
                controlId: control.id,
            }),
        ]);

        this._eventBus.publish(
            new PolicyUnassociatedToControlEvent(
                account,
                user,
                existingPoliciesToDelete,
                controlForEvent,
                beforeIsReady,
                after.isReady,
            ),
        );

        if (!isEmpty(deletedPolicyIds) && !isEmpty(control)) {
            // Get the first one since we can be sure that all controls will have
            // same workspace id (product id)
            const workspaceId = (await this.productRepository.getProductByControlId(control.id))
                ?.id;
            if (!isNil(workspaceId)) {
                // Sanity check we have what we need
                // Trigger for Custom Workflow
                this._eventBus.publish(
                    new ControlUnattachedFromPolicyWorkflowTriggerEvent(
                        account,
                        user,
                        deletedPolicyIds,
                        control.id,
                        workspaceId,
                    ),
                );
                this._eventBus.publish(
                    new ControlReadinessEvent(account, control.id, workspaceId, user),
                );
            }
        }
    }

    private async getPolicyVersionFile(
        id: number,
        account: Account,
    ): Promise<FileBufferType[] | void> {
        const policies = await this.policiesCoreService.getControlPolicies(
            id,
            this.unlimitedRequest,
        );

        const policyVersions = policies.data.map(policy => policy.currentPublishedVersion());

        // filter out policies with no approved versions, ENG-25551
        const validPolicyVersionsToInclude = policyVersions.filter(policy => !isNil(policy));

        if (isEmpty(validPolicyVersionsToInclude)) {
            return;
        }

        const policyVersionIds = validPolicyVersionsToInclude.map(
            policyVersion => policyVersion.id,
        );

        // Could be with or without appendix
        const policyVersionsFiles = await tenantWrapper(account, () => {
            return this.policiesCoreService.getDownloadablePolicyVersionByIds(
                account,
                policyVersionIds,
            );
        });

        if (!isEmpty(policyVersionsFiles)) {
            const streams = await getStreamsFromPolicyVersionsFiles(
                policyVersionsFiles,
                this.downloader,
            );
            return mapFilesToPrefix(streams, FileNames.POLICIES);
        }
    }

    private async getEvidenceLibraryFiles(
        id: number,
        account: Account,
        // @AUDIT-REFACTOR: TODO rename to audit
        auditorFramework?: Audit,
    ): Promise<FileBufferType[]> {
        let evidenceData: LibraryDocument[] = [];
        if (isNil(auditorFramework)) {
            const { data } = await this.documentLibraryCoreService.getControlEvidence(account, id, {
                page: 1,
                limit: undefined,
                excludeIds: undefined,
                excludeSourceless: true,
            } as ControlReportsRequestDto);
            evidenceData = data;
        } else {
            evidenceData = await this.documentLibraryCoreService.getControlEvidenceWithinATimeFrame(
                id,
                this.unlimitedRequest,
                getAuditorFrameworkTimePeriodForEvidence(auditorFramework),
            );
        }

        if (!isEmpty(evidenceData)) {
            const control = await this.grcCoreService.findControlById(id, account, ['products']);

            const directories = await this.getStreamDirectoryOfEachEvidence(evidenceData, control);

            const directory = directories.map(file => {
                return mapFilesToPrefix(file, FileNames.EVIDENCE_LIBRARY);
            });

            const filesArray = [];
            // Convert [][] to []
            directory.forEach(file => {
                file.forEach(piceOfFile => {
                    filesArray.push(piceOfFile);
                });
            });

            return filesArray;
        }
    }

    private async getExternalFiles(id: number, account: Account): Promise<FileBufferType[]> {
        const control = await this.grcCoreService.findControlById(id, account, ['products']);
        let externalEvidence = (
            await this.externalEvidenceCoreService.getControlEvidence(id, this.unlimitedRequest)
        ).data;
        externalEvidence = externalEvidence.map(evidence => {
            return {
                ...evidence,
                description: evidence.description ?? '',
            } as ExternalEvidence;
        });
        if (!isEmpty(externalEvidence)) {
            const files = externalEvidence.filter(ee => {
                return !isNil(ee.file);
            });
            const urls = externalEvidence.filter(ee => {
                return !isNil(ee.url);
            });
            let streams = [];
            if (!isEmpty(files)) {
                streams = await getStreamsFromFiles(files, this.downloader, true);
            }
            if (!isEmpty(urls)) {
                streams.push(
                    await getCSVStream(
                        urls,
                        `Controls-${control.code}-URL-miscellaneous-evidence-${moment().format(
                            'MMDDYYYY',
                        )}.csv`,
                    ),
                );
            }
            return mapFilesToPrefix(streams, FileNames.MISCELLANEOUS_EVIDENCE);
        }
    }

    public async getPoliciesWithCurrentPublishedVersion(controlId: number): Promise<Policy[]> {
        const policies =
            await this.policiesCoreService.getControlPoliciesWithCurrentPublishedVersionWithContent(
                controlId,
            );

        return policies.filter(policy => !isNil(policy));
    }

    private async getStreamDirectoryOfEachEvidence(
        evidenceData: LibraryDocument[],
        control: Control,
    ): Promise<FileBufferType[][]> {
        const names = [];

        const folders = evidenceData.map(async evidence => {
            let streamsFiles = [];
            const formattedUrls = [];
            const files = [];
            names.push(sanitizeFileName(evidence.name));
            evidence.evidenceRenewalSchemas.forEach(renewalSchema => {
                if (renewalSchema.libraryDocumentVersion.type === LibraryDocumentVersionType.URL) {
                    formattedUrls.push({
                        name: evidence.name,
                        createdAt: renewalSchema.libraryDocumentVersion.filedAt,
                        renewalDate: renewalSchema.renewalDate,
                        url: renewalSchema.libraryDocumentVersion.source,
                        description: isNil(evidence?.description) ? '' : evidence.description,
                    });
                } else {
                    files.push(renewalSchema.libraryDocumentVersion);
                }
                return renewalSchema.libraryDocumentVersion;
            });

            if (!isEmpty(files)) {
                streamsFiles = await getStreamsFromFiles(
                    files.map(file => ({ file: file.source })),
                    this.downloader,
                    true,
                );
            }

            if (!isEmpty(formattedUrls)) {
                streamsFiles.push(
                    await getCSVCustomStream(
                        formattedUrls,
                        `Controls-${control.code}-URL-evidence-library-${moment().format(
                            'MMDDYYYY',
                        )}.csv`,
                    ),
                );
            }

            return streamsFiles;
        });

        const allStreams = await Promise.all(folders);

        //Create a directory for each evidence
        return allStreams.map((streamEvidence, index) => {
            return mapFilesToPrefix(streamEvidence, `${names[index]}`);
        });
    }

    private get controlRepository(): ControlRepository {
        return this.getCustomTenantRepository(ControlRepository);
    }

    private get controlIsReadyViewRepository(): ControlIsReadyViewRepository {
        return this.getCustomTenantRepository(ControlIsReadyViewRepository);
    }

    private get controlTicketViewRepository(): ControlTicketViewRepository {
        return this.getCustomTenantRepository(ControlTicketViewRepository);
    }

    private get controlReadyRepository(): Repository<ControlIsReadyView> {
        return this.getTenantRepository(ControlIsReadyView);
    }

    private get frameworkRepository(): FrameworkRepository {
        return this.getCustomTenantRepository(FrameworkRepository);
    }

    private get controlFlagsViewRepository(): ControlFlagsViewRepository {
        return this.getCustomTenantRepository(ControlFlagsViewRepository);
    }

    private get productRepository(): ProductRepository {
        return this.getCustomTenantRepository(ProductRepository);
    }

    private get userFeatureRepository(): UserFeatureRepository {
        return this.getCustomTenantRepository(UserFeatureRepository);
    }

    private unlimitedRequest = { page: 1, limit: null, excludeIds: null };
}
