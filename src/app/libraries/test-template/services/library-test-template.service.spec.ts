import { ConnectionState, SortDir, SortType } from '@drata/enums';
import { EventBus } from '@nestjs/cqrs';
import { TestingModule } from '@nestjs/testing';
import { ConnectionEntity } from 'app/companies/connections/entities/connection.entity';
import { ConnectionsCoreService } from 'app/companies/connections/services/connections-core.service';
import { Product } from 'app/companies/products/entities/product.entity';
import { WorkspacesCoreService } from 'app/companies/products/services/workspaces-core.service';
import { ControlRepository } from 'app/grc/repositories/control.repository';
import { LibraryTemplateCoreService } from 'app/libraries/services/library-template-core.service';
import { LibraryTestTemplateBulkAddRequestDto } from 'app/libraries/test-template/dtos/library-test-template-bulk-add-request.dto';
import { LibraryTestTemplateImportRequestDto } from 'app/libraries/test-template/dtos/library-test-template-import-request.dto';
import { LibraryTestTemplateMappingsRequestDto } from 'app/libraries/test-template/dtos/library-test-template-mappings-request.dto';
import { LibraryTestTemplateSearchRequestDto } from 'app/libraries/test-template/dtos/library-test-template-search-request.dto';
import { LibraryTestTemplateDocument } from 'app/libraries/test-template/entities/library-test-template.opensearch.entity';
import { LibraryTestTemplateData } from 'app/libraries/test-template/repositories/library-test-template.data';
import { LibraryTestTemplateService } from 'app/libraries/test-template/services/library-test-template.service';
import { libraryTestTemplateConnectionMock } from 'app/libraries/test-template/services/mocks/library-test-template-connection.mock';
import { LibraryTestTemplateDetails } from 'app/libraries/test-template/types/library-test-template-details.type';
import { LibraryTestTemplateError } from 'app/libraries/test-template/types/library-test-template-errors.type';
import { ControlTestInstance } from 'app/monitors/entities/control-test-instance.entity';
import { MonitorInstanceCheckType } from 'app/monitors/entities/monitor-instance-check-type.entity';
import { MonitorInstance } from 'app/monitors/entities/monitor-instance.entity';
import { ControlTestInstanceRepository } from 'app/monitors/repositories/control-test-instance.repository';
import { MonitorInstanceCheckTypeRepository } from 'app/monitors/repositories/monitor-instance-check-type.repository';
import { MonitorInstanceRepository } from 'app/monitors/repositories/monitor-instance.repository';
import { User } from 'app/users/entities/user.entity';
import { Account } from 'auth/entities/account.entity';
import { CacheService } from 'cache/cache.service';
import { ClientType } from 'commons/enums/auth/client-type.enum';
import { CheckType } from 'commons/enums/check-type.enum';
import { RunMode } from 'commons/enums/run-mode.enum';
import { ConflictException } from 'commons/exceptions/conflict.exception';
import { NotFoundException } from 'commons/exceptions/not-found.exception';
import { createAppTestingModule } from 'commons/helpers/app-testing-module.helper';
import { MockFactory } from 'commons/mocks/factories/mock.factory';
import { MockType } from 'commons/mocks/types/mock.type';
import { PaginationType } from 'commons/types/pagination.type';
import config from 'config';
import { SearchEngine } from 'dependencies/search-engine/search-engine';
import { ControlTemplate } from 'site-admin/entities/control-template.entity';
import { ControlTestTemplate } from 'site-admin/entities/control-test-template.entity';
import { MonitorTemplateCheckType } from 'site-admin/entities/monitor-template-check-type.entity';
import { MonitorTemplate } from 'site-admin/entities/monitor-template.entity';
import { TenancyContext } from 'tenancy/contexts/tenancy.context';

jest.mock('commons/decorators/transaction', () => ({
    TenancyTransaction() {
        return jest.fn();
    },
}));

describe('LibraryTestTemplateService', () => {
    const TEST_CUSTOM_BASE_ID = config.get<number>('autopilot2.customTestBaseId');
    const TEST_ACCOUNT_ID = '********-1234-1234-1234-********9abc';
    const TEST_TEMPLATE_ID = 123;
    const TEST_ID = 1;

    let service: LibraryTestTemplateService;
    let libraryTestTemplateData: jest.Mocked<LibraryTestTemplateData>;
    let connectionsCoreService: MockType<ConnectionsCoreService>;
    let templateCoreService: MockType<LibraryTemplateCoreService>;
    let searchEngine: jest.Mocked<SearchEngine>;
    let workspacesCoreService: MockType<WorkspacesCoreService>;
    let controlTestInstanceRepository: MockType<ControlTestInstanceRepository>;
    let cacheService: MockType<CacheService>;
    let mockMonitorInstanceRepository: MockType<MonitorInstanceRepository>;
    let mockMonitorInstanceCheckTypeRepository: MockType<MonitorInstanceCheckTypeRepository>;
    let controlRepository: MockType<ControlRepository>;
    let eventBusServiceMock: MockType<EventBus>;

    beforeEach(async () => {
        // Create mocks
        libraryTestTemplateData = {
            filterLibraryTestTemplateDocuments: jest.fn(),
            getTotalDocumentsCount: jest.fn(),
            getAllTemplateIds: jest.fn(),
            getLibraryTestTemplateFilterValues: jest.fn(),
        } as any;

        connectionsCoreService = MockFactory.getMock(ConnectionsCoreService);

        templateCoreService = MockFactory.getMock(LibraryTemplateCoreService);

        searchEngine = {
            upsertDocumentsInBulk: jest.fn(),
        } as any;

        workspacesCoreService = MockFactory.getMock(WorkspacesCoreService);

        cacheService = MockFactory.getMock(CacheService);
        eventBusServiceMock = MockFactory.getMock(EventBus);
        controlTestInstanceRepository = {
            find: jest.fn(),
        } as any;

        service = {
            getTestTemplateDetails: jest.fn().mockResolvedValue({
                id: 123,
                name: 'Test Template',
                activeTests: [{ id: 1, name: 'Test Instance' }],
                autopilotRecipeTemplates: [{ id: 1, name: 'Recipe Template' }],
            } as unknown as LibraryTestTemplateDetails),
        } as any;

        const module: TestingModule = await createAppTestingModule({
            providers: [
                LibraryTestTemplateService,
                { provide: LibraryTestTemplateData, useValue: libraryTestTemplateData },
                { provide: ConnectionsCoreService, useValue: connectionsCoreService },
                { provide: LibraryTemplateCoreService, useValue: templateCoreService },
                { provide: SearchEngine, useValue: searchEngine },
                { provide: WorkspacesCoreService, useValue: workspacesCoreService },
                { provide: TenancyContext, useValue: libraryTestTemplateConnectionMock },
                {
                    provide: CacheService,
                    useValue: cacheService,
                },
                {
                    provide: EventBus,
                    useValue: eventBusServiceMock,
                },
            ],
        }).compile();

        service = module.get<LibraryTestTemplateService>(LibraryTestTemplateService);
        controlTestInstanceRepository = libraryTestTemplateConnectionMock.getCustomRepository(
            ControlTestInstanceRepository,
        );
        mockMonitorInstanceRepository =
            libraryTestTemplateConnectionMock.getCustomRepository(MonitorInstanceRepository);
        mockMonitorInstanceCheckTypeRepository =
            libraryTestTemplateConnectionMock.getCustomRepository(
                MonitorInstanceCheckTypeRepository,
            );
        controlRepository =
            libraryTestTemplateConnectionMock.getCustomRepository(ControlRepository);
    });

    it('should be defined', () => {
        expect(service).toBeDefined();
    });

    describe('searchTestTemplates', () => {
        it('should return templates based on search criteria', async () => {
            const account = new Account();
            const dto = new LibraryTestTemplateSearchRequestDto();

            const mockTemplates: LibraryTestTemplateDocument[] = [
                {
                    templateId: 1,
                    name: 'Test Template 1',
                    description: 'This is an automated monitoring test',
                    category: 'INFRASTRUCTURE',
                    rating: 'RECOMMENDED',
                    createdAt: new Date('2025-06-01'),
                    updatedAt: new Date('2025-06-02'),
                    frameworks: ['SOC 2'],
                    resources: ['Lambda'],
                    connections: ['AWS'],
                    lastIndexed: new Date(),
                    testId: 1,
                },
            ];

            const mockPaginatedResult: PaginationType<LibraryTestTemplateDocument> = {
                data: mockTemplates,
                total: 1,
                page: 1,
                limit: 10,
            };

            libraryTestTemplateData.filterLibraryTestTemplateDocuments.mockResolvedValue(
                mockPaginatedResult,
            );
            libraryTestTemplateData.getTotalDocumentsCount.mockResolvedValue(10);
            libraryTestTemplateData.getAllTemplateIds.mockResolvedValue([1, 2, 3]);

            connectionsCoreService.getActiveConnectionsByProductId?.mockResolvedValue([
                { clientType: ClientType.AWS, clientId: 'id1', clientAlias: 'alias1' } as any,
            ]);

            controlTestInstanceRepository.find?.mockResolvedValue([]);

            libraryTestTemplateData.getLibraryTestTemplateFilterValues.mockResolvedValue({
                frameworks: ['SOC 2'],
                categories: ['INFRASTRUCTURE'],
                connections: ['AWS'],
                connectionResourceMap: { Connection1: ['Lambda'] },
            });

            const result = await service.searchTestTemplates(account, dto);

            expect(result).toBeDefined();
            expect(libraryTestTemplateData.filterLibraryTestTemplateDocuments).toHaveBeenCalled();
            expect(result.templates).toHaveLength(1);
            expect(result.templates[0].name).toBe('Test Template 1');
        });

        it('should return empty response when an error occurs', async () => {
            const account = new Account();
            const dto = new LibraryTestTemplateSearchRequestDto();

            libraryTestTemplateData.filterLibraryTestTemplateDocuments.mockRejectedValue(
                new Error('Test error'),
            );

            const result = await service.searchTestTemplates(account, dto);

            expect(result).toBeDefined();
            expect(result.templates).toHaveLength(0);
        });
    });

    describe('indexAllTestTemplates', () => {
        it('should index all test templates', async () => {
            const mockTemplates: ControlTestTemplate[] = [
                {
                    id: 1,
                    name: 'Test Template 1',
                    description: 'This is an automated monitoring test',
                    createdAt: new Date(),
                    updatedAt: new Date(),
                    controlTemplates: [
                        {
                            requirementTemplates: [
                                {
                                    requirementIndexTemplate: {
                                        frameworkTemplate: {
                                            name: 'SOC 2',
                                        },
                                    },
                                },
                            ],
                        },
                    ],
                    monitorTemplates: [
                        {
                            monitorTemplateCheckTypes: [{ checkType: CheckType.INFRASTRUCTURE }],
                            requestDescriptions: JSON.stringify([
                                {
                                    requestDescriptionType: 0,
                                    clientType: ClientType.AWS,
                                },
                            ]),
                        },
                    ],
                } as any,
            ];

            templateCoreService.getTestTemplateIndexData?.mockResolvedValue(mockTemplates);
            templateCoreService.getResourcesFromRecipeTemplate?.mockResolvedValue(['Lambda']);
            searchEngine.upsertDocumentsInBulk.mockResolvedValue(undefined);

            await service.indexAllTestTemplates();

            expect(templateCoreService.getTestTemplateIndexData).toHaveBeenCalled();
            expect(searchEngine.upsertDocumentsInBulk).toHaveBeenCalled();
        });
    });

    describe('getTestTemplateDetails', () => {
        it('should return a test template by ID', async () => {
            const account = createMockAccount();
            const templateId = 123;

            const mockTemplateDetails = {
                id: templateId,
                name: 'Test Template',
            };

            templateCoreService.getTestTemplateDetails?.mockResolvedValue(mockTemplateDetails);
            templateCoreService.getAutopilotRecipeTemplate?.mockResolvedValue({
                id: 1,
                name: 'Recipe Template',
            });

            connectionsCoreService.getActiveConnectionsByProductId?.mockResolvedValue([
                { clientType: ClientType.AWS, clientId: 'id1', clientAlias: 'alias1' } as any,
            ]);

            controlTestInstanceRepository.find?.mockResolvedValue([
                { id: 1, name: 'Test Instance' } as any,
            ]);

            const result = await service.getTestTemplateDetails(templateId, account);

            expect(result).toBeDefined();
            expect(result).toEqual(
                expect.objectContaining({
                    id: templateId,
                    name: 'Test Template',
                    autopilotRecipeTemplates: expect.anything(),
                }),
            );
            expect(result.id).toBe(templateId);
        });
    });

    describe('getTestTemplateMappings', () => {
        it('should pass the parameters to the core service properly', async () => {
            const templateId = 1;
            const request = new LibraryTestTemplateMappingsRequestDto();
            request.page = 1;
            request.limit = 10;
            request.sort = SortType.NAME;
            request.sortDir = SortDir.ASC;
            request.frameworkTemplateIds = [1, 2, 3];

            templateCoreService.findControlTemplatesRelatedToControlTestId?.mockResolvedValue({
                data: [],
                total: 0,
                page: 1,
                limit: 10,
            });

            await service.getTestTemplateMappings(templateId, request);

            expect(
                templateCoreService.findControlTemplatesRelatedToControlTestId,
            ).toHaveBeenCalledWith(
                request.limit,
                request.page,
                { templateTestId: templateId, frameworkTemplateIds: [1, 2, 3] },
                SortType.NAME,
                SortDir.ASC,
            );
        });
    });

    describe('importTestTemplateById', () => {
        let user: User;

        beforeEach(() => {
            user = new User();
            user.id = 1;
            user.email = '<EMAIL>';
            user.entryId = 'entryId';

            jest.clearAllMocks();
        });

        describe('Given valid template and request data', () => {
            it('Should create control test instances successfully', async () => {
                const account = createMockAccount();
                const requestDto = new LibraryTestTemplateImportRequestDto();
                requestDto.name = 'Test Instance Name';
                requestDto.description = 'Test Description';
                requestDto.draft = true;
                requestDto.workspaceIds = [1, 2];

                const workspaceA = new Product();
                workspaceA.id = 1;
                workspaceA.name = 'Workspace 1';

                const workspaceB = new Product();
                workspaceB.id = 2;
                workspaceB.name = 'Workspace 2';

                const mockWorkspaces = [workspaceA, workspaceB];

                const mockControlTestTemplate = createMockControlTestTemplate();

                mockControlTestTemplate.name = 'Template Name';
                mockControlTestTemplate.description = 'Template Description';
                mockControlTestTemplate.controlTemplates = [createMockControlTemplate()];

                const mockTestInstance1 = createMockControlTestInstance();
                mockTestInstance1.id = 1;
                mockTestInstance1.products = [workspaceA];

                const mockTestInstance2 = createMockControlTestInstance();
                mockTestInstance2.id = 2;
                mockTestInstance2.products = [workspaceB];

                const mockTestInstances = [mockTestInstance1, mockTestInstance2];
                const mockMonitorInstances = [createMockMonitorInstance()];

                workspacesCoreService.selectValidWorkspacesOrFail?.mockResolvedValue(
                    mockWorkspaces,
                );
                templateCoreService.getTestTemplateById?.mockResolvedValue(mockControlTestTemplate);
                controlTestInstanceRepository.findOneBy?.mockResolvedValue(null);

                controlRepository.getControlsByTemplateIdsAndWorkspaceId?.mockResolvedValue([]);

                controlTestInstanceRepository.getControlTestInstanceGreatestTestIdWithDeletedTests
                    ?.mockResolvedValue(90001)
                    .mockResolvedValue(90002);
                controlTestInstanceRepository.save?.mockResolvedValue(mockTestInstances);

                const createAndSaveMonitorInstancesSpy = jest.spyOn(
                    service,
                    'createAndSaveMonitorInstances',
                );

                createAndSaveMonitorInstancesSpy.mockResolvedValue(mockMonitorInstances);

                const result = await service.importTestTemplateById(
                    account,
                    user,
                    TEST_TEMPLATE_ID,
                    requestDto,
                );

                expect(workspacesCoreService.selectValidWorkspacesOrFail).toHaveBeenCalledWith(
                    account,
                    requestDto.workspaceIds,
                );
                expect(templateCoreService.getTestTemplateById).toHaveBeenCalledWith(
                    TEST_TEMPLATE_ID,
                );
                expect(controlTestInstanceRepository.findOneBy).toHaveBeenCalledWith({
                    name: requestDto.name,
                });
                expect(
                    controlRepository.getControlsByTemplateIdsAndWorkspaceId,
                ).toHaveBeenCalledWith(
                    mockControlTestTemplate.controlTemplates.map(
                        controlTemplate => controlTemplate.id,
                    ),
                    workspaceA.id,
                );

                expect(controlTestInstanceRepository.save).toHaveBeenCalled();
                const globalPrefix = config.get<string>('cache.globalPrefix') || 'globalstore';
                expect(cacheService.set).toHaveBeenCalledWith(
                    `${globalPrefix}:{${account.id}}:library-test-template:latest-control-test-id`,
                    90003,
                    { ttl: 60 },
                );

                expect(createAndSaveMonitorInstancesSpy).toHaveBeenCalledTimes(2);
                expect(result).toHaveLength(2);
                expect(result[0].monitorInstances).toEqual(mockMonitorInstances);
                expect(result[1].monitorInstances).toEqual(mockMonitorInstances);
            });
        });

        describe('Given a valid test template, request data, but no control templates mapped to the test template', () => {
            it('Should create control test instances without control templates', async () => {
                const account = createMockAccount();
                const requestDto = new LibraryTestTemplateImportRequestDto();
                requestDto.name = 'Test Instance Name';
                requestDto.description = 'Test Description';
                requestDto.draft = true;
                requestDto.workspaceIds = [1];

                const workspace = new Product();
                workspace.id = 1;
                workspace.name = 'Workspace 1';

                const mockWorkspaces = [workspace];

                const mockControlTestTemplate = createMockControlTestTemplate();
                mockControlTestTemplate.controlTemplates = [];

                workspacesCoreService.selectValidWorkspacesOrFail?.mockResolvedValue(
                    mockWorkspaces,
                );
                templateCoreService.getTestTemplateById?.mockResolvedValue(mockControlTestTemplate);
                controlTestInstanceRepository.findOneBy?.mockResolvedValue(null);

                controlRepository.getControlsByTemplateIdsAndWorkspaceId?.mockResolvedValue([]);

                controlTestInstanceRepository.getControlTestInstanceGreatestTestIdWithDeletedTests?.mockResolvedValue(
                    90001,
                );
                const controlTestInstance = createMockControlTestInstance();
                controlTestInstanceRepository.save?.mockResolvedValue([controlTestInstance]);

                const result = await service.importTestTemplateById(
                    account,
                    user,
                    TEST_TEMPLATE_ID,
                    requestDto,
                );

                expect(
                    controlRepository.getControlsByTemplateIdsAndWorkspaceId,
                ).not.toHaveBeenCalled();
                expect(result).toEqual([controlTestInstance]);
            });
        });

        describe('Given template not found', () => {
            it('Should throw NotFoundException', async () => {
                const account = createMockAccount();
                const templateId = 999;
                const requestDto = new LibraryTestTemplateImportRequestDto();
                requestDto.name = 'Test Instance Name';
                requestDto.workspaceIds = [1];

                const workspace = new Product();
                workspace.id = 1;
                workspace.name = 'Workspace 1';

                const mockWorkspaces = [workspace];

                workspacesCoreService.selectValidWorkspacesOrFail?.mockResolvedValue(
                    mockWorkspaces,
                );
                templateCoreService.getTestTemplateById?.mockResolvedValue(null);

                await expect(
                    service.importTestTemplateById(account, user, templateId, requestDto),
                ).rejects.toThrow(NotFoundException);

                expect(workspacesCoreService.selectValidWorkspacesOrFail).toHaveBeenCalledWith(
                    account,
                    requestDto.workspaceIds,
                );
                expect(templateCoreService.getTestTemplateById).toHaveBeenCalledWith(templateId);
                expect(controlTestInstanceRepository.findOneBy).not.toHaveBeenCalled();
            });
        });

        describe('Given test name already exists', () => {
            it('Should throw ConflictException', async () => {
                const account = createMockAccount();
                const requestDto = new LibraryTestTemplateImportRequestDto();
                requestDto.name = 'Existing Test Name';
                requestDto.workspaceIds = [1];

                const workspace = new Product();
                workspace.id = 1;
                workspace.name = 'Workspace 1';

                const mockWorkspaces = [workspace];

                const mockControlTestTemplate = createMockControlTestTemplate();

                mockControlTestTemplate.name = 'Template Name';
                mockControlTestTemplate.description = 'Template Description';
                mockControlTestTemplate.controlTemplates = [createMockControlTemplate()];

                const existingTestInstance = createMockControlTestInstance();
                existingTestInstance.id = 1;
                existingTestInstance.name = 'Existing Test Name';

                workspacesCoreService.selectValidWorkspacesOrFail?.mockResolvedValue(
                    mockWorkspaces,
                );

                templateCoreService.getTestTemplateById?.mockResolvedValue(mockControlTestTemplate);
                controlTestInstanceRepository.findOneBy?.mockResolvedValue(existingTestInstance);

                await expect(
                    service.importTestTemplateById(account, user, TEST_TEMPLATE_ID, requestDto),
                ).rejects.toThrow(ConflictException);

                expect(workspacesCoreService.selectValidWorkspacesOrFail).toHaveBeenCalledWith(
                    account,
                    requestDto.workspaceIds,
                );
                expect(templateCoreService.getTestTemplateById).toHaveBeenCalledWith(
                    TEST_TEMPLATE_ID,
                );
                expect(controlTestInstanceRepository.findOneBy).toHaveBeenCalledWith({
                    name: requestDto.name,
                });
            });
        });
    });

    describe('getNextControlTestId', () => {
        const mockAccount = createMockAccount();
        const globalPrefix = config.get<string>('cache.globalPrefix') || 'globalstore';
        const cacheKey = `${globalPrefix}:{${mockAccount.id}}:library-test-template:latest-control-test-id`;

        describe('Given cache contains a value', () => {
            describe('When cached value is greater than customTestBaseId', () => {
                it('Should return cached value + 1 and update cache', async () => {
                    const cachedValue = TEST_CUSTOM_BASE_ID + 100;
                    cacheService.get?.mockResolvedValue(cachedValue);

                    const result = await service.getNextControlTestId(mockAccount);

                    expect(cacheService.get).toHaveBeenCalledWith(cacheKey);
                    expect(cacheService.set).toHaveBeenCalledWith(cacheKey, cachedValue + 1, {
                        ttl: 60,
                    });
                    expect(result).toBe(cachedValue + 1);
                    expect(
                        controlTestInstanceRepository.getControlTestInstanceGreatestTestIdWithDeletedTests,
                    ).not.toHaveBeenCalled();
                });
            });

            describe('When cached value is less than customTestBaseId', () => {
                it('Should return customTestBaseId and update cache', async () => {
                    const cachedValue = 5000; // Less than TEST_CUSTOM_BASE_ID (90000)
                    cacheService.get?.mockResolvedValue(cachedValue);

                    const result = await service.getNextControlTestId(mockAccount);

                    expect(cacheService.get).toHaveBeenCalledWith(cacheKey);
                    expect(cacheService.set).toHaveBeenCalledWith(cacheKey, TEST_CUSTOM_BASE_ID, {
                        ttl: 60,
                    });
                    expect(result).toBe(TEST_CUSTOM_BASE_ID);
                    expect(
                        controlTestInstanceRepository.getControlTestInstanceGreatestTestIdWithDeletedTests,
                    ).not.toHaveBeenCalled();
                });
            });
        });

        describe('Given cache is empty', () => {
            describe('When database value is greater than customTestBaseId', () => {
                it('Should fetch from repository, return db value + 1, and update cache', async () => {
                    const dbValue = TEST_CUSTOM_BASE_ID + 100;
                    cacheService.get?.mockResolvedValue(null);
                    controlTestInstanceRepository.getControlTestInstanceGreatestTestIdWithDeletedTests?.mockResolvedValue(
                        dbValue,
                    );

                    const result = await service.getNextControlTestId(mockAccount);

                    expect(cacheService.get).toHaveBeenCalledWith(cacheKey);
                    expect(
                        controlTestInstanceRepository.getControlTestInstanceGreatestTestIdWithDeletedTests,
                    ).toHaveBeenCalled();
                    expect(cacheService.set).toHaveBeenCalledWith(cacheKey, dbValue + 1, {
                        ttl: 60,
                    });
                    expect(result).toBe(dbValue + 1);
                });
            });

            describe('When database value is less than customTestBaseId', () => {
                it('Should fetch from repository, return customTestBaseId, and update cache', async () => {
                    const dbValue = 5000; // Less than TEST_CUSTOM_BASE_ID (90000)
                    cacheService.get?.mockResolvedValue(null);
                    controlTestInstanceRepository.getControlTestInstanceGreatestTestIdWithDeletedTests?.mockResolvedValue(
                        dbValue,
                    );

                    const result = await service.getNextControlTestId(mockAccount);

                    expect(cacheService.get).toHaveBeenCalledWith(cacheKey);
                    expect(
                        controlTestInstanceRepository.getControlTestInstanceGreatestTestIdWithDeletedTests,
                    ).toHaveBeenCalled();
                    expect(cacheService.set).toHaveBeenCalledWith(cacheKey, TEST_CUSTOM_BASE_ID, {
                        ttl: 60,
                    });
                    expect(result).toBe(TEST_CUSTOM_BASE_ID);
                });
            });
        });
    });

    describe('createAndSaveMonitorInstances', () => {
        const mockAccount = createMockAccount();
        let mockTemplate: ControlTestTemplate;
        let mockControlTestInstance: ControlTestInstance;

        beforeEach(() => {
            jest.clearAllMocks();
            mockTemplate = createMockControlTestTemplate();
            mockControlTestInstance = createMockControlTestInstance();
        });

        describe('Given a template with monitor templates', () => {
            it('Should create and save monitor instances for each monitor template', async () => {
                const savedMonitorInstance = createMockMonitorInstance();
                savedMonitorInstance.id = 1;
                savedMonitorInstance.controlTestInstance = mockControlTestInstance;
                mockMonitorInstanceRepository.save?.mockResolvedValue(savedMonitorInstance);

                const result = await service.createAndSaveMonitorInstances(
                    mockAccount,
                    mockTemplate,
                    mockControlTestInstance,
                );

                expect(mockMonitorInstanceRepository.save).toHaveBeenCalled();
                expect(result).toHaveLength(mockTemplate.monitorTemplates.length);
                expect(result[0]).toEqual(savedMonitorInstance);
            });

            it('Should set controlTestInstance relationship on each monitor instance', async () => {
                const savedMonitorInstance = createMockMonitorInstance();
                savedMonitorInstance.controlTestInstance = mockControlTestInstance;
                mockMonitorInstanceRepository.save?.mockResolvedValue(savedMonitorInstance);

                await service.createAndSaveMonitorInstances(
                    mockAccount,
                    mockTemplate,
                    mockControlTestInstance,
                );

                const saveCall = mockMonitorInstanceRepository.save?.mock.calls[0][0];
                expect(saveCall.controlTestInstance).toEqual(mockControlTestInstance);
            });

            it('Should create monitor instance check types for each saved monitor instance', async () => {
                const savedMonitorInstance = createMockMonitorInstance();
                savedMonitorInstance.id = 1;
                savedMonitorInstance.controlTestInstance = mockControlTestInstance;
                const mockCheckTypes = [createMockMonitorInstanceCheckType()];

                mockMonitorInstanceCheckTypeRepository.save?.mockResolvedValue(mockCheckTypes);
                mockMonitorInstanceRepository.save?.mockResolvedValue(savedMonitorInstance);

                await service.createAndSaveMonitorInstances(
                    mockAccount,
                    mockTemplate,
                    mockControlTestInstance,
                );

                expect(mockMonitorInstanceCheckTypeRepository.save).toHaveBeenCalledWith(
                    expect.arrayContaining([
                        expect.objectContaining({
                            monitorInstance: savedMonitorInstance,
                        }),
                    ]),
                );
            });
        });

        describe('Given a template with no monitor templates', () => {
            it('Should return empty array', async () => {
                mockTemplate.monitorTemplates = [];

                const result = await service.createAndSaveMonitorInstances(
                    mockAccount,
                    mockTemplate,
                    mockControlTestInstance,
                );

                expect(result).toEqual([]);
                expect(mockMonitorInstanceRepository.save).not.toHaveBeenCalled();
            });
        });

        describe('Given multiple monitor templates', () => {
            it('Should process all monitor templates and return all saved instances', async () => {
                const secondMonitorTemplate = createMockMonitorTemplate();
                secondMonitorTemplate.id = 2;
                mockTemplate.monitorTemplates.push(secondMonitorTemplate);

                const savedInstance1 = createMockMonitorInstance();
                savedInstance1.id = 1;
                savedInstance1.controlTestInstance = mockControlTestInstance;
                const savedInstance2 = createMockMonitorInstance();
                savedInstance2.id = 2;
                savedInstance2.controlTestInstance = mockControlTestInstance;

                mockMonitorInstanceRepository.save
                    ?.mockResolvedValueOnce(savedInstance1)
                    .mockResolvedValueOnce(savedInstance2);

                const result = await service.createAndSaveMonitorInstances(
                    mockAccount,
                    mockTemplate,
                    mockControlTestInstance,
                );

                expect(mockMonitorInstanceRepository.save).toHaveBeenCalled();
                expect(result).toHaveLength(2);
                expect(result[0].id).toBe(1);
                expect(result[1].id).toBe(2);
            });
        });
    });

    describe('createAndSaveMonitorCheckTypes', () => {
        const mockAccount = createMockAccount();
        let mockMonitorTemplate: MonitorTemplate;
        let mockMonitorInstance: MonitorInstance;

        beforeEach(() => {
            jest.clearAllMocks();
            mockMonitorTemplate = createMockMonitorTemplate();
            mockMonitorInstance = createMockMonitorInstance();
            mockMonitorInstance.controlTestInstance = createMockControlTestInstance();
        });

        describe('Given a monitor template with check types', () => {
            it('Should create monitor instance check types from template check types', async () => {
                const savedCheckTypes = [createMockMonitorInstanceCheckType()];
                mockMonitorInstanceCheckTypeRepository.save?.mockResolvedValue(savedCheckTypes);

                const result = await service.createAndSaveMonitorCheckTypes(
                    mockAccount,
                    mockMonitorTemplate,
                    mockMonitorInstance,
                );

                expect(result).toEqual(savedCheckTypes);
            });

            it('Should perform bulk save operation with array of check types', async () => {
                const savedCheckTypes = [
                    createMockMonitorInstanceCheckType(),
                    createMockMonitorInstanceCheckType(),
                ];
                mockMonitorInstanceCheckTypeRepository.save?.mockResolvedValue(savedCheckTypes);

                await service.createAndSaveMonitorCheckTypes(
                    mockAccount,
                    mockMonitorTemplate,
                    mockMonitorInstance,
                );

                // Verify bulk save: save called once with array
                expect(mockMonitorInstanceCheckTypeRepository.save).toHaveBeenCalledTimes(1);
                expect(mockMonitorInstanceCheckTypeRepository.save).toHaveBeenCalledWith(
                    expect.arrayContaining([
                        expect.objectContaining({
                            checkType: mockMonitorTemplate.monitorTemplateCheckTypes[0].checkType,
                            monitorInstance: mockMonitorInstance,
                        }),
                    ]),
                );
            });

            it('Should set monitorInstance relationship on each check type', async () => {
                const savedCheckTypes = [createMockMonitorInstanceCheckType()];
                mockMonitorInstanceCheckTypeRepository.save?.mockResolvedValue(savedCheckTypes);

                await service.createAndSaveMonitorCheckTypes(
                    mockAccount,
                    mockMonitorTemplate,
                    mockMonitorInstance,
                );

                const saveCall = mockMonitorInstanceCheckTypeRepository.save?.mock.calls[0][0];
                expect(saveCall[0].monitorInstance).toEqual(mockMonitorInstance);
            });

            it('Should copy checkType from template check types', async () => {
                const savedCheckTypes = [createMockMonitorInstanceCheckType()];
                mockMonitorInstanceCheckTypeRepository.save?.mockResolvedValue(savedCheckTypes);

                await service.createAndSaveMonitorCheckTypes(
                    mockAccount,
                    mockMonitorTemplate,
                    mockMonitorInstance,
                );

                const saveCall = mockMonitorInstanceCheckTypeRepository.save?.mock.calls[0][0];
                expect(saveCall[0].checkType).toBe(
                    mockMonitorTemplate.monitorTemplateCheckTypes[0].checkType,
                );
            });
        });

        describe('Given a monitor template with multiple check types', () => {
            it('Should create check types for all template check types', async () => {
                const secondCheckType = createMockMonitorTemplateCheckType();
                secondCheckType.id = 2;
                secondCheckType.checkType = CheckType.AGENT;
                mockMonitorTemplate.monitorTemplateCheckTypes.push(secondCheckType);

                const savedCheckTypes = [
                    createMockMonitorInstanceCheckType(),
                    createMockMonitorInstanceCheckType(),
                ];
                mockMonitorInstanceCheckTypeRepository.save?.mockResolvedValue(savedCheckTypes);

                await service.createAndSaveMonitorCheckTypes(
                    mockAccount,
                    mockMonitorTemplate,
                    mockMonitorInstance,
                );

                const saveCall = mockMonitorInstanceCheckTypeRepository.save?.mock.calls[0][0];
                expect(saveCall).toHaveLength(2);
                expect(saveCall[0].checkType).toBe(CheckType.INFRASTRUCTURE);
                expect(saveCall[1].checkType).toBe(CheckType.AGENT);
            });
        });

        describe('Given a monitor template with no check types', () => {
            it('Should perform bulk save with empty array', async () => {
                mockMonitorTemplate.monitorTemplateCheckTypes = [];
                mockMonitorInstanceCheckTypeRepository.save?.mockResolvedValue([]);

                const result = await service.createAndSaveMonitorCheckTypes(
                    mockAccount,
                    mockMonitorTemplate,
                    mockMonitorInstance,
                );

                expect(mockMonitorInstanceCheckTypeRepository.save).toHaveBeenCalledWith([]);
                expect(result).toEqual([]);
            });
        });
    });

    describe('validateBulkAddTestTemplates', () => {
        describe('When test template has runMode AP1', () => {
            let template: ControlTestTemplate;

            beforeEach(() => {
                template = createMockControlTestTemplate();
                template.runMode = RunMode.AP1;
                template.id = 1;
                template.name = 'Template A';

                templateCoreService.getTestTemplatesByIds?.mockResolvedValue([template]);
                connectionsCoreService.getActiveConnectionsWithProducts?.mockResolvedValue([]);
            });

            it('Should return error', async () => {
                const dto = new LibraryTestTemplateBulkAddRequestDto();
                dto.testTemplateIds = [1];
                dto.productIds = [1, 2, 3];

                const result = await service.validateBulkAddTestTemplates(dto);
                expect(result.errors).toEqual([
                    {
                        testTemplateId: template.id,
                        name: template.name,
                        testId: template.testId,
                        errorType: LibraryTestTemplateError[LibraryTestTemplateError.NOT_SUPPORTED],
                    },
                ]);
            });
        });

        describe('When test template has runMode AP2 ', () => {
            describe('When test template has a monitor template with check type AWS but the workspaces do not have AWS connection', () => {
                let template: ControlTestTemplate;

                beforeEach(() => {
                    template = createMockControlTestTemplate();
                    template.runMode = RunMode.AP2;
                    template.id = 1;
                    template.name = 'Template A';
                    const monitorTemplate = createMockMonitorTemplate();
                    monitorTemplate.monitorTemplateCheckTypes = [
                        createMockMonitorTemplateCheckType(),
                    ];
                    monitorTemplate.requestDescriptions = JSON.stringify([
                        {
                            clientType: ClientType.AWS,
                            requestDescriptionType: 'CONNECTION',
                            description:
                                'Drata inspects EFS and verifies that the Encrypted property is set to true, via AWS API.',
                        },
                    ]);
                    template.monitorTemplates = [monitorTemplate];
                    templateCoreService.getTestTemplatesByIds?.mockResolvedValue([template]);
                    connectionsCoreService.getActiveConnectionsWithProducts?.mockResolvedValue([]);
                });

                it('Should return error', async () => {
                    const dto = new LibraryTestTemplateBulkAddRequestDto();
                    dto.testTemplateIds = [1];
                    dto.productIds = [1, 2, 3];
                    const result = await service.validateBulkAddTestTemplates(dto);
                    expect(result.errors).toEqual([
                        {
                            testTemplateId: template.id,
                            name: template.name,
                            testId: template.testId,
                            errorType:
                                LibraryTestTemplateError[
                                    LibraryTestTemplateError.MISSING_CONNECTION
                                ],
                        },
                    ]);
                });
            });

            describe('When test template has a monitor template with check type AWS and the workspaces have AWS connection', () => {
                let template: ControlTestTemplate;

                beforeEach(() => {
                    template = createMockControlTestTemplate();
                    template.runMode = RunMode.AP2;
                    const monitorTemplate = createMockMonitorTemplate();
                    monitorTemplate.monitorTemplateCheckTypes = [
                        createMockMonitorTemplateCheckType(),
                    ];
                    monitorTemplate.requestDescriptions = JSON.stringify([
                        {
                            clientType: ClientType.AWS,
                            requestDescriptionType: 'CONNECTION',
                            description:
                                'Drata inspects EFS and verifies that the Encrypted property is set to true, via AWS API.',
                        },
                    ]);
                    template.monitorTemplates = [monitorTemplate];
                    templateCoreService.getTestTemplatesByIds?.mockResolvedValue([template]);
                    const product = new Product();
                    product.id = 1;
                    const connection = new ConnectionEntity();
                    connection.clientType = ClientType.AWS;
                    connection.state = ConnectionState.ACTIVE;
                    connection.products = [product];

                    connectionsCoreService.getActiveConnectionsWithProducts?.mockResolvedValue([
                        connection,
                    ]);
                });

                it('Should not return error', async () => {
                    const dto = new LibraryTestTemplateBulkAddRequestDto();
                    dto.testTemplateIds = [1];
                    dto.productIds = [1, 2, 3];
                    const result = await service.validateBulkAddTestTemplates(dto);
                    expect(result.errors).toEqual([]);
                });
            });
        });

        describe(
            'When one test template has runMode AP1 and the other has runMode AP2 ' +
                'but the workspace does not have the required connection',
            () => {
                let templateA: ControlTestTemplate;
                let templateB: ControlTestTemplate;

                beforeEach(() => {
                    templateA = createMockControlTestTemplate();
                    templateA.runMode = RunMode.AP1;
                    templateA.id = 1;
                    templateA.testId = 1;
                    templateA.name = 'Template A';
                    templateB = createMockControlTestTemplate();
                    templateB.runMode = RunMode.AP2;
                    templateB.id = 2;
                    templateB.testId = 2;
                    templateB.name = 'Template B';
                    const monitorTemplate = createMockMonitorTemplate();
                    monitorTemplate.monitorTemplateCheckTypes = [
                        createMockMonitorTemplateCheckType(),
                    ];
                    monitorTemplate.requestDescriptions = JSON.stringify([
                        {
                            clientType: ClientType.AWS,
                            requestDescriptionType: 'CONNECTION',
                            description:
                                'Drata inspects EFS and verifies that the Encrypted property is set to true, via AWS API.',
                        },
                    ]);
                    templateB.monitorTemplates = [monitorTemplate];
                    templateCoreService.getTestTemplatesByIds?.mockResolvedValue([
                        templateA,
                        templateB,
                    ]);
                    connectionsCoreService.getActiveConnectionsWithProducts?.mockResolvedValue([]);
                });

                it('Should return error for both templates', async () => {
                    const dto = new LibraryTestTemplateBulkAddRequestDto();
                    dto.testTemplateIds = [1, 2];
                    dto.productIds = [1, 2, 3];

                    const result = await service.validateBulkAddTestTemplates(dto);
                    expect(result.errors).toEqual([
                        {
                            testTemplateId: templateA.id,
                            name: templateA.name,
                            testId: templateA.testId,
                            errorType:
                                LibraryTestTemplateError[LibraryTestTemplateError.NOT_SUPPORTED],
                        },
                        {
                            testTemplateId: templateB.id,
                            name: templateB.name,
                            testId: templateB.testId,
                            errorType:
                                LibraryTestTemplateError[
                                    LibraryTestTemplateError.MISSING_CONNECTION
                                ],
                        },
                    ]);
                });
            },
        );

        describe(
            'When one test template has runMode AP1 and the other has runMode AP2 ' +
                'and the workspace has the required connection',
            () => {
                let templateA: ControlTestTemplate;
                let templateB: ControlTestTemplate;

                beforeEach(() => {
                    templateA = createMockControlTestTemplate();
                    templateA.runMode = RunMode.AP1;
                    templateA.id = 1;
                    templateA.name = 'Template A';
                    templateB = createMockControlTestTemplate();
                    templateB.runMode = RunMode.AP2;
                    templateB.id = 2;
                    templateB.name = 'Template B';
                    const monitorTemplate = createMockMonitorTemplate();
                    monitorTemplate.monitorTemplateCheckTypes = [
                        createMockMonitorTemplateCheckType(),
                    ];
                    monitorTemplate.requestDescriptions = JSON.stringify([
                        {
                            clientType: ClientType.AWS,
                            requestDescriptionType: 'CONNECTION',
                            description:
                                'Drata inspects EFS and verifies that the Encrypted property is set to true, via AWS API.',
                        },
                    ]);
                    templateB.monitorTemplates = [monitorTemplate];
                    templateCoreService.getTestTemplatesByIds?.mockResolvedValue([
                        templateA,
                        templateB,
                    ]);
                    const product = new Product();
                    product.id = 1;
                    const connection = new ConnectionEntity();
                    connection.clientType = ClientType.AWS;
                    connection.state = ConnectionState.ACTIVE;
                    connection.products = [product];
                    connectionsCoreService.getActiveConnectionsWithProducts?.mockResolvedValue([
                        connection,
                    ]);
                });

                it('Should return error for template A only', async () => {
                    const dto = new LibraryTestTemplateBulkAddRequestDto();
                    dto.testTemplateIds = [1, 2];
                    dto.productIds = [1, 2, 3];

                    const result = await service.validateBulkAddTestTemplates(dto);
                    expect(result.errors).toEqual([
                        {
                            testTemplateId: templateA.id,
                            name: templateA.name,
                            testId: templateA.testId,
                            errorType:
                                LibraryTestTemplateError[LibraryTestTemplateError.NOT_SUPPORTED],
                        },
                    ]);
                });
            },
        );
    });

    describe('validateImportTestTemplateViaTestId', () => {
        describe('When control test template has runMode AP1', () => {
            const testId = 1;
            let template: ControlTestTemplate;

            beforeEach(() => {
                template = createMockControlTestTemplate();
                template.runMode = RunMode.AP1;
                templateCoreService.getTestTemplateByTestId?.mockResolvedValue(template);
            });

            it('Should return error', async () => {
                const result = await service.validateImportTestTemplateViaTestId(testId);
                expect(result.error).toEqual({
                    testTemplateId: template.id,
                    name: template.name,
                    testId: template.testId,
                    errorType: LibraryTestTemplateError.NOT_SUPPORTED,
                });
            });
        });

        describe('When control test template has runMode AP2 ', () => {
            describe('When control test template has a monitor template with check type AWS but the workspaces do not have AWS connection', () => {
                const testId = 1;
                let template: ControlTestTemplate;

                beforeEach(() => {
                    template = createMockControlTestTemplate();
                    template.runMode = RunMode.AP2;
                    const monitorTemplate = createMockMonitorTemplate();
                    monitorTemplate.monitorTemplateCheckTypes = [
                        createMockMonitorTemplateCheckType(),
                    ];
                    monitorTemplate.requestDescriptions = JSON.stringify([
                        {
                            clientType: ClientType.AWS,
                            requestDescriptionType: 'CONNECTION',
                            description:
                                'Drata inspects EFS and verifies that the Encrypted property is set to true, via AWS API.',
                        },
                    ]);
                    template.monitorTemplates = [monitorTemplate];
                    templateCoreService.getTestTemplateByTestId?.mockResolvedValue(template);
                    connectionsCoreService.getActiveConnectionsWithProducts?.mockResolvedValue([]);
                });

                it('Should return error', async () => {
                    const result = await service.validateImportTestTemplateViaTestId(testId);
                    expect(result.error).toEqual({
                        testTemplateId: template.id,
                        name: template.name,
                        testId: template.testId,
                        errorType: LibraryTestTemplateError.MISSING_CONNECTION,
                    });
                });
            });

            describe('When control test template has a monitor template with check type AWS and the workspaces have AWS connection', () => {
                const templateId = 1;
                let template: ControlTestTemplate;

                beforeEach(() => {
                    template = createMockControlTestTemplate();
                    template.runMode = RunMode.AP2;
                    const monitorTemplate = createMockMonitorTemplate();
                    monitorTemplate.monitorTemplateCheckTypes = [
                        createMockMonitorTemplateCheckType(),
                    ];
                    monitorTemplate.requestDescriptions = JSON.stringify([
                        {
                            clientType: ClientType.AWS,
                            requestDescriptionType: 'CONNECTION',
                            description:
                                'Drata inspects EFS and verifies that the Encrypted property is set to true, via AWS API.',
                        },
                    ]);
                    template.monitorTemplates = [monitorTemplate];
                    templateCoreService.getTestTemplateByTestId?.mockResolvedValue(template);
                    const product = new Product();
                    product.id = 1;
                    const connection = new ConnectionEntity();
                    connection.clientType = ClientType.AWS;
                    connection.state = ConnectionState.ACTIVE;
                    connection.products = [product];
                    connectionsCoreService.getActiveConnectionsWithProducts?.mockResolvedValue([
                        connection,
                    ]);
                });

                it('Should not return error', async () => {
                    const result = await service.validateImportTestTemplateViaTestId(templateId);
                    expect(result.error).toBeNull();
                });
            });
        });
    });

    describe('validateImportTestTemplateRunMode', () => {
        describe('When control test template has runMode AP1', () => {
            let template: ControlTestTemplate;

            beforeEach(() => {
                template = createMockControlTestTemplate();
                template.runMode = RunMode.AP1;
            });

            it('Should return error', () => {
                const result = service.validateImportTestTemplateRunMode(template);
                expect(result).toEqual({
                    testTemplateId: template.id,
                    name: template.name,
                    testId: template.testId,
                    errorType: LibraryTestTemplateError.NOT_SUPPORTED,
                });
            });
        });

        describe('When control test template has runMode AP2', () => {
            let template: ControlTestTemplate;

            beforeEach(() => {
                template = createMockControlTestTemplate();
                template.runMode = RunMode.AP2;
            });

            it('Should not return error', () => {
                const result = service.validateImportTestTemplateRunMode(template);
                expect(result).toBeNull();
            });
        });
    });

    describe('validateImportTestTemplateConnection', () => {
        describe('When control test template has no monitor templates', () => {
            let template: ControlTestTemplate;

            beforeEach(() => {
                template = createMockControlTestTemplate();
                template.monitorTemplates = [];
            });

            it('Should not return error', () => {
                const result = service.validateImportTestTemplateConnection(template, []);
                expect(result).toBeNull();
            });
        });

        describe('When control test template has monitor templates but no connections are required', () => {
            let template: ControlTestTemplate;

            beforeEach(() => {
                template = createMockControlTestTemplate();
                template.monitorTemplates = [createMockMonitorTemplate()];
            });

            it('Should not return error', () => {
                const result = service.validateImportTestTemplateConnection(template, []);
                expect(result).toBeNull();
            });
        });

        describe('When control test template has monitor templates and connections are required but no active connections', () => {
            let template: ControlTestTemplate;

            beforeEach(() => {
                template = createMockControlTestTemplate();
                template.monitorTemplates = [createMockMonitorTemplate()];
                template.monitorTemplates[0].requestDescriptions = JSON.stringify([
                    {
                        clientType: ClientType.AWS,
                        requestDescriptionType: 'CONNECTION',
                        description:
                            'Drata inspects EFS and verifies that the Encrypted property is set to true, via AWS API.',
                    },
                ]);
            });

            it('Should return error', () => {
                const result = service.validateImportTestTemplateConnection(template, []);
                expect(result).toEqual({
                    testTemplateId: template.id,
                    name: template.name,
                    testId: template.testId,
                    errorType: LibraryTestTemplateError.MISSING_CONNECTION,
                });
            });
        });

        describe('When control test template has monitor templates and connections are required and active connections exist', () => {
            let template: ControlTestTemplate;

            beforeEach(() => {
                template = createMockControlTestTemplate();
                template.monitorTemplates = [createMockMonitorTemplate()];
                template.monitorTemplates[0].requestDescriptions = JSON.stringify([
                    {
                        clientType: ClientType.AWS,
                        requestDescriptionType: 'CONNECTION',
                        description:
                            'Drata inspects EFS and verifies that the Encrypted property is set to true, via AWS API.',
                    },
                ]);
            });

            it('Should not return error', () => {
                const result = service.validateImportTestTemplateConnection(template, [
                    {
                        clientType: ClientType.AWS,
                        state: ConnectionState.ACTIVE,
                    } as any,
                ]);
                expect(result).toBeNull();
            });
        });
    });

    describe('getActiveTestsAcrossWorkspaces', () => {
        describe('When control test template is not found', () => {
            const account = createMockAccount();

            beforeEach(() => {
                templateCoreService.getTestTemplateIdByTestIds?.mockResolvedValue(null);
            });

            it('Should throw NotFoundException', async () => {
                templateCoreService.getTestTemplateIdByTestIds?.mockResolvedValue(null);
                await expect(
                    service.getActiveTestsAcrossWorkspaces(TEST_ID, account),
                ).rejects.toThrow(NotFoundException);
            });
        });

        describe('When control test template is found', () => {
            const account = createMockAccount();

            beforeEach(() => {
                const template = createMockControlTestTemplate();
                templateCoreService.getTestTemplateIdByTestIds?.mockResolvedValue(template);
            });

            it('Should return active tests', async () => {
                const defaultLimit = config.get('pagination.limit');
                const defaultPage = config.get('pagination.page');
                const testInstance = createMockControlTestInstance();
                controlTestInstanceRepository.findAndCount?.mockResolvedValue([[testInstance], 1]);
                const result = await service.getActiveTestsAcrossWorkspaces(TEST_ID, account);
                expect(result.data).toEqual([testInstance]);
                expect(result.total).toEqual(1);
                expect(result.page).toEqual(defaultPage);
                expect(result.limit).toEqual(defaultLimit);
            });

            it('Should return active tests with custom pagination', async () => {
                const page = 2;
                const limit = 20;
                const testInstance = createMockControlTestInstance();
                controlTestInstanceRepository.findAndCount?.mockResolvedValue([[testInstance], 1]);
                const result = await service.getActiveTestsAcrossWorkspaces(
                    TEST_ID,
                    account,
                    page,
                    limit,
                );
                expect(result.data).toEqual([testInstance]);
                expect(result.total).toEqual(1);
                expect(result.page).toEqual(page);
                expect(result.limit).toEqual(limit);
            });
        });
    });

    function createMockControlTestTemplate(): ControlTestTemplate {
        const template = new ControlTestTemplate();
        template.id = TEST_TEMPLATE_ID;
        template.testId = TEST_ID;
        template.name = 'Test Template';
        template.monitorTemplates = [createMockMonitorTemplate()];
        return template;
    }

    function createMockMonitorTemplate(): MonitorTemplate {
        const template = new MonitorTemplate();
        template.id = 1;
        template.name = 'Monitor Template';
        template.monitorTemplateCheckTypes = [createMockMonitorTemplateCheckType()];
        return template;
    }

    function createMockMonitorTemplateCheckType(): MonitorTemplateCheckType {
        const checkType = new MonitorTemplateCheckType();
        checkType.id = 1;
        checkType.checkType = CheckType.INFRASTRUCTURE;
        return checkType;
    }

    function createMockControlTemplate(): ControlTemplate {
        const template = new ControlTemplate();
        template.id = 1;
        template.name = 'Control Template';
        return template;
    }

    function createMockAccount(): Account {
        const account = new Account();
        account.id = TEST_ACCOUNT_ID;
        return account;
    }

    function createMockControlTestInstance(): ControlTestInstance {
        const instance = new ControlTestInstance();
        instance.id = 1;
        instance.testId = TEST_TEMPLATE_ID;
        instance.name = 'Test Instance';
        // Add products array to prevent the error
        instance.products = [{ id: 1, name: 'Workspace 1' } as Product];
        return instance;
    }

    function createMockMonitorInstance(): MonitorInstance {
        const instance = new MonitorInstance();
        instance.id = 1;
        return instance;
    }

    function createMockMonitorInstanceCheckType(): MonitorInstanceCheckType {
        const checkType = new MonitorInstanceCheckType();
        checkType.id = 1;
        checkType.checkType = CheckType.INFRASTRUCTURE;
        return checkType;
    }
});
