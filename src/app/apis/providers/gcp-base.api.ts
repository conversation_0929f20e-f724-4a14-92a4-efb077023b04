import { ErrorCode } from '@drata/enums';
import { ApiData } from 'app/apis/classes/api/api-data.class';
import { ApiExclusion } from 'app/apis/classes/api/api-exclusion.class';
import { ApiResponse } from 'app/apis/classes/api/api-response.class';
import { RateLimitException } from 'app/apis/exceptions/rate-limit.exception';
import { isUserConfigClassificationError } from 'app/apis/interfaces/api-error-cause-classified.interface';
import { IApiServices } from 'app/apis/interfaces/api-services.interface';
import { Api } from 'app/apis/providers/api';
import { GcpProject } from 'app/apis/services/gcp/classes/gcp-project.class';
import { GcpConstants } from 'app/apis/services/gcp/gcp.constants';
import { GcpMapper } from 'app/apis/services/gcp/gcp.mapper';
import { GcpSdk } from 'app/apis/services/gcp/gcp.sdk';
import { GcpBaseApiConstructorParams } from 'app/apis/services/gcp/gcp.type';
import { PingService } from 'app/apis/services/gcp/ping.service';
import { HealthCheckResponse } from 'app/apis/types/health-check/health-check-response.type';
import { ConnectionMetadata } from 'app/companies/connections/classes/connection-metadata.class';
import { ConnectionEntity } from 'app/companies/connections/entities/connection.entity';
import { Account } from 'auth/entities/account.entity';
import { CACHE_IS_UP } from 'cache/cache.module';
import { CacheService } from 'cache/cache.service';
import { InMemoryCacheService } from 'cache/in-memory-cache.service';
import { plainToInstance } from 'class-transformer';
import { PolloAdapter } from 'commons/adapters/pollo.adapter';
import { Retry } from 'commons/decorators/retry.decorator';
import { ApiDataSource } from 'commons/enums/api-data-source.enum';
import { ConnectionFactory } from 'commons/factories/connection.factory';
import { getSharedAgents } from 'commons/helpers/http.helper';
import { forEachTokenPage, tokenize } from 'commons/helpers/pagination/pagination.helper';
import { TokenPagination } from 'commons/helpers/pagination/token.pagination';
import { TokenPaginationResponse } from 'commons/helpers/pagination/token.pagination.response';
import { functionName } from 'commons/helpers/runtime.helper';
import config from 'config';
import { FeatureFlagCategory } from 'feature-flags/enums/feature-flag-category.enum';
import { FeatureFlag } from 'feature-flags/feature-flag.enum';
import { FeatureFlagService } from 'feature-flags/feature-flag.service';
import { JWT, UserRefreshClient, auth } from 'google-auth-library';
import { cloudresourcemanager_v1 } from 'googleapis';
import { first, isEmpty, isNil, pick, startsWith } from 'lodash';
import { PolloMessage } from 'pollo-logger/classes/pollo.message';

export class GcpBaseApi extends Api implements IApiServices {
    /**
     *
     */
    private sdk: GcpSdk;

    /**
     *
     */
    protected account: Account;

    /**
     *
     */
    protected featureFlagService: FeatureFlagService;

    /**
     *
     */
    protected GCP_API_CACHE_KEY = 'gcp-api-cache';

    /**
     *
     */
    protected GCP_API_CACHE_TTL = GcpConstants.DEFAULT_GCP_API_CACHE_TTL;

    /**
     *
     */
    organizationId: string;

    /**
     *
     */
    protected inMemoryCacheService: InMemoryCacheService;

    constructor({
        connection,
        httpService,
        gcpCacheService,
        loggingContext,
        account,
        featureFlagService,
    }: GcpBaseApiConstructorParams) {
        super(connection, httpService, gcpCacheService as any, loggingContext);
        this.account = account;
        this.featureFlagService = featureFlagService;
    }

    /**
     * @description Initialize the client API and setup any prerequisites
     * @returns {void}
     */
    async initialize(): Promise<void> {
        this.client = GcpBaseApi.refresh(this.metadata);

        this.setResourceAccountId(await this.getClientId());

        if (await this.shouldRetryIndividualRequests()) {
            this.GCP_API_CACHE_TTL = GcpConstants.EXTENDED_GCP_API_CACHE_TTL;
        }

        return Promise.resolve();
    }

    /**
     *
     */
    async getClientId(): Promise<string> {
        const organizationId = await this.getOrganizationId();
        return isNil(organizationId)
            ? `${this.client.projectId}`
            : `${organizationId}:${this.client.projectId}`;
    }

    /**
     * We are required to create a refresh here as we may be required to jump to
     * GSuite scope within a transaction - we are not able to add the subject on the
     * fly as the access/JWT token will be corrupted if added to an existing client.
     *
     * @param metadata - The connection metadata
     * @param admin
     * @param scopes
     */
    public static refresh(metadata: ConnectionMetadata, admin?: string, scopes?: string[]): JWT {
        const keys = JSON.parse(metadata.key);
        const client: JWT | UserRefreshClient = auth.fromJSON(keys) as JWT;

        if (client instanceof JWT) {
            client.scopes = config.get('gcp.scopes');

            if (!isNil(admin)) {
                client.subject = admin;

                if (!isEmpty(scopes)) {
                    client.scopes = scopes;
                }
            }

            if (client.gaxios && client.gaxios.defaults) {
                const { httpsAgent } = getSharedAgents();
                client.gaxios.defaults.agent = httpsAgent;
            }
        }

        return client;
    }

    /**
     *
     * @param domain
     */
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    ping(_domain: string): Promise<boolean> {
        return new PingService(this.client, this.metadata).ping();
    }

    /**
     *
     * @returns
     */
    async getClientAlias(): Promise<string | null> {
        return this.connection.clientAlias ? Promise.resolve(this.connection?.clientAlias) : null;
    }

    getMetadataForDisplay(): Map<string, any> {
        return null;
    }

    async healthCheck(): Promise<HealthCheckResponse> {
        try {
            await this.ping(this.metadata.domain);
        } catch (error) {
            this.logger.error(
                PolloAdapter.cxt('HealthCheck GCP Error:', this.loggingContext)
                    .setSubContext(this.healthCheck.name)
                    .setContext(this.constructor.name)
                    .setResult(error.response)
                    .setError(error),
            );
            const errorCode = this.getConnectionError(error);
            return { success: false, error, errorCode };
        }
        return { success: true };
    }

    getConnectionError(error: any): ErrorCode {
        if (!isNil(error?.response?.data?.error?.message)) {
            if (error?.response?.data?.error?.message.match(/permission/gi)) {
                return ErrorCode.GCP_ACCESS_DENIED;
            } else {
                return ErrorCode.UNDEFINED;
            }
        } else if (error?.response?.data?.error?.match(/invalid_grant/gi)) {
            return ErrorCode.GCP_INVALID_GRANT;
        } else {
            return ErrorCode.UNDEFINED;
        }
    }

    /**
     *
     */
    getDataSource(): ApiDataSource {
        return ApiDataSource.GCP;
    }

    /**
     *
     */
    protected getSdk(): GcpSdk {
        if (isNil(this.sdk)) {
            this.sdk = new GcpSdk(
                this.client,
                this.metadata,
                this.httpService,
                this.loggingContext,
            );
        }

        return this.sdk;
    }

    /**
     * Get the GCP Organization ID with database metadata storage
     * Stores the organization ID in connection metadata for persistence
     */
    async getOrganizationId(): Promise<string> {
        if (!isNil(this.organizationId)) {
            return this.organizationId;
        }

        const metadata = this.connection.getMetadata();
        if (!isNil(metadata) && !isNil(metadata.organizationId)) {
            this.organizationId = metadata.organizationId;
            return this.organizationId;
        }

        try {
            this.organizationId = await this.getSdk().getOrganizationId();

            if (!isNil(this.organizationId)) {
                metadata.organizationId = this.organizationId;
                this.connection.setMetadata(metadata);

                const connectionExists = !isNil(this.connection.id);
                if (connectionExists) {
                    await this.updateMetadata(this.account, this.connection);
                }
            }
        } catch (error) {
            this.onCaughtError(error, true);
        }
        return this.organizationId;
    }

    /**
     * Save connection to database
     * @param account - The account
     * @param connection - The connection entity to save
     */
    private async updateMetadata(account: Account, connection: ConnectionEntity): Promise<void> {
        const tenantConnection = await ConnectionFactory.getConnection(account);
        const repository = tenantConnection.getRepository(ConnectionEntity);

        await repository.update(connection.id, {
            metadata: ConnectionMetadata.stringify(connection.getMetadata()),
            updatedAt: new Date(),
        });
    }

    /**
     *
     */
    async getOrganizationalAdmins(): Promise<string[]> {
        let admins: string[] = [];

        try {
            admins = await this.getSdk().getOrganizationAdmins();
        } catch (error) {
            this.onCaughtError(error, true);
        }
        return Promise.resolve(admins);
    }

    /**
     *
     * @param data
     * @param raw
     * @returns
     */
    protected data<T extends ApiData>(data: T, raw: string): ApiResponse<T> {
        if (!isNil(this.getResourceAccountId())) {
            data.accountId = this.getResourceAccountId();
        }

        return {
            data,
            raw,
            request: null,
            source: this.getDataSource(),
        };
    }

    /**
     *
     * @param data
     */
    private addPostDataValues(data: ApiData[]): void {
        for (const datum of data) {
            if (!isNil(this.getResourceAccountId())) {
                datum.accountId = this.getResourceAccountId();
            }
        }
    }

    /**
     *
     * @param data
     * @param raw
     * @param isNotImplemented
     */
    protected tokenPaginatedResponse<T extends ApiData>(
        data: TokenPaginationResponse<T>,
        raw?: string,
        isNotImplemented?: boolean,
    ): ApiResponse<TokenPaginationResponse<T>> {
        if (isNil(isNotImplemented)) {
            isNotImplemented = false;
        }

        this.addPostDataValues(data.data.page);

        return {
            data,
            raw,
            request: null,
            source: this.getDataSource(),
            isNotImplemented,
        };
    }

    /**
     *
     * @param data
     * @param raw
     * @returns
     */
    protected array<T extends ApiData>(data: T[], raw?: string): ApiResponse<T[]> {
        this.addPostDataValues(data);

        return {
            data,
            raw,
            request: null,
            source: this.getDataSource(),
        };
    }

    @Retry({ maxRetries: 8, retryWait: 30_000 })
    async getProjects(): Promise<GcpProject[]> {
        const gcpProjects: GcpProject[] = [];

        try {
            await forEachTokenPage(
                async pageToken => {
                    const { projects: data, nextPageToken } = await this.getSdk().getProjects(
                        TokenPagination.MAX_RESULTS,
                        pageToken ?? undefined,
                    );

                    if (isNil(data) || isEmpty(data)) {
                        return tokenize([], null);
                    }

                    return tokenize(data, nextPageToken ?? null);
                },
                async (data: cloudresourcemanager_v1.Schema$Project[]) => {
                    gcpProjects.push(
                        ...data
                            .map(item => GcpMapper.project(item))
                            .filter(project => !isEmpty(project.id)),
                    );
                },
            );
        } catch (error) {
            if (error instanceof RateLimitException) {
                throw error;
            }

            this.onCaughtError(error);
        }

        return gcpProjects;
    }

    /**
     * The caching mechanism here is hand-rolled until the new caching service is implemented.
     */
    protected async getProjectsInScope(): Promise<GcpProject[]> {
        const cacheKey = this.generateCacheKey(this.getProjectsInScope.name);
        const cached = await this.getFromCache<GcpProject[]>(cacheKey);

        if (cached) {
            this.logger.log(
                PolloAdapter.acct('Fetched filteredProjects from cache', this.account)
                    .setContext(this.constructor.name)
                    .setSubContext(this.getProjectsInScope.name)
                    .setIdentifier({
                        cacheKey,
                        count: cached.length,
                    }),
            );

            return cached;
        }

        const projects = await this.getProjects();
        const filteredProjects = this.filterProjectsInScope(projects);
        await this.setCache(cacheKey, filteredProjects, this.GCP_API_CACHE_TTL);

        this.logger.log(
            PolloAdapter.acct('Set filteredProjects in cache', this.account, GcpBaseApi.name)
                .setContext(this.constructor.name)
                .setSubContext(this.getProjectsInScope.name)
                .setIdentifier({
                    cacheKey,
                    count: filteredProjects.length,
                }),
        );

        return filteredProjects;
    }

    /**
     *
     * @param projects - The list of GCP projects to filter
     * @returns - The list of GCP projects in scope
     */
    private filterProjectsInScope(projects: GcpProject[]): GcpProject[] {
        const sysProjects = projects.filter(gcpProject => startsWith(gcpProject?.id, 'sys-'));

        const excludedTagProjects = projects.filter(this.isExcludedByTag);

        // For some reason, there are projects without project id, so we need to filter them out
        const projectsWithoutProjectId = projects.filter(gcpProject => isEmpty(gcpProject.id));

        const skippedProjectIds = [...sysProjects, ...excludedTagProjects].map(
            skippedProject => skippedProject.id,
        );

        const projectsInScope = projects.filter(
            gcpProject => !skippedProjectIds.includes(gcpProject.id) && !isNil(gcpProject.id), // FYI temporary check. this filter is necessarily done to figure out an edge case
        );

        if (!isEmpty(projects)) {
            this.logger.log(
                PolloAdapter.acct(
                    'GcpBaseApi.filterProjectsInScope() gathered projects for GCP Organization',
                    this.account,
                    GcpBaseApi.name,
                ).setIdentifier({
                    totalProjectsCount: projects.length,
                    sysProjectsCount: sysProjects.length,
                    excludedTagProjectsCount: excludedTagProjects.length,
                    projectsInScopeCount: projectsInScope.length,
                    projectsWithoutProjectIdCount: projectsWithoutProjectId.length,
                    firstProjectWithoutProjectId: first(projectsWithoutProjectId), // logging a project without id
                }),
            );
        }

        return projectsInScope;
    }

    /**
     *
     * @param project - The GCP project to check
     * @returns
     */
    private isExcludedByTag(project: GcpProject): boolean {
        return !isNil(project.labels?.[ApiExclusion.TAG.toLowerCase()]);
    }

    /**
     *
     * @param name - The name of the function
     * @param id - A unique identifier for the function
     * @returns - A generated cache key
     */
    protected generateCacheKey(name: string, id?: string) {
        const globalPrefix = config.get<string>('cache.globalPrefix') || 'globalstore';
        return `${globalPrefix}:{${this.account.id}}:${this.GCP_API_CACHE_KEY}:connection-${this.connection.id}-${name}${!isNil(id) ? `-${id}` : ''}`;
    }

    /**
     *
     * @param cacheKey - The key to use for caching
     * @param call - The function to call if the cache is empty
     * @param ttl - The time to live for the cache
     * @returns
     */
    protected async cacheResponse<T>(
        cacheKey: string,
        call: () => Promise<T>,
        ttl: number,
    ): Promise<T> {
        const cached = await this.getFromCache<T>(cacheKey);

        if (cached) {
            return cached;
        }

        const response = await call();

        await this.setCache(cacheKey, response, ttl);

        return response;
    }

    setInMemoryCacheService(inMemoryCacheService: InMemoryCacheService): void {
        this.inMemoryCacheService = inMemoryCacheService;
    }

    protected async getFromCache<T>(key: string, toClazz = null): Promise<T | null> {
        if (!config.get('cache.enabled') || !CACHE_IS_UP) {
            return Promise.resolve(null);
        }

        const cache: CacheService | InMemoryCacheService = this.inMemoryCacheService
            ? this.inMemoryCacheService
            : this.cacheService;

        const data = await cache.get(key);

        if (!isNil(data)) {
            this.logger.log(
                PolloMessage.msg(`Cache hit ${key}`)
                    .setContext(this.constructor.name)
                    .setSubContext(this.getFromCache.name),
            );
        } else {
            this.logger.log(
                PolloMessage.msg(`Cache miss ${key}`)
                    .setContext(this.constructor.name)
                    .setSubContext(this.getFromCache.name),
            );
        }

        if (!isNil(toClazz)) {
            return plainToInstance(toClazz, data);
        }

        return data as T;
    }

    protected setCache<T>(key: string, data: T, ttl = 60): Promise<void> {
        if (!config.get('cache.enabled') || !CACHE_IS_UP) {
            return Promise.resolve();
        }

        if (isNil(data)) {
            return Promise.resolve();
        }

        const cache: CacheService | InMemoryCacheService = this.inMemoryCacheService
            ? this.inMemoryCacheService
            : this.cacheService;

        void cache.set(key, data, ttl).then((status: never) => {
            this.logger.log(
                PolloMessage.msg(`Cache set ${key}`)
                    .setContext(this.constructor.name)
                    .setSubContext(this.setCache.name)
                    .setIdentifier({
                        key,
                        status,
                    }),
            );
        });

        return Promise.resolve();
    }

    protected onCaughtError(error: Error, swallow?: boolean): void {
        if (isUserConfigClassificationError(error)) {
            const polloMessage = PolloMessage.msg(error.message)
                .setSubContext(functionName(3))
                .setIdentifier({
                    connection: pick(this.connection, [
                        'id',
                        'clientType',
                        'providerType',
                        'state',
                        'accountId',
                        'clientAlias',
                        'failedAt',
                        'enabledAt',
                        'deletedAt',
                        'manuallyUpdatedAt',
                        'aliasUpdatedAt',
                    ]),
                    errorClassification: 'USER_CONFIG',
                })
                .setError(error);

            this.logger.warn(polloMessage);

            if (isNil(swallow) || !swallow) {
                throw error;
            }
            return;
        }

        // Fall back to the base implementation for other errors
        super.onCaughtError(error, swallow);
    }

    protected shouldRetryIndividualRequests(): Promise<boolean> {
        return this.featureFlagService.evaluateAsTenant(
            {
                category: FeatureFlagCategory.PERFORMANCE,
                name: FeatureFlag.RELEASE_GCP_INDIVIDUALIZE_RETRIES,
                defaultValue: false,
            },
            this.account,
        );
    }
}
