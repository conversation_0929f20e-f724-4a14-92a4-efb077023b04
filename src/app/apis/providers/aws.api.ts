/* eslint-disable no-await-in-loop */
/* eslint-disable @typescript-eslint/no-unused-vars */
import { ListDistributionsCommandOutput } from '@aws-sdk/client-cloudfront';
import { ListTagsCommandOutput as ListTagsCommandOutputCloudTrail } from '@aws-sdk/client-cloudtrail';
import { DescribeInstancesCommandOutput as DescribeInstancesCommandOutputEC2 } from '@aws-sdk/client-ec2';
import {
    DescribeRepositoriesCommandOutput,
    ListTagsForResourceCommandOutput as ListTagsForResourceCommandOutputECR,
} from '@aws-sdk/client-ecr';
import {
    DescribeClustersCommandOutput as DescribeClustersCommandOutputECS,
    DescribeServicesCommandOutput as DescribeServicesCommandOutputECS,
    DescribeTasksCommandOutput as DescribeTasksCommandOutputECS,
    ListClustersCommandOutput as ListClustersCommandOutputECS,
    ListTasksCommandOutput as ListTasksCommandOutputECS,
} from '@aws-sdk/client-ecs';
import {
    DescribeClusterCommandOutput as DescribeClusterCommandOutputEKS,
    DescribeNodegroupCommandOutput,
} from '@aws-sdk/client-eks';
import { LoadBalancerDescription } from '@aws-sdk/client-elastic-load-balancing';
import { DescribeLoadBalancersCommandOutput } from '@aws-sdk/client-elastic-load-balancing-v2';
import { ListTagsForResourceCommandOutput as ListTagsForResourceCommandOutputElasticache } from '@aws-sdk/client-elasticache';
import { SimulatePrincipalPolicyCommandOutput } from '@aws-sdk/client-iam';
import { BucketLocationConstraint, GetBucketTaggingCommandOutput } from '@aws-sdk/client-s3';
import { ListSubscriptionsByTopicCommandOutput } from '@aws-sdk/client-sns';
import { ListQueueTagsCommandOutput } from '@aws-sdk/client-sqs';
import {
    GetWebACLCommandOutput as GetWebACLCommandOutputWAF,
    ListTagsForResourceCommandOutput as ListTagsForResourceCommandOutputWAF,
    ListWebACLsCommandOutput as ListWebACLsCommandOutputWAF,
} from '@aws-sdk/client-waf';
import {
    ListTagsForResourceCommandOutput as ListTagsForResourceCommandOutputWAFv2,
    ListWebACLsCommandOutput as ListWebACLsCommandOutputWAFv2,
} from '@aws-sdk/client-wafv2';
import { AsyncGenChain } from '@drata/common';
import { ErrorCode } from '@drata/enums';
import { HttpService } from '@nestjs/axios';
import { NotImplementedException } from '@nestjs/common';
import { Bucket } from 'app/apis/classes/api-data-storage/bucket.class';
import { Cache } from 'app/apis/classes/api-data-storage/cache.class';
import { Container } from 'app/apis/classes/api-data-storage/container.class';
import { DatabaseCluster } from 'app/apis/classes/api-data-storage/database-cluster.class';
import { Database } from 'app/apis/classes/api-data-storage/database.class';
import { Indexer } from 'app/apis/classes/api-data-storage/indexer.class';
import { NodeGroup } from 'app/apis/classes/api-data-storage/node-group.class';
import { Queue } from 'app/apis/classes/api-data-storage/queue.class';
import { Table } from 'app/apis/classes/api-data-storage/table.class';
import { Alarm } from 'app/apis/classes/api-data/alarm.class';
import { BucketAccount } from 'app/apis/classes/api-data/bucket-account.class';
import { Cluster } from 'app/apis/classes/api-data/cluster.class';
import { Event } from 'app/apis/classes/api-data/event.class';
import { Ids } from 'app/apis/classes/api-data/ids-data.class';
import { Instance } from 'app/apis/classes/api-data/instance.class';
import { LoadBalancer } from 'app/apis/classes/api-data/load-balancer.class';
import { NetworkInterface } from 'app/apis/classes/api-data/network-interface.class';
import { Report } from 'app/apis/classes/api-data/report.class';
import { Rule } from 'app/apis/classes/api-data/rule.class';
import { SecurityGroup } from 'app/apis/classes/api-data/security-group.class';
import { TagCollection } from 'app/apis/classes/api-data/tag-collection.class';
import { User } from 'app/apis/classes/api-data/user.class';
import { Waffable } from 'app/apis/classes/api-data/waffable-data.class';
import { ApiData } from 'app/apis/classes/api/api-data.class';
import { ApiExclusion } from 'app/apis/classes/api/api-exclusion.class';
import { ApiResponse } from 'app/apis/classes/api/api-response.class';
import { AwsBucketLifeCycleConfiguration } from 'app/apis/classes/aws/aws-bucket-lifecycle-configuration.class';
import { AwsUserAccessReviewServiceUser } from 'app/apis/classes/aws/aws-user-access-review-service-user.class';
import { AwsUsersNextPageToken } from 'app/apis/classes/aws/aws-users-next-page-token.class';
import { AwsSetupType } from 'app/apis/enums/aws-setup-type.enum';
import {
    isIApiErrorCauseClassified,
    isUserConfigClassificationError,
} from 'app/apis/interfaces/api-error-cause-classified.interface';
import { IAssetOwners } from 'app/apis/interfaces/asset-owners.interface';
import { IBucketLifecycleConfiguration } from 'app/apis/interfaces/bucket-lifecycle-configuration.interface';
import { IInfrastructureBucketLifeCycleConfiguration } from 'app/apis/interfaces/infrastructure-bucket-lifecycle-configuration.interface';
import { IInfrastructureServices } from 'app/apis/interfaces/infrastructure-services.interface';
import { ProviderWithAssets } from 'app/apis/interfaces/provider-with-assets.interface';
import { IUserAccessReviewServices } from 'app/apis/interfaces/user-access-review-services.interface';
import { Api } from 'app/apis/providers/api';
import { AwsConstants } from 'app/apis/services/aws/aws.constants';
import { AwsMapper as Mapper } from 'app/apis/services/aws/aws.mapper';
import { AwsSdk } from 'app/apis/services/aws/aws.sdk';
import { SCIMConfiguration } from 'app/apis/services/aws/classes/scim-configuration.class';
import { IEndpointAvailableResponse } from 'app/apis/services/aws/interfaces/endpoint-available-response.interface';
import { IIdentityCenterInstance } from 'app/apis/services/aws/interfaces/identity-center-instance.interface';
import { IScimProps } from 'app/apis/services/aws/interfaces/list-identity-center-users.interface';
import { PaginatedType as Paginated } from 'app/apis/types/api-data/paginated.type';
import { AccessData, Statement } from 'app/apis/types/aws/aws-access-data';
import { ConfigurationParams } from 'app/apis/types/aws/aws-configuration-params.type';
import { FilterType } from 'app/apis/types/filter/filter.type';
import { HealthCheckResponse } from 'app/apis/types/health-check/health-check-response.type';
import { InfrastructureAuthorizationDetailsType as AuthDetails } from 'app/apis/types/infrastructure/infrastructure-authorization-details.type';
import { InfrastructureMfaDeviceType } from 'app/apis/types/infrastructure/infrastructure-mfa-device.type';
import { ServicePageableType } from 'app/apis/types/service/service-pageable.type';
import { AssetRequestDto } from 'app/assets/dtos/asset-request.dto';
import { ConnectionSetting } from 'app/companies/connections/entities/connection-setting.entity';
import { ConnectionEntity } from 'app/companies/connections/entities/connection.entity';
import { IRegionSetting } from 'app/companies/connections/interfaces/settings/region-setting.interface';
import { EMPTY_STRING } from 'app/v2/companies/connections/constants';
import { pingWorkflow } from 'app/worker/workflows';
import type { Account } from 'auth/entities/account.entity';

import { CacheService } from 'cache/cache.service';
import { isJSON } from 'class-validator';
import { PolloAdapter } from 'commons/adapters/pollo.adapter';
import { LoggingContext } from 'commons/adapters/types/logging-context.type';
import {
    EvidenceWithTags,
    ExternalTags,
    getTags,
    isEvidenceWithTags,
} from 'commons/custom-facts/tag-key-value.custom-fact.helper';
import { ResourceScoping } from 'commons/decorators/resource-scoping.decorator';
import { Retry } from 'commons/decorators/retry.decorator';
import { AccountEntitlementType } from 'commons/enums/account-entitlement-type.enum';
import { ApiDataSource } from 'commons/enums/api-data-source.enum';
import { ApiErrorCauseClassification } from 'commons/enums/api-error-cause-classification.enum';
import { AssetClassType } from 'commons/enums/asset-class-type.enum';
import { AssetProvider } from 'commons/enums/asset-provider.enum';
import { AssetType } from 'commons/enums/asset-type.enum';
import { ClientType } from 'commons/enums/auth/client-type.enum';
import { AutopilotMetricNamespace as MetricNamespace } from 'commons/enums/autopilot/autopilot-metric-namespace.enum';
import { AutopilotMetric as Metric } from 'commons/enums/autopilot/autopilot-metric.enum';
import { ConnectionSettingType } from 'commons/enums/connection-setting-type.enum';
import { getAccountAlias, getAccountIdFromArn, resolveClientId } from 'commons/helpers/aws.helper';
import { serialize } from 'commons/helpers/error.helper';
import { forEachTokenPage, tokenize } from 'commons/helpers/pagination/pagination.helper';
import { TokenPagination } from 'commons/helpers/pagination/token.pagination';
import { TokenPaginationResponse } from 'commons/helpers/pagination/token.pagination.response';
import { getConcurrentBatchSize } from 'commons/helpers/parallelize/aws-org-units-parallelization.helper';
import { compareLowerCase, iIncludes } from 'commons/helpers/string.helper';
import { getTemporalClient } from 'commons/helpers/temporal/client';
import { KeyValuePair } from 'commons/types/key-value-pair.type';
import config from 'config';
import { AwsClientBuilder } from 'dependencies/aws/aws-client.builder';
import { FeatureFlagCategory } from 'feature-flags/enums/feature-flag-category.enum';
import { FeatureFlag } from 'feature-flags/feature-flag.enum';
import { FeatureFlagService } from 'feature-flags/feature-flag.service';
import { chunk, get, has, isArray, isEmpty, isNil, set, uniq } from 'lodash';
import { PolloMessage } from 'pollo-logger/classes/pollo.message';

export const defaultRetryOptions = {
    maxRetries: AwsConstants.MAX_RETRIES,
    retryWait: AwsConstants.RETRY_WAIT_MILLISECONDS,
};

export class AwsApi
    extends Api
    implements
        IInfrastructureServices,
        ProviderWithAssets,
        IInfrastructureBucketLifeCycleConfiguration,
        IUserAccessReviewServices
{
    protected awsClientBuilder: AwsClientBuilder;

    private parentOrgClientBuilder: AwsClientBuilder | null;

    protected sdk: AwsSdk;

    private accountAlias: string;

    private accountId: string;

    private configurationParams: ConfigurationParams;

    private identityCenterId: string;

    private identityCenterRegion: string;

    private uarGranularData: boolean | null;

    private policyVersionCache: { [arn: string]: string } = {};

    private policyStatementCache: { [arn: string]: Statement[] } = {};

    constructor(
        connection: ConnectionEntity,
        httpService: HttpService,
        cacheService: CacheService,
        loggingContext: LoggingContext,
        private account: Account,
        private featureFlagService: FeatureFlagService,
    ) {
        super(connection, httpService, cacheService, loggingContext);
        this.configurationParams = this.getConfigurationParams();
    }

    getSecurityAuditActions(permissions: string[]): Promise<SimulatePrincipalPolicyCommandOutput> {
        return this.sdk.getSimulatedActions(this.configurationParams.RoleArn, permissions);
    }

    /**
     * Manually forces a credential refresh.
     * Useful for long-running tasks to ensure fresh credentials.
     */
    async forceRefreshCredentials(): Promise<void> {
        await this.sdk.forceRefreshCredentials();
    }

    getScimProps(): IScimProps {
        const { scimConfiguration } = {
            ...this.connection?.getMetadata(),
        };
        return {
            token: get(scimConfiguration, 'SCIMToken', ''),
            endpoint: get(scimConfiguration, 'SCIMEndpoint', ''),
        };
    }

    getIdentityCenterId() {
        return this.identityCenterId;
    }

    getIdentityCenterRegion() {
        return this.identityCenterRegion;
    }

    loadAssetsOwners(): Promise<IAssetOwners> {
        return Promise.reject(new NotImplementedException());
    }

    loadPaginatedAssets(
        _nextPageToken?: string,
    ): Promise<{ data: AssetRequestDto[]; nextPageToken: string }> {
        return Promise.reject(new NotImplementedException());
    }

    getConfigClient(): AwsClientBuilder {
        return !isNil(this.parentOrgClientBuilder)
            ? this.parentOrgClientBuilder
            : this.awsClientBuilder;
    }

    filterByOwnIdentityCenterInstance(instances: IIdentityCenterInstance[]) {
        return instances?.filter(instance => instance.OwnerAccountId === this.accountId) ?? [];
    }

    async getIdentityCenterRegionIdByAccountType(): Promise<{
        [key: string]: IIdentityCenterInstance[];
    } | null> {
        const client = this.getConfigClient();
        const regions = await this.getRegions();

        if (isEmpty(regions)) {
            return null;
        }

        const regionsDetected: {
            [key: string]: IIdentityCenterInstance[];
        } = {};
        for (const region of regions) {
            try {
                regionsDetected[region] = [];
                const identityCenterInstances = await this.sdk.getIdentityCenterId(client, region);
                const filteredInstances =
                    this.filterByOwnIdentityCenterInstance(identityCenterInstances);
                if (isEmpty(identityCenterInstances) || isEmpty(filteredInstances)) {
                    delete regionsDetected[region];
                    continue;
                }
                regionsDetected[region] = filteredInstances ?? [];
                break;
            } catch (error) {
                this.logger.error(
                    PolloAdapter.cxt(
                        `Error getting Identity Center and Region instances`,
                        this.loggingContext,
                    )
                        .setContext(this.constructor.name)
                        .setSubContext(this.getIdentityCenterRegionIdByAccountType.name)
                        .setError(error),
                );
                return null;
            }
        }
        return regionsDetected;
    }

    getAwsFetchingUserServices(
        maxResults: number,
        awsUsersNextPageToken: AwsUsersNextPageToken,
    ): {
        [key: string]: () => Promise<ApiResponse<Paginated<User>>> | undefined;
    } {
        return {
            [AwsConstants.IAM_NAME]: () => {
                if (awsUsersNextPageToken.isIAMServiceToken()) {
                    this.logger.log(
                        PolloAdapter.cxt('Fetching users from AWS IAM service', this.loggingContext)
                            .setContext(this.constructor.name)
                            .setSubContext(this.getAwsFetchingUserServices.name),
                    );
                    return this.getIAMUsers(maxResults, awsUsersNextPageToken?.getNextPageToken());
                }
            },
            [AwsConstants.IDENTITY_CENTER_NAME]: () => {
                if (awsUsersNextPageToken.isIdentityCenterServiceToken()) {
                    this.logger.log(
                        PolloAdapter.cxt(
                            'Fetching users from AWS Identity Center service',
                            this.loggingContext,
                        )
                            .setContext(this.constructor.name)
                            .setSubContext(this.getAwsFetchingUserServices.name),
                    );
                    return this.getIdentityCenterUsers(
                        maxResults,
                        awsUsersNextPageToken?.getNextPageToken(),
                    );
                }
            },
        };
    }

    getDataSource(): ApiDataSource {
        return ApiDataSource.AWS;
    }

    getAssetOwnerId(): Promise<string> {
        return Promise.resolve(this.accountId || this.account.id);
    }

    getAssetProvider(): AssetProvider {
        return AssetProvider.AWS;
    }

    protected getResourceTags(): KeyValuePair[] {
        return [];
    }

    getClientType(): ClientType {
        return ClientType.AWS;
    }

    protected data<T extends ApiData>(data: T, raw: string): ApiResponse<T> {
        data.organizationalUnitName = this.getOrganizationalUnitName();
        data.resourceOrganizationPath = this.getOrganizationPath();
        data.accountId = this.getResourceAccountId();
        data.accountName = this.getResourceAccountName();

        return {
            data,
            raw,
            request: null,
            source: this.getDataSource(),
        };
    }

    protected array<T extends ApiData>(data: T[], raw?: string): ApiResponse<T[]> {
        this.addPostDataValues(data);

        return {
            data,
            raw,
            request: null,
            source: this.getDataSource(),
        };
    }

    protected arrayResponse<T extends ApiData>(data: T[], raw?: string): ApiResponse<T[]> {
        if (!isEmpty(this.getResourceTags())) {
            data = this.reduceTags(data, this.getResourceTags());
        }

        this.addPostDataValues(data);
        this.setArrayExclusions(data);

        return {
            data,
            raw,
            request: null,
            source: this.getDataSource(),
        };
    }

    protected tokenPaginatedResponse<T extends ApiData>(
        data: TokenPaginationResponse<T>,
        raw?: string,
        isNotImplemented = false,
    ): ApiResponse<TokenPaginationResponse<T>> {
        if (!isEmpty(this.getResourceTags())) {
            data.data.page = this.reduceTags(data.data.page, this.getResourceTags());
        }

        this.setExclusions(data);
        this.addPostDataValues(data.data.page);

        return {
            data,
            raw,
            request: null,
            source: this.getDataSource(),
            isNotImplemented,
        };
    }

    protected irreduciblePaginatedResponse<T extends ApiData>(
        data: TokenPaginationResponse<T>,
        raw?: string,
        isNotImplemented = false,
    ): ApiResponse<TokenPaginationResponse<T>> {
        this.setExclusions(data);
        this.addPostDataValues(data.data.page);

        return {
            data,
            raw,
            request: null,
            source: this.getDataSource(),
            isNotImplemented,
        };
    }

    private addPostDataValues(data: ApiData[]): void {
        for (const datum of data) {
            if (!isNil(this.getOrganizationalUnitId())) {
                datum.organizationalUnitId = this.getOrganizationalUnitId();
            }
            if (!isNil(this.getOrganizationalUnitName())) {
                datum.organizationalUnitName = this.getOrganizationalUnitName();
            }
            if (!isNil(this.getOrganizationPath())) {
                datum.resourceOrganizationPath = this.getOrganizationPath();
            }
            if (!isNil(this.getResourceAccountId())) {
                datum.accountId = this.getResourceAccountId();
            }
            if (!isNil(this.getResourceAccountName())) {
                datum.accountName = this.getResourceAccountName();
            }
        }
    }

    async initialize(): Promise<void> {
        this.awsClientBuilder = new AwsClientBuilder(
            this.configurationParams,
            AwsSetupType.STANDARD,
            this.loggingContext,
        );
        await this.awsClientBuilder.buildConfiguration();

        if (!isNil(this.metadata.organizationRole)) {
            this.parentOrgClientBuilder = new AwsClientBuilder(
                {
                    RoleArn: this.metadata.organizationRole,
                    ExternalId: this.metadata.externalId,
                    RoleSessionName: config.get('aws.autopilot.roleSessionName'),
                },
                AwsSetupType.STANDARD,
                this.loggingContext,
            );
            await this.parentOrgClientBuilder.buildConfiguration();
        } else {
            this.parentOrgClientBuilder = null;
        }

        // aws sdk v3 clients require a default region
        await this.setDefaultRegion();

        this.sdk = new AwsSdk(this.awsClientBuilder, this.metadata.key, this.loggingContext);

        await this.didInitialize();
    }

    protected async didInitialize(): Promise<void> {
        if (isNil(this.metadata['awsAlias'])) {
            this.accountAlias = await getAccountAlias(this.awsClientBuilder);
            this.accountId = await this.sdk.getAccountId();
            this.metadata.awsAlias = this.accountAlias;
            this.metadata.awsAccountId = this.accountId;
            this.connection.setMetadata(this.metadata);
        } else {
            this.accountAlias = this.metadata['awsAlias'];
            this.accountId = this.metadata['awsAccountId'];
        }

        this.setResourceAccountId(this.accountId);
        this.setResourceAccountName(this.accountAlias);

        /**
         * This ping lets us quickly check if anything is or was wrong
         * with the connection before doing anything more complicated
         */
        await this.sdk.ping();
    }

    async ping(domain: string): Promise<boolean> {
        const shouldUseWorkflow = await this.featureFlagService.evaluate(
            {
                category: FeatureFlagCategory.NONE,
                name: FeatureFlag.PING_AS_WORKFLOW,
                defaultValue: false,
            },
            this.connection.user,
            this.account,
        );

        if (shouldUseWorkflow) {
            this.logger.log(
                PolloAdapter.cxt(
                    'Feature flag enabled, Running ping as workflow',
                    this.loggingContext,
                ).setIdentifier({
                    flagName: FeatureFlag.PING_AS_WORKFLOW,
                }),
            );
            const temporalClient = await getTemporalClient(domain);
            return temporalClient.executeWorkflow(pingWorkflow, {
                taskQueue: config.get('temporal.taskQueues.temporal-default'),
                args: [
                    {
                        connectionMetadata: this.connection.metadata,
                        clientType: this.getClientType(),
                        account: { id: this.connection.accountId, domain },
                    },
                ],
                memo: {
                    accountId: this.connection.accountId,
                    domain,
                },
            });
        } else {
            return this.sdk.ping();
        }
    }

    async pingV2(region: string): Promise<boolean> {
        return this.sdk.pingV2(region);
    }

    async healthCheck(): Promise<HealthCheckResponse> {
        try {
            await this.ping(this.metadata.domain);
            return {
                success: true,
            };
        } catch (error) {
            this.logger.error(
                PolloAdapter.cxt('HealthCheck AWS Error:', this.loggingContext)
                    .setResult(error.response)
                    .setError(error),
            );
            return {
                success: false,
                error,
                errorCode: ErrorCode.AWS_CONNECTION_ACCESS_DENIED,
            };
        }
    }

    getConnectionError(error: any): ErrorCode {
        if (error.code === AwsConstants.ACCESS_DENIED_ERROR) {
            return ErrorCode.AWS_CONNECTION_ACCESS_DENIED;
        } else {
            return ErrorCode.UNDEFINED;
        }
    }

    getInfrastructureMfa(): Promise<User[]> {
        return Promise.reject(new NotImplementedException());
    }

    getAuthorizationDetails(_maxResults: number, _nextPageToken: string): Promise<AuthDetails> {
        return Promise.reject(new NotImplementedException());
    }

    async checkSCIMEndpointHealth(): Promise<IEndpointAvailableResponse | null> {
        try {
            const ffStatus = await this.shouldPullIdentityCenterUsers();
            this.logger.log(
                PolloAdapter.cxt(
                    `Identity Center feature flag has status: ${ffStatus}${
                        !ffStatus ? ' ,skipped SCIM Health Check verification' : ''
                    }`,
                    this.loggingContext,
                )
                    .setContext(this.constructor.name)
                    .setSubContext(this.checkSCIMEndpointHealth.name),
            );
            if (ffStatus) {
                return await this.isScimEndpointAvailable(this.getScimProps());
            }
            return null;
        } catch (error) {
            this.logger.log(
                PolloAdapter.cxt(
                    'Error requesting endpoint health check from AWS IC SCIM endpoint',
                    this.loggingContext,
                )
                    .setContext(this.constructor.name)
                    .setSubContext(this.checkSCIMEndpointHealth.name)
                    .setError(error),
            );
            return null;
        }
    }

    isScimEndpointAvailable(scimProps: IScimProps): Promise<IEndpointAvailableResponse> {
        return this.sdk.isScimEndpointAvailable(scimProps);
    }

    async getUsers(
        _domain: string,
        maxResults: number,
        nextPageToken: string,
    ): Promise<ApiResponse<Paginated<User>>> {
        const awsUsersNextPageToken = new AwsUsersNextPageToken(nextPageToken, this.account);
        try {
            const apiResponse = this.getAwsFetchingUserServices(maxResults, awsUsersNextPageToken)[
                awsUsersNextPageToken.getSource()
            ];

            const response = await apiResponse();

            if (!isNil(response) && has(response, 'data.nextPageToken')) {
                response.data.nextPageToken = this.resolveNextToken(
                    awsUsersNextPageToken,
                    response,
                );
            }

            //TODO We may want to refactor this method given the response object may be empty.
            return response;
        } catch (error) {
            this.logger.error(
                PolloAdapter.cxt(
                    `Error getting users from ${awsUsersNextPageToken.getSource()}`,
                    this.loggingContext,
                )
                    .setContext(this.getUsers.name)
                    .setSubContext(this.getUsers.name)
                    .setError(error),
            );
        }
    }

    private resolveNextToken(
        currentAwsUsersPageToken: AwsUsersNextPageToken,
        currentResponse: ApiResponse<Paginated<User>>,
    ): string | null {
        const { data: dataResponse } = currentResponse;
        const hasNextPageToken = !isNil(dataResponse?.nextPageToken);
        if (currentAwsUsersPageToken.isIAMServiceToken() && !hasNextPageToken) {
            this.logger.log(
                PolloAdapter.cxt('Getting new Identity Center token', this.loggingContext)
                    .setContext(this.constructor.name)
                    .setSubContext(this.resolveNextToken.name),
            );
            return AwsUsersNextPageToken.getNewIdentityCenterToken();
        }

        if (hasNextPageToken) {
            return AwsUsersNextPageToken.stringify(
                currentAwsUsersPageToken.getSource(),
                currentResponse.data.nextPageToken,
            );
        }

        return dataResponse?.nextPageToken;
    }

    private async getIAMUsers(
        maxResults: number,
        nextPageToken: string | null,
    ): Promise<ApiResponse<Paginated<User>>> {
        const client = this.getConfigClient();

        const accessReviewEntitlementValue = await this.getEntitlementValue(
            this.account,
            AccountEntitlementType.ACCESS_REVIEW,
        );

        const users = await this.sdk.listUsers(
            client,
            maxResults,
            nextPageToken,
            accessReviewEntitlementValue,
        );

        /**
         * Notice: Closure here!
         */
        const processUser = async (
            id: string,
            name: string,
        ): Promise<{ id: string; hasMfa: boolean; iamAccess: boolean }> => {
            const mfaDevices = await this.sdk.listMfaDevices(client, name);

            const hasMfa = !isEmpty(mfaDevices.devices);
            const iamAccess = await this.sdk.getLoginProfile(client, name, this.loggingContext);
            return { id, hasMfa, iamAccess };
        };

        const usersBatchSize = getConcurrentBatchSize(
            this.connection,
            'aws.concurrency.usersBatchSize',
            users.data.length,
        );

        this.logger.log(
            PolloAdapter.cxt('getUsers()', this.loggingContext)
                .setContext(this.constructor.name)
                .setSubContext(this.getIAMUsers.name)
                .setIdentifier({
                    usersBatchSize,
                    connectionsId: this.connection.id,
                }),
        );

        for (const usersBatch of chunk(users.data, usersBatchSize)) {
            const usersMap = new Map<string, User>();
            usersBatch.forEach(user => usersMap.set(user.id, user));

            const promises = usersBatch.map(user => processUser(user.id, user.name));

            const batchResults = (await Promise.allSettled(promises))
                .filter(result => result.status === 'fulfilled')
                .map(
                    result =>
                        (
                            result as PromiseFulfilledResult<{
                                id: string;
                                hasMfa: boolean;
                                iamAccess: boolean;
                            }>
                        ).value,
                );

            for (const { id, hasMfa, iamAccess } of batchResults) {
                const user = usersMap.get(id);
                if (isNil(user)) {
                    continue;
                }
                user.hasMfa = hasMfa ?? false;
                user.iamAccess = iamAccess;
            }
        }

        this.logger.log(
            PolloAdapter.cxt('Fetching paginated AWS IAM users', this.loggingContext)
                .setContext(this.constructor.name)
                .setSubContext(this.getIAMUsers.name),
        );
        return this.paginated(users.data, users.token);
    }

    async shouldPullIdentityCenterUsers(): Promise<boolean> {
        try {
            const isFFActive = await this.featureFlagService.evaluateAs(
                {
                    category: FeatureFlagCategory.NONE,
                    name: FeatureFlag.AWS_IDENTITY_CENTER,
                    defaultValue: false,
                },
                this.account,
            );

            this.logger.log(
                PolloAdapter.cxt(
                    `Identity Center feature flag status: ${isFFActive}`,
                    this.loggingContext,
                )
                    .setContext(this.constructor.name)
                    .setSubContext(this.shouldPullIdentityCenterUsers.name),
            );
            return isFFActive;
        } catch (error) {
            this.logger.log(
                PolloAdapter.cxt(
                    'Error getting Identity Center Feature Flag value',
                    this.loggingContext,
                )
                    .setContext(this.constructor.name)
                    .setSubContext(this.shouldPullIdentityCenterUsers.name)
                    .setError(error),
            );
            return false;
        }
    }

    async verifyExclusionTag(
        identities: IIdentityCenterInstance[],
        region: string,
    ): Promise<{ identityCenterId: string; region: string } | null> {
        const client = this.getConfigClient();
        const [identityCenterProps] = identities;
        try {
            const response = await this.sdk.listTagsByResource(
                client,
                identityCenterProps?.InstanceArn,
                region,
            );
            if (Array.isArray(response)) {
                const hasDrataExcludeTag =
                    response.find(tag => tag.Key === ApiExclusion.TAG) ?? null;
                if (!isNil(hasDrataExcludeTag)) {
                    return null;
                }
                return {
                    identityCenterId: identityCenterProps?.IdentityStoreId,
                    region,
                };
            }
        } catch (error) {
            this.logger.log(
                PolloAdapter.cxt('Error getting list of tags by resource', this.loggingContext)
                    .setContext(this.constructor.name)
                    .setSubContext(this.verifyExclusionTag.name),
            );
            return null;
        }
        return null;
    }

    async getVerifiedIdentityCenterAndRegion(
        accumulator: {
            [key: string]: IIdentityCenterInstance[];
        } | null,
    ): Promise<{ identityCenterId: string; region: string } | null> {
        if (isNil(accumulator)) {
            return null;
        }

        const [regionAndIdentityId] = Object.entries(accumulator ?? {});
        const [region, identity] = regionAndIdentityId ?? [];

        if (isNil(region) || !Array.isArray(identity)) {
            return null;
        }
        const verifiedIdentityAndRegion = await this.verifyExclusionTag(identity, region);
        if (!isNil(verifiedIdentityAndRegion)) {
            return verifiedIdentityAndRegion;
        }

        this.logger.log(
            PolloAdapter.cxt(
                'Any Identity Center and region available to pull Identity Center users',
                this.loggingContext,
            )
                .setContext(this.constructor.name)
                .setSubContext(this.getVerifiedIdentityCenterAndRegion.name),
        );
        return null;
    }

    setRegionAndIdentityCenterId(
        props: {
            identityCenterId: string;
            region: string;
        } | null,
    ): void {
        if (!isNil(props)) {
            this.identityCenterId = props?.identityCenterId;
            this.identityCenterRegion = props?.region;
        }
    }

    async setRegionAndIdentityCenterIdWithAcc() {
        const identitiesIdsAccumulator = await this.getIdentityCenterRegionIdByAccountType();

        const verifiedIdentityCenterAndRegion =
            await this.getVerifiedIdentityCenterAndRegion(identitiesIdsAccumulator);

        this.setRegionAndIdentityCenterId(verifiedIdentityCenterAndRegion);
    }

    private async getIdentityCenterUsers(
        maxResults: number,
        nextPageToken: string | null,
    ): Promise<ApiResponse<Paginated<User>>> {
        this.logger.log(
            PolloAdapter.cxt(
                `Verifying Identity Center sync process for aws account id: ${this.accountId}`,
                this.loggingContext,
            )
                .setContext(this.constructor.name)
                .setSubContext(this.getIdentityCenterUsers.name),
        );
        const isIdentityCenterFFActive = await this.shouldPullIdentityCenterUsers();

        const { enabled: isScimEndpointEnabled = false, orgRootAccountId = '' } = get(
            this.connection.getMetadata(),
            'scimConfiguration',
            {} as SCIMConfiguration,
        );

        const isAWSInfraConnection = isEmpty(orgRootAccountId);
        const isOrgUnitsConnection = !isAWSInfraConnection;
        const isOrgUnitsMgmtConnection =
            isOrgUnitsConnection && orgRootAccountId === this.accountId;
        const shouldSkipIdentityCenterSync = !isIdentityCenterFFActive || !isScimEndpointEnabled;

        if (isAWSInfraConnection || isOrgUnitsMgmtConnection) {
            if (shouldSkipIdentityCenterSync) {
                this.logger.log(
                    PolloAdapter.cxt('Skipping Identity Center sync', this.loggingContext)
                        .setContext(this.constructor.name)
                        .setSubContext(this.getIdentityCenterUsers.name)
                        .setIdentifier({
                            FFenabled: isIdentityCenterFFActive,
                            scimEnabled: isScimEndpointEnabled,
                        }),
                );
                return this.paginated([], null);
            }
        } else {
            return this.paginated([], null);
        }

        const client = this.getConfigClient();
        if (isNil(this.identityCenterId) || isNil(this.identityCenterRegion)) {
            await this.setRegionAndIdentityCenterIdWithAcc();
        }
        const users = await this.sdk.listIdentityCenterUsers({
            client,
            maxResults,
            nextPageToken,
            region: this.identityCenterRegion,
            IdentityStoreId: this.identityCenterId,
            loggingContext: this.loggingContext,
            scimProps: this.getScimProps(),
        });
        this.logger.log(
            PolloAdapter.cxt(
                `Retrieved ${users?.data?.length ?? 0} users from AWS Identity Center`,
                this.loggingContext,
            )
                .setContext(this.constructor.name)
                .setSubContext(this.getIdentityCenterUsers.name)
                .setIdentifier({
                    isAWSInfraConnection,
                    isOrgUnitsConnection,
                    isOrgUnitsMgmtConnection,
                }),
        );
        return this.paginated(users?.data, users?.token);
    }

    getMetadataForDisplay(): Map<string, any> {
        const metadata = new Map();
        metadata['key'] = this.metadata['key'];
        metadata['externalId'] = this.metadata['externalId'];
        if (this.metadata && !isEmpty(this.metadata.scimConfiguration)) {
            metadata['scimConfiguration'] = {
                enabled: this.metadata?.scimConfiguration?.enabled ?? false,
                error: this.metadata?.scimConfiguration?.error ?? {},
            };
        }
        return metadata;
    }

    async getRegions(
        includeOptedInRegions = true,
        region = config.get('aws.s3.region'),
    ): Promise<string[]> {
        const regionConnectionSettings = this.connection?.settings?.filter(
            setting => setting.type === ConnectionSettingType.REGION,
        );

        if (isEmpty(regionConnectionSettings)) {
            return this.sdk.listRegions(includeOptedInRegions, region);
        }

        const validRegions = await this.getRegionFromSettings(regionConnectionSettings);
        this.logger.log(
            PolloAdapter.cxt('overriding regions with database values', this.loggingContext)
                .setContext(this.constructor.name)
                .setSubContext(this.getRegions.name)
                .setIdentifier({
                    clientId: this.connection.clientId,
                    regions: validRegions,
                }),
        );
        return validRegions;
    }

    async getRegionFromSettings(regionConnectionSettings: ConnectionSetting[]): Promise<string[]> {
        const validRegionConnectionSettings = regionConnectionSettings.filter(
            region =>
                region.getValue<IRegionSetting>().isActive &&
                region.getValue<IRegionSetting>().isAvailable,
        );
        const clientSpecificConnectionSettings = validRegionConnectionSettings.filter(
            region => region.subConnectionId === this.connection.clientId,
        );

        // If this gets call by subApis the clientId will be override, if you want to specify regions for a particular
        // org-unit sub account, the connectionSettings needs to have the subConnectionId setup.
        if (!isEmpty(clientSpecificConnectionSettings)) {
            this.logger.log(
                PolloAdapter.cxt(
                    'getting regions from connection settings with specific clientId',
                    this.loggingContext,
                )
                    .setContext(this.constructor.name)
                    .setSubContext(this.getRegionFromSettings.name)
                    .setIdentifier({
                        clientId: this.connection.clientId,
                        regionConnectionSettings: clientSpecificConnectionSettings,
                    }),
            );
            return clientSpecificConnectionSettings.map(
                region => region.getValue<IRegionSetting>().name,
            );
        }
        // If no settings for an specific client Id is found, use the general connection settings
        this.logger.log(
            PolloAdapter.cxt('getting regions from connection settings', this.loggingContext)
                .setContext(this.constructor.name)
                .setSubContext(this.getRegionFromSettings.name)
                .setIdentifier({
                    clientId: this.connection.clientId,
                    regionConnectionSettings: validRegionConnectionSettings,
                }),
        );
        return validRegionConnectionSettings.map(region => region.getValue<IRegionSetting>().name);
    }

    protected async setDefaultRegion(): Promise<void> {
        try {
            const regionConnectionSettings = this.connection.settings.filter(
                setting => setting.type === ConnectionSettingType.REGION,
            );
            const regions = await this.getRegionFromSettings(regionConnectionSettings);

            const regionToUse = !isEmpty(regions) ? regions[0] : config.get('aws.region.default');

            this.awsClientBuilder.setDefaultRegion(regionToUse);
            if (!isNil(this.parentOrgClientBuilder)) {
                this.parentOrgClientBuilder.setDefaultRegion(regionToUse);
            }

            this.logger.log(
                PolloAdapter.cxt('setting default region', this.loggingContext)
                    .setContext(this.constructor.name)
                    .setSubContext(this.setDefaultRegion.name)
                    .setIdentifier({
                        connectionId: this.connection.id,
                        clientId: this.connection.clientId,
                        region: regionToUse,
                        fromSettings: !isEmpty(regions),
                    }),
            );
        } catch (e) {
            this.logger.warn(
                PolloAdapter.cxt(
                    'Error getting regions from settings, setting default',
                    this.loggingContext,
                )
                    .setContext(this.constructor.name)
                    .setSubContext(this.setDefaultRegion.name)
                    .setIdentifier({
                        connectionId: this.connection.id,
                        clientId: this.connection.clientId,
                        default: config.get('aws.region.default'),
                    })
                    .setError(e),
            );
        }
    }

    async getWAFResources(
        region: string,
        _pagination: TokenPagination,
    ): Promise<ApiResponse<TokenPaginationResponse<Waffable>>> {
        let page = [];

        const targets = [
            'getApplicationLoadBalancersV2',
            'getPublicApiGatewaysV1',
            'getGraphGateways',
        ];

        for (const target of targets) {
            /**
             * https://drata.atlassian.net/browse/ENG-32512
             */
            this.logger.log(
                PolloAdapter.cxt('getting WAF resources', this.loggingContext)
                    .setContext(this.constructor.name)
                    .setSubContext(this.getWAFResources.name)
                    .setIdentifier({
                        target,
                        region,
                    }),
            );

            await forEachTokenPage(
                (nextPageToken: string) => {
                    return this[target](region, TokenPagination.page(nextPageToken));
                },
                async (data: Waffable[]) => {
                    /**
                     * https://drata.atlassian.net/browse/ENG-32512
                     */
                    this.logger.log(
                        PolloAdapter.cxt('getting WAF resources:pipe', this.loggingContext)
                            .setContext(this.constructor.name)
                            .setSubContext(this.getWAFResources.name)
                            .setIdentifier({
                                data,
                                region,
                            }),
                    );
                    page = page.concat(data);
                },
            );
        }

        return this.tokenPaginatedResponse(tokenize(page, null), null);
    }

    @ResourceScoping()
    async getGlobalWAFResources(
        _pagination: TokenPagination,
    ): Promise<ApiResponse<TokenPaginationResponse<ApiData>>> {
        let page = [];

        const targets = ['getCloudFrontDistributions'];

        for (const target of targets) {
            await forEachTokenPage(
                (nextPageToken: string) => {
                    return this[target](TokenPagination.page(nextPageToken));
                },
                async (data: ApiData[]) => {
                    page = page.concat(data);
                },
            );
        }

        return this.tokenPaginatedResponse(tokenize(page, null), null);
    }

    @ResourceScoping()
    async getResourcesByWAF(
        region: string,
        rule: Rule,
    ): Promise<ApiResponse<TokenPaginationResponse<Waffable>>> {
        let page = [];

        /**
         * We get the global CloudFront WAF acl for free - so do not query.
         */
        if (!compareLowerCase(region, AwsConstants.GLOBAL_REGION)) {
            page = page.concat(await this.getAlbResourcesByWAF(region, rule));
            page = page.concat(await this.getApiResourcesByWAF(region, rule));
        }

        return this.irreduciblePaginatedResponse(tokenize(page, null), null);
    }

    @Retry({ maxRetries: 8, retryWait: 30000 })
    private getAlbResourcesByWAF(region: string, rule: Rule): Promise<Waffable[]> {
        return this.sdk.listAlbResourcesByAcl(region, rule.dimension);
    }

    @Retry({ maxRetries: 8, retryWait: 30000 })
    private async getApiResourcesByWAF(region: string, rule: Rule): Promise<Waffable[]> {
        const resources = await this.sdk.listApiResourcesByAcl(region, rule.dimension);

        /**
         * The rest api resources are matched on a stage - so pull the id out and place it in the
         * resources list so we can match on the dimension as just the id as well as the whole arn
         */
        return resources.concat(
            resources.map((arn: { id: string }) => {
                const match = /restapis\/(.*?)\/stages/g.exec(arn.id);

                let capture = null;

                if (!isEmpty(match) && match.length === 2) {
                    capture = match[1];
                }

                return {
                    id: capture,
                    name: capture,
                    hasWaf: false,
                    raw: capture,
                };
            }),
        );
    }

    @ResourceScoping()
    @Retry()
    async getSSHSecurityGroups(
        region: string,
        pagination: TokenPagination,
    ): Promise<ApiResponse<TokenPaginationResponse<SecurityGroup>>> {
        const filters = [
            {
                name: 'ip-permission.from-port',
                values: [Api.SSH],
            },
            {
                name: 'ip-permission.to-port',
                values: [Api.SSH],
            },
        ];

        if (!isEmpty(this.getResourceTags())) {
            filters.push({
                name: 'tag-key',
                values: uniq(this.getResourceTags().map(tag => tag.key)),
            });
        }

        const securityGroups = await this.sdk.listSecurityGroups(
            region,
            pagination.getMaxResults(),
            pagination.getNextPageToken(),
            filters,
        );

        return this.tokenPaginatedResponse(securityGroups);
    }

    @ResourceScoping()
    @Retry()
    async getOpenProtocolSecurityGroups(
        region: string,
        pagination: TokenPagination,
    ): Promise<ApiResponse<TokenPaginationResponse<SecurityGroup>>> {
        const filters = [
            {
                name: 'ip-permission.protocol',
                values: [Api.ALL_PROTOCOLS],
            },
        ];

        if (!isEmpty(this.getResourceTags())) {
            filters.push({
                name: 'tag-key',
                values: uniq(this.getResourceTags().map(tag => tag.key)),
            });
        }

        const securityGroups = await this.sdk.listSecurityGroups(
            region,
            pagination.getMaxResults(),
            pagination.getNextPageToken(),
            filters,
        );

        return this.tokenPaginatedResponse(securityGroups);
    }

    @Retry()
    async getNetworkInterfaces(
        region: string,
        maxResults: number,
        nextPageToken: string,
        filters: FilterType[],
    ): Promise<ApiResponse<TokenPaginationResponse<NetworkInterface>>> {
        const interfaces = await this.sdk.listNetworkInterfaces(
            region,
            maxResults,
            nextPageToken,
            filters,
        );

        return this.tokenPaginatedResponse(interfaces);
    }

    @Retry()
    @ResourceScoping()
    async getDatabases(
        region: string,
        pagination: TokenPagination,
    ): Promise<ApiResponse<TokenPaginationResponse<Database>>> {
        const databases = await this.sdk.listDatabases(
            region,
            pagination.getMaxResults(),
            pagination.getNextPageToken(),
        );

        return this.tokenPaginatedResponse(databases);
    }

    @ResourceScoping()
    @Retry()
    async getDatabasesClusters(
        region: string,
        pagination: TokenPagination,
    ): Promise<ApiResponse<TokenPaginationResponse<DatabaseCluster>>> {
        const clusters = await this.sdk.listDatabaseClusters(
            region,
            pagination.getMaxResults(),
            pagination.getNextPageToken(),
        );

        return this.tokenPaginatedResponse(clusters);
    }

    @ResourceScoping()
    @Retry({ maxRetries: 8, retryWait: 30000 })
    async getCachesV1(
        region: string,
        pagination: TokenPagination,
    ): Promise<ApiResponse<TokenPaginationResponse<Cache>>> {
        const caches = await this.sdk.listElasticacheClusters(
            region,
            pagination.getMaxResults(),
            pagination.getNextPageToken(),
        );

        if (!isEmpty(caches)) {
            for (const cache of caches.data.page ?? []) {
                const tagsResponse = await this.getElasticacheTags(region, cache);

                if (isNil(tagsResponse)) {
                    this.logger.warn(
                        PolloAdapter.cxt(
                            'Unable to retrieve tags for resource',
                            this.loggingContext,
                        )
                            .setContext(this.constructor.name)
                            .setSubContext(this.getCachesV1.name)
                            .setIdentifier(cache),
                    );
                }
                cache.tags = this.sdk.tagList(cache.dimension, get(tagsResponse, 'TagList', null));
            }
        }

        return this.tokenPaginatedResponse(caches);
    }

    @Retry()
    async getElasticacheTags(
        region: string,
        cache: Cache,
    ): Promise<ListTagsForResourceCommandOutputElasticache | null> {
        let tagsResponse = null;

        try {
            tagsResponse = await this.sdk.getElasticacheTags(region, cache);
        } catch (error) {
            this.logger.warn(
                PolloAdapter.cxt(`Error retrieving tags for resource`, this.loggingContext)
                    .setContext(this.constructor.name)
                    .setSubContext(this.getElasticacheTags.name)
                    .setIdentifier({
                        cache,
                        region,
                    })
                    .setError(error),
            );
        }

        return tagsResponse;
    }

    @ResourceScoping()
    @Retry({ maxRetries: 8, retryWait: 30000 })
    async getCachesV2(
        region: string,
        pagination: TokenPagination,
    ): Promise<ApiResponse<TokenPaginationResponse<Cache>>> {
        const caches = await this.sdk.listElasticacheGroups(
            region,
            pagination.getMaxResults(),
            pagination.getNextPageToken(),
        );

        if (!isEmpty(caches)) {
            for (const cache of caches.data.page ?? []) {
                const tagsResponse = await this.getElasticacheTags(region, cache);

                if (isNil(tagsResponse)) {
                    this.logger.warn(
                        PolloAdapter.cxt(
                            'Unable to retrieve tags for resource',
                            this.loggingContext,
                        )
                            .setContext(this.constructor.name)
                            .setSubContext(this.getCachesV2.name)
                            .setIdentifier(cache),
                    );
                }

                cache.tags = this.sdk.tagList(cache.dimension, get(tagsResponse, 'TagList', null));
            }
        }

        return this.tokenPaginatedResponse(caches);
    }

    @ResourceScoping()
    @Retry({ maxRetries: 8, retryWait: 30000 })
    async getIndexers(region: string): Promise<ApiResponse<Indexer[]>> {
        const indexers = await this.sdk.listESClusters(region);

        for (const indexer of indexers) {
            indexer.tags = await this.getTagsFromESDomain(region, indexer.dimension);
        }

        return this.arrayResponse(indexers);
    }

    @Retry({ maxRetries: 8, retryWait: 30000 })
    async getTagsFromESDomain(region: string, arn: string): Promise<TagCollection> {
        const tagsResponse = await this.sdk.getESClusterTags(region, arn);
        return this.sdk.tagList(arn, get(tagsResponse, 'TagList', null));
    }

    @ResourceScoping()
    @Retry({ maxRetries: 8, retryWait: 30000 })
    async getQueues(
        region: string,
        pagination: TokenPagination,
    ): Promise<ApiResponse<TokenPaginationResponse<Queue>>> {
        const queues = await this.sdk.listSqsQueues(
            region,
            pagination.getMaxResults(),
            pagination.getNextPageToken(),
        );

        for (const queue of queues.data.page ?? []) {
            const tagsResponse = await this.getSqsQueueTags(region, queue.description);

            queue.tags = Mapper.tagItems(queue.description, get(tagsResponse, 'Tags', null));
        }

        return this.tokenPaginatedResponse(queues);
    }

    async getSqsQueueTags(region: string, url: string): Promise<ListQueueTagsCommandOutput> {
        return this.sdk.getSqsQueueTags(region, url);
    }

    getBucketAccounts(
        _pagination: TokenPagination,
    ): Promise<ApiResponse<TokenPaginationResponse<BucketAccount>>> {
        return Promise.resolve({
            data: tokenize([new BucketAccount()], null),
            raw: null,
            request: null,
            source: this.getDataSource(),
            isNotImplemented: false,
        });
    }

    @ResourceScoping()
    async getBuckets(
        _account: BucketAccount,
        pagination: TokenPagination,
    ): Promise<ApiResponse<TokenPaginationResponse<Bucket>>> {
        /**
         * This is A TEMPORARY fix until we develop region discovery
         *
         * https://drata.atlassian.net/browse/ENG-389
         * https://drata.atlassian.net/browse/ENG-5086
         * https://drata.atlassian.net/browse/ENG-5589
         */
        await this.setDefaultRegion();

        const response = await this.sdk.buckets(pagination.getNextPageToken());
        const metadata = this.connection.getMetadata();
        const accountId = getAccountIdFromArn(metadata.key);
        const buckets = Mapper.buckets(response, accountId, this.getResourceAccountName());

        const bucketsPage = buckets.data.page ?? [];
        for (const bucket of bucketsPage) {
            let tags = null;
            try {
                const bucketLocationResult = await this.getBucketRegion(
                    bucket.name,
                    bucket.accountId,
                );
                // For buckets in us-east-1, AWS returns an empty string or undefined for LocationConstraint
                bucket.region = isEmpty(bucketLocationResult)
                    ? config.get('aws.region.default')
                    : bucketLocationResult;
            } catch (error) {
                // Minimize error object size by not including the full bucket object
                const bucketInfo = {
                    name: bucket?.name,
                    accountId: bucket?.accountId,
                    region: bucket?.region,
                };

                this.logErrorLevelBasedOnClassification(
                    error,
                    PolloAdapter.cxt('GetBuckets: getBucketRegion failed', this.loggingContext)
                        .setContext(this.constructor.name)
                        .setSubContext(this.getBuckets.name)
                        .setIdentifier({
                            connectionId: this.connection.id,
                            bucket: bucketInfo,
                        })
                        .setError(error),
                );
            }

            /**
             * https://drata.atlassian.net/browse/ENG-15052
             *
             * We should default bucket region to an empty string in
             * the case the bucket location returns `EU` to prevent
             * tests from going into Error.
             */
            if (bucket.region === AwsConstants.EU_REGION) {
                bucket.region = '';
            }

            /**
             * https://app.shortcut.com/drata/story/18986/standby-105-five-clients-the-specified-bucket-does-not-exist
             */
            try {
                /**
                 * We are not able to get the buckets in the initial call
                 */
                tags = await this.getBucketTags(bucket.name, bucket.region, bucket.accountId);
            } catch (error) {
                const connection = {
                    id: this.connection.id,
                    productIds: this.connection.products?.map(p => p.id) || [],
                };

                const bucketInfo = {
                    name: bucket.name,
                    accountId: bucket.accountId,
                    region: bucket.region,
                };

                const polloMessage = PolloAdapter.cxt(
                    'GetBuckets: getBucketTags failed',
                    this.loggingContext,
                )
                    .setContext(this.constructor.name)
                    .setSubContext(this.getBuckets.name)
                    .setIdentifier({
                        connection,
                        bucket: bucketInfo,
                    })
                    .setError(error);

                if (
                    iIncludes([AwsConstants.ACCESS_DENIED, AwsConstants.NO_TAG_SET], error.message)
                ) {
                    this.logger.warn(polloMessage);
                } else {
                    this.logErrorLevelBasedOnClassification(error, polloMessage);
                }

                tags = { TagSet: [] };
            }

            if (!isEmpty(tags)) {
                /**
                 * Get the tags and add tags by reference
                 */
                Mapper.tagBucket(bucket, tags);
            }
        }

        buckets.data.page = buckets.data.page.filter(bucket => {
            const tagKeys = this.extractAndSanitizeTagKeys(bucket.tags, 'tags', 'key');
            return !tagKeys.includes(ApiExclusion.TAG.toLowerCase());
        });

        return this.tokenPaginatedResponse(buckets, response as unknown as string);
    }

    @Retry()
    async getBucketTags(
        name: string,
        region: string,
        owner: string,
    ): Promise<GetBucketTaggingCommandOutput> {
        return this.sdk.tags(name, region, owner);
    }

    @Retry()
    async getBucketRegion(name: string, owner: string): Promise<BucketLocationConstraint> {
        return this.sdk.getBucketLocation(name, owner);
    }

    @Retry()
    async getBucketEncryption(bucket: Bucket): Promise<ApiResponse<Bucket>> {
        /**
         * This is A TEMPORARY fix until we develop region discovery
         *
         * https://drata.atlassian.net/browse/ENG-3894
         * https://drata.atlassian.net/browse/ENG-5086
         * https://drata.atlassian.net/browse/ENG-5589
         */
        await this.setDefaultRegion();

        let response = null;

        try {
            response = await this.sdk.encryptions(bucket.name, bucket.accountId, bucket.region);
        } catch (error) {
            bucket.error = serialize(error);
        }

        return this.data(Mapper.bucketEncryption(bucket, response), response);
    }

    async getBucketPublicAccess(bucket: Bucket): Promise<ApiResponse<Bucket>> {
        /**
         * This is A TEMPORARY fix until we develop region discovery
         *
         * https://drata.atlassian.net/browse/ENG-3894
         * https://drata.atlassian.net/browse/ENG-5086
         * https://drata.atlassian.net/browse/ENG-5589
         */
        await this.setDefaultRegion();

        let response = null;

        try {
            response = await this.sdk.publicAccess(bucket.name, bucket.accountId, bucket.region);
        } catch (error) {
            bucket.error = serialize(error);
        }

        return this.data(Mapper.bucketPublicAccess(bucket, response), response);
    }

    async getBucketVersioning(bucket: Bucket): Promise<ApiResponse<Bucket>> {
        /**
         * This is A TEMPORARY fix until we develop region discovery
         *
         * https://drata.atlassian.net/browse/ENG-3894
         * https://drata.atlassian.net/browse/ENG-5086
         * https://drata.atlassian.net/browse/ENG-5589
         */
        await this.setDefaultRegion();

        let response = null;

        try {
            response = await this.sdk.versioning(bucket.name, bucket.accountId, bucket.region);
        } catch (error) {
            bucket.error = serialize(error);
        }

        const target = {
            id: bucket.name,
            name: bucket.name,
            resourceType: AwsConstants.S3,
            encryptions: [],
            hasVersioning: false,
            accountId: bucket.accountId,
            accountName: bucket.accountName,
            region: bucket.region,
            raw: { bucket, response },
        };

        if (!isEmpty(response)) {
            if (has(response, 'Status')) {
                target.hasVersioning = compareLowerCase(response.Status, AwsConstants.ENABLED);
            }
        }

        return this.data(target as never, response);
    }

    @Retry()
    async getBucketLifecycleConfiguration(
        bucketName: string,
    ): Promise<IBucketLifecycleConfiguration> {
        try {
            return await this.sdk.getBucketLifecycleConfiguration(bucketName);
        } catch (error) {
            const errorIdentifiers = [
                AwsConstants.NO_SUCH_LIFE_CYCLE_CONFIGURATION,
                AwsConstants.ILLEGAL_LOCATION_CONSTRAIN_EXCEPTION,
                AwsConstants.PERMANENT_REDIRECT,
            ];

            if (errorIdentifiers.includes(error.code) || errorIdentifiers.includes(error.name)) {
                return new AwsBucketLifeCycleConfiguration([]);
            }

            this.logger.error(
                PolloAdapter.cxt(
                    `AWS getBucketLifecycleConfiguration failed: ${error.message}`,
                    this.loggingContext,
                )
                    .setContext(this.constructor.name)
                    .setSubContext(this.getBucketLifecycleConfiguration.name)
                    .setError(error)
                    .setIdentifier({
                        bucketName,
                    }),
            );
            throw error;
        }
    }

    @ResourceScoping()
    @Retry({ maxRetries: 8, retryWait: 30000 })
    async getContainers(
        region: string,
        pagination: TokenPagination,
    ): Promise<ApiResponse<TokenPaginationResponse<Container>>> {
        const page = [];
        let nextPageToken = null;

        const response = await this.sdk.getContainers(
            region,
            pagination.getMaxResults(),
            pagination.getNextPageToken(),
        );

        if (!isNil(response)) {
            const clusters = get(response, 'clusters', []);

            this.logger.log(
                PolloAdapter.cxt('List of Clusters', this.loggingContext)
                    .setContext(this.constructor.name)
                    .setSubContext(this.getContainers.name)
                    .setResult({ clusters, region }),
            );

            for (const name of clusters) {
                const cluster = await this.sdk.describeEksCluster(region, name);

                this.logger.log(
                    PolloAdapter.cxt('Got details of cluster', this.loggingContext)
                        .setContext(this.constructor.name)
                        .setSubContext(this.getContainers.name)
                        .setResult({ cluster, region }),
                );

                if (!isNil(cluster)) {
                    const container = Mapper.container(cluster);

                    await forEachTokenPage(
                        (anotherPageToken: string) => {
                            return this.getContainerNodeGroups(
                                region,
                                TokenPagination.MAX_RESULTS,
                                anotherPageToken,
                                container.name,
                            );
                        },
                        async (nodeGroups: string[]) => {
                            this.logger.log(
                                PolloAdapter.cxt('List of node groups', this.loggingContext)
                                    .setContext(this.constructor.name)
                                    .setSubContext(this.getContainers.name)
                                    .setResult({ nodeGroups, region }),
                            );
                            for (const nodeGroupName of nodeGroups) {
                                /**
                                 * Get the node group information
                                 */
                                const nodeGroup: DescribeNodegroupCommandOutput =
                                    await this.getContainerNodeGroup(
                                        region,
                                        container.name,
                                        nodeGroupName,
                                    );

                                this.logger.log(
                                    PolloAdapter.cxt(
                                        'Got node group information',
                                        this.loggingContext,
                                    )
                                        .setContext(this.constructor.name)
                                        .setSubContext(this.getContainers.name)
                                        .setResult({ nodeGroup, region }),
                                );
                                if (!isNil(nodeGroup)) {
                                    const target = nodeGroup.nodegroup;
                                    if (target?.nodegroupArn && target?.nodegroupName) {
                                        /**
                                         * Create a new node instance
                                         */
                                        const groups = get(
                                            target,
                                            'resources.autoScalingGroups',
                                            [],
                                        );

                                        const node = new NodeGroup();
                                        node.id = target.nodegroupArn;
                                        node.name = target.nodegroupName;
                                        node.dimension = target.nodegroupName;
                                        node.raw = target;
                                        node.groups = groups.map((group: any) => {
                                            return group.name;
                                        });

                                        container.nodes.push(node);
                                    }
                                }
                            }
                        },
                    );

                    page.push(container);
                }

                nextPageToken = response.nextToken;
            }
        }

        return this.tokenPaginatedResponse(tokenize(page, nextPageToken), null);
    }

    @Retry()
    async getContainerNodeGroups(
        region: string,
        maxResults: number,
        nextPageToken: string,
        clusterName: string,
    ): Promise<TokenPaginationResponse<string[]>> {
        return this.sdk.getContainerNodeGroups(region, maxResults, nextPageToken, clusterName);
    }

    @Retry()
    async getContainerNodeGroup(
        region: string,
        clusterName: string,
        nodegroupName: string,
    ): Promise<DescribeNodegroupCommandOutput> {
        return this.sdk.getContainerNodeGroup(region, clusterName, nodegroupName);
    }

    @ResourceScoping()
    @Retry()
    async getClusters(
        region: string,
        pagination: TokenPagination,
    ): Promise<ApiResponse<TokenPaginationResponse<Cluster>>> {
        const response = await this.sdk.getClusters(
            region,
            pagination.getMaxResults(),
            pagination.getNextPageToken(),
        );

        this.logger.log(
            PolloAdapter.cxt('Raw getClusters response', this.loggingContext)
                .setContext(this.constructor.name)
                .setSubContext(this.getClusters.name)
                .setResult({ response, region }),
        );

        const clusters = Mapper.cluster(response, 'clusterArns');

        let tags = null;

        if (!isEmpty(clusters.data.page)) {
            tags = await this.describeClusters(
                region,
                clusters.data.page.map(cluster => {
                    return cluster.id;
                }),
            );
        }

        return this.tokenPaginatedResponse(
            Mapper.tagClusters(clusters, tags),
            response as unknown as string,
        );
    }

    @Retry()
    async describeClusters(
        region: string,
        clusterNames: string[],
    ): Promise<DescribeClustersCommandOutputECS> {
        return this.sdk.describeClusters(region, clusterNames);
    }

    @ResourceScoping()
    @Retry({ maxRetries: 8, retryWait: 30000 })
    async getClusterServices(
        region: string,
        cluster: Cluster,
        pagination: TokenPagination,
    ): Promise<ApiResponse<TokenPaginationResponse<Cluster>>> {
        /**
         * We are required to check the tags for each service - we are only allowed to get 10 tag
         * calls in a single API request - so update the pagination to only request 10 services.
         *
         * https://docs.aws.amazon.com/AmazonECS/latest/APIReference/API_DescribeServices.html
         */
        pagination.setMaxResults(10);

        const response = await this.sdk.getClusterServices(
            region,
            cluster.id,
            pagination.getMaxResults(),
            pagination.getNextPageToken(),
        );

        const services = Mapper.clusterServices(response);

        let tags = null;

        this.logger.log(
            PolloAdapter.cxt('Got cluster services', this.loggingContext)
                .setContext(this.constructor.name)
                .setSubContext(this.getClusterServices.name)
                .setResult({ services, region }),
        );

        if (!isEmpty(services.data.page)) {
            tags = await this.describeClusterServices(
                region,
                cluster.id,
                services.data.page.map(service => {
                    return service.id;
                }),
            );

            this.logger.log(
                PolloAdapter.cxt('Got tags from response', this.loggingContext)
                    .setContext(this.constructor.name)
                    .setSubContext(this.getClusterServices.name)
                    .setResult({ tags, region }),
            );
        }

        return this.tokenPaginatedResponse(
            Mapper.tagClusterServices(services, tags),
            response as unknown as string,
        );
    }

    @Retry()
    async describeClusterServices(
        region: string,
        clusterName: string,
        clusterServices: string[],
    ): Promise<DescribeServicesCommandOutputECS> {
        return this.sdk.describeClusterServices(region, clusterName, clusterServices);
    }

    @ResourceScoping()
    @Retry({ maxRetries: 8, retryWait: 30000 })
    async getInstances(
        region: string,
        pagination: TokenPagination,
    ): Promise<ApiResponse<TokenPaginationResponse<Instance>>> {
        const response = await this.sdk.getEc2Instances(
            region,
            pagination.getMaxResults(),
            pagination.getNextPageToken(),
        );

        this.logger.log(
            PolloAdapter.cxt('Getting instances raw response', this.loggingContext)
                .setContext(this.constructor.name)
                .setSubContext(this.getInstances.name)
                .setResult({ response, region }),
        );

        return this.tokenPaginatedResponse(
            Mapper.instances(region, response),
            response as unknown as string,
        );
    }

    @Retry({ maxRetries: 8, retryWait: 30000 })
    async getMetricAlarms(
        region: string,
        type: Metric,
        namespace: MetricNamespace,
        instanceId: string,
        subInstanceId: string,
        engine: string,
        instance?: ApiData,
    ): Promise<ApiResponse<Alarm[]>> {
        let data: Alarm[] = [];
        try {
            const response = await this.sdk.listMetricAlarms(
                region,
                type,
                namespace,
                instanceId,
                subInstanceId,
                engine,
                has(instance, 'instanceClass') ? instance['instanceClass'] : null,
            );

            if (!isEmpty(response)) {
                data = get(response as any, 'MetricAlarms', []).map((alarm: any) => {
                    return {
                        apiDataSource: ApiDataSource.AWS,
                        id: alarm.AlarmName,
                        name: alarm.AlarmName,
                        description: alarm.AlarmArn,
                        dimension: alarm.AlarmName,
                        actions: alarm.AlarmActions,
                        hasTopic: true,
                        hasSubscription: false,
                        region,
                        raw: alarm,
                    };
                });

                // Force credential refresh before processing alarm subscriptions
                // This helps with long-running tasks that process many alarms
                await this.forceRefreshCredentials();

                for (const alarm of data) {
                    if (!isEmpty(alarm.actions)) {
                        alarm.hasSubscription = true;
                        for (const action of alarm.actions ?? []) {
                            if (this.isQueue(action)) {
                                await this.verifyAlarmSubscriptions(alarm, action, region);
                            }
                        }
                    }
                }
            }

            return this.array(data, String(response));
        } catch (error) {
            return Promise.reject(error);
        }
    }

    @ResourceScoping()
    async getWebApplicationFirewallAclsV2(
        region: string,
        pagination: TokenPagination,
    ): Promise<ApiResponse<TokenPaginationResponse<Rule>>> {
        const response = await this.aclsV2(
            region,
            pagination.getMaxResults(),
            pagination.getNextPageToken(),
        );

        let page = null;
        let nextPageToken = null;

        if (!isEmpty(response)) {
            page = [];

            for (const acl of get(response, 'WebACLs', [])) {
                let tags = new TagCollection();

                if (!isEmpty(this.getResourceTags())) {
                    tags = await this.getAclsV2Tags(region, acl.Id, acl.ARN);
                }

                page.push({
                    id: acl.Id,
                    name: acl.Name,
                    description: acl.Description,
                    dimension: acl.ARN,
                    raw: acl,
                    tags,
                });
            }

            nextPageToken = response.NextMarker;
        }

        return this.tokenPaginatedResponse(
            tokenize(page, nextPageToken),
            response as unknown as string,
        );
    }

    @Retry()
    private async getAclsV2Tags(region: string, id: string, arn: string): Promise<TagCollection> {
        let nextPageToken: string;

        const tag = new TagCollection();
        tag.dimension = id;

        do {
            const response = await this.sdk.getTagsForV2Acl(region, arn);

            if (!isNil(response)) {
                const tagList = get(response, 'TagInfoForResource.TagList', []);

                for (const t of tagList) {
                    tag.tags.push({ key: t.Key, value: t.Value });
                }

                nextPageToken = response.NextMarker;
            }
        } while (!isEmpty(nextPageToken));

        return tag;
    }

    async getStorageFirewallAcls(
        region: string,
        pagination: TokenPagination,
    ): Promise<ApiResponse<TokenPaginationResponse<Ids>>> {
        let page = [];
        let nextPageToken = null;

        const response = await this.sdk.listDetectors(
            region,
            pagination.getMaxResults(),
            pagination.getNextPageToken(),
        );

        if (!isEmpty(response)) {
            for (const detectorId of response.DetectorIds ?? []) {
                try {
                    const detector = await this.sdk.getDetector(region, detectorId);

                    this.logger.log(
                        PolloAdapter.cxt('Guard Duty Detector Response', this.loggingContext)
                            .setResult({
                                detector,
                            })
                            .setContext(this.constructor.name)
                            .setSubContext(this.getStorageFirewallAcls.name),
                    );

                    if (!isNil(detector)) {
                        let storageMonitoringIsEnabled = false;

                        const isEnabled = compareLowerCase(detector.Status, AwsConstants.ENABLED);

                        /**
                         * https://drata.atlassian.net/browse/ENG-32512
                         */
                        this.logger.log(
                            PolloAdapter.cxt(
                                `getting storageFirewallAcls:detector`,
                                this.loggingContext,
                            )
                                .setContext(this.constructor.name)
                                .setSubContext(this.getStorageFirewallAcls.name)
                                .setIdentifier({
                                    detector,
                                    detectorId,
                                    region,
                                }),
                        );

                        const dataSources = get(detector, 'DataSources', []);

                        if (!isEmpty(dataSources)) {
                            const s3Logs = get(dataSources, 'S3Logs', {
                                Status: AwsConstants.DISABLED,
                            });

                            storageMonitoringIsEnabled = !compareLowerCase(
                                s3Logs.Status,
                                AwsConstants.DISABLED,
                            );
                        }

                        const rules = await this.getGuardDutyRules(region);

                        page.push({
                            id: detectorId,
                            name: detectorId,
                            description: detectorId,
                            dimension: detectorId,
                            isEnabled,
                            storageMonitoringIsEnabled,
                            hasEvent: rules.length > 0,
                            region,
                            raw: { detector, rules },
                            tags: Mapper.tagMap(detectorId, detector.Tags),
                        });
                    }
                } catch (error) {
                    /**
                     * https://drata.atlassian.net/browse/ENG-32512
                     */
                    this.logErrorLevelBasedOnClassification(
                        error,
                        PolloAdapter.cxt('getting storageFirewallAcls failed', this.loggingContext)
                            .setContext(this.constructor.name)
                            .setSubContext(this.getStorageFirewallAcls.name)
                            .setIdentifier({
                                region,
                                detectorId,
                            })
                            .setError(error),
                    );

                    /**
                     * after intercepting the error we let it bubble up
                     */
                    throw error;
                }
            }

            nextPageToken = response.NextToken;
        }

        if (isEmpty(response.DetectorIds)) {
            page = await this.getMalwareProtectionPlans(region);
        }

        return this.tokenPaginatedResponse(tokenize(page, nextPageToken));
    }

    private async getMalwareProtectionPlans(region: string) {
        return AsyncGenChain.unfoldTokens(async token => {
            const response = await this.sdk.listMalwareProtectionPlans(region, token);

            this.logger.log(
                PolloAdapter.cxt(
                    'List Guard Duty Malware Protection Plan Response',
                    this.loggingContext,
                )
                    .setResult({
                        response,
                    })
                    .setContext(this.constructor.name)
                    .setSubContext(this.getMalwareProtectionPlans.name),
            );

            return {
                elements: response.MalwareProtectionPlans ?? [],
                nextToken: response.NextToken,
            };
        })
            .map(async plan => {
                const malwareProtectionPlan = await this.sdk.getMalwareProtectionPlan(
                    region,
                    plan.MalwareProtectionPlanId ?? '',
                );

                this.logger.log(
                    PolloAdapter.cxt(
                        'Get Guard Duty Malware Protection Plan Response',
                        this.loggingContext,
                    )
                        .setResult({
                            malwareProtectionPlan,
                        })
                        .setContext(this.constructor.name)
                        .setSubContext(this.getMalwareProtectionPlans.name),
                );

                const isEnabled = compareLowerCase(
                    malwareProtectionPlan.Status,
                    AwsConstants.ACTIVE,
                );

                const S3Buket = malwareProtectionPlan?.ProtectedResource?.S3Bucket ?? {};

                const storageMonitoringIsEnabled = !isEmpty(S3Buket);

                const rules = await this.getGuardDutyRules(region);

                return {
                    id: malwareProtectionPlan.Arn ?? '',
                    name: malwareProtectionPlan.Arn ?? '',
                    description: malwareProtectionPlan.Arn ?? '',
                    dimension: malwareProtectionPlan.Arn ?? '',
                    isEnabled,
                    storageMonitoringIsEnabled,
                    hasEvent: rules.length > 0,
                    region,
                    raw: { malwareProtectionPlan, rules },
                };
            })
            .toArray();
    }

    private async getGuardDutyRules(region: string): Promise<any[]> {
        let page = [];
        let response = null;

        let nextPageToken = null;

        do {
            response = await this.sdk.listRules(region, nextPageToken);

            /**
             * TO DO: remove this log once we're done with
             * https://drata.atlassian.net/browse/ENG-14555
             */

            this.logger.log(
                PolloAdapter.cxt('Guard Duty Rules Response', this.loggingContext)
                    .setContext(this.constructor.name)
                    .setSubContext(this.getGuardDutyRules.name)
                    .setResult({
                        response,
                        region,
                    }),
            );

            if (!isNil(response)) {
                const rules = get(response, 'Rules', []);

                for (const rule of rules) {
                    let tags = new TagCollection();

                    if (!isEmpty(this.getResourceTags())) {
                        tags = await this.sdk.listRuleTags(region, rule.Name, rule.Arn);
                    }

                    /**
                     * https://app.shortcut.com/drata/story/22910/standby-105-parsing-error-on-aws-guard-duty
                     */
                    if (isJSON(rule.EventPattern)) {
                        const pattern = JSON.parse(rule.EventPattern);

                        const sources = get(pattern, 'source', []);
                        const detailTypes = get(pattern, 'detail-type', []);

                        if (
                            compareLowerCase(rule.State, AwsConstants.ENABLED) &&
                            sources.includes('aws.guardduty') &&
                            detailTypes.includes('GuardDuty Finding')
                        ) {
                            page.push({
                                id: rule.Arn,
                                name: rule.Name,
                                raw: rule,
                                tags,
                            });
                        }
                    }
                }

                nextPageToken = response.NextToken;
            }
        } while (!isNil(nextPageToken));

        if (!isEmpty(this.getResourceTags())) {
            page = this.reduceTags(page, this.getResourceTags());
        }

        return page;
    }

    async hasLoadBalancerResources(region: string): Promise<boolean> {
        return Promise.resolve(
            (await this.getInstances(region, new TokenPagination(10, null))).data.data.page.length >
                0,
        );
    }

    @ResourceScoping()
    async getLoadBalancersV1(
        region: string,
        pagination: TokenPagination,
    ): Promise<ApiResponse<TokenPaginationResponse<LoadBalancer>>> {
        const response = await this.sdk.getLoadBalancersV1(
            region,
            pagination.getMaxResults(),
            pagination.getNextPageToken(),
        );
        const loadBalancerDescriptions = get(response, 'LoadBalancerDescriptions', []);
        for await (const lb of loadBalancerDescriptions) {
            if (!lb.LoadBalancerName) {
                continue;
            }

            const { TagDescriptions = [] } = await this.sdk.getLoadBalancersV1Tags(region, [
                lb.LoadBalancerName,
            ]);
            (lb as LoadBalancerDescription & { tags: any }).tags = TagDescriptions;
        }
        return this.tokenPaginatedResponse(
            Mapper.loadBalancersV1(region, response),
            response as unknown as string,
        );
    }

    @ResourceScoping()
    async getLoadBalancersV2(
        region: string,
        pagination: TokenPagination,
    ): Promise<ApiResponse<TokenPaginationResponse<LoadBalancer>>> {
        const response = await this.sdk.getLoadBalancersV2(
            region,
            pagination.getMaxResults(),
            pagination.getNextPageToken(),
        );
        const loadBalancerArns = get(response, 'LoadBalancers', []);
        for await (const lb of loadBalancerArns) {
            if (!lb.LoadBalancerArn) {
                continue;
            }

            const { TagDescriptions = [] } = await this.sdk.getLoadBalancersV2Tags(region, [
                lb.LoadBalancerArn,
            ]);
            (lb as DescribeLoadBalancersCommandOutput & { tags: any }).tags = TagDescriptions;
        }
        return this.tokenPaginatedResponse(
            Mapper.loadBalancersV2(region, response),
            response as unknown as string,
        );
    }

    @ResourceScoping()
    async getCloudData(
        region: string,
        pagination: TokenPagination,
    ): Promise<ApiResponse<TokenPaginationResponse<Table>>> {
        const response = await this.sdk.getTables(
            region,
            pagination.getMaxResults(),
            pagination.getNextPageToken(),
        );

        const cloudData = Mapper.cloudData(region, response);

        if (!isEmpty(cloudData.data.page)) {
            for (const table of cloudData.data.page ?? []) {
                const tableResponse = await this.sdk.describeTable(region, table.name);

                const description = Mapper.table(tableResponse);

                if (!isNil(table) && !isNil(description)) {
                    // Description might be NULL or UNDEFINED :why:
                    const tags = await this.sdk.getTableTags(region, description.arn);

                    table.tags = Mapper.tagList(table.dimension, get(tags, 'Tags', []));
                }
            }
        }

        return this.tokenPaginatedResponse(cloudData, response as unknown as string);
    }

    @ResourceScoping()
    async getRootUnused(
        pagination: TokenPagination,
        startTime: Date,
        endTime: Date,
    ): Promise<ApiResponse<TokenPaginationResponse<Event>>> {
        const events = await this.sdk.listRootEventsInUsEast1(
            pagination.getMaxResults(),
            pagination.getNextPageToken(),
            startTime,
            endTime,
        );
        return this.tokenPaginatedResponse(events);
    }

    @ResourceScoping()
    async getApplicationLoadBalancersV2(
        region: string,
        pagination: TokenPagination,
    ): Promise<ApiResponse<TokenPaginationResponse<Waffable>>> {
        const response = await this.sdk.getLoadBalancersV2(
            region,
            pagination.getMaxResults(),
            pagination.getNextPageToken(),
        );

        return this.tokenPaginatedResponse(
            Mapper.applicationLoadBalancersV2(region, response),
            response as unknown as string,
        );
    }

    @ResourceScoping()
    public async getPublicApiGatewaysV1(
        region: string,
        pagination: TokenPagination,
    ): Promise<ApiResponse<TokenPaginationResponse<Waffable>>> {
        const response = await this.sdk.getApiGatewaysV1(
            region,
            pagination.getMaxResults(),
            pagination.getNextPageToken(),
        );

        return this.tokenPaginatedResponse(Mapper.publicApiGateway(response), response as never);
    }

    @ResourceScoping()
    private async getGraphGateways(
        region: string,
        pagination: TokenPagination,
    ): Promise<ApiResponse<TokenPaginationResponse<Waffable>>> {
        const response = await this.sdk.getGraphqlApis(
            region,
            pagination.getMaxResults(),
            pagination.getNextPageToken(),
        );

        return this.tokenPaginatedResponse(
            Mapper.graphGateway(response),
            response as unknown as string,
        );
    }

    private async getCloudFrontDistributions(
        pagination: TokenPagination,
    ): Promise<ApiResponse<TokenPaginationResponse<ApiData>>> {
        const response = await this.sdk.getCloudFrontDistributions(
            pagination.getMaxResults(),
            pagination.getNextPageToken(),
            config.get('aws.region.default'),
        );

        const distributions = Mapper.cloudFrontDistribution(response);

        for (const distribution of distributions.data.page ?? []) {
            const dimension = distribution.dimension ?? EMPTY_STRING;
            const tags = await this.sdk.getCloudFrontDistributionTags(dimension);
            distribution.tags = Mapper.tagItems(dimension, get(tags, 'Tags', null));
        }

        return this.tokenPaginatedResponse(distributions, response as unknown as string);
    }

    async generateCredentialReport(): Promise<ApiResponse<string>> {
        return Promise.reject(new NotImplementedException());
    }

    getCredentialReport(): Promise<ApiResponse<Report>> {
        return Promise.reject(new NotImplementedException());
    }

    listMfaDevices(
        username: string,
        maxResults: number,
        nextPageToken: string,
    ): Promise<InfrastructureMfaDeviceType> {
        return this.sdk.listMfaDevices(
            this.parentOrgClientBuilder ?? this.awsClientBuilder,
            username,
            maxResults,
            nextPageToken,
        );
    }

    getClientId(): Promise<string> {
        return Promise.resolve(resolveClientId(this.connection));
    }

    getClientAlias(): Promise<string> {
        return Promise.resolve(this.accountAlias);
    }

    private isQueue(arn: string): boolean {
        return arn.startsWith(AwsConstants.QUEUE_PREFIX);
    }

    private async verifyAlarmSubscriptions(
        alarm: Alarm,
        arn: string,
        region: string,
    ): Promise<void> {
        let response: ListSubscriptionsByTopicCommandOutput | null = null;

        try {
            response = await this.getSubscriptionsByTopic(arn, region);
        } catch (error) {
            const polloMessage = PolloAdapter.cxt(
                'Alarm subscriptions by topic failed',
                this.loggingContext,
            )
                .setContext(this.constructor.name)
                .setSubContext(this.verifyAlarmSubscriptions.name)
                .setIdentifier({
                    alarm: {
                        id: alarm.id,
                        name: alarm.name,
                        description: alarm.description,
                        actions: alarm.actions,
                        metricName: alarm.raw.MetricName,
                        namespace: alarm.raw.Namespace,
                        dimensions: alarm.raw.Dimensions,
                    },
                    arn,
                    region,
                })
                .setError(error);

            if (
                has(error, 'message') &&
                [AwsConstants.TOPIC_ACCOUNT_NOT_FOUND, AwsConstants.TOPIC_DOES_NOT_EXIST].includes(
                    error.message,
                )
            ) {
                alarm.hasTopic = false;
                this.logger.warn(polloMessage);
            } else {
                error.message = `${error.message}:${arn}`;
                this.logErrorLevelBasedOnClassification(error, polloMessage);
                return Promise.reject(error);
            }
        }

        if (!isNil(response)) {
            for (const subscription of response.Subscriptions ?? []) {
                alarm.hasSubscription =
                    subscription.SubscriptionArn !== AwsConstants.PENDING_CONFIRMATION;

                if (subscription.Protocol === AwsConstants.EMAIL_PROTOCOL) {
                    alarm.email = subscription.Endpoint;
                }
            }
        }
    }

    @Retry()
    private getSubscriptionsByTopic(
        arn: string,
        region: string,
        nextPageToken?: string,
    ): Promise<ListSubscriptionsByTopicCommandOutput> {
        return this.sdk.listSubscriptions(region, arn, nextPageToken);
    }

    async loadAssets(): Promise<AssetRequestDto[]> {
        const assets = [];
        const cloudtrailAssetIds: Set<string> = new Set();
        const sdk = this.sdk;

        const regions = await this.getRegions();

        for (const region of regions) {
            await this.loadEc2Assets(assets, region);
            await this.loadEksAssets(assets, region);
            await this.loadEcrAssets(assets, region);
            await this.loadRDSClusters(assets, region);
            await this.loadEcsTasks(assets, region);
            await this.loadWafAssets(assets, region);

            await this.loadCloudTrailAssets(assets, sdk, region, cloudtrailAssetIds);
        }

        await this.loadWafAssets(assets, AwsConstants.GLOBAL_REGION);

        await this.loadCloudfrontAssets(assets, sdk, AwsConstants.GLOBAL_REGION);

        await this.loadS3Assets(assets, sdk, AwsConstants.GLOBAL_REGION);

        return assets;
    }

    @ResourceScoping()
    @Retry(defaultRetryOptions)
    protected async loadEcsTasks(assets: any[], region: string): Promise<void> {
        let clusterPageToken = undefined;
        do {
            let clusterResponse;
            try {
                clusterResponse = await this.listEcsClusters(
                    region,
                    config.get('aws.ecsClusterMaxResults'),
                    clusterPageToken,
                );
            } catch (error) {
                this.logAssetError(error, 'Virtual Assets: AWS ECS listClusters', region);
                return;
            }
            clusterPageToken = get(clusterResponse, 'nextToken', null);
            for (const arn of clusterResponse.clusterArns ?? []) {
                let taskPageToken = undefined;
                do {
                    let listTasksResponse;
                    try {
                        listTasksResponse = await this.listEcsTasks(
                            region,
                            config.get('aws.ecsTaskMaxResults'),
                            taskPageToken,
                            arn,
                        );
                    } catch (error) {
                        this.logAssetError(error, 'Virtual Assets: AWS ECS listTasks', region, arn);
                        continue;
                    }
                    taskPageToken = listTasksResponse.nextToken;
                    if (!isEmpty(listTasksResponse.taskArns)) {
                        let describeTasksResponse;
                        try {
                            describeTasksResponse = await this.describeEcsTasks(
                                region,
                                arn,
                                listTasksResponse.taskArns,
                            );
                        } catch (error) {
                            this.logAssetError(
                                error,
                                'Virtual Assets: AWS ECS describeTasks',
                                region,
                                arn,
                            );
                            continue;
                        }
                        const tasks = describeTasksResponse.tasks;
                        for (const task of tasks) {
                            if (!isNil(task.taskArn)) {
                                try {
                                    const splitArn = task.taskArn.split('/');
                                    const name = splitArn.pop();
                                    const desc = 'AWS ECS Task';
                                    const tagKeys = this.extractAndSanitizeTagKeys(
                                        task,
                                        'tags',
                                        'key',
                                    );
                                    const assetIsExcluded = tagKeys.includes(
                                        ApiExclusion.TAG.toLowerCase(),
                                    );

                                    if (!isNil(name) && !assetIsExcluded) {
                                        assets.push(
                                            this.createAsset({
                                                name,
                                                assetClassType: AssetClassType.CONTAINER,
                                                desc,
                                                externalId: name,
                                                region,
                                                tags: getTags(task),
                                            }),
                                        );
                                        this.logAssetSuccess('ECS', region, name);
                                    }
                                } catch (error) {
                                    const polloMessage = PolloAdapter.cxt(
                                        'Could not parse arn for Virtual Asset AWS ECS Task',
                                        this.loggingContext,
                                    )
                                        .setContext(this.constructor.name)
                                        .setSubContext(this.loadEcsTasks.name)
                                        .setIdentifier({
                                            connection: this.connection,
                                            arn,
                                        });
                                    this.logger.warn(polloMessage);
                                }
                            }
                        }
                    }
                } while (!isNil(taskPageToken));
            }
        } while (!isNil(clusterPageToken));
    }

    @Retry()
    async listEcsClusters(
        region: string,
        maxResults: number,
        nextPageToken: string,
    ): Promise<ListClustersCommandOutputECS> {
        return this.sdk.getClusters(region, maxResults, nextPageToken);
    }

    @Retry()
    async listEcsTasks(
        region: string,
        maxResults: number,
        nextToken: string,
        cluster: string,
    ): Promise<ListTasksCommandOutputECS> {
        return this.sdk.listEcsTasks(region, maxResults, nextToken, cluster);
    }

    @Retry()
    async describeEcsTasks(
        region: string,
        cluster: string | undefined,
        taskArns: string[] | undefined,
    ): Promise<DescribeTasksCommandOutputECS> {
        return this.sdk.describeEcsTasks(region, cluster, taskArns);
    }

    @Retry(defaultRetryOptions)
    protected async loadRDSClusters(assets: AssetRequestDto[], region: string): Promise<void> {
        const pagination = new TokenPagination(config.get('aws.rdsInstanceMaxResults'), null);
        do {
            let response: ApiResponse<TokenPaginationResponse<Database>>;
            try {
                response = await this.getDatabases(region, pagination);
            } catch (error) {
                this.logAssetError(error, 'Virtual Assets: AWS RDS listDatabases', region);
                return;
            }
            pagination.setNewNextPageToken(response.data.data.nextPageToken);
            const instances = response.data.data.page ?? [];
            for (const instance of instances) {
                const name = instance.name;
                const desc = 'AWS RDS Instance';
                const tagKeys = this.extractAndSanitizeTagKeys(instance, 'tags.tags', 'key');
                const assetIsExcluded = tagKeys.includes(ApiExclusion.TAG.toLowerCase());
                if (!isNil(name) && !assetIsExcluded) {
                    assets.push(
                        this.createAsset({
                            name,
                            assetClassType: AssetClassType.DATABASE,
                            desc,
                            externalId: instance.id,
                            region,
                            tags: isEvidenceWithTags(instance) ? getTags(instance) : undefined,
                        }),
                    );
                    this.logAssetSuccess('RDS', region, name);
                }
            }
        } while (pagination.hasNextPageToken());
    }

    @ResourceScoping()
    @Retry(defaultRetryOptions)
    protected async loadEc2Assets(assets: AssetRequestDto[], region: string): Promise<void> {
        let pageToken = undefined;
        do {
            let response;
            try {
                response = await this.getEc2Instances(
                    region,
                    config.get('aws.ec2InstanceMaxResults'),
                    pageToken,
                );
            } catch (error) {
                this.logAssetError(error, 'Virtual Assets: AWS EC2 describeInstances', region);
                return;
            }
            pageToken = get(response, 'NextToken', null);
            const reservations = response.Reservations ?? [];
            const terminatedStatus = 'terminated';

            for (const reservation of reservations) {
                for (const instance of reservation.Instances ?? []) {
                    const name = instance.InstanceId;
                    const state = instance.State.Name;
                    const tagKeys = this.extractAndSanitizeTagKeys(instance, 'Tags', 'Key');
                    const assetIsExcluded = tagKeys.includes(ApiExclusion.TAG.toLowerCase());
                    const removedAt = state === terminatedStatus ? new Date() : null;
                    if (!isNil(name) && !assetIsExcluded) {
                        assets.push(
                            this.createAsset({
                                name,
                                assetClassType: AssetClassType.COMPUTE,
                                desc: 'AWS EC2 Instance',
                                externalId: name,
                                removedAt,
                                region,
                                tags: getTags(instance),
                            }),
                        );
                        this.logAssetSuccess('EC2', region, name);
                    }
                }
            }
        } while (!isNil(pageToken));
    }

    async getEc2Instances(
        region: string,
        maxResults: number = config.get('aws.ec2InstanceMaxResults'),
        nextPageToken?: string,
        filters?: FilterType[],
    ): Promise<DescribeInstancesCommandOutputEC2> {
        return this.sdk.getEc2Instances(region, maxResults, nextPageToken, filters);
    }

    @ResourceScoping()
    @Retry(defaultRetryOptions)
    private async loadCloudfrontAssets(assets: any[], sdk: AwsSdk, region: string): Promise<void> {
        let pageToken = undefined;
        do {
            let response: ListDistributionsCommandOutput;
            try {
                response = await sdk.getCloudFrontDistributions(
                    config.get('aws.cloudFrontDistributionsMaxResults'),
                    pageToken,
                );
            } catch (error) {
                this.logAssetError(
                    error,
                    'Virtual Assets: AWS CloudFront listDistributions',
                    region,
                );
                return;
            }

            for (const distribution of response?.DistributionList?.Items ?? []) {
                const name = distribution.Id;
                let tags;
                try {
                    tags = await sdk.getCloudFrontTags(distribution.ARN);
                } catch (error) {
                    this.logAssetError(
                        error,
                        'Virtual Assets: AWS CloudFront listTagsForResource',
                        region,
                        name,
                    );
                    continue;
                }
                const tagKeys = this.extractAndSanitizeTagKeys(tags, 'Tags.Items', 'Key');
                const assetIsExcluded = tagKeys.includes(ApiExclusion.TAG.toLowerCase());
                if (!isNil(name) && !assetIsExcluded) {
                    assets.push(
                        this.createAsset({
                            name,
                            assetClassType: AssetClassType.NETWORKING,
                            desc: 'AWS CloudFront Distribution',
                            externalId: name,
                            region,
                            tags: getTags(tags),
                        }),
                    );
                    this.logAssetSuccess('CloudFront', region, name);
                }
            }
            pageToken = get(response, 'NextMarker', null);
        } while (!isNil(pageToken));
    }

    @Retry(defaultRetryOptions)
    protected async loadS3Assets(
        assets: AssetRequestDto[],
        _sdk: AwsSdk,
        region: string,
    ): Promise<void> {
        let allBuckets = [];
        try {
            await forEachTokenPage(
                (nextPageToken: string) => {
                    return this.getBuckets(null as any, TokenPagination.page(nextPageToken));
                },
                async (buckets: any) => {
                    if (!isEmpty(buckets)) {
                        allBuckets = allBuckets.concat(buckets);
                    }
                },
            );
        } catch (error) {
            this.logAssetError(error, 'Virtual Assets: AWS S3 listBuckets', region);
            return;
        }

        const desc = 'AWS S3 Bucket';
        for (const bucket of allBuckets) {
            const name = get(bucket, 'name');
            const tagKeys = this.extractAndSanitizeTagKeys(bucket, 'tags.tags', 'key');
            const assetIsExcluded =
                tagKeys.includes(ApiExclusion.TAG.toLowerCase()) ||
                !isEmpty(get(bucket, 'exclusion'));
            if (!isNil(name) && !assetIsExcluded) {
                assets.push(
                    this.createAsset({
                        name,
                        assetClassType: AssetClassType.STORAGE,
                        desc,
                        externalId: name,
                        region,
                        tags: getTags(bucket),
                    }),
                );
                this.logAssetSuccess('S3', region, name);
            }
        }
    }

    @ResourceScoping()
    @Retry(defaultRetryOptions)
    protected async loadEksAssets(assets: AssetRequestDto[], region: string): Promise<void> {
        let pageToken: string | null | undefined;
        let response;

        const maxResults: number = config.get('aws.eksClusterMaxResults');

        do {
            try {
                response = await this.getEksClusters(region, maxResults, pageToken ?? '');
            } catch (error) {
                this.logAssetError(error, 'Virtual Assets: AWS EKS listClusters', region);
                return;
            }

            pageToken = get(response, 'nextToken', null);

            if (!isArray(response?.clusters)) {
                continue;
            }

            const EKS_CLUSTER_ASSET_DESCRIPTION = 'AWS EKS Cluster';

            for (const clusterName of response.clusters ?? []) {
                let clusterDescription;

                try {
                    clusterDescription = await this.describeEksCluster(region, clusterName);
                } catch (error) {
                    this.logAssetError(
                        error,
                        'Virtual Assets: AWS EKS listClusters',
                        region,
                        clusterName,
                    );
                    continue;
                }

                const cluster = get(clusterDescription, 'cluster', null);

                if (!isNil(cluster)) {
                    const tagKeys = this.extractAndSanitizeTagKeys(cluster, 'tags');
                    const assetIsExcluded = tagKeys.includes(ApiExclusion.TAG.toLowerCase());

                    if (assetIsExcluded) {
                        continue;
                    }

                    const defaultExternalId = !isNil(region)
                        ? `${clusterName}-${region}`
                        : clusterName;

                    const externalId = cluster.arn ?? defaultExternalId;

                    assets.push(
                        this.createAsset({
                            name: clusterName,
                            assetClassType: AssetClassType.CONTAINER,
                            desc: EKS_CLUSTER_ASSET_DESCRIPTION,
                            externalId,
                            region,
                            tags: getTags(cluster as EvidenceWithTags),
                        }),
                    );

                    this.logAssetSuccess('EKS', region, externalId);
                }
            }
        } while (!isNil(pageToken));
    }

    @Retry()
    async getEksClusters(
        region: string,
        maxResults: number,
        nextPageToken: string,
    ): Promise<ListClustersCommandOutputECS> {
        return this.sdk.getContainers(region, maxResults, nextPageToken);
    }

    @Retry()
    async describeEksCluster(
        region: string,
        name: string,
    ): Promise<DescribeClusterCommandOutputEKS> {
        return this.sdk.describeEksCluster(region, name);
    }

    @ResourceScoping()
    @Retry(defaultRetryOptions)
    protected async loadEcrAssets(assets: AssetRequestDto[], region: string): Promise<void> {
        let pageToken = undefined;
        do {
            let response;
            try {
                response = await this.getEcrRepositories(
                    region,
                    config.get('aws.ecrRepositoryMaxResults'),
                    pageToken,
                );
            } catch (error) {
                this.logAssetError(error, 'Virtual Assets: AWS ECR describeRepositories', region);
                return;
            }
            pageToken = get(response, 'nextToken', null);
            if (!isNil(response)) {
                const repositories = response.repositories ?? [];
                for (const repository of repositories) {
                    const name = repository.repositoryName;
                    const desc = 'AWS ECR Repository';
                    const externalId = repository.repositoryArn;
                    let tagsResponse;
                    try {
                        tagsResponse = await this.getEcrTags(externalId, region);
                    } catch (error) {
                        this.logAssetError(
                            error,
                            'Virtual Assets: AWS ECR listTagsForResource',
                            region,
                            externalId,
                        );
                        continue;
                    }
                    const tagKeys = this.extractAndSanitizeTagKeys(tagsResponse, 'tags', 'Key');
                    const assetIsExcluded = tagKeys.includes(ApiExclusion.TAG.toLowerCase());
                    if (!isNil(name) && !assetIsExcluded) {
                        assets.push(
                            this.createAsset({
                                name,
                                assetClassType: AssetClassType.CONTAINER,
                                desc,
                                externalId,
                                region,
                                tags: getTags(tagsResponse),
                            }),
                        );
                        this.logAssetSuccess('ECR', region, externalId);
                    }
                }
            }
        } while (!isNil(pageToken));
    }

    @Retry()
    async getEcrRepositories(
        region: string,
        maxResults: number,
        nextPageToken: string,
    ): Promise<DescribeRepositoriesCommandOutput> {
        return this.sdk.getEcrRepositories(region, maxResults, nextPageToken);
    }

    @Retry()
    async getEcrTags(arn: string, region: string): Promise<ListTagsForResourceCommandOutputECR> {
        return this.sdk.getEcrTags(arn, region);
    }

    @ResourceScoping()
    @Retry(defaultRetryOptions)
    protected async loadCloudTrailAssets(
        assets: AssetRequestDto[],
        sdk: AwsSdk,
        region: string,
        previouslyPulledAssets: Set<string>,
    ): Promise<void> {
        const { trailList } = await sdk.describeCloudTrailTrails(region);
        if (!isNil(trailList)) {
            const accountId = await sdk.getAccountId();
            for (const {
                Name: name = 'Default Cloudtrail Trail Name',
                TrailARN: externalId = 'Default Cloudtrail Trail ARN',
                HomeRegion: homeRegion,
            } of trailList) {
                const isCurrentAccount = externalId.includes(`:${accountId}:`);
                if (!previouslyPulledAssets.has(externalId)) {
                    previouslyPulledAssets.add(externalId);
                    if (!isCurrentAccount) {
                        const error = new Error(
                            'Organizational level trails are not supported if the parent account does not match the connection account.',
                        );
                        const polloMessage = PolloAdapter.cxt(error?.message, this.loggingContext)
                            .setContext(this.constructor.name)
                            .setSubContext(this.loadCloudTrailAssets.name)
                            .setIdentifier({
                                connectionId: this.connection.id,
                                externalOwnerId: this.getAssetOwnerId(),
                                awsRegion: homeRegion,
                                assetIdentifier: externalId ?? 'n/a',
                            });

                        this.logger.warn(polloMessage);
                        continue;
                    }

                    let tagResponse;
                    let tagKeys;
                    let tagList;
                    try {
                        tagResponse = await this.getCloudTrailTags(homeRegion, externalId);
                        tagList = get(tagResponse, 'ResourceTagList[0]', null);
                        tagKeys = this.extractAndSanitizeTagKeys(tagList, 'TagsList', 'Key');
                    } catch (error) {
                        this.logAssetError(
                            error,
                            'Virtual Assets: AWS Cloudtrail listTags',
                            homeRegion,
                            externalId,
                        );
                        continue;
                    }
                    const assetIsExcluded = tagKeys.includes(ApiExclusion.TAG.toLowerCase());
                    if (!assetIsExcluded) {
                        assets.push(
                            this.createAsset({
                                name,
                                assetClassType: AssetClassType.SOFTWARE,
                                desc: 'AWS Cloudtrail',
                                externalId,
                                region: homeRegion,
                                tags: getTags(tagList),
                            }),
                        );
                        this.logAssetSuccess('CloudTrail', region, externalId);
                    }
                }
            }
        }
    }

    @Retry()
    async getCloudTrailTags(
        region: string,
        resource: string,
    ): Promise<ListTagsCommandOutputCloudTrail> {
        return this.sdk.getCloudTrailTags(region, resource);
    }

    @ResourceScoping()
    protected async loadWafAssets(assets: AssetRequestDto[], region: string): Promise<void> {
        // Emulated local resources do not support this resource type
        if (!config.get('aws.localAws.enabled')) {
            await this.addV1WebAclsToAssets(assets, region);
            await this.addV2WebAclsToAssets(assets, region);
        }
    }

    @Retry(defaultRetryOptions)
    protected async addV1WebAclsToAssets(assets: AssetRequestDto[], region: string): Promise<void> {
        const maxResultsAllowed = config.get('aws.wafMaxResults');
        let nextPageToken: string;

        do {
            let webAcls;
            let nextMarker;

            try {
                ({ WebACLs: webAcls, NextMarker: nextMarker } = await this.getAcls(
                    region,
                    maxResultsAllowed,
                    nextPageToken,
                ));
            } catch (error) {
                this.logAssetError(error, 'Virtual Assets: AWS WAF V1 listWebACLs', region);
                return;
            }

            for (const { WebACLId: externalId, Name: name } of webAcls) {
                let assetIsExcluded = false;
                let tagResponse;
                try {
                    const { WebACL: details } = await this.getAclsDetails(region, externalId);

                    if (!isNil(details.WebACLArn)) {
                        tagResponse = await this.getTagsForV1Acl(region, details.WebACLArn);

                        const tags = this.extractAndSanitizeTagKeys(
                            tagResponse,
                            'TagInfoForResource.TagList',
                            'Key',
                        );

                        assetIsExcluded = tags.includes(ApiExclusion.TAG.toLowerCase());
                    }
                } catch (error) {
                    this.logAssetError(
                        error,
                        'Virtual Assets: AWS WAFV1 - could not parse tags from resource.',
                        region,
                    );

                    continue;
                }

                const assetDescription = 'AWS V1 Web ACL';

                if (!assetIsExcluded) {
                    assets.push(
                        this.createAsset({
                            name,
                            assetClassType: AssetClassType.NETWORKING,
                            desc: assetDescription,
                            externalId,
                            region,
                            tags: getTags(tagResponse?.TagInfoForResource),
                        }),
                    );

                    this.logAssetSuccess('WAFV1 ACL', region, externalId);
                }
            }

            nextPageToken = nextMarker;
        } while (!isNil(nextPageToken));
    }

    @Retry()
    async getAcls(
        region: string,
        maxResults: number,
        nextPageToken: string,
    ): Promise<ListWebACLsCommandOutputWAF> {
        return this.sdk.acls(region, maxResults, nextPageToken);
    }

    @Retry()
    async getAclsDetails(region: string, webAclId: string): Promise<GetWebACLCommandOutputWAF> {
        return this.sdk.getAclsDetails(region, webAclId);
    }

    @Retry()
    async getTagsForV1Acl(
        region: string,
        acl: string,
    ): Promise<ListTagsForResourceCommandOutputWAF> {
        return this.sdk.getTagsForV1Acl(region, acl);
    }

    @Retry(defaultRetryOptions)
    protected async addV2WebAclsToAssets(assets: AssetRequestDto[], region: string): Promise<void> {
        const maxResultsAllowed = config.get('aws.wafMaxResults');
        let nextPageToken: string;

        do {
            let webAcls: ListWebACLsCommandOutputWAFv2['WebACLs'];
            let nextMarker;

            try {
                ({ WebACLs: webAcls, NextMarker: nextMarker } = await this.sdk.aclsV2(
                    region,
                    maxResultsAllowed,
                    nextPageToken,
                ));
            } catch (error) {
                this.logAssetError(error, 'Virtual Assets: AWS WAFV2 listWebACLs', region);

                return;
            }

            for (const { Id: externalId, Name: name, ARN: arn } of webAcls || []) {
                let tagResponse;

                try {
                    tagResponse = await this.getTagsForV2Acl(region, arn);
                } catch (error) {
                    this.logAssetError(
                        error,
                        'Virtual Assets: AWS WAFV2 listTagsForResource',
                        region,
                        arn,
                    );

                    continue;
                }

                const tags = this.extractAndSanitizeTagKeys(
                    tagResponse,
                    'TagInfoForResource.TagList',
                    'Key',
                );

                const assetIsExcluded = tags.includes(ApiExclusion.TAG.toLowerCase());
                const assetHasNameAndExternalId = name && externalId;

                const assetDescription = 'AWS V2 Web ACL';
                if (!assetIsExcluded && assetHasNameAndExternalId) {
                    assets.push(
                        this.createAsset({
                            name,
                            assetClassType: AssetClassType.NETWORKING,
                            desc: assetDescription,
                            externalId,
                            region,
                            tags: getTags(tagResponse?.TagInfoForResource),
                        }),
                    );

                    this.logAssetSuccess('WAFV2 ACL', region, externalId);
                }
            }

            nextPageToken = nextMarker;
        } while (!isNil(nextPageToken));
    }

    @Retry()
    async aclsV2(
        region: string,
        maxResults: number,
        nextPageToken: string,
    ): Promise<ListWebACLsCommandOutputWAFv2> {
        return this.sdk.aclsV2(region, maxResults, nextPageToken);
    }

    @Retry(defaultRetryOptions)
    private async getTagsForV2Acl(
        region: string,
        arn: string,
    ): Promise<ListTagsForResourceCommandOutputWAFv2> {
        return this.sdk.getTagsForV2Acl(region, arn);
    }

    protected logAssetSuccess(resource: string, region: string, assetExternalId: string) {
        const polloMessage = PolloAdapter.cxt(
            `${resource} asset was successfully retrieved`,
            this.loggingContext,
        )
            .setContext(this.constructor.name)
            .setSubContext(this.logAssetSuccess.name)
            .setIdentifier({
                connectionId: this.connection.id,
                externalOwnerId: this.getAssetOwnerId(),
                awsRegion: region,
                assetIdentifier: assetExternalId,
            });
        this.logger.log(polloMessage);
    }

    protected logAssetError(
        error: Error,
        method: string,
        region: string,
        assetExternalId?: string,
    ) {
        if (
            isIApiErrorCauseClassified(error) &&
            error._causeClassification === ApiErrorCauseClassification.USER_CONFIG
        ) {
            this.logAssetWarning(error, method, region, assetExternalId);
            return;
        }
        const polloMessage = PolloAdapter.cxt(error?.message, this.loggingContext)
            .setContext(this.constructor.name)
            .setSubContext(method)
            .setError(error)
            .setIdentifier({
                connectionId: this.connection.id,
                externalOwnerId: this.getAssetOwnerId(),
                awsRegion: region,
                assetIdentifier: assetExternalId ?? 'n/a',
            });

        this.logger.error(polloMessage);
    }

    private logAssetWarning(
        error: Error,
        method: string,
        region: string,
        assetExternalId?: string,
    ) {
        const polloMessage = PolloAdapter.cxt(error?.message, this.loggingContext)
            .setContext(this.constructor.name)
            .setSubContext(method)
            .setError(error)
            .setIdentifier({
                connectionId: this.connection.id,
                externalOwnerId: this.getAssetOwnerId(),
                awsRegion: region,
                assetIdentifier: assetExternalId ?? 'n/a',
            });

        this.logger.warn(polloMessage);
    }

    protected extractAndSanitizeTagKeys<T>(
        instance: T,
        path: string,
        keyIdentifier?: string,
    ): string[] {
        const extractedTags = [];
        const tags = get(instance, path, null);
        if (!isNil(tags)) {
            if (Array.isArray(tags)) {
                tags.forEach(tag => {
                    if (keyIdentifier) {
                        const key = get(tag, keyIdentifier, '');
                        if (!isEmpty(key)) {
                            extractedTags.push(key.toLowerCase());
                        }
                    }
                });
            } else {
                Object.keys(tags).forEach(key => {
                    extractedTags.push(key.toLowerCase());
                });
            }
        }
        return extractedTags;
    }

    protected createAsset({
        name,
        assetClassType,
        desc,
        externalId,
        removedAt,
        region,
        tags,
    }: {
        name: string;
        assetClassType: AssetClassType;
        desc: string;
        externalId: string;
        region?: string;
        removedAt?: Date;
        tags?: ExternalTags;
    }): AssetRequestDto {
        const asset = new AssetRequestDto();

        asset.name = !isNil(region) ? `${name} | ${region}` : name;
        asset.description = desc ?? '';
        asset.assetType = AssetType.VIRTUAL;
        asset.assetClassTypes = [assetClassType];
        asset.removedAt = removedAt ?? null;
        asset.externalId = externalId;
        asset.tags = tags;

        return asset;
    }

    protected getAwsClientId(): string {
        return this.connection.clientId;
    }

    getGlobalRegion(_response: ApiResponse<any>): string {
        return Api.GLOBAL_REGION;
    }

    async getUserIdentities(
        account: Account,
        maxResults: number,
        nextPageToken: string | null,
    ): Promise<ServicePageableType<AwsUserAccessReviewServiceUser>> {
        const response = await this.getUsers(account.domain, maxResults, nextPageToken);

        const uarServiceUsers: AwsUserAccessReviewServiceUser[] = [];

        if (!isEmpty(response?.data?.data)) {
            // NOTE: the getUsers method already formatted the accessData.
            for (const user of response.data.data ?? []) {
                await this.getPoliciesDetails(user);
                uarServiceUsers.push(
                    new AwsUserAccessReviewServiceUser({
                        id: user.id,
                        email: user.email, // NOTE: the api response sometimes returns this value as null.
                        groups: user?.accessData?.raw?.groups ?? [],
                        hasMFA: user.hasMfa,
                        isAdmin: user.isAdmin ?? false,
                        name: user.name,
                        raw: user,
                        roles: user?.accessData?.raw?.roles ?? [],
                        username: user.name ?? user.email ?? user.id,
                    }),
                );
            }
        }

        return {
            data: uarServiceUsers,
            token: response.data.nextPageToken ?? undefined,
        };
    }

    private async getPoliciesDetails(user: User) {
        if (isNil(this.uarGranularData)) {
            this.uarGranularData = await this.featureFlagService.evaluateAs(
                {
                    category: FeatureFlagCategory.NONE,
                    name: FeatureFlag.UAR_AWS_GRANULAR_DATA,
                    defaultValue: false,
                },
                this.account,
            );
        }

        if (!this.uarGranularData) {
            return;
        }

        const accessData: AccessData = user.accessData?.raw ?? {};

        try {
            for (const policy of accessData.policies ?? []) {
                if (isNil(policy.arn) || isEmpty(policy.arn)) {
                    continue;
                }

                const policyVersionId = await this.getPolicyVersion(policy.arn);
                policy.version = policyVersionId;
                policy.statements = await this.getPolicyStatements(policy.arn, policyVersionId);
            }

            for (const group of accessData.groups ?? []) {
                if (isEmpty(group.policies)) {
                    continue;
                }
                for (const policy of group.policies ?? []) {
                    if (isNil(policy.arn) || isEmpty(policy.arn)) {
                        continue;
                    }
                    const policyVersionId = await this.getPolicyVersion(policy.arn);
                    policy.version = policyVersionId;
                    policy.statements = await this.getPolicyStatements(policy.arn, policyVersionId);
                }
            }
        } catch (error) {
            this.logger.error(
                PolloAdapter.cxt(
                    `Error while getting policy details for user ${user.id}`,
                    this.loggingContext,
                )
                    .setContext(this.constructor.name)
                    .setSubContext(this.getIdentityCenterRegionIdByAccountType.name)
                    .setError(error),
            );
        }

        set(user, 'accessData.raw', accessData);
    }

    private async getPolicyVersion(policyArn: string): Promise<string> {
        let version = this.policyVersionCache[policyArn];
        if (!isEmpty(version)) {
            return version;
        }

        const policyVersionList = await this.sdk.listPolicyVersion(policyArn);
        const versions = policyVersionList.Versions?.sort(
            (a, b) => (b?.CreateDate?.getTime() ?? 0) - (a?.CreateDate?.getTime() ?? 0),
        );
        version = get(versions, '[0].VersionId', 'v1');

        this.policyVersionCache[policyArn] = version;

        return version;
    }

    private async getPolicyStatements(
        policyArn: string,
        policyVersionId: string,
    ): Promise<Statement[]> {
        let statements = this.policyStatementCache[policyArn];
        if (!isEmpty(statements)) {
            return statements;
        }

        const policyVersion = await this.sdk.getPolicyVersion(policyArn, policyVersionId);
        if (isNil(policyVersion.PolicyVersion?.Document)) {
            return [];
        }

        const decodedPolicy = decodeURIComponent(policyVersion.PolicyVersion.Document);
        const policyJson = JSON.parse(decodedPolicy);

        statements = [];
        for (const statement of policyJson.Statement ?? []) {
            statements = statements.concat({
                effect: statement.Effect,
                actions: statement.Action,
                resource: statement.Resource,
            });
        }

        this.policyStatementCache[policyArn] = statements;

        return statements;
    }

    private getConfigurationParams(): ConfigurationParams {
        return {
            RoleArn: this.metadata.key,
            ExternalId: this.metadata.externalId,
            RoleSessionName: config.get('aws.autopilot.roleSessionName'),
        };
    }

    protected logErrorLevelBasedOnClassification(error: unknown, polloMessage: PolloMessage) {
        if (isUserConfigClassificationError(error)) {
            this.logger.warn(polloMessage);
        } else {
            this.logger.error(polloMessage);
        }
    }
}
