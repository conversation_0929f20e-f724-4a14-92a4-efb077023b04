import { Bucket } from 'app/apis/classes/api-data-storage/bucket.class';
import { Cache } from 'app/apis/classes/api-data-storage/cache.class';
import { DatabaseCluster } from 'app/apis/classes/api-data-storage/database-cluster.class';
import { Database } from 'app/apis/classes/api-data-storage/database.class';
import { Indexer } from 'app/apis/classes/api-data-storage/indexer.class';
import { Queue } from 'app/apis/classes/api-data-storage/queue.class';
import { Table } from 'app/apis/classes/api-data-storage/table.class';
import { Alarm } from 'app/apis/classes/api-data/alarm.class';
import { BucketAccount } from 'app/apis/classes/api-data/bucket-account.class';
import { Cluster } from 'app/apis/classes/api-data/cluster.class';
import { Event } from 'app/apis/classes/api-data/event.class';
import { Instance } from 'app/apis/classes/api-data/instance.class';
import { LoadBalancer } from 'app/apis/classes/api-data/load-balancer.class';
import { NetworkInterface } from 'app/apis/classes/api-data/network-interface.class';
import { Report } from 'app/apis/classes/api-data/report.class';
import { Rule } from 'app/apis/classes/api-data/rule.class';
import { SecurityGroup } from 'app/apis/classes/api-data/security-group.class';
import { User } from 'app/apis/classes/api-data/user.class';
import { Waffable } from 'app/apis/classes/api-data/waffable-data.class';
import { ApiData } from 'app/apis/classes/api/api-data.class';
import { ApiResponse } from 'app/apis/classes/api/api-response.class';
import { IApiServices } from 'app/apis/interfaces/api-services.interface';
import { IEndpointAvailableResponse } from 'app/apis/services/aws/interfaces/endpoint-available-response.interface';
import { PaginatedType as Paginated } from 'app/apis/types/api-data/paginated.type';
import { FilterType } from 'app/apis/types/filter/filter.type';
import { InfrastructureAuthorizationDetailsType as AuthDetails } from 'app/apis/types/infrastructure/infrastructure-authorization-details.type';
import { InfrastructureMfaDeviceType } from 'app/apis/types/infrastructure/infrastructure-mfa-device.type';
import { HttpResponseTaskData } from 'app/autopilot/classes/http-response-task-data.class';
import { TaskResult } from 'app/autopilot/classes/task-result.class';
import { AutopilotMetricNamespace as MetricNamespace } from 'commons/enums/autopilot/autopilot-metric-namespace.enum';
import { AutopilotMetric as Metric } from 'commons/enums/autopilot/autopilot-metric.enum';
import { Pagination } from 'commons/helpers/pagination/pagination';
import { PaginationResponse } from 'commons/helpers/pagination/pagination.response';

export interface IInfrastructureServices extends IApiServices {
    /**
     * Manually forces a credential refresh.
     * Useful for long-running tasks to ensure fresh credentials.
     * Optional - only implemented by providers that support credential refresh (e.g., AWS).
     */
    forceRefreshCredentials?(): Promise<void>;

    /**
     *
     */
    getInfrastructureMfa(): Promise<User[]>;

    /**
     *
     * @param maxResults
     * @param nextPageToken
     */
    getAuthorizationDetails(maxResults: number, nextPageToken: string): Promise<AuthDetails>;

    /**
     *
     * @param username
     * @param maxResults
     * @param nextPageToken
     */
    listMfaDevices(
        username: string,
        maxResults: number,
        nextPageToken: string,
    ): Promise<InfrastructureMfaDeviceType>;

    /**
     *
     * @param maxResults
     * @param nextPageToken
     */
    getUsers(
        domain: string,
        maxResults: number,
        nextPageToken: string | null,
        hasMultiDomain: boolean,
    ): Promise<ApiResponse<Paginated<User>>>;

    /**
     *
     */
    getRegions(includeOptedInRegions?: boolean): Promise<string[]>;

    /**
     *
     * @param url
     * @returns
     */
    getInfrastructureSsl(url: string): Promise<TaskResult<HttpResponseTaskData>>;

    /**
     *
     * @param region
     * @param pagination
     */
    getWAFResources(
        region: string,
        pagination: Pagination,
    ): Promise<ApiResponse<PaginationResponse<Waffable>>>;

    /**
     *
     * @param pagination
     */
    getGlobalWAFResources(
        pagination: Pagination,
    ): Promise<ApiResponse<PaginationResponse<Waffable>>>;

    /**
     *
     * @param region
     * @param rule
     */
    getResourcesByWAF(
        region: string,
        rule: Rule,
    ): Promise<ApiResponse<PaginationResponse<Waffable>>>;

    /**
     *
     * @param region
     * @param pagination
     */
    getSSHSecurityGroups(
        region: string,
        pagination: Pagination,
    ): Promise<ApiResponse<PaginationResponse<SecurityGroup>>>;

    /**
     *
     * @param region
     * @param pagination
     */
    getOpenProtocolSecurityGroups(
        region: string,
        pagination: Pagination,
    ): Promise<ApiResponse<PaginationResponse<SecurityGroup>>>;

    /**
     *
     * @param region
     * @param maxResults
     * @param nextPageToken
     * @param filters
     */
    getNetworkInterfaces(
        region: string,
        maxResults: number,
        nextPageToken: string,
        filters?: FilterType[],
    ): Promise<ApiResponse<PaginationResponse<NetworkInterface[]>>>;

    /**
     *
     * @param region
     * @param pagination
     * @param filters
     */
    getDatabases(
        region: string,
        pagination: Pagination,
        filters?: FilterType[],
    ): Promise<ApiResponse<PaginationResponse<Database>>>;

    /**
     *
     * @param region
     * @param pagination
     * @param filters
     */
    getDatabasesClusters(
        region: string,
        pagination: Pagination,
        filters?: FilterType[],
    ): Promise<ApiResponse<PaginationResponse<DatabaseCluster>>>;

    /**
     *
     * @param region
     * @param pagination
     */
    getCachesV1(
        region: string,
        pagination: Pagination,
    ): Promise<ApiResponse<PaginationResponse<Cache>>>;

    /**
     *
     * @param region
     * @param pagination
     */
    getCachesV2(
        region: string,
        pagination: Pagination,
    ): Promise<ApiResponse<PaginationResponse<Cache>>>;

    /**
     *
     * @param region
     */
    getIndexers(region: string): Promise<ApiResponse<Indexer[]>>;

    /**
     *
     * @param region
     * @param pagination
     * @param filters
     */
    getQueues(
        region: string,
        pagination: Pagination,
    ): Promise<ApiResponse<PaginationResponse<Queue>>>;

    /**
     *
     * @param pagination
     */
    getBucketAccounts(
        pagination: Pagination,
        subscriptionId?: string,
        excludeStorageV2?: boolean,
    ): Promise<ApiResponse<PaginationResponse<BucketAccount>>>;

    /**
     *
     * @param account
     * @param pagination
     */
    getBuckets(
        account: BucketAccount,
        pagination: Pagination,
    ): Promise<ApiResponse<PaginationResponse<Bucket>>>;

    /**
     *
     * @param bucket
     */
    getBucketEncryption(bucket: Bucket): Promise<ApiResponse<Bucket>>;

    /**
     *
     * @param bucket
     */
    getBucketPublicAccess(bucket: Bucket): Promise<ApiResponse<Bucket>>;

    /**
     *
     * @param bucket
     */
    getBucketVersioning(bucket: Bucket): Promise<ApiResponse<Bucket>>;

    /**
     *
     * @param region
     * @param type
     * @param namespace
     * @param instanceId
     * @param subInstanceId
     * @param engine
     * @param instance
     */
    getMetricAlarms(
        region: string,
        type: Metric,
        namespace: MetricNamespace,
        instanceId: string,
        subInstanceId: string,
        engine: string,
        instance?: ApiData,
    ): Promise<ApiResponse<Alarm[]>>;

    /**
     *
     * @param region
     * @param pagination
     * @param filters
     */
    getInstances(
        region: string,
        pagination: Pagination,
    ): Promise<ApiResponse<PaginationResponse<Instance>>>;

    /**
     *
     * @param region
     * @param pagination
     */
    getWebApplicationFirewallAclsV2(
        region: string,
        pagination: Pagination,
    ): Promise<ApiResponse<PaginationResponse<Rule>>>;

    /**
     *
     * @param region
     * @param pagination
     */
    getStorageFirewallAcls(
        region: string,
        pagination: Pagination,
    ): Promise<ApiResponse<PaginationResponse<any>>>;

    /**
     *
     * @param region
     */
    hasLoadBalancerResources(region: string): Promise<boolean>;

    /**
     *
     * @param region
     * @param pagination
     */
    getLoadBalancersV1(
        region: string,
        pagination: Pagination,
    ): Promise<ApiResponse<PaginationResponse<LoadBalancer>>>;

    /**
     *
     * @param region
     * @param pagination
     */
    getLoadBalancersV2(
        region: string,
        pagination: Pagination,
    ): Promise<ApiResponse<PaginationResponse<LoadBalancer>>>;

    /**
     *
     * @param region
     * @param pagination
     */
    getCloudData(
        region: string,
        pagination: Pagination,
    ): Promise<ApiResponse<PaginationResponse<Table>>>;

    /**
     *
     * @param region
     * @param pagination
     */
    getClusters(
        region: string,
        pagination: Pagination,
    ): Promise<ApiResponse<PaginationResponse<Cluster>>>;

    /**
     *
     * @param region
     * @param cluster
     * @param pagination
     */
    getClusterServices(
        region: string,
        cluster: Cluster,
        pagination: Pagination,
    ): Promise<ApiResponse<PaginationResponse<Cluster>>>;

    /**
     *
     * @param region
     * @param pagination
     */
    getContainers(
        region: string,
        pagination: Pagination,
    ): Promise<ApiResponse<PaginationResponse<Cluster>>>;

    /**
     *
     */
    generateCredentialReport(): Promise<ApiResponse<string>>;

    /**
     *
     */
    getCredentialReport(): Promise<ApiResponse<Report>>;

    /**
     *
     * @param pagination
     */
    getRootUnused(
        pagination: Pagination,
        startTime?: Date,
        endTime?: Date,
    ): Promise<ApiResponse<PaginationResponse<Event>>>;

    /**
     *
     * @param response
     */
    getGlobalRegion(response: ApiResponse<any>): string;

    /**
     *
     *@param connection
     */
    checkSCIMEndpointHealth(): Promise<IEndpointAvailableResponse | null>;
}
