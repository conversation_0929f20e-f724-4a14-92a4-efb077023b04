import { Alarm } from 'app/apis/classes/api-data/alarm.class';
import { ObservabilityUser } from 'app/apis/classes/api-data/observability-user.class';
import { User } from 'app/apis/classes/api-data/user.class';
import { ApiData } from 'app/apis/classes/api/api-data.class';
import { ApiResponse } from 'app/apis/classes/api/api-response.class';
import { IApiServices } from 'app/apis/interfaces/api-services.interface';
import { IObservabilityIntegration } from 'app/apis/interfaces/observability-integrations.interface';
import { IObservabilityLogsRetentionEvidence } from 'app/apis/interfaces/observability-logs-retention-evidence.interface';
import { IObservabilityCapacitiesAndUsageMonitoringEvidence } from 'app/apis/interfaces/observability/observability-capacity-and-usage-monitoring.interface';
import { IObservabilityLogsAreCentrallyStoredEvidence } from 'app/apis/interfaces/observability/observability-logs-are-centrally-stored-evidence.interface';
import { IObservabilityLogsAreMonitoredForSuspiciousActivityEvidence } from 'app/apis/interfaces/observability/observability-logs-monitored-for-suspicious-activity-evidence.interface';
import { PaginatedType as Paginated } from 'app/apis/types/api-data/paginated.type';
import { IObservabilityApiFailure } from 'app/autopilot/tasks/observability/observability-api-failure.interface';
import { UserIdentity } from 'app/users/entities/user-identity.entity';
import { AutopilotMetricNamespace as MetricNamespace } from 'commons/enums/autopilot/autopilot-metric-namespace.enum';
import { AutopilotMetric as Metric } from 'commons/enums/autopilot/autopilot-metric.enum';

export interface IObservabilityServices extends IApiServices {
    /**
     * Manually forces a credential refresh.
     * Useful for long-running tasks to ensure fresh credentials.
     * Optional - only implemented by providers that support credential refresh.
     */
    forceRefreshCredentials?(): Promise<void>;

    /**
     *
     * @param domain
     * @param maxResults
     * @param nextPageToken
     */
    getUsers(
        domain: string,
        maxResults: number,
        nextPageToken: string,
    ): Promise<ApiResponse<Paginated<User>>>;

    getObservabilityUser(user: UserIdentity): Promise<ObservabilityUser | IObservabilityApiFailure>;

    /**
     *
     * @param region
     * @param type
     * @param namespace
     * @param instanceId
     * @param subInstanceId
     * @param engine
     * @param instance
     * @param subInstance
     */
    getMetricAlarms(
        region: string,
        type: Metric,
        namespace: MetricNamespace,
        instanceId: string,
        subInstanceId: string,
        engine: string,
        instance?: ApiData,
        subInstance?: ApiData,
    ): Promise<ApiResponse<Alarm[]>>;

    /**
     *
     */
    infrastructureIntegrations(): Promise<IObservabilityIntegration>;

    /**
     *
     */
    logsRetentionEvidence(): Promise<
        IObservabilityLogsRetentionEvidence[] | IObservabilityApiFailure
    >;

    /**
     *
     */
    capacityAndUsageMonitoring(): Promise<
        IObservabilityCapacitiesAndUsageMonitoringEvidence | IObservabilityApiFailure
    >;

    /**
     *
     */
    logsAreMonitoredForSuspiciousActivity(): Promise<
        IObservabilityLogsAreMonitoredForSuspiciousActivityEvidence | IObservabilityApiFailure
    >;

    /**
     *
     */
    logsAreCentrallyStored(): Promise<
        IObservabilityLogsAreCentrallyStoredEvidence | IObservabilityApiFailure
    >;
}
