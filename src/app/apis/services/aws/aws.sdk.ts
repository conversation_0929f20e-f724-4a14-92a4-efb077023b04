import { APIGateway, GetRestApisCommandOutput } from '@aws-sdk/client-api-gateway';
import { AppSync, ListGraphqlApisCommandOutput } from '@aws-sdk/client-appsync';
import {
    CloudFrontClient,
    ListDistributionsCommand as ListDistributionsCommandCloudFront,
    ListDistributionsCommandOutput as ListDistributionsCommandOutputCloudFront,
    ListTagsForResourceCommand as ListTagsForResourceCommandCloudFront,
    ListTagsForResourceCommandOutput as ListTagsForResourceCommandOutputCloudFront,
} from '@aws-sdk/client-cloudfront';
import {
    CloudTrailClient,
    DescribeTrailsCommand,
    DescribeTrailsCommandOutput,
    Event as EventCloudTrail,
    ListTagsCommand as ListTagsCommandCloudTrail,
    ListTagsCommandOutput as ListTagsCommandOutputCloudTrail,
    LookupEventsCommand,
} from '@aws-sdk/client-cloudtrail';
import {
    CloudWatchClient,
    DescribeAlarmsForMetricCommand,
    DescribeAlarmsForMetricCommandInput,
    DescribeAlarmsForMetricCommandOutput,
} from '@aws-sdk/client-cloudwatch';
import {
    CodeCommit,
    GetApprovalRuleTemplateCommandOutput,
    GetRepositoryCommandOutput,
    ListAssociatedApprovalRuleTemplatesForRepositoryCommandOutput,
    ListRepositoriesCommandOutput,
} from '@aws-sdk/client-codecommit';
import {
    DescribeTableCommandOutput,
    DynamoDB,
    ListTablesCommandOutput,
    ListTagsOfResourceCommandOutput,
} from '@aws-sdk/client-dynamodb';
import {
    DescribeInstancesCommand,
    DescribeInstancesCommandOutput as DescribeInstancesCommandOutputEC2,
    DescribeNetworkInterfacesCommand,
    DescribeRegionsCommand as DescribeRegionsCommandEC2,
    DescribeRegionsCommandOutput as DescribeRegionsCommandOutputEC2,
    DescribeSecurityGroupsCommand as DescribeSecurityGroupsCommandEC2,
    EC2Client,
    Region,
} from '@aws-sdk/client-ec2';
import {
    DescribeRepositoriesCommand,
    DescribeRepositoriesCommandOutput,
    ECRClient,
    ListTagsForResourceCommand as ListTagsForResourceCommandECR,
    ListTagsForResourceCommandOutput as ListTagsForResourceCommandOutputECR,
} from '@aws-sdk/client-ecr';
import {
    DescribeClustersCommand as DescribeClustersCommandECS,
    DescribeClustersCommandOutput as DescribeClustersCommandOutputECS,
    DescribeServicesCommand as DescribeServicesCommandECS,
    DescribeServicesCommandOutput as DescribeServicesCommandOutputECS,
    DescribeTasksCommand as DescribeTasksCommandECS,
    DescribeTasksCommandOutput as DescribeTasksCommandOutputECS,
    ECSClient,
    ListClustersCommand as ListClustersCommandECS,
    ListClustersCommandOutput as ListClustersCommandOutputECS,
    ListServicesCommand as ListServicesCommandECS,
    ListServicesCommandOutput as ListServicesCommandOutputECS,
    ListTasksCommand as ListTasksCommandECS,
    ListTasksCommandOutput as ListTasksCommandOutputECS,
    ServiceField,
} from '@aws-sdk/client-ecs';
import {
    DescribeClusterCommand,
    DescribeClusterCommandOutput as DescribeClusterCommandOutputEKS,
    DescribeNodegroupCommand,
    DescribeNodegroupCommandOutput,
    EKSClient,
    ListClustersCommand as ListClustersCommandEKS,
    ListClustersCommandOutput as ListClustersCommandOutputEKS,
    ListNodegroupsCommand,
} from '@aws-sdk/client-eks';
import {
    DescribeLoadBalancersCommandOutput as DescribeLoadBalancersCommandOutputELB,
    DescribeTagsCommandOutput as DescribeTagsCommandOutputELB,
    ElasticLoadBalancing,
} from '@aws-sdk/client-elastic-load-balancing';
import {
    DescribeLoadBalancersCommandOutput as DescribeLoadBalancersCommandOutputELBv2,
    DescribeTagsCommandOutput as DescribeTagsCommandOutputELBv2,
    ElasticLoadBalancingV2,
} from '@aws-sdk/client-elastic-load-balancing-v2';
import {
    DescribeCacheClustersCommand,
    DescribeCacheClustersCommandOutput,
    DescribeReplicationGroupsCommand,
    ElastiCacheClient,
    ListTagsForResourceCommand as ListTagsForResourceCommandElasticache,
    ListTagsForResourceCommandOutput as ListTagsForResourceCommandOutputElasticache,
} from '@aws-sdk/client-elasticache';
import {
    DescribeElasticsearchDomainsCommand,
    DescribeElasticsearchDomainsCommandOutput,
    ElasticsearchDomainStatus,
    ElasticsearchServiceClient,
    ListDomainNamesCommand,
    ListDomainNamesCommandOutput,
    ListTagsCommand as ListTagsCommandES,
    ListTagsCommandOutput as ListTagsCommandOutputES,
} from '@aws-sdk/client-elasticsearch-service';
import {
    EventBridgeClient,
    ListRulesCommand,
    ListRulesCommandOutput,
    ListTagsForResourceCommand as ListTagsForResourceCommandEventBridge,
} from '@aws-sdk/client-eventbridge';
import {
    GetDetectorCommand,
    GetDetectorCommandOutput,
    GetMalwareProtectionPlanCommand,
    GetMalwareProtectionPlanCommandOutput,
    GuardDutyClient,
    ListDetectorsCommand,
    ListDetectorsCommandOutput,
    ListMalwareProtectionPlansCommand,
    ListMalwareProtectionPlansCommandOutput,
} from '@aws-sdk/client-guardduty';
import {
    GetLoginProfileCommand,
    GetPolicyVersionCommand,
    GetPolicyVersionCommandOutput,
    GroupDetail,
    IAM,
    IAMClient,
    ListMFADevicesCommand,
    ListPolicyVersionsCommand,
    ListPolicyVersionsCommandOutput,
    ListUsersCommandOutput as ListUsersCommandOutputIAM,
    SimulatePrincipalPolicyCommandOutput,
    UserDetail,
} from '@aws-sdk/client-iam';
import {
    IdentitystoreClient,
    ListUsersCommand as ListUsersCommandIdentityStore,
    ListUsersCommandOutput as ListUsersCommandOutputIdentityStore,
    User as UserIdentityStore,
} from '@aws-sdk/client-identitystore';
import {
    DescribeOrganizationalUnitCommandInput as DescribeOrganizationalUnitCommandInputOrganizations,
    ListAccountsCommandInput as ListAccountsCommandInputOrganizations,
    ListAccountsCommandOutput,
    ListAccountsForParentCommandInput as ListAccountsForParentCommandInputOrganizations,
    ListAccountsForParentCommandOutput as ListAccountsForParentCommandOutputOrganizations,
    ListOrganizationalUnitsForParentCommandInput,
    ListParentsCommandInput as ListParentsCommandInputOrganizations,
    ListParentsCommandOutput,
    ListTagsForResourceCommandInput as ListTagsForResourceCommandInputOrganizations,
    Organizations,
} from '@aws-sdk/client-organizations';
import {
    DescribeDBClustersCommand,
    DescribeDBClustersCommandOutput,
    DescribeDBInstancesCommand,
    RDSClient,
} from '@aws-sdk/client-rds';
import {
    BucketLocationConstraint,
    GetBucketEncryptionCommandOutput,
    GetBucketLifecycleConfigurationCommand,
    GetBucketTaggingCommand,
    GetBucketTaggingCommandOutput,
    GetBucketVersioningCommandOutput,
    GetPublicAccessBlockCommandOutput,
    ListBucketsCommand,
    ListBucketsCommandInput,
    ListBucketsCommandOutput,
    S3,
} from '@aws-sdk/client-s3';
import {
    ListSubscriptionsByTopicCommand,
    ListSubscriptionsByTopicCommandOutput,
    SNSClient,
} from '@aws-sdk/client-sns';
import {
    ListQueuesCommand,
    ListQueueTagsCommand,
    ListQueueTagsCommandOutput,
    SQSClient,
} from '@aws-sdk/client-sqs';
import {
    InstanceStatus,
    ListInstancesCommandOutput as ListInstancesCommandOutputSSOAdmin,
    SSOAdmin,
    Tag,
} from '@aws-sdk/client-sso-admin';
import { GetCallerIdentityCommand, STSClient } from '@aws-sdk/client-sts';
import {
    GetWebACLCommandOutput as GetWebACLCommandOutputWAF,
    ListTagsForResourceCommandOutput as ListTagsForResourceCommandOutputWAF,
    ListWebACLsCommandOutput as ListWebACLsCommandOutputWAF,
    WAF,
} from '@aws-sdk/client-waf';
import {
    ListWebACLsCommandOutput as ListWebACLsCommandOutputWAFRegional,
    WAFRegional,
} from '@aws-sdk/client-waf-regional';
import {
    ListResourcesForWebACLCommand,
    ListTagsForResourceCommandOutput as ListTagsForResourceCommandOutputWAFv2,
    ListTagsForResourceCommand as ListTagsForResourceCommandWAFv2,
    ListWebACLsCommandOutput as ListWebACLsCommandOutputWAFv2,
    ListWebACLsCommand as ListWebACLsCommandWAFv2,
    WAFV2Client,
} from '@aws-sdk/client-wafv2';
import { ErrorCode } from '@drata/enums';
import { HttpStatus, InternalServerErrorException } from '@nestjs/common';
import { rolesHasAdminAccessValue } from 'app/access-review/helpers/access-review.helper';
import { Cache } from 'app/apis/classes/api-data-storage/cache.class';
import { Database } from 'app/apis/classes/api-data-storage/database.class';
import { Indexer } from 'app/apis/classes/api-data-storage/indexer.class';
import { Queue } from 'app/apis/classes/api-data-storage/queue.class';
import { Event } from 'app/apis/classes/api-data/event.class';
import { NetworkInterface } from 'app/apis/classes/api-data/network-interface.class';
import { Repository } from 'app/apis/classes/api-data/repository.class';
import { SecurityGroup } from 'app/apis/classes/api-data/security-group.class';
import { TagCollection } from 'app/apis/classes/api-data/tag-collection.class';
import { User } from 'app/apis/classes/api-data/user.class';
import { Waffable } from 'app/apis/classes/api-data/waffable-data.class';
import { ApiExclusion } from 'app/apis/classes/api/api-exclusion.class';
import { OrganizationalTags } from 'app/apis/classes/aws/api-endpoint-page/organizational-tags.class';
import { OrganizationalUnitAccounts } from 'app/apis/classes/aws/api-endpoint-page/organizational-unit-accounts.class';
import { OrganizationalUnits } from 'app/apis/classes/aws/api-endpoint-page/organizational-units.class';
import { SubAccounts } from 'app/apis/classes/aws/api-endpoint-page/sub-accounts.class';
import { AwsBucketLifeCycleConfiguration } from 'app/apis/classes/aws/aws-bucket-lifecycle-configuration.class';
import { OrganizationalUnit } from 'app/apis/classes/aws/organizational-unit.class';
import { PolicyType } from 'app/apis/enums/aws/policy-type.enum';
import { RateLimitException } from 'app/apis/exceptions/rate-limit.exception';
import { AwsConstants } from 'app/apis/services/aws/aws.constants';
import { AwsMetricFactory } from 'app/apis/services/aws/aws.metric.factory';
import { IClient } from 'app/apis/services/aws/factory/client.interface';
import { IEndpointAvailableResponse } from 'app/apis/services/aws/interfaces/endpoint-available-response.interface';
import { IIdentityCenterInstance } from 'app/apis/services/aws/interfaces/identity-center-instance.interface';
import {
    IGetIdentityCenterUsers,
    IIdentityCenterLogging,
    IIdentityCenterWithStatus,
    IListIdentityCenterUsers,
    IScimProps,
} from 'app/apis/services/aws/interfaces/list-identity-center-users.interface';
import { AccessData } from 'app/apis/types/aws/aws-access-data';
import { FilterType } from 'app/apis/types/filter/filter.type';
import { InfrastructureMfaDeviceType } from 'app/apis/types/infrastructure/infrastructure-mfa-device.type';
import { ConnectionMetadata } from 'app/companies/connections/classes/connection-metadata.class';
import { UserAccessData } from 'app/users/entities/user-access-data.class';
import axios, { AxiosResponse } from 'axios';
import { PolloAdapter } from 'commons/adapters/pollo.adapter';
import { LoggingContext } from 'commons/adapters/types/logging-context.type';
import { ApiErrorCauseClassification } from 'commons/enums/api-error-cause-classification.enum';
import { ClientType } from 'commons/enums/auth/client-type.enum';
import { AutopilotMetricNamespace as MetricNamespace } from 'commons/enums/autopilot/autopilot-metric-namespace.enum';
import { AutopilotMetric as Metric } from 'commons/enums/autopilot/autopilot-metric.enum';
import { SettledStatus } from 'commons/enums/notifications/settled-status.enum';
import { assignCauseClassification } from 'commons/helpers/api-error-cause-classified.helper';
import { arrayToMap } from 'commons/helpers/array.helper';
import { extractRegionFromS3Endpoint } from 'commons/helpers/aws.helper';
import { tokenize } from 'commons/helpers/pagination/pagination.helper';
import { TokenPaginationResponse } from 'commons/helpers/pagination/token.pagination.response';
import { maskString } from 'commons/helpers/security.helper';
import { compareLowerCase, lcIncludes, matchesAnyRegex } from 'commons/helpers/string.helper';
import { getLastSegmentFromUrl } from 'commons/helpers/url.helper';
import { KeyValuePair } from 'commons/types/key-value-pair.type';
import config from 'config';
import { AwsClientBuilder } from 'dependencies/aws/aws-client.builder';
import { concat, get, has, isEmpty, isNil, isNumber, last, slice, uniq } from 'lodash';
import { PolloMessage } from 'pollo-logger/classes/pollo.message';
import { PolloLogger } from 'pollo-logger/pollo.logger';

export class AwsSdk implements IClient {
    private clientBuilder: AwsClientBuilder;

    private key: string = null;

    private logger = PolloLogger.logger(this.constructor.name);

    constructor(
        awsClientBuilder: AwsClientBuilder,
        key: string,
        private readonly loggingContext?: LoggingContext,
    ) {
        this.clientBuilder = awsClientBuilder;
        this.key = key;
    }

    /**
     * Manually forces a credential refresh.
     * Useful for long-running tasks to ensure fresh credentials.
     */
    async forceRefreshCredentials(): Promise<void> {
        await this.clientBuilder.refreshCredentials();
    }

    async getSimulatedActions(
        roleArn: string,
        actions: string[],
    ): Promise<SimulatePrincipalPolicyCommandOutput> {
        const iam = await this.clientBuilder.client(IAM).build();
        const params = {
            PolicySourceArn: roleArn,
            ActionNames: actions,
        };
        return this.request(() => {
            return iam.simulatePrincipalPolicy(params);
        }, this.getSimulatedActions.name);
    }

    getClientType(): ClientType {
        return ClientType.AWS;
    }

    async ping(): Promise<boolean> {
        const keyRoleName = last(this.key.split('/'));

        const params = {
            RoleName: keyRoleName,
        };

        const iam = await this.clientBuilder.client(IAM).build();
        const role = await this.request(() => {
            return iam.getRole(params);
        }, this.ping.name);

        if (!isNil(role) && role.Role.Arn === this.key) {
            return Promise.resolve(true);
        }

        return Promise.reject();
    }

    async pingV2(region: string): Promise<boolean> {
        const ec2 = await this.ec2(region);

        const response: DescribeRegionsCommandOutputEC2 = await this.request(() => {
            return ec2.send(
                new DescribeRegionsCommandEC2({
                    AllRegions: true,
                    Filters: [
                        {
                            Name: 'opt-in-status',
                            Values: ['opt-in-not-required'],
                        },
                    ],
                }),
            );
        }, this.pingV2.name);

        const regionResponse = this.get(response, 'Regions').map((r: { RegionName: string }) => {
            return r.RegionName;
        });

        if (!isEmpty(regionResponse)) {
            return Promise.resolve(true);
        }

        return Promise.reject();
    }

    async codeCommitPing(metadata: ConnectionMetadata): Promise<boolean> {
        const params = {
            RoleName: config.get('aws.codecommit.roleName'),
        };
        const iam = await this.iam();
        const role = await iam.getRole(params);

        this.logger.log(
            PolloMessage.msg('Checking role...').setIdentifier({
                key: metadata.key,
                roleArn: role.Role?.Arn,
            }),
        );

        if (isNil(role) || role.Role?.Arn !== metadata.key) {
            const message = `Role: ${role.Role?.Arn} does not match: ${metadata.key}`;
            this.logger.error(PolloMessage.msg(message).setIdentifier({ metadata, role }));
            throw new Error(message);
        }

        const cc = await this.codeCommit(config.get('aws.codecommit.defaultRegion'));

        const repositories = await cc.listRepositories();
        return !isNil(repositories);
    }

    async listOrganizationalUnits(
        params: ListOrganizationalUnitsForParentCommandInput,
    ): Promise<OrganizationalUnits> {
        const response = await this.request(async () => {
            const awsOrganizationsClient = await this.clientBuilder.client(Organizations).build();
            return awsOrganizationsClient.listOrganizationalUnitsForParent(params);
        }, this.listOrganizationalUnits.name);

        return new OrganizationalUnits(response);
    }

    async listTagsForResource(
        params: ListTagsForResourceCommandInputOrganizations,
    ): Promise<OrganizationalTags> {
        let tags = null;
        const response = await this.request(async () => {
            const awsOrganizationsClient = await this.clientBuilder.client(Organizations).build();
            return awsOrganizationsClient.listTagsForResource(params);
        }, this.listTagsForResource.name);

        tags = new OrganizationalTags(response);
        return tags;
    }

    async listOrganizationalUnitAccounts(
        params: ListAccountsForParentCommandInputOrganizations,
    ): Promise<OrganizationalUnitAccounts> {
        const response: ListAccountsForParentCommandOutputOrganizations = await this.request(
            async () => {
                const awsOrganizationsClient = await this.clientBuilder
                    .client(Organizations)
                    .build();
                return awsOrganizationsClient.listAccountsForParent(params);
            },
            this.listOrganizationalUnitAccounts.name,
        );

        return new OrganizationalUnitAccounts(response);
    }

    async listAccounts(params: ListAccountsCommandInputOrganizations): Promise<SubAccounts> {
        let subAccounts: SubAccounts | null = null;
        const listAccountsResponse: ListAccountsCommandOutput = await this.request(async () => {
            const awsOrganizationsClient = await this.clientBuilder.client(Organizations).build();

            return awsOrganizationsClient.listAccounts(params);
        }, this.listAccounts.name);

        subAccounts = new SubAccounts(listAccountsResponse);
        return subAccounts;
    }

    async listAccountParent(
        params: ListParentsCommandInputOrganizations,
    ): Promise<{ parentId: string; parentType: string }> {
        const response: ListParentsCommandOutput = await this.request(async () => {
            const awsOrganizationsClient = await this.clientBuilder.client(Organizations).build();
            return awsOrganizationsClient.listParents(params);
        }, this.listAccountParent.name);

        const firstParent = response.Parents?.[0];

        return {
            parentId: firstParent?.Id ?? 'ROOT',
            parentType: firstParent?.Type ?? 'ROOT',
        };
    }

    async describeOrganizationalUnit(
        params: DescribeOrganizationalUnitCommandInputOrganizations,
    ): Promise<OrganizationalUnit> {
        const response = await this.request(async () => {
            const awsOrganizationsClient = await this.clientBuilder.client(Organizations).build();
            return awsOrganizationsClient.describeOrganizationalUnit(params);
        }, this.describeOrganizationalUnit.name);

        return new OrganizationalUnit(response);
    }

    async listUsers(
        client: AwsClientBuilder,
        maxResults: number,
        nextPageToken: string,
        accessReviewEntitlementValue = false,
    ): Promise<{ data: User[]; token: string }> {
        const response = await this.request(async () => {
            const iamClient = await client.client(IAM).build();

            return iamClient.getAccountAuthorizationDetails({
                MaxItems: maxResults,
                Marker: isNil(nextPageToken) ? undefined : nextPageToken,
                Filter: ['User', 'Group'],
            });
        }, this.listUsers.name);

        const users = response?.UserDetailList ?? [];
        const groupMap = arrayToMap<string, GroupDetail>(
            response?.GroupDetailList ?? [],
            'GroupName',
        );

        const data: User[] = users
            .map(user => {
                const rawAccessData = this.getAccessData(
                    user,
                    groupMap,
                    accessReviewEntitlementValue,
                );
                return {
                    id: user.UserId,
                    email: null,
                    hasMfa: false,
                    name: user.UserName,
                    raw: user,
                    serviceAccount: false,
                    tags: this.tagList(user.UserId, user.Tags),
                    accessData: {
                        clientType: this.getClientType(),
                        permissionLinkIdentifiers: [client?.region ?? 'global', user?.UserName],
                        raw: rawAccessData,
                        groups: rawAccessData.groups
                            .slice(0, UserAccessData.MAX_GROUP_LENGTH)
                            .map(group => group.name),
                    },
                    isAdmin:
                        rolesHasAdminAccessValue(rawAccessData.groups) ||
                        rolesHasAdminAccessValue(rawAccessData.policies),
                    resourceArn: user.Arn,
                };
            })
            .filter((user: { tags: TagCollection }) => this.doesNotHaveTag(user, ApiExclusion.TAG));

        const token = isNil(response) ? null : response.IsTruncated ? response.Marker : null;

        return { data, token };
    }

    async listTagsByResource(
        client: AwsClientBuilder,
        arn: string,
        region: string,
    ): Promise<Tag[]> {
        const ssoClient = await client.client(SSOAdmin).setRegion(region).build();

        const tagsArray = await ssoClient.listTagsForResource({ ResourceArn: arn });

        if (!isNil(tagsArray?.Tags)) {
            return tagsArray.Tags;
        }

        return [];
    }

    async getIdentityCenterId(
        client: AwsClientBuilder,
        region: string,
        maxResults = 1,
    ): Promise<IIdentityCenterInstance[]> {
        const ssoClient = await client.client(SSOAdmin).setRegion(region).build();

        const responseInstances: ListInstancesCommandOutputSSOAdmin = await ssoClient.listInstances(
            { MaxResults: maxResults },
        );

        const instances = responseInstances?.Instances;

        if (!Array.isArray(instances) || isEmpty(instances)) {
            return [];
        }

        const activeIdentityStoreInstances =
            instances?.filter(instance => instance.Status === InstanceStatus.ACTIVE) ?? [];

        const instancesFiltered: IIdentityCenterInstance[] = [];
        activeIdentityStoreInstances.forEach(instance => {
            const { IdentityStoreId, OwnerAccountId, InstanceArn } = instance;
            if (!isNil(IdentityStoreId) && !isNil(OwnerAccountId) && !isNil(InstanceArn)) {
                instancesFiltered.push({
                    IdentityStoreId,
                    InstanceArn,
                    OwnerAccountId,
                });
            }
        });

        return instancesFiltered;
    }

    async isScimEndpointAvailable(scimProps: IScimProps): Promise<IEndpointAvailableResponse> {
        let logMessage = '';
        const { endpoint, token } = scimProps;
        try {
            const response = await this.getPromiseByScimEndpoint({
                endpoint: `${endpoint}/Users`,
                token,
            });

            const validResponse = !isNil(response) && response?.status === HttpStatus.OK;

            logMessage = `AWS IC: Scim endpoint ${
                validResponse ? 'available' : 'unavailable due to empty response'
            }`;
            return { available: validResponse, log: logMessage };
        } catch (error) {
            const authErrors = [HttpStatus.UNAUTHORIZED, HttpStatus.FORBIDDEN];
            const codeError = parseInt(error?.response?.status);
            const responseErrorProps = {
                available: false,
                code: codeError,
                error,
            };
            const errorMap = {
                [HttpStatus.FORBIDDEN]: ErrorCode.AWS_IDENTITY_CENTER_FORBIDDEN,
                [HttpStatus.UNAUTHORIZED]: ErrorCode.AWS_IDENTITY_CENTER_UNAUTHORIZED,
            };
            if (authErrors.includes(codeError)) {
                logMessage = `AWS IC: Scim endpoint failed due to authorization problems with status code ${codeError}`;

                return {
                    ...responseErrorProps,
                    log: logMessage,
                    code: errorMap[codeError],
                };
            }
            logMessage = 'AWS IC: Service unavailable';
            return {
                ...responseErrorProps,
                log: logMessage,
                code: ErrorCode.AWS_IDENTITY_CENTER_SERVICE_UNAVAILABLE,
            };
        }
    }

    getEmailFromIdentityCenterUser(user: UserIdentityStore): string {
        const { Emails } = user;
        if (!Array.isArray(Emails)) {
            return '';
        }
        return Emails.find(email => email?.Primary)?.Value ?? '';
    }

    mapIdentityCenterUsers(identityCenterUsers: UserIdentityStore[]): User[] {
        return identityCenterUsers.map(user => {
            // Debugging log for https://drata.atlassian.net/browse/ENG-70261
            this.logInfo(
                `Mapping user ${user.UserName} from Identity Center`,
                this.mapIdentityCenterUsers.name,
                {
                    userId: user.UserId,
                    externalIds: user?.ExternalIds ?? [],
                },
            );

            return {
                id: user.UserId,
                email: this.getEmailFromIdentityCenterUser(user),
                hasMfa: false,
                name: user.UserName,
                raw: user,
                // This value is false by default, given that AWS doesn't provide any way to get this information. Drata Admin can change this value manually from App
                serviceAccount: false,
                firstName: get(user, 'Name.GivenName', null),
                lastName: get(user, 'Name.FamilyName', null),
                tags: {},
                accessData: new UserAccessData(),
                idpId: get(user, 'ExternalIds[0].Id', null),
            } as User;
        });
    }

    getPromiseByScimEndpoint({ endpoint, token }: IScimProps): Promise<AxiosResponse<any, any>> {
        return axios.get(endpoint, {
            headers: {
                Authorization: `Bearer ${token}`,
            },
        });
    }

    async identityCenterUsersWithStatus(
        users: User[],
        identityCenterLogging: IIdentityCenterLogging,
        scimProps: IScimProps,
    ): Promise<User[]> {
        const promisesUserWithStatus = users?.map(async user => {
            const response = await this.getPromiseByScimEndpoint({
                endpoint: `${scimProps?.endpoint}/Users/<USER>
                token: scimProps?.token,
            });
            const userStatus = response?.data?.active;
            this.logger.log(
                PolloAdapter.cxt(
                    `user with ID ${user?.id} got status ${userStatus} from SCIM`,
                    this.loggingContext,
                ).setSubContext(this.identityCenterUsersWithStatus.name),
            );
            return {
                ...user,
                active: userStatus as boolean,
            };
        });

        const promisesSettled = (await Promise.allSettled(promisesUserWithStatus)).filter(
            promise => {
                const isPromiseFulfilled = promise?.status === SettledStatus.FULFILLED;
                if (!isPromiseFulfilled) {
                    this.logger.log(
                        PolloAdapter.cxt(
                            `Identity Center user failed settling the promise`,
                            this.loggingContext,
                        )
                            .setError(promise?.reason)
                            .setSubContext(this.identityCenterUsersWithStatus.name),
                    );
                }
                return isPromiseFulfilled;
            },
        );

        return promisesSettled?.map(
            promiseResolved => (promiseResolved as PromiseFulfilledResult<User>)?.value,
        );
    }

    hasValidSCIMProps(scimProps: IScimProps, region: string, IdentityStoreId: string): boolean {
        const { endpoint, token } = scimProps;
        return !isNil(endpoint) && !isNil(token) && !isEmpty(region) && !isEmpty(IdentityStoreId);
    }

    async getIdentityCenterUsers({
        client,
        maxResults,
        nextPageToken,
        region,
        IdentityStoreId,
    }: IGetIdentityCenterUsers): Promise<{
        users: UserIdentityStore[];
        token: string | null;
    }> {
        const identityStoreClient = await client
            .client(IdentitystoreClient)
            .setRegion(region)
            .build();

        const response: ListUsersCommandOutputIdentityStore = await identityStoreClient.send(
            new ListUsersCommandIdentityStore({
                IdentityStoreId,
                MaxResults: maxResults,
                NextToken: isNil(nextPageToken) ? undefined : nextPageToken,
            }),
        );

        return {
            users: get(response, 'Users', []),
            token: response?.NextToken ?? null,
        };
    }

    listIdentityCenterUsersWithStatus({
        usersResponse,
        loggingContext,
        scimProps,
        accountId,
    }: IIdentityCenterWithStatus) {
        const data: User[] = this.mapIdentityCenterUsers(usersResponse);
        return this.identityCenterUsersWithStatus(
            data,
            { loggingContext, accountId } as IIdentityCenterLogging,
            scimProps,
        );
    }

    async listIdentityCenterUsers({
        client,
        maxResults,
        nextPageToken,
        region,
        IdentityStoreId,
        loggingContext,
        scimProps,
    }: IListIdentityCenterUsers): Promise<{
        data: User[];
        token: string | null;
    }> {
        const accountId = await this.getAccountId();
        const icPropsValid = this.hasValidSCIMProps(scimProps, region, IdentityStoreId);

        if (!icPropsValid) {
            const { token, endpoint } = scimProps;
            const tokenLength = token?.length;
            const reducedToken = token?.slice(0, Math.floor(tokenLength * 0.4));
            const maskedLength = isNumber(tokenLength) ? Math.floor(tokenLength * 0.9) : undefined;
            this.logger.log(
                PolloAdapter.cxt(
                    'Some properties of the SCIM AWS Connection are incorrect. Please review the configuration.',
                    loggingContext,
                )
                    .setIdentifier({
                        scimEndpoint: maskString(endpoint, maskedLength),
                        scimToken: maskString(reducedToken, maskedLength),
                        region: maskString(region, maskedLength),
                        IdentityStoreId: maskString(IdentityStoreId, maskedLength),
                    })
                    .setContext(this.constructor.name)
                    .setSubContext(this.listIdentityCenterUsers.name),
            );
            return { data: [], token: null };
        }

        const icUsersResponse = await this.getIdentityCenterUsers({
            client,
            maxResults,
            nextPageToken,
            region,
            IdentityStoreId,
        });
        if (
            isNil(icUsersResponse) ||
            !Array.isArray(icUsersResponse?.users) ||
            isEmpty(icUsersResponse?.users)
        ) {
            this.logger.log(
                PolloAdapter.cxt(
                    'No users retrieved from Identity Centers, returning empty object',
                    loggingContext,
                ).setSubContext(this.listIdentityCenterUsers.name),
            );
            return { data: [], token: null };
        }

        const usersWithStatus =
            (await this.listIdentityCenterUsersWithStatus({
                usersResponse: icUsersResponse?.users,
                loggingContext,
                scimProps,
                accountId,
            })) ?? [];

        return {
            data: usersWithStatus?.filter(user => user.active),
            token: icUsersResponse?.token,
        };
    }

    async getLoginProfile(
        client: AwsClientBuilder,
        username: string,
        loggingContext: LoggingContext,
    ): Promise<boolean> {
        try {
            await this.request(async () => {
                const iamClient = await client.client(IAMClient).build();

                return iamClient.send(new GetLoginProfileCommand({ UserName: username }));
            }, this.getLoginProfile.name);
        } catch (error) {
            this.logger.log(
                PolloAdapter.cxt(
                    'Something went wrong when attempting to retrieve login profile',
                    loggingContext,
                ).setIdentifier({
                    username,
                    status: get(error, '$metadata.httpStatusCode', get(error, 'statusCode', null)),
                    code: get(error, 'name', get(error, 'code', null)),
                }),
            );

            const status = get(error, '$metadata.httpStatusCode', get(error, 'statusCode', null));
            if (status === HttpStatus.NOT_FOUND) {
                return Promise.resolve(false);
            }
        }
        return Promise.resolve(true);
    }

    async listMfaDevices(
        clientBuilder: AwsClientBuilder,
        username: string,
        maxResults?: number,
        nextPageToken?: string,
    ): Promise<InfrastructureMfaDeviceType> {
        const details = await this.request(async () => {
            const iamClient = await clientBuilder.client(IAMClient).build();
            return iamClient.send(
                new ListMFADevicesCommand({
                    MaxItems: maxResults,
                    Marker: nextPageToken,
                    UserName: username,
                }),
            );
        }, this.listMfaDevices.name);

        return {
            username,
            devices: get(details, 'MFADevices', null),
            nextPageToken: get(details, 'Marker', null),
            isTruncated: get(details, 'IsTruncated', false),
        };
    }

    async listRegions(
        includeOptedInRegions = true,
        region = config.get('aws.s3.region'),
    ): Promise<string[]> {
        const OptInStatusFilter = {
            Name: 'opt-in-status',
            Values: ['opt-in-not-required'],
        };

        if (includeOptedInRegions) {
            OptInStatusFilter.Values.push('opted-in');
        }
        const response = await this.request(async () => {
            const ec2Client = await this.ec2(region);

            return ec2Client.send(
                new DescribeRegionsCommandEC2({ AllRegions: true, Filters: [OptInStatusFilter] }),
            );
        }, this.listRegions.name);

        const regions = this.get(response, 'Regions').map((regionItem: { RegionName: string }) => {
            return regionItem.RegionName;
        });

        const accountId = await this.getAccountId();
        const filteredRegions = regions.filter(
            (regionItem: string) => regionItem !== 'ap-northeast-3',
        );

        this.logger.log(
            PolloAdapter.cxt(`List regions for accountId: ${accountId}`, this.loggingContext)
                .setIdentifier({
                    regions: filteredRegions,
                    includeOptedInRegions,
                })
                .setContext(this.constructor.name)
                .setSubContext(this.listRegions.name),
        );

        return filteredRegions;
    }

    async listAlbResourcesByAcl(region: string, acl: string): Promise<Waffable[]> {
        const response = await this.request(async () => {
            const wafv2Client = await this.wafV2(this.getRegion(region));
            return wafv2Client.send(
                new ListResourcesForWebACLCommand({
                    WebACLArn: acl,
                    ResourceType: 'APPLICATION_LOAD_BALANCER',
                }),
            );
        }, this.listAlbResourcesByAcl.name);

        return this.parseWaffableList(response);
    }

    async listApiResourcesByAcl(region: string, acl: string): Promise<Waffable[]> {
        const response = await this.request(async () => {
            const wafv2Client = await this.wafV2(this.getRegion(region));

            return wafv2Client.send(
                new ListResourcesForWebACLCommand({ WebACLArn: acl, ResourceType: 'API_GATEWAY' }),
            );
        }, this.listApiResourcesByAcl.name);

        return this.parseWaffableList(response);
    }

    async listSecurityGroups(
        region: string,
        maxResults: number,
        nextPageToken: string,
        filters: FilterType[],
    ): Promise<TokenPaginationResponse<SecurityGroup>> {
        const response = await this.request(async () => {
            const ec2Client = await this.ec2(region);

            return ec2Client.send(
                new DescribeSecurityGroupsCommandEC2({
                    MaxResults: maxResults,
                    NextToken: nextPageToken,
                    Filters: this.buildFilters(filters),
                }),
            );
        }, this.listSecurityGroups.name);

        const nextToken = get(response, 'NextToken', null);
        const securityGroups = get(response, 'SecurityGroups', []);
        const page = [];

        for (const group of securityGroups) {
            const securityGroup = new SecurityGroup();

            securityGroup.id = group.GroupId;
            securityGroup.name = group.GroupName;
            securityGroup.description = group.Description;
            securityGroup.dimension = group.GroupName;
            securityGroup.vpcId = group.VpcId;
            securityGroup.region = region;
            securityGroup.raw = group;
            securityGroup.tags = this.tagList(group.GroupName, get(group, 'Tags', null));

            for (const permission of group.IpPermissions) {
                const ipv4 = [];
                const ipv6 = [];

                let fromPort = null;
                let toPort = null;

                for (const range of permission.IpRanges) {
                    ipv4.push(range.CidrIp);
                }

                for (const range of permission.Ipv6Ranges) {
                    ipv6.push(range.CidrIpv6);
                }

                if (has(permission, 'FromPort')) {
                    fromPort = permission.FromPort;
                }

                if (has(permission, 'ToPort')) {
                    toPort = permission.ToPort;
                }

                /**
                 * https://drata.atlassian.net/browse/ENG-1306
                 */
                const isInternal =
                    !isEmpty(get(permission, 'UserIdGroupPairs', [])) &&
                    isEmpty(get(permission, 'PrefixListIds', [])) &&
                    isEmpty(ipv4) &&
                    isEmpty(ipv6);

                securityGroup.permissions.push({
                    protocol: permission.IpProtocol,
                    fromPort,
                    toPort,
                    ipv4,
                    ipv6,
                    isInternal,
                });
            }

            page.push(securityGroup);
        }

        return tokenize(
            /**
             * Exclude auto-generated Security Groups from EKS
             */
            page.map((group: any) => {
                const tag = this.getTag(group, 'kubernetes.io/cluster');

                if (!isNil(tag)) {
                    const exclusion = new ApiExclusion();
                    exclusion.tag = tag.key;
                    exclusion.reason = String(tag.value);
                    group.exclusion = exclusion;
                }

                return group;
            }),
            nextToken,
        );
    }

    async listNetworkInterfaces(
        region: string,
        maxResults: number,
        nextPageToken: string,
        filters: FilterType[],
    ): Promise<TokenPaginationResponse<NetworkInterface>> {
        const response = await this.request(async () => {
            const ec2Client = await this.ec2(region);

            return ec2Client.send(
                new DescribeNetworkInterfacesCommand({
                    MaxResults: maxResults,
                    NextToken: nextPageToken,
                    Filters: this.buildFilters(filters),
                }),
            );
        }, this.listNetworkInterfaces.name);
        const nextToken = response.NextToken ?? null;

        const page = (response.NetworkInterfaces ?? []).map((network: any) => {
            return {
                id: network.NetworkInterfaceId,
                name: null,
                description: network.Description,
                groupIds: network.Groups?.map((group: any) => group.GroupId),
                region,
                raw: network,
                tags: this.tagList(network.NetworkInterfaceId, get(network, 'TagSet', [])),
            };
        });

        return tokenize(page, nextToken);
    }

    async listDatabases(
        region: string,
        maxResults: number,
        nextPageToken: string | null,
    ): Promise<TokenPaginationResponse<Database>> {
        const response = await this.request(async () => {
            const rdsClient = await this.rds(region, 240000);
            return rdsClient.send(
                new DescribeDBInstancesCommand({
                    MaxRecords: maxResults,
                    Marker: nextPageToken ?? undefined,
                }),
            );
        }, this.listDatabases.name);

        const page = (response.DBInstances ?? []).map((database: any) => {
            return {
                id: database.DbiResourceId,
                name: database.DBInstanceIdentifier,
                dimension: database.DBInstanceIdentifier,
                isEncrypted: Boolean(database.StorageEncrypted),
                isMultiAz: Boolean(database.MultiAZ),
                isBackedUp:
                    (database.BackupRetentionPeriod ?? 0) > 0 ||
                    !isEmpty(get(database, 'ReadReplicaSourceDBInstanceIdentifier', [])),
                engine: get(database, 'Engine', null),
                instanceClass: get(database, 'DBInstanceClass', null),
                clusterId: get(database, 'DBClusterIdentifier', null),
                resourceType: get(database, 'Engine', null),
                region,
                raw: database,
                tags: this.tagList(database.DBInstanceIdentifier, get(database, 'TagList', null)),
            };
        });

        return tokenize(page, isNil(response) ? null : response.Marker);
    }

    async listDatabaseClusters(
        region: string,
        maxResults: number,
        nextPageToken: string,
    ): Promise<any> {
        const response = await this.request(() => {
            return this.getRDSClusters(region, maxResults, nextPageToken);
        }, this.listDatabaseClusters.name);

        const page = (response.DBClusters ?? []).map((databaseCluster: any) => {
            return {
                id: databaseCluster.DBClusterIdentifier,
                name: databaseCluster.DBClusterIdentifier,
                dimension: databaseCluster.DBClusterIdentifier,
                isEncrypted: Boolean(databaseCluster.StorageEncrypted),
                isMultiAz: this.isMultiAzCluster(databaseCluster),
                isBackedUp: has(databaseCluster, 'EarliestRestorableTime'),
                engine: get(databaseCluster, 'Engine', null),
                instanceClass: get(databaseCluster, 'DBClusterInstanceClass', null),
                resourceType: get(databaseCluster, 'Engine', null),
                tags: this.tagList(
                    databaseCluster.DBClusterIdentifier,
                    get(databaseCluster, 'TagList', null),
                ),
                nodes: (databaseCluster.DBClusterMembers ?? []).map(database => {
                    return {
                        id: database.DBInstanceIdentifier,
                        name: database.DBInstanceIdentifier,
                        dimension: database.DBInstanceIdentifier,
                        isEncrypted: Boolean(databaseCluster.StorageEncrypted),
                        isMultiAz: Boolean(databaseCluster.MultiAZ),
                        isBackedUp: (databaseCluster.BackupRetentionPeriod ?? 0) > 0,
                        clusterId: databaseCluster.DBClusterIdentifier,
                        engine: get(databaseCluster, 'Engine', null),
                        instanceClass: get(databaseCluster, 'DBClusterInstanceClass', null),
                        resourceType: get(databaseCluster, 'Engine', null),
                        region,
                        raw: database,
                    };
                }),
                region,
                raw: databaseCluster,
            };
        });

        return tokenize(page, isNil(response) ? null : response.Marker);
    }

    async listElasticacheClusters(
        region: string,
        maxResults: number,
        nextPageToken: string,
    ): Promise<TokenPaginationResponse<Cache>> {
        const response: DescribeCacheClustersCommandOutput = await this.request(async () => {
            const elastiCacheClient = await this.elastiCache(region);
            return elastiCacheClient.send(
                new DescribeCacheClustersCommand({
                    MaxRecords: maxResults,
                    Marker: nextPageToken,
                    ShowCacheNodeInfo: true,
                    ShowCacheClustersNotInReplicationGroups: false,
                }),
            );
        }, this.listElasticacheClusters.name);

        /**
         * We are looking to find single-node instances here so filter out all instances
         * that are a member of a replication group - we will gather those stats in V2.
         *
         * We are filtering out memcached clusters since they can't be encrypted at rest.
         */
        const page = (response?.CacheClusters ?? [])
            .filter(cluster => {
                return (
                    isNil(get(cluster, 'ReplicationGroupId', null)) &&
                    get(cluster, 'Engine') !== 'memcached'
                );
            })
            .map((cluster: any) => {
                return {
                    id: get(cluster, 'CacheClusterId', null),
                    name: get(cluster, 'CacheClusterId', null),
                    description: get(cluster, 'CacheClusterId', null),
                    dimension: get(cluster, 'ARN', null),
                    resourceType: get(cluster, 'Engine', AwsConstants.ELASTICACHE),
                    isEncrypted: get(cluster, 'AtRestEncryptionEnabled', false),
                    isMultiAz: get(cluster, 'PreferredAvailabilityZone', null) === 'Multiple',
                    isBackedUp: get(cluster, 'SnapshotRetentionLimit', 0) > 0,
                    region,
                    raw: cluster,
                };
            });

        return tokenize(page, isNil(response.Marker) ? null : response.Marker);
    }

    async listElasticacheGroups(
        region: string,
        maxResults: number,
        nextPageToken: string,
    ): Promise<TokenPaginationResponse<Cache>> {
        const response = await this.request(async () => {
            const elastiCacheClient = await this.elastiCache(region);

            return elastiCacheClient.send(
                new DescribeReplicationGroupsCommand({
                    MaxRecords: maxResults,
                    Marker: nextPageToken,
                }),
            );
        }, this.listElasticacheGroups.name);

        const page = (response.ReplicationGroups ?? []).map((group: any) => {
            const zones = (group.NodeGroups ?? []).map((nodes: any) => {
                return (nodes.NodeGroupMembers ?? []).map((member: any) => {
                    return member.PreferredAvailabilityZone;
                });
            });

            const multiAzEnabled = compareLowerCase(group.MultiAZ ?? '', AwsConstants.ENABLED);

            let isMultiAz = false;

            if (!isEmpty(zones)) {
                isMultiAz = uniq(zones[0]).length > 1;
            }

            return {
                id: get(group, 'ReplicationGroupId', null),
                name: get(group, 'ReplicationGroupId', null),
                description: get(group, 'Description', null),
                dimension: get(group, 'ARN', null),
                resourceType: get(group, 'Engine', AwsConstants.ELASTICACHE),
                isEncrypted: get(group, 'AtRestEncryptionEnabled', null),
                isMultiAz: multiAzEnabled || isMultiAz,
                isBackedUp: get(group, 'SnapshotRetentionLimit', 0) > 0,
                region,
                raw: group,
            };
        });

        return tokenize(page, isNil(response?.Marker) ? null : response.Marker);
    }

    async getElasticacheTags(
        region: string,
        cache: Cache,
    ): Promise<ListTagsForResourceCommandOutputElasticache | null> {
        const arn = cache.dimension;

        const isActive =
            compareLowerCase(get(cache, 'raw.Status', null), AwsConstants.AVAILABLE) ||
            compareLowerCase(get(cache, 'raw.CacheClusterStatus', null), AwsConstants.AVAILABLE);

        let response = null;

        if (isActive) {
            response = await this.request(async () => {
                const elastiCacheClient = await this.elastiCache(region);
                return elastiCacheClient.send(
                    new ListTagsForResourceCommandElasticache({ ResourceName: arn }),
                );
            }, this.getElasticacheTags.name);
        }

        return response;
    }

    async listESClusters(region: string): Promise<Indexer[]> {
        let data = [];

        const domains: ListDomainNamesCommandOutput = await this.request(async () => {
            const esClient = await this.es(region);
            return esClient.send(new ListDomainNamesCommand());
        }, this.listESClusters.name);

        if (!isEmpty(domains.DomainNames)) {
            const names = domains.DomainNames?.map((domain: { DomainName: string }) => {
                return domain.DomainName;
            });

            const batchSize = 5;
            let batch = [];
            let start = 0;
            let end = batchSize;

            do {
                batch = slice(names, start, end);

                // eslint-disable-next-line no-await-in-loop
                const response: DescribeElasticsearchDomainsCommandOutput = await this.request(
                    async () => {
                        const esClient = await this.es(region);

                        return esClient.send(
                            new DescribeElasticsearchDomainsCommand({ DomainNames: batch }),
                        );
                    },
                    this.listESClusters.name,
                );

                const page = (response.DomainStatusList ?? []).map(
                    (indexer: ElasticsearchDomainStatus) => {
                        return {
                            id: indexer.DomainId,
                            name: indexer.DomainName,
                            dimension: indexer.ARN,
                            resourceType: AwsConstants.ELASTICSEARCH,
                            isEncrypted: indexer?.EncryptionAtRestOptions?.Enabled ?? false,
                            isMultiAz:
                                indexer?.ElasticsearchClusterConfig?.ZoneAwarenessEnabled ?? false,
                            region,
                            raw: indexer,
                        };
                    },
                );

                data = concat(data, page);

                start = start + batchSize;
                end = end + batchSize;
            } while (batch.length > 0);
        }

        return data;
    }

    async getAccountId(): Promise<string> {
        const response = await this.request(async () => {
            const stsClient = await this.sts();
            return stsClient.send(new GetCallerIdentityCommand({}));
        }, this.getAccountId.name);

        return response.Account;
    }

    async listRootEventsInUsEast1(
        maxResults: number,
        nextPageToken: string,
        startTime: Date,
        endTime: Date,
    ): Promise<TokenPaginationResponse<Event>> {
        const response = await this.request(async () => {
            const cloudTrailClient = await this.cloudTrailWithRegion(
                config.get('aws.region.default'),
            );
            return cloudTrailClient.send(
                new LookupEventsCommand({
                    LookupAttributes: [
                        {
                            AttributeKey: 'Username',
                            AttributeValue: 'root',
                        },
                    ],
                    StartTime: startTime,
                    EndTime: endTime,
                    MaxResults: maxResults,
                    NextToken: nextPageToken,
                }),
            );
        }, this.listRootEventsInUsEast1.name);

        const page = (response?.Events ?? []).map((event: EventCloudTrail) => {
            return {
                id: event.EventId,
                name: event.EventName,
                raw: event,
            };
        });

        return tokenize(page, get(response, 'NextToken', null));
    }

    async getCloudFrontDistributions(
        maxResults: number,
        nextPageToken: string,
        region?: string,
    ): Promise<ListDistributionsCommandOutputCloudFront> {
        return this.request(async () => {
            const cloudFrontClient = region
                ? await this.cloudFrontWithRegion(region)
                : await this.cloudFront();
            return cloudFrontClient.send(
                new ListDistributionsCommandCloudFront({
                    MaxItems: maxResults,
                    Marker: nextPageToken,
                }),
            );
        }, this.getCloudFrontDistributions.name);
    }

    async getCloudFrontTags(arn: string): Promise<ListTagsForResourceCommandOutputCloudFront> {
        return this.request(async () => {
            const cloudFrontClient = await this.cloudFront();
            return cloudFrontClient.send(
                new ListTagsForResourceCommandCloudFront({ Resource: arn }),
            );
        }, this.getCloudFrontTags.name);
    }

    async listRuleTags(region: string, id: string, arn: string): Promise<TagCollection> {
        const tag = new TagCollection();
        tag.dimension = id;

        const response = await this.request(async () => {
            const eventBridgeClient = await this.eventBridge(this.getRegion(region));

            return eventBridgeClient.send(
                new ListTagsForResourceCommandEventBridge({ ResourceARN: arn }),
            );
        }, this.listRuleTags.name);

        if (!isNil(response)) {
            const tagList = get(response, 'Tags', []);

            for (const t of tagList) {
                tag.tags.push({ key: t.Key, value: t.Value });
            }
        }

        return tag;
    }

    async listSqsQueues(
        region: string,
        maxResults: number,
        nextPageToken: string,
    ): Promise<TokenPaginationResponse<Queue>> {
        const response = await this.request(async () => {
            const sqsClient = await this.sqs(region);
            return sqsClient.send(
                new ListQueuesCommand({ MaxResults: maxResults, NextToken: nextPageToken }),
            );
        }, this.listSqsQueues.name);

        const page = get(response, 'QueueUrls', []).map((queue: any) => {
            return {
                id: getLastSegmentFromUrl(queue),
                name: getLastSegmentFromUrl(queue),
                description: queue,
                dimension: getLastSegmentFromUrl(queue),
                resourceType: AwsConstants.SQS,
                region,
                raw: queue,
            };
        });

        return tokenize(page, isNil(response) ? null : response.NextToken);
    }

    async getBucketLifecycleConfiguration(
        bucketName: string,
    ): Promise<AwsBucketLifeCycleConfiguration> {
        const s3Client = await this.s3();

        const response = await this.request(() => {
            return s3Client.send(
                new GetBucketLifecycleConfigurationCommand({ Bucket: bucketName }),
            );
        }, this.getBucketLifecycleConfiguration.name);

        return new AwsBucketLifeCycleConfiguration(response.Rules);
    }

    async getS3BucketTags(name: string, owner?: string): Promise<GetBucketTaggingCommandOutput> {
        try {
            const s3client = await this.s3();

            return await this.request(() => {
                return s3client.send(
                    new GetBucketTaggingCommand({ Bucket: name, ExpectedBucketOwner: owner }),
                );
            }, this.getS3BucketTags.name);
        } catch (error) {
            if (has(error, 'message') && error.message.includes(AwsConstants.NO_TAG_SET)) {
                return { TagSet: [], $metadata: {} };
            } else {
                throw error;
            }
        }
    }

    async listPolicyVersion(policyArn: string): Promise<ListPolicyVersionsCommandOutput> {
        const iam = await this.clientBuilder.client(IAMClient).build();

        return this.request(() => {
            return iam.send(new ListPolicyVersionsCommand({ PolicyArn: policyArn }));
        }, this.listPolicyVersion.name);
    }

    async getPolicyVersion(
        policyArn: string,
        versionId: string,
    ): Promise<GetPolicyVersionCommandOutput> {
        const iam = await this.clientBuilder.client(IAMClient).build();

        return this.request(() => {
            return iam.send(
                new GetPolicyVersionCommand({ PolicyArn: policyArn, VersionId: versionId }),
            );
        }, this.getPolicyVersion.name);
    }

    getESClusterTags(region: string, arn: string): Promise<ListTagsCommandOutputES> {
        return this.request(async () => {
            const esClient = await this.es(region);
            return esClient.send(new ListTagsCommandES({ ARN: arn }));
        }, this.getESClusterTags.name);
    }

    listMetricAlarms(
        region: string,
        type: Metric,
        namespace: MetricNamespace,
        instanceId: string,
        subInstanceId: string,
        engine: string,
        instanceClass: string,
    ): Promise<DescribeAlarmsForMetricCommandOutput> {
        let dimension = null;
        let targetNamespace = null;
        let targetMetric = null;

        const target = AwsMetricFactory.getMetric(type, namespace, engine, instanceClass);

        if (!isNil(target)) {
            dimension = target.dimension;
            targetNamespace = target.namespace;
            targetMetric = target.metric;
        } else {
            dimension = this.getInstanceKey(namespace);
            targetNamespace = this.getNamespaceString(namespace);
            targetMetric = this.getMetricString(type);
        }

        const params: DescribeAlarmsForMetricCommandInput = {
            Namespace: targetNamespace,
            MetricName: targetMetric,
            Dimensions: [],
        };

        /**
         * See if an instance has been passed in - if so set the dimension
         */
        if (!isNil(instanceId)) {
            params.Dimensions.push({ Name: dimension, Value: instanceId });
        }

        /**
         * See if an sub-instance has been passed in - if so set the dimension
         */
        if (!isNil(subInstanceId)) {
            /**
             * This needs to be updated - poor magic string whiff ...
             */
            params.Dimensions.push({
                Name: 'ServiceName',
                Value: subInstanceId,
            });
        }

        return this.request(async () => {
            const cloudWatchClient = await this.cloudWatch(region);
            return cloudWatchClient.send(new DescribeAlarmsForMetricCommand(params));
        }, this.listMetricAlarms.name);
    }

    listSubscriptions(
        region: string,
        arn: string,
        nextPageToken: string,
    ): Promise<ListSubscriptionsByTopicCommandOutput> {
        this.logger.log(
            PolloMessage.msg('Listing SNS subscriptions for topic')
                .setIdentifier({ region, arn })
                .setContext(this.constructor.name)
                .setSubContext(this.listSubscriptions.name),
        );
        return this.request(async () => {
            const snsClient = await this.sns(region);

            return snsClient.send(
                new ListSubscriptionsByTopicCommand({ TopicArn: arn, NextToken: nextPageToken }),
            );
        }, this.listSubscriptions.name);
    }

    listRules(region: string, nextPageToken: string): Promise<ListRulesCommandOutput> {
        return this.request(async () => {
            const eventBridgeClient = await this.eventBridge(region);

            return eventBridgeClient.send(new ListRulesCommand({ NextToken: nextPageToken }));
        }, this.listRules.name);
    }

    getSqsQueueTags(region: string, url: string): Promise<ListQueueTagsCommandOutput> {
        return this.request(async () => {
            const sqsClient = await this.sqs(region);

            return sqsClient.send(new ListQueueTagsCommand({ QueueUrl: url }));
        }, this.getSqsQueueTags.name);
    }

    listDetectors(
        region: string,
        maxResults: number,
        nextPageToken: string,
    ): Promise<ListDetectorsCommandOutput> {
        return this.request(async () => {
            const guardDutyClient = await this.guardDuty(region);
            return guardDutyClient.send(
                new ListDetectorsCommand({ MaxResults: maxResults, NextToken: nextPageToken }),
            );
        }, this.listDetectors.name);
    }

    getDetector(region: string, detectorId: string): Promise<GetDetectorCommandOutput> {
        return this.request(async () => {
            const guardDutyClient = await this.guardDuty(region);

            return guardDutyClient.send(new GetDetectorCommand({ DetectorId: detectorId }));
        }, this.getDetector.name);
    }

    getEc2Instances(
        region: string,
        maxResults: number,
        nextPageToken?: string,
        filters?: FilterType[],
    ): Promise<DescribeInstancesCommandOutputEC2> {
        const params = {
            MaxResults: maxResults,
            NextToken: nextPageToken,
        };

        if (!isEmpty(filters)) {
            (params as any).Filters = this.buildFilters(filters);
        }

        return this.request(async () => {
            const ec2Client = await this.ec2(region);
            return ec2Client.send(new DescribeInstancesCommand(params));
        }, this.getEc2Instances.name);
    }

    getRDSClusters(
        region: string,
        maxResults: number,
        nextPageToken: string,
    ): Promise<DescribeDBClustersCommandOutput> {
        return this.request(async () => {
            const rdsClient = await this.rds(region, 240000);

            return rdsClient.send(
                new DescribeDBClustersCommand({
                    MaxRecords: maxResults,
                    Marker: nextPageToken,
                }),
            );
        }, this.getRDSClusters.name);
    }

    describeEksCluster(region: string, name: string): Promise<DescribeClusterCommandOutputEKS> {
        return this.request(async () => {
            const eksClient = await this.eks(region);

            return eksClient.send(new DescribeClusterCommand({ name }));
        }, this.describeEksCluster.name);
    }

    listEcsTasks(
        region: string,
        maxResults: number,
        nextToken: string,
        cluster: string,
    ): Promise<ListTasksCommandOutputECS> {
        return this.request(async () => {
            const ecsClient = await this.ecs(region);

            return ecsClient.send(
                new ListTasksCommandECS({
                    maxResults: maxResults,
                    nextToken: nextToken,
                    cluster: cluster,
                }),
            );
        }, this.listEcsTasks.name);
    }

    describeEcsTasks(
        region: string,
        cluster: string | undefined,
        taskArns: string[] | undefined,
    ): Promise<DescribeTasksCommandOutputECS> {
        return this.request(async () => {
            const ecsClient = await this.ecs(region);

            return ecsClient.send(
                new DescribeTasksCommandECS({
                    cluster: cluster,
                    tasks: taskArns,
                    include: ['TAGS'],
                }),
            );
        }, this.describeEcsTasks.name);
    }

    getEcrRepositories(
        region: string,
        maxResults: number,
        nextPageToken: string,
    ): Promise<DescribeRepositoriesCommandOutput> {
        return this.request(async () => {
            const ecrClient = await this.ecr(region);
            return ecrClient.send(
                new DescribeRepositoriesCommand({
                    maxResults: maxResults,
                    nextToken: nextPageToken,
                }),
            );
        }, this.getEcrRepositories.name);
    }

    getEcrTags(arn: string, region: string): Promise<ListTagsForResourceCommandOutputECR> {
        return this.request(async () => {
            const ecrClient = await this.ecr(region);
            return ecrClient.send(new ListTagsForResourceCommandECR({ resourceArn: arn }));
        }, this.getEcrTags.name);
    }

    describeCloudTrailTrails(region: string): Promise<DescribeTrailsCommandOutput> {
        return this.request(async () => {
            const cloudTrailClient = await this.cloudTrail(region);
            return cloudTrailClient.send(new DescribeTrailsCommand());
        }, this.describeCloudTrailTrails.name);
    }

    getCloudTrailTags(region: string, resource: string): Promise<ListTagsCommandOutputCloudTrail> {
        return this.request(async () => {
            const cloudTrailClient = await this.cloudTrail(region);
            return cloudTrailClient.send(
                new ListTagsCommandCloudTrail({ ResourceIdList: [resource] }),
            );
        }, this.getCloudTrailTags.name);
    }

    getApiGatewaysV1(
        region: string,
        maxResults: number,
        nextPageToken: string,
    ): Promise<GetRestApisCommandOutput> {
        return this.request(async () => {
            const apiGatewayV1Client = await this.apiGatewayV1(region);
            return apiGatewayV1Client.getRestApis({
                limit: maxResults,
                position: nextPageToken,
            });
        }, this.getApiGatewaysV1.name);
    }

    getGraphqlApis(
        region: string,
        maxResults: number,
        nextPageToken: string,
    ): Promise<ListGraphqlApisCommandOutput> {
        return this.request(async () => {
            const appSyncClient = await this.appSync(region);
            return appSyncClient.listGraphqlApis({
                maxResults: AwsConstants.APPSYNC_MAX_RESULTS,
                nextToken: nextPageToken,
            });
        }, this.getGraphqlApis.name);
    }

    getCloudFrontDistributionTags(
        resource: string,
    ): Promise<ListTagsForResourceCommandOutputCloudFront> {
        return this.request(async () => {
            const cloudFrontClient = await this.cloudFrontWithRegion(
                config.get('aws.region.default'),
            );
            return cloudFrontClient.send(
                new ListTagsForResourceCommandCloudFront({
                    Resource: resource,
                }),
            );
        }, this.getCloudFrontDistributionTags.name);
    }

    acls(
        region: string,
        maxResults: number,
        nextPageToken: string,
    ): Promise<ListWebACLsCommandOutputWAF> {
        return this.request(async () => {
            return (await this.waf(region)).listWebACLs(
                this.buildWafParams(maxResults, nextPageToken),
            );
        }, this.acls.name);
    }

    regionalAcls(
        region: string,
        maxResults: number,
        nextPageToken: string,
    ): Promise<ListWebACLsCommandOutputWAFRegional> {
        return this.request(async () => {
            return (await this.wafRegional(region)).listWebACLs(
                this.buildWafParams(maxResults, nextPageToken),
            );
        }, this.regionalAcls.name);
    }

    getAclsDetails(region: string, webAclId: string): Promise<GetWebACLCommandOutputWAF> {
        return this.request(async () => {
            return (await this.waf(region)).getWebACL({ WebACLId: webAclId });
        }, this.getAclsDetails.name);
    }

    aclsV2(
        region: string,
        maxResults: number,
        nextPageToken: string,
    ): Promise<ListWebACLsCommandOutputWAFv2> {
        return this.request(async () => {
            const wafv2 = await this.wafV2(this.getRegion(region));
            return wafv2.send(
                new ListWebACLsCommandWAFv2(
                    this.buildWafV2Params(region, maxResults, nextPageToken),
                ),
            );
        }, this.aclsV2.name);
    }

    getTagsForV2Acl(region: string, acl: string): Promise<ListTagsForResourceCommandOutputWAFv2> {
        return this.request(async () => {
            const wafv2 = await this.wafV2(this.getRegion(region));
            return wafv2.send(new ListTagsForResourceCommandWAFv2({ ResourceARN: acl }));
        }, this.getTagsForV2Acl.name);
    }

    getTagsForV1Acl(region: string, acl: string): Promise<ListTagsForResourceCommandOutputWAF> {
        return this.request(async () => {
            return (await this.waf(this.getRegion(region))).listTagsForResource({
                ResourceARN: acl,
            });
        }, this.getTagsForV1Acl.name);
    }

    getLoadBalancersV1(
        region: string,
        maxResults: number,
        nextPageToken: string,
    ): Promise<DescribeLoadBalancersCommandOutputELB> {
        return this.request(async () => {
            return (await this.elb(region)).describeLoadBalancers({
                PageSize: maxResults,
                Marker: nextPageToken,
            });
        }, this.getLoadBalancersV1.name);
    }

    getLoadBalancersV1Tags(region: string, names: string[]): Promise<DescribeTagsCommandOutputELB> {
        return this.request(async () => {
            return (await this.elb(region)).describeTags({ LoadBalancerNames: names });
        }, this.getLoadBalancersV1Tags.name);
    }

    getLoadBalancersV2(
        region: string,
        maxResults: number,
        nextPageToken: string,
    ): Promise<DescribeLoadBalancersCommandOutputELBv2> {
        return this.request(async () => {
            return (await this.elbV2(region)).describeLoadBalancers({
                PageSize: maxResults,
                Marker: nextPageToken,
            });
        }, this.getLoadBalancersV1Tags.name);
    }

    getLoadBalancersV2Tags(
        region: string,
        arns: string[],
    ): Promise<DescribeTagsCommandOutputELBv2> {
        return this.request(async () => {
            return (await this.elbV2(region)).describeTags({ ResourceArns: arns });
        }, this.getLoadBalancersV1Tags.name);
    }

    async buckets(nextPageToken: string | null): Promise<ListBucketsCommandOutput> {
        const input: ListBucketsCommandInput = {
            MaxBuckets: config.get('aws.buckets.maxResults'),
        };

        if (nextPageToken) {
            input.ContinuationToken = nextPageToken;
        }

        return this.request(async () => {
            return (await this.s3()).send(new ListBucketsCommand(input));
        }, this.buckets.name);
    }

    async getBucketLocation(name: string, owner: string): Promise<BucketLocationConstraint> {
        const s3 = await this.s3();

        return this.request(async () => {
            try {
                const getBucketLocation = s3.getBucketLocation({
                    Bucket: name,
                    ExpectedBucketOwner: owner,
                });
                const { LocationConstraint } = await getBucketLocation;
                return LocationConstraint;
            } catch (error) {
                if (error.name === AwsConstants.PERMANENT_REDIRECT) {
                    const redirectResult = await this.handleS3PermanentRedirect(
                        error,
                        null,
                        name,
                        owner,
                        s3Client =>
                            s3Client.getBucketLocation({
                                Bucket: name,
                                ExpectedBucketOwner: owner,
                            }),
                        this.getBucketLocation.name,
                    );

                    if (redirectResult.success && redirectResult.result) {
                        return redirectResult.result;
                    }

                    throw redirectResult.error;
                }

                // Re-throw other errors
                throw error;
            }
        }, this.getBucketLocation.name);
    }

    async tags(
        name: string,
        region: string,
        owner: string,
    ): Promise<GetBucketTaggingCommandOutput> {
        const s3 = await this.regionalS3(region);
        return this.request(async () => {
            try {
                return await s3.getBucketTagging({
                    Bucket: name,
                    ExpectedBucketOwner: owner,
                });
            } catch (error) {
                if (error.name === AwsConstants.PERMANENT_REDIRECT) {
                    const redirectResult = await this.handleS3PermanentRedirect(
                        error,
                        region,
                        name,
                        owner,
                        s3Client =>
                            s3Client.getBucketTagging({
                                Bucket: name,
                                ExpectedBucketOwner: owner,
                            }),
                        this.tags.name,
                    );

                    if (redirectResult.success && redirectResult.result) {
                        return redirectResult.result;
                    }

                    throw redirectResult.error;
                }
            }
        }, this.tags.name);
    }

    async encryptions(
        name: string,
        accountId: string,
        region: string,
    ): Promise<
        | GetBucketEncryptionCommandOutput
        | {
              ServerSideEncryptionConfiguration: [];
          }
    > {
        try {
            const regionalS3 = await this.regionalS3(region);

            return await regionalS3.getBucketEncryption({
                Bucket: name,
                ExpectedBucketOwner: accountId,
            });
        } catch (error) {
            if (error.name === AwsConstants.PERMANENT_REDIRECT) {
                const redirectResult = await this.handleS3PermanentRedirect(
                    error,
                    region,
                    name,
                    accountId,
                    s3Client =>
                        s3Client.getBucketEncryption({
                            Bucket: name,
                            ExpectedBucketOwner: accountId,
                        }),
                    this.encryptions.name,
                );

                if (redirectResult.success && redirectResult.result) {
                    return redirectResult.result;
                } else if (redirectResult.error) {
                    // Use the redirect error for logging but continue with normal error handling
                    error = redirectResult.error;
                }
            }

            // Check both SDK v2 and v3 error structures for status code
            const statusCode = get(
                error,
                '$metadata.httpStatusCode',
                get(error, 'statusCode', null),
            );

            if (statusCode === HttpStatus.NOT_FOUND) {
                return {
                    ServerSideEncryptionConfiguration: [],
                };
            }

            this.logger.warn(
                PolloMessage.msg('Get Bucket encryption: FAILED')
                    .setContext(this.constructor.name)
                    .setSubContext(this.encryptions.name)
                    .setIdentifier({ name, accountId, region })
                    .setError(error),
            );

            throw error;
        }
    }

    async publicAccess(
        name: string,
        accountId: string,
        region: string,
    ): Promise<GetPublicAccessBlockCommandOutput | { PublicAccessBlockConfiguration: [] }> {
        try {
            const regionalS3 = await this.regionalS3(region);
            return await regionalS3.getPublicAccessBlock({
                Bucket: name,
                ExpectedBucketOwner: accountId,
            });
        } catch (error) {
            if (error.name === AwsConstants.PERMANENT_REDIRECT) {
                const redirectResult = await this.handleS3PermanentRedirect(
                    error,
                    region,
                    name,
                    accountId,
                    s3Client =>
                        s3Client.getPublicAccessBlock({
                            Bucket: name,
                            ExpectedBucketOwner: accountId,
                        }),
                    this.publicAccess.name,
                );

                if (redirectResult.success && redirectResult.result) {
                    return redirectResult.result;
                } else if (redirectResult.error) {
                    error = redirectResult.error;
                }
            }

            const statusCode = get(
                error,
                '$metadata.httpStatusCode',
                get(error, 'statusCode', null),
            );

            if (statusCode === HttpStatus.NOT_FOUND) {
                return {
                    PublicAccessBlockConfiguration: [],
                };
            }

            this.logger.warn(
                PolloMessage.msg('Get Bucket public access: FAILED')
                    .setContext(this.constructor.name)
                    .setSubContext('publicAccess')
                    .setIdentifier({ name, accountId, region })
                    .setError(error)
                    .setResult(error),
            );

            throw error;
        }
    }

    async versioning(
        name: string,
        accountId: string,
        region: string,
    ): Promise<GetBucketVersioningCommandOutput | null> {
        try {
            const regionalS3 = await this.regionalS3(region);
            return await regionalS3.getBucketVersioning({
                Bucket: name,
                ExpectedBucketOwner: accountId,
            });
        } catch (error) {
            if (error.name === AwsConstants.PERMANENT_REDIRECT) {
                const redirectResult = await this.handleS3PermanentRedirect(
                    error,
                    region,
                    name,
                    accountId,
                    s3Client =>
                        s3Client.getBucketVersioning({
                            Bucket: name,
                            ExpectedBucketOwner: accountId,
                        }),
                    this.versioning.name,
                );

                if (redirectResult.success && redirectResult.result) {
                    return redirectResult.result;
                } else if (redirectResult.error) {
                    // Use the redirect error for logging but continue with normal error handling
                    error = redirectResult.error;
                }
            }

            // Check both SDK v2 and v3 error structures for status code
            const statusCode = get(
                error,
                '$metadata.httpStatusCode',
                get(error, 'statusCode', null),
            );

            if (statusCode === HttpStatus.NOT_FOUND) {
                return null;
            }

            this.logWarn(
                'Get Bucket versioning: FAILED',
                'versioning',
                { name, accountId, region },
                error,
            );

            throw error;
        }
    }

    getTables(
        region: string,
        maxResults: number,
        nextPageToken: string,
    ): Promise<ListTablesCommandOutput> {
        return this.request(async () => {
            return (await this.dynamoDB(region)).listTables({
                Limit: maxResults,
                ExclusiveStartTableName: nextPageToken,
            });
        }, this.getTables.name);
    }

    describeTable(region: string, name: string): Promise<DescribeTableCommandOutput> {
        return this.request(async () => {
            return (await this.dynamoDB(region)).describeTable({
                TableName: name,
            });
        }, this.describeTable.name);
    }

    getTableTags(region: string, arn: string): Promise<ListTagsOfResourceCommandOutput> {
        return this.request(async () => {
            return (await this.dynamoDB(region)).listTagsOfResource({
                ResourceArn: arn,
            });
        }, this.getTableTags.name);
    }

    async getContainerNodeGroups(
        region: string,
        maxResults: number,
        nextPageToken: string,
        clusterName: string,
    ): Promise<TokenPaginationResponse<string[]>> {
        const response = await this.request(async () => {
            const eksClient = await this.eks(region);
            return eksClient.send(
                new ListNodegroupsCommand({
                    maxResults,
                    nextToken: nextPageToken,
                    clusterName,
                }),
            );
        }, this.getContainerNodeGroups.name);

        return tokenize(response.nodegroups, response.nextToken);
    }

    getContainers(
        region: string,
        maxResults: number,
        nextPageToken: string,
    ): Promise<ListClustersCommandOutputEKS> {
        return this.request(async () => {
            const eksClient = await this.eks(region);
            return eksClient.send(
                new ListClustersCommandEKS({ maxResults: maxResults, nextToken: nextPageToken }),
            );
        }, this.getContainers.name);
    }

    getContainerNodeGroup(
        region: string,
        clusterName: string,
        nodegroupName: string,
    ): Promise<DescribeNodegroupCommandOutput> {
        return this.request(async () => {
            const eksClient = await this.eks(region);

            return eksClient.send(new DescribeNodegroupCommand({ clusterName, nodegroupName }));
        }, this.getContainerNodeGroup.name);
    }

    getClusters(
        region: string,
        maxResults: number,
        nextPageToken: string,
    ): Promise<ListClustersCommandOutputECS> {
        return this.request(async () => {
            const ecsClient = await this.ecs(region);

            return ecsClient.send(
                new ListClustersCommandECS({
                    maxResults: maxResults,
                    nextToken: nextPageToken,
                }),
            );
        }, this.getClusters.name);
    }

    describeClusters(
        region: string,
        clusterNames: string[],
    ): Promise<DescribeClustersCommandOutputECS> {
        return this.request(async () => {
            const ecsClient = await this.ecs(region);

            return ecsClient.send(
                new DescribeClustersCommandECS({
                    clusters: clusterNames,
                    include: [ServiceField.TAGS],
                }),
            );
        }, this.describeClusters.name);
    }

    getClusterServices(
        region: string,
        clusterName: string,
        maxResults: number,
        nextPageToken: string,
    ): Promise<ListServicesCommandOutputECS> {
        return this.request(async () => {
            const ecsClient = await this.ecs(region);

            return ecsClient.send(
                new ListServicesCommandECS({
                    cluster: clusterName,
                    maxResults: maxResults,
                    nextToken: nextPageToken,
                }),
            );
        }, this.getClusterServices.name);
    }

    describeClusterServices(
        region: string,
        clusterName: string,
        clusterServices: string[],
    ): Promise<DescribeServicesCommandOutputECS> {
        return this.request(async () => {
            const ecsClient = await this.ecs(region);

            return ecsClient.send(
                new DescribeServicesCommandECS({
                    cluster: clusterName,
                    include: [ServiceField.TAGS],
                    services: clusterServices,
                }),
            );
        }, this.describeClusterServices.name);
    }

    async listIAMUsers(page: string | null, maxItems?: number): Promise<ListUsersCommandOutputIAM> {
        const iamClient = await this.iam();
        const params: { Marker?: string; MaxItems?: number } = {};

        if (!isNil(page)) {
            params.Marker = page;
        }

        if (!isNil(maxItems)) {
            params.MaxItems = maxItems;
        }

        return this.request(() => iamClient.listUsers(params), this.listIAMUsers.name);
    }

    async getRegions(): Promise<Region[]> {
        const service = await this.ec2(config.get('aws.codecommit.defaultRegion'));

        const regions = await this.request(() => {
            return service.send(new DescribeRegionsCommandEC2());
        }, this.getRegions.name);

        return get(regions, 'Regions');
    }

    async listRepositories(region: string): Promise<ListRepositoriesCommandOutput> {
        const service = await this.codeCommit(region);
        return this.request(() => {
            return service.listRepositories();
        }, this.listRepositories.name);
    }

    async getRepository(
        region: string,
        repositoryName: string,
    ): Promise<GetRepositoryCommandOutput> {
        const service = await this.codeCommit(region);

        return this.request(() => {
            return service.getRepository({
                repositoryName,
            });
        }, this.getRepository.name);
    }

    async simulatePrincipalPolicy(params: any): Promise<SimulatePrincipalPolicyCommandOutput> {
        return this.request(async () => {
            return (await this.iam()).simulatePrincipalPolicy(params);
        }, this.simulatePrincipalPolicy.name);
    }

    async listAssociatedApprovalRuleTemplatesForRepository(
        region: string,
        repo: Repository,
    ): Promise<ListAssociatedApprovalRuleTemplatesForRepositoryCommandOutput> {
        const codeCommitClient = await this.codeCommit(region);
        return this.request(() => {
            return codeCommitClient.listAssociatedApprovalRuleTemplatesForRepository({
                repositoryName: repo.name,
            });
        }, this.listAssociatedApprovalRuleTemplatesForRepository.name);
    }

    async getApprovalRuleTemplate(
        region: string,
        templateName: string,
    ): Promise<GetApprovalRuleTemplateCommandOutput> {
        const codeCommitClient = await this.codeCommit(region);
        return this.request(() => {
            return codeCommitClient.getApprovalRuleTemplate({
                approvalRuleTemplateName: templateName,
            });
        }, this.getApprovalRuleTemplate.name);
    }

    tagList(dimension: string, tags: unknown[]): TagCollection {
        const tag = new TagCollection();
        tag.dimension = dimension;

        if (isNil(tags)) {
            return tag;
        }

        tag.tags = tags.map((t: any) => {
            return {
                key: has(t, 'Key') ? t.Key : t.key,
                value: has(t, 'Value') ? t.Value : t.value,
            };
        });

        return tag;
    }

    listMalwareProtectionPlans(
        region: string,
        nextPageToken: string | undefined,
    ): Promise<ListMalwareProtectionPlansCommandOutput> {
        return this.request(async () => {
            const guardDutyClient = await this.guardDuty(region);
            return guardDutyClient.send(
                new ListMalwareProtectionPlansCommand({
                    NextToken: nextPageToken,
                }),
            );
        }, this.listMalwareProtectionPlans.name);
    }

    getMalwareProtectionPlan(
        region: string,
        planId: string,
    ): Promise<GetMalwareProtectionPlanCommandOutput> {
        return this.request(async () => {
            const guardDutyClient = await this.guardDuty(region);
            return guardDutyClient.send(
                new GetMalwareProtectionPlanCommand({
                    MalwareProtectionPlanId: planId,
                }),
            );
        }, this.getMalwareProtectionPlan.name);
    }

    protected buildWafParams(maxResults: number, nextPageToken: string): any {
        const params: any = {};

        params.NextMarker = nextPageToken;
        params.Limit = maxResults;

        return params;
    }

    protected buildWafV2Params(region: string, maxResults: number, nextPageToken: string): any {
        const params = this.buildWafParams(maxResults, nextPageToken);
        params.Scope = this.getScope(region);

        return params;
    }

    private getScope(region: string): string {
        return compareLowerCase(region, AwsConstants.GLOBAL_REGION)
            ? AwsConstants.CLOUDFRONT
            : AwsConstants.REGIONAL;
    }

    private doesNotHaveTag(tags: { tags: TagCollection }, tag: string): boolean {
        return isNil(this.getTag(tags, tag));
    }

    protected get(value: any, index: string): [] {
        return get(value, index) || [];
    }

    protected getAccessData(
        user: UserDetail,
        groupMap: Map<string, GroupDetail>,
        accessReviewEntitlementValue = false,
    ): AccessData {
        const accessReviewData = {
            arn: user.Arn,
            groups: [],
            policies: [],
            roles: [],
        } as AccessData;

        if (!accessReviewEntitlementValue) {
            return accessReviewData;
        }

        accessReviewData.groups = (user?.GroupList ?? []).map(userGroupName => {
            const userGroup = groupMap.get(userGroupName);
            return {
                arn: userGroup?.Arn ?? null,
                name: userGroupName,
                policies: [
                    ...this.mapPolicies(
                        userGroup?.AttachedManagedPolicies ?? [],
                        PolicyType.AttachedManagedPolicies,
                    ),
                ],
            };
        });

        accessReviewData.policies = [
            ...this.mapPolicies(
                user?.AttachedManagedPolicies ?? [],
                PolicyType.AttachedManagedPolicies,
            ),
            ...this.mapPolicies(user?.UserPolicyList ?? [], PolicyType.AttachedManagedPolicies),
        ];

        accessReviewData.roles = [...accessReviewData.policies, ...accessReviewData.groups].map(
            v => v.name,
        );

        return accessReviewData;
    }

    private iam(): Promise<IAM> {
        return this.clientBuilder.client(IAM).build();
    }

    private codeCommit(region: string): Promise<CodeCommit> {
        return this.clientBuilder.client(CodeCommit).setRegion(region).build();
    }

    private dynamoDB(region: string): Promise<DynamoDB> {
        return this.clientBuilder.client(DynamoDB).setRegion(region).build();
    }

    private regionalS3(region: string): Promise<S3> {
        return this.clientBuilder.client(S3).setRegion(region).build();
    }

    private elb(region: string): Promise<ElasticLoadBalancing> {
        return this.clientBuilder.client(ElasticLoadBalancing).setRegion(region).build();
    }

    private elbV2(region: string): Promise<ElasticLoadBalancingV2> {
        return this.clientBuilder.client(ElasticLoadBalancingV2).setRegion(region).build();
    }

    private waf(region: string): Promise<WAF> {
        return this.clientBuilder.client(WAF).setRegion(region).build();
    }

    private wafRegional(region: string): Promise<WAFRegional> {
        return this.clientBuilder.client(WAFRegional).setRegion(region).build();
    }

    protected ecs(region: string): Promise<ECSClient> {
        return this.clientBuilder.client(ECSClient).setRegion(region).build();
    }

    protected cloudTrailWithRegion(region: string): Promise<CloudTrailClient> {
        return this.clientBuilder.client(CloudTrailClient).setRegion(region).build();
    }

    protected guardDuty(region: string): Promise<GuardDutyClient> {
        return this.clientBuilder.client(GuardDutyClient).setRegion(region).build();
    }

    protected sqs(region: string): Promise<SQSClient> {
        return this.clientBuilder.client(SQSClient).setRegion(region).build();
    }

    protected ec2(region: string): Promise<EC2Client> {
        return this.clientBuilder.client(EC2Client).setRegion(region).build();
    }

    private eks(region: string): Promise<EKSClient> {
        return this.clientBuilder.client(EKSClient).setRegion(region).build();
    }

    private ecr(region: string): Promise<ECRClient> {
        return this.clientBuilder.client(ECRClient).setRegion(region).build();
    }

    private wafV2(region: string): Promise<WAFV2Client> {
        return this.clientBuilder.client(WAFV2Client).setRegion(region).build();
    }

    private s3(): Promise<S3> {
        return this.clientBuilder.client(S3).build();
    }

    private rds(region: string, timeout?: number): Promise<RDSClient> {
        const clientWrapper = this.clientBuilder.client(RDSClient).setRegion(region);

        if (!isNil(timeout)) {
            clientWrapper.setHttpOptions({ timeout });
        }

        return clientWrapper.build();
    }

    private sts(): Promise<STSClient> {
        return this.clientBuilder.client(STSClient).build();
    }

    private elastiCache(region: string): Promise<ElastiCacheClient> {
        return this.clientBuilder.client(ElastiCacheClient).setRegion(region).build();
    }

    private es(region: string): Promise<ElasticsearchServiceClient> {
        return this.clientBuilder.client(ElasticsearchServiceClient).setRegion(region).build();
    }

    private cloudWatch(region: string): Promise<CloudWatchClient> {
        return this.clientBuilder.client(CloudWatchClient).setRegion(region).build();
    }

    private cloudFront(): Promise<CloudFrontClient> {
        return this.clientBuilder.client(CloudFrontClient).build();
    }

    private cloudFrontWithRegion(region: string): Promise<CloudFrontClient> {
        return this.clientBuilder.client(CloudFrontClient).setRegion(region).build();
    }

    private apiGatewayV1(region: string): Promise<APIGateway> {
        return this.clientBuilder.client(APIGateway).setRegion(region).build();
    }

    private appSync(region: string): Promise<AppSync> {
        return this.clientBuilder.client(AppSync).setRegion(region).build();
    }

    private sns(region: string): Promise<SNSClient> {
        return this.clientBuilder.client(SNSClient).setRegion(region).build();
    }

    private cloudTrail(region: string): Promise<CloudTrailClient> {
        return this.clientBuilder.client(CloudTrailClient).setRegion(region).build();
    }

    private eventBridge(region: string): Promise<EventBridgeClient> {
        return this.clientBuilder.client(EventBridgeClient).setRegion(region).build();
    }

    private getTag(tags: { tags: TagCollection }, tag: string): KeyValuePair {
        return get(tags, 'tags.tags', []).find((target: KeyValuePair) => {
            return target.key === tag;
        });
    }

    private isRateLimitError(error: any, errorMessage: string): boolean {
        // Check error message for rate limiting indicators
        if (has(error, 'message')) {
            if (
                lcIncludes(errorMessage, AwsConstants.RATE_EXCEEDED) ||
                lcIncludes(errorMessage, AwsConstants.REQUEST_THROTTLED) ||
                lcIncludes(errorMessage, AwsConstants.TOO_MANY_REQUESTS) ||
                lcIncludes(errorMessage, AwsConstants.RATE_LIMIT_EXCEEDED)
            ) {
                return true;
            }
        }

        // Check both AWS SDK v2 and v3 error structures
        const errorName = get(error, 'name', null);
        const errorCode = get(error, 'code', null);

        if (
            errorName === AwsConstants.THROTTLING_EXCEPTION ||
            errorCode === AwsConstants.THROTTLING_EXCEPTION ||
            errorName === AwsConstants.TOO_MANY_REQUESTS_EXCEPTION ||
            errorCode === AwsConstants.TOO_MANY_REQUESTS_EXCEPTION
        ) {
            return true;
        }

        return false;
    }

    private isUserConfigError(error: any, errorMessage: string): boolean {
        // Check error message against known user config error patterns
        if (matchesAnyRegex(errorMessage, AwsConstants.USER_CONFIG_ERROR_MESSAGES)) {
            return true;
        }

        const errorName = get(error, 'name', null);
        const errorCode = get(error, 'code', null);

        // Check for access denied errors (common in v3)
        if (
            errorName === AwsConstants.ACCESS_DENIED_EXCEPTION ||
            errorCode === AwsConstants.ACCESS_DENIED_EXCEPTION ||
            errorName === AwsConstants.ACCESS_DENIED_ERROR ||
            errorCode === AwsConstants.ACCESS_DENIED_ERROR
        ) {
            return true;
        }

        return false;
    }

    private async request(aws: () => any, hint: string): Promise<any> {
        let response = null;

        try {
            response = await (aws as any)();
        } catch (error) {
            const errorMessage = error.message || '';

            if (this.isRateLimitError(error, errorMessage)) {
                throw new RateLimitException(errorMessage, hint);
            }
            // Handle user configuration errors
            else if (this.isUserConfigError(error, errorMessage)) {
                const classifiedError = assignCauseClassification(
                    error,
                    ApiErrorCauseClassification.USER_CONFIG,
                );
                throw classifiedError ?? error;
            }

            throw error;
        }

        return response;
    }

    private getRegion(region: string): string {
        return compareLowerCase(region, AwsConstants.GLOBAL_REGION)
            ? config.get('aws.region.default')
            : region;
    }

    private buildFilters(filters: FilterType[]): any {
        if (!isEmpty(filters)) {
            return filters.map((filter: { name: string; values: string[] }) => {
                return {
                    Name: filter.name,
                    Values: filter.values,
                };
            });
        }

        return [];
    }

    private isMultiAzCluster(databaseCluster: any): boolean {
        const engine = get(databaseCluster, 'Engine', null);

        /**
         * https://drata.atlassian.net/browse/ENG-6337
         */
        return (
            databaseCluster.MultiAZ ||
            engine === AwsConstants.AURORA ||
            engine === AwsConstants.AURORA_MYSQL ||
            engine === AwsConstants.AURORA_POSTGRES
        );
    }

    private getMetricString(type: Metric): string | null {
        let metricString: string | null = null;

        switch (type) {
            case Metric.CPU: {
                metricString = AwsConstants.CPU_MONITOR;
                break;
            }

            case Metric.FREE_STORAGE: {
                metricString = AwsConstants.FREE_STORAGE_MONITOR;
                break;
            }

            case Metric.FREE_LOCAL_STORAGE: {
                metricString = AwsConstants.FREE_LOCAL_STORAGE_MONITOR;
                break;
            }

            case Metric.READ_IOPS: {
                metricString = AwsConstants.IOPS_READ_MONITOR;
                break;
            }

            case Metric.SELECT_THROUGHPUT: {
                metricString = AwsConstants.SELECT_THROUGHPUT_MONITOR;
                break;
            }

            case Metric.WRITE_IOPS: {
                metricString = AwsConstants.IOPS_WRITE_MONITOR;
                break;
            }

            case Metric.INSERT_THROUGHPUT: {
                metricString = AwsConstants.INSERT_THROUGHPUT_MONITOR;
                break;
            }

            case Metric.MESSAGE_AGE: {
                metricString = AwsConstants.MESSAGE_AGE_MONITOR;
                break;
            }

            case Metric.CLOUD_DATA_FREE_STORAGE: {
                metricString = AwsConstants.CLOUD_DATA_FREE_STORAGE_MONITOR;
                break;
            }

            case Metric.CLOUD_DATA_CLUSTER_FREE_STORAGE: {
                metricString = AwsConstants.CLOUD_DATA_CLUSTER_FREE_STORAGE_MONITOR;
                break;
            }

            default:
                throw new InternalServerErrorException(`Metric ${type} not supported`);
        }

        return metricString;
    }

    private getNamespaceString(type: MetricNamespace): string | null {
        let namespaceString: string | null = null;

        switch (type) {
            case MetricNamespace.DATABASE: {
                namespaceString = AwsConstants.DATA_NAMESPACE;
                break;
            }

            case MetricNamespace.DATABASE_CLUSTER: {
                namespaceString = AwsConstants.DATA_CLUSTER_NAMESPACE;
                break;
            }

            case MetricNamespace.INSTANCE: {
                namespaceString = AwsConstants.INSTANCE_NAMESPACE;
                break;
            }

            case MetricNamespace.QUEUE: {
                namespaceString = AwsConstants.QUEUE_NAMESPACE;
                break;
            }

            case MetricNamespace.CLOUD_DATA: {
                namespaceString = AwsConstants.CLOUD_DATA_NAMESPACE;
                break;
            }

            case MetricNamespace.CLOUD_DATA_CLUSTER: {
                namespaceString = AwsConstants.CLOUD_DATA_NAMESPACE;
                break;
            }

            case MetricNamespace.CLUSTER_INSTANCE: {
                namespaceString = AwsConstants.CLUSTER_INSTANCE_NAMESPACE;
                break;
            }

            case MetricNamespace.CONTAINER_INSTANCE: {
                namespaceString = AwsConstants.CONTAINER_INSTANCE_NAMESPACE;
                break;
            }

            case MetricNamespace.AUTOSCALING_GROUP: {
                namespaceString = AwsConstants.AUTOSCALING_GROUP_NAMESPACE;
                break;
            }

            default:
                throw new InternalServerErrorException(`Namespace ${type} not supported`);
        }

        return namespaceString;
    }

    private getInstanceKey(type: MetricNamespace): string | null {
        let instanceKey: string | null = null;

        switch (type) {
            case MetricNamespace.DATABASE: {
                instanceKey = AwsConstants.DATA_INSTANCE_KEY;
                break;
            }

            case MetricNamespace.DATABASE_CLUSTER: {
                instanceKey = AwsConstants.DATA_CLUSTER_INSTANCE_KEY;
                break;
            }

            case MetricNamespace.INSTANCE: {
                instanceKey = AwsConstants.INSTANCE_INSTANCE_KEY;
                break;
            }

            case MetricNamespace.QUEUE: {
                instanceKey = AwsConstants.QUEUE_INSTANCE_KEY;
                break;
            }

            case MetricNamespace.CLOUD_DATA: {
                instanceKey = AwsConstants.CLOUD_DATA_INSTANCE_KEY;
                break;
            }

            case MetricNamespace.CLOUD_DATA_CLUSTER: {
                instanceKey = AwsConstants.CLOUD_DATA_INSTANCE_KEY;
                break;
            }

            case MetricNamespace.CLUSTER_INSTANCE: {
                instanceKey = AwsConstants.CLUSTER_INSTANCE_INSTANCE_KEY;
                break;
            }

            case MetricNamespace.CONTAINER_INSTANCE: {
                instanceKey = AwsConstants.CONTAINER_INSTANCE_INSTANCE_KEY;
                break;
            }

            case MetricNamespace.AUTOSCALING_GROUP: {
                instanceKey = AwsConstants.AUTOSCALING_GROUP_INSTANCE_KEY;
                break;
            }

            default:
                throw new InternalServerErrorException(`InstanceKey ${type} not supported`);
        }

        return instanceKey;
    }

    private parseWaffableList(response: any): Waffable[] {
        return get(response, 'ResourceArns', []).map((arn: string) => {
            return {
                id: arn,
                name: arn,
                hasWaf: false,
                raw: arn,
            };
        });
    }

    /**
     * AWS SDK v3 removed automatic region resolution for S3 buckets that existed in v2.
     * At least that's what it appears like from testing. When accessing a bucket in the
     * wrong region, v3 returns PermanentRedirect errors instead of automatically retrying.
     * This method handles these redirects by extracting the correct region and retrying the operation.
     */
    private async handleS3PermanentRedirect<T>(
        error: any,
        region: string | null,
        bucketName: string,
        accountId: string,
        operation: (s3Client: S3) => Promise<T>,
        subContext: string,
    ): Promise<{ success: boolean; result?: T; error?: any }> {
        const endpoint = get(error, 'Endpoint', null);
        const correctRegion = extractRegionFromS3Endpoint(endpoint, region);

        // Only retry if we got a different region
        if ((!isNil(region) && correctRegion === region) || isNil(correctRegion)) {
            return { success: false, error };
        }

        try {
            this.logInfo('Retrying with correct region from PermanentRedirect', subContext, {
                name: bucketName,
                accountId,
                originalRegion: region,
                correctRegion,
                endpoint,
            });

            const correctRegionalS3 = await this.regionalS3(correctRegion);
            const result = await operation(correctRegionalS3);
            return { success: true, result };
        } catch (retryError) {
            // Check if we're getting the same error again to avoid loops
            if (retryError.name === AwsConstants.PERMANENT_REDIRECT) {
                this.logError(
                    'Still getting PermanentRedirect after region change, avoiding retry loop',
                    subContext,
                    {
                        name: bucketName,
                        accountId,
                        originalRegion: region,
                        correctRegion,
                    },
                    retryError,
                );
            } else {
                this.logError(
                    'Failed to retry with correct region',
                    subContext,
                    { name: bucketName, accountId, region },
                    retryError,
                );
            }
            return { success: false, error: retryError };
        }
    }

    private mapPolicies(
        policies: Array<{ PolicyArn?: string; PolicyName?: string }>,
        type: PolicyType,
    ): AccessData['policies'] {
        return policies
            .filter(policy => !isEmpty(policy?.PolicyName))
            .map(policy => ({
                arn: policy?.PolicyArn ?? '',
                name: policy?.PolicyName ?? '',
                type,
            }));
    }

    private createLogMessage(
        message: string,
        subContext: string,
        identifier?: any,
        error?: any,
    ): PolloMessage {
        let logMessage: PolloMessage;

        if (this.loggingContext) {
            logMessage = PolloAdapter.cxt(message, this.loggingContext);
        } else {
            logMessage = PolloMessage.msg(message);
        }

        logMessage.setContext(this.constructor.name).setSubContext(subContext);

        if (identifier) {
            logMessage.setIdentifier(identifier);
        }

        if (error) {
            logMessage.setError(error);
        }

        return logMessage;
    }

    private logInfo(message: string, subContext: string, identifier?: any): void {
        this.logger.log(this.createLogMessage(message, subContext, identifier));
    }

    private logWarn(message: string, subContext: string, identifier?: any, error?: any): void {
        this.logger.warn(this.createLogMessage(message, subContext, identifier, error));
    }

    private logError(message: string, subContext: string, identifier?: any, error?: any): void {
        this.logger.error(this.createLogMessage(message, subContext, identifier, error));
    }
}
