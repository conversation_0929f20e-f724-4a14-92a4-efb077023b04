import { CheckResultStatus, ErrorCode, ProviderType, TestSource } from '@drata/enums';
import {
    BadRequestException,
    HttpStatus,
    Injectable,
    InternalServerErrorException,
    NotImplementedException,
    ServiceUnavailableException,
} from '@nestjs/common';
import { AccessReviewCoreService } from 'app/access-review/services/access-review-core.service';
import { ITicketService } from 'app/apis/interfaces/ticket-service.interface';
import { TicketingIssueType } from 'app/apis/types/ticketing/ticketing-issue.type';
import { ConnectionEntity } from 'app/companies/connections/entities/connection.entity';
import { ConnectionsCoreService } from 'app/companies/connections/services/connections-core.service';
import { ControlService } from 'app/control/control.service';
import { ExternalEvidenceCoreService } from 'app/document-library/services/external-evidence-core.service';
import { FrameworkRepository } from 'app/frameworks/repositories/framework.repository';
import { ControlEvidenceRequestDto } from 'app/grc/dtos/control-evidence-request.dto';
import { ControlPoliciesRequestDto } from 'app/grc/dtos/control-policies-request.dto';
import { ControlsRequestDto } from 'app/grc/dtos/controls-request.dto';
import { Control } from 'app/grc/entities/control.entity';
import { ExternalEvidence } from 'app/grc/entities/external-evidence.entity';
import { MonitorsRequestDto } from 'app/monitors/dtos/monitors-request.dto';
import { ControlTestInstance } from 'app/monitors/entities/control-test-instance.entity';
import { MonitorConstants } from 'app/monitors/entities/monitor-contants.class';
import { MonitorInstance } from 'app/monitors/entities/monitor-instance.entity';
import { MonitorFindingsType } from 'app/monitors/enums/monitor-findings-type.enum';
import { updateTicketsForMonitoringSummaryResultsIndexForAccount } from 'app/monitors/helpers/monitoring-summary-indexing.helper';
import { MonitoringSummaryIndexingCoreService } from 'app/monitors/services/monitoring-summary-indexing-core.service';
import { MonitorsCoreService } from 'app/monitors/services/monitors-core.service';
import { TicketMonitoringTemplateBuilder } from 'app/tickets/classes/automate-ticket-monitoring/ticket-monitoring-template.builder';
import { TicketMonitoringTypeFactory } from 'app/tickets/classes/automate-ticket-monitoring/ticket-monitoring-type.factory';
import { TicketBaseRequestDto } from 'app/tickets/dtos/ticket-base-request.dto';
import { TicketsCreateFieldRequestDto } from 'app/tickets/dtos/tickets-create-field.dto';
import { TicketsCreateRequestDto } from 'app/tickets/dtos/tickets-create-request.dto';
import { TicketsMonitorInstancesRequestDto } from 'app/tickets/dtos/tickets-monitor-instances-request.dto';
import { TicketMonitoringEntity } from 'app/tickets/entities/ticket-monitoring.entity';
import { Ticket } from 'app/tickets/entities/ticket.entity';
import { TicketLinkingSourceType } from 'app/tickets/enums/ticket-linking-source-type.enum';
import { TicketConnectionError } from 'app/tickets/errors/ticket-connection.error';
import { getTicketNumberByUrl, isExternalTicketDone } from 'app/tickets/helper/ticket.helper';
import { TicketCreatedApplicationUsersEvent } from 'app/tickets/observables/events/ticket-created-application-users.event';
import { TicketCreatedControlEvent } from 'app/tickets/observables/events/ticket-created-control.event';
import { TicketCreatedMonitorInstanceEvent } from 'app/tickets/observables/events/ticket-created-monitor.event';
import { TicketRepository } from 'app/tickets/repositories/ticket.repository';
import { TicketsCoreService } from 'app/tickets/services/tickets-core.service';
import { CreatedIssueType } from 'app/tickets/types/created-issue.type';
import { TicketType, TicketWithUrl } from 'app/tickets/types/ticket.type';
import { User } from 'app/users/entities/user.entity';
import { PersonnelTicketRepository } from 'app/users/personnel/repositories/personnel-ticket.repository';
import { Policy } from 'app/users/policies/entities/policy.entity';
import { PoliciesCoreService } from 'app/users/policies/services/policies-core.service';
import { UsersCoreService } from 'app/users/services/users-core.service';
import { Account } from 'auth/entities/account.entity';
import { CacheBusterWithPrefix } from 'cache/cache.decorator';
import { PolloAdapter } from 'commons/adapters/pollo.adapter';
import { TenancyTransaction } from 'commons/decorators/transaction';
import { PaginationRequestDto } from 'commons/dtos/pagination-request.dto';
import { ClientType } from 'commons/enums/auth/client-type.enum';
import { Caches } from 'commons/enums/cache.enum';
import { CheckType } from 'commons/enums/check-type.enum';
import { MimeType } from 'commons/enums/mime-type.enum';
import { PersonnelTicketType } from 'commons/enums/personnel/personnel-ticket-type.enum';
import { TicketReferenceType } from 'commons/enums/tickets/ticket-reference-type.enum';
import { ConnectionFailedException } from 'commons/exceptions/connection-failed.exception';
import { ErrorCodeException } from 'commons/exceptions/error-code.exception';
import { NotFoundException } from 'commons/exceptions/not-found.exception';
import { getBufferFromStream } from 'commons/helpers/buffer.helper';
import { replaceTarget } from 'commons/helpers/control-test-instance.helper';
import { getEvidenceDateFormatted } from 'commons/helpers/date.helper';
import { sanitizeFileName } from 'commons/helpers/file.helper';
import { batchIteration } from 'commons/helpers/performance/batch-iteration.helper';
import { getProductId } from 'commons/helpers/products.helper';
import { AppService } from 'commons/services/app.service';
import { FileBufferOnlyType, FileBufferTicketType } from 'commons/types/file-buffer.type';
import { PaginationType } from 'commons/types/pagination.type';
import config from 'config';
import {
    chain,
    filter,
    first,
    flatMap,
    flatten,
    get,
    groupBy,
    isEmpty,
    isEqual,
    isNil,
    keyBy,
    map,
    omit,
    reduce,
} from 'lodash';
import moment from 'moment';
import { PolloMessage } from 'pollo-logger/classes/pollo.message';
import { TemplatedContentService } from 'templated-content/templated-content.service';
import { EntityNotFoundError, In } from 'typeorm';

type PromiseSettled<T> = {
    status: 'rejected' | 'fulfilled';
    reason: any;
    value: T[];
};

@Injectable()
export class TicketsOrchestrationService extends AppService {
    constructor(
        private readonly connectionsCoreService: ConnectionsCoreService,
        private readonly ticketsCoreService: TicketsCoreService,
        private readonly monitoringSummaryIndexingCoreService: MonitoringSummaryIndexingCoreService,
        private readonly monitorsCoreService: MonitorsCoreService,
        private readonly templatedContentService: TemplatedContentService,
        private readonly usersCoreService: UsersCoreService,
        private readonly controlService: ControlService,
        private readonly policiesCoreService: PoliciesCoreService,
        private readonly externalEvidenceCoreService: ExternalEvidenceCoreService,
        private readonly accessReviewCoreService: AccessReviewCoreService,
    ) {
        super();
    }

    async fetchAndSync(
        account: Account,
        ticketsMonitoring: TicketMonitoringEntity[] = [],
    ): Promise<void> {
        const product = account.getCurrentProduct();

        for (const ticketMonitoring of ticketsMonitoring) {
            if (!this.isTicketEnabledForProduct(ticketMonitoring, product.id)) {
                continue;
            }

            try {
                const factory = new TicketMonitoringTypeFactory(this.ticketsCoreService, this);
                // eslint-disable-next-line no-await-in-loop
                await factory.getSynchronizer(ticketMonitoring).sync(account);
            } catch (error) {
                this.logger.error(
                    PolloAdapter.acct(
                        `Error processing ticket monitoring with id \n
                            ${ticketMonitoring.id}`,
                        account,
                    )
                        .setIdentifier({ accountId: account.id })
                        .setError(error),
                );
            }
        }

        await updateTicketsForMonitoringSummaryResultsIndexForAccount(
            this.monitoringSummaryIndexingCoreService,
            account,
        );
    }

    isTicketEnabledForProduct(
        ticketMonitoring: TicketMonitoringEntity,
        productId: number,
    ): boolean {
        return !isNil(ticketMonitoring.enabledAt) && ticketMonitoring.product.id === productId;
    }

    async listNotReadyControls(account: Account, criteria: ControlsRequestDto): Promise<Control[]> {
        const controls = await this.controlService.listControls(account, criteria);
        return controls.data;
    }

    async getConnection(connectionId: number): Promise<ConnectionEntity | null> {
        let connection: ConnectionEntity | null = null;

        try {
            connection = await this.connectionsCoreService.getTicketingConnectionById(connectionId);
        } catch (error) {
            if (!(error instanceof EntityNotFoundError)) {
                throw new BadRequestException(
                    `Connection, designated by 'connectionId', must have provider type: ${ProviderType.TICKETING}`,
                );
            }
        }

        if (isNil(connection)) {
            throw new NotFoundException(ErrorCode.CONNECTION_NOT_FOUND);
        }

        return connection;
    }

    @CacheBusterWithPrefix<CreatedIssueType>({
        store: Caches.LIST_CONTROLS,
    })
    async createTicketWrapper(
        account: Account,
        user: User,
        requestDto: TicketsCreateRequestDto,
    ): Promise<CreatedIssueType> {
        const { createdExternalTicket } = await this.createTicket(account, user, requestDto);

        return createdExternalTicket;
    }

    async createTicket(
        account: Account,
        user: User,
        requestDto: TicketsCreateRequestDto,
        automateTicketing = false,
    ): Promise<{ createdExternalTicket: CreatedIssueType; ticket: Ticket }> {
        const { relatedEntityIds, relatedEntityEnum } = requestDto;

        this.validateCreateIssueFields(requestDto.fields);

        let fields: { fields: Record<string, unknown> } = { fields: {} };
        let attachments: FileBufferOnlyType[] = [];

        try {
            const connection = await this.ticketsCoreService.getConnection(requestDto.connectionId);
            const clientType = connection.clientType;
            const ticketService = await this.ticketsCoreService.getTicketService(
                account,
                connection.id,
                connection,
            );

            if (isNil(ticketService)) {
                throw new InternalServerErrorException('Could not fetch ticket service');
            }

            if (automateTicketing) {
                const {
                    summary,
                    content,
                    attachments: contentAttachments,
                } = await this.getContent(account, requestDto, clientType);

                fields = new TicketMonitoringTemplateBuilder(this.templatedContentService).build(
                    requestDto,
                    summary,
                    content,
                );

                attachments = contentAttachments ?? [];
            }

            let createdExternalTicket: CreatedIssueType;
            if (requestDto.useRichContent) {
                try {
                    createdExternalTicket = await this.createRichContentTicket(
                        account,
                        user,
                        requestDto,
                        ticketService,
                        clientType,
                    );
                } catch (error) {
                    this.logger.error(
                        PolloMessage.msg('Unable to create rich content ticket: ' + error.message)
                            .setDomain(account.domain)
                            .setResult(requestDto)
                            .setError(error),
                    );
                }
            } else {
                createdExternalTicket = await ticketService.createTicket(
                    requestDto,
                    fields,
                    attachments,
                );
            }

            if (!isNil(createdExternalTicket)) {
                const { ticketId } = createdExternalTicket;
                // need to create our drata ticket to link to the external ticket just made
                const newTicket = new Ticket();
                newTicket.externalTicketId = ticketId;
                newTicket.isDone = false;
                newTicket.user = user;
                newTicket.connection = connection;

                const ticketNumber = await ticketService.getTicketDisplayNumber(ticketId);

                switch (relatedEntityEnum) {
                    case TicketReferenceType.MONITOR:
                        // Frontend is sending only one monitor instance ID
                        const monitorInstanceId = relatedEntityIds[0];

                        const monitor =
                            await this.monitorsCoreService.getMonitorInstanceByIdWithControlTest(
                                monitorInstanceId,
                            );

                        newTicket.monitorInstances = [monitor];

                        this._eventBus.publish(
                            new TicketCreatedMonitorInstanceEvent(
                                account,
                                user,
                                await this.getTicketWithUrl(newTicket, account),
                                ticketNumber,
                                monitor,
                            ),
                        );
                        break;
                    case TicketReferenceType.CONTROL:
                        // Frontend is sending only one control ID
                        const controlId = relatedEntityIds[0];

                        const control = await this.controlService.getControlById(controlId);

                        newTicket.controls = [control];

                        this._eventBus.publish(
                            new TicketCreatedControlEvent(
                                account,
                                user,
                                await this.getTicketWithUrl(newTicket, account),
                                ticketNumber,
                                control,
                            ),
                        );
                        break;
                    case TicketReferenceType.APPLICATION_USER:
                        const applicationUsers =
                            await this.accessReviewCoreService.getApplicationUsersByIds(
                                relatedEntityIds,
                            );

                        newTicket.reviewApplicationUsers = applicationUsers;

                        this._eventBus.publish(
                            new TicketCreatedApplicationUsersEvent(
                                account,
                                user,
                                await this.getTicketWithUrl(newTicket, account),
                                ticketNumber,
                                applicationUsers,
                            ),
                        );
                        break;
                }

                const ticket = await this.ticketRepository.save(newTicket);

                return {
                    ticket,
                    createdExternalTicket,
                };
            }
        } catch (error) {
            this.logger.error(
                PolloAdapter.acct('createTicket: ' + error.message, account)
                    .setResult(requestDto)
                    .setError(error),
            );
            if (error instanceof BadRequestException) {
                this.throwTicketsBadRequest(
                    error.message,
                    ErrorCode.CREATE_TICKET_DATATYPE_NOT_SUPPORTED,
                );
            } else {
                throw new ServiceUnavailableException(error);
            }

            throw error;
        }
    }

    async createRichContentTicket(
        account: Account,
        user: User,
        requestDto: TicketsCreateRequestDto,
        ticketService: ITicketService,
        clientType: ClientType,
    ): Promise<CreatedIssueType> {
        if (requestDto.relatedEntityEnum !== TicketReferenceType.MONITOR) {
            throw new BadRequestException('Given IDs must belong to monitor instances.');
        }

        // Frontend is sending only one monitor instance ID
        const [monitorInstanceId] = requestDto.relatedEntityIds;

        const monitorInstance = await this.getMonitorInstanceByIdWithControlTest(monitorInstanceId);

        const { content, createSubtasks } = await this.getRichTestContent(
            account,
            monitorInstance,
            requestDto,
            clientType,
        );

        const { description, summary } = content;

        const fields: { fields: Record<string, unknown> } = { fields: { description, summary } };

        if (createSubtasks) {
            const parentTicket = await ticketService.createRichContentTicket(requestDto, fields);

            const subtaskTicketTypes = await ticketService.getSubtaskTicketTypes(
                requestDto.projectId?.toString(),
            );

            if (isEmpty(subtaskTicketTypes)) {
                this.logger.log(
                    PolloMessage.msg('createRichContentTicket: Unable to determine subtask type ID')
                        .setDomain(account.domain)
                        .setResult(requestDto),
                );

                return parentTicket;
            }

            const subtaskTicketTypeId = first(subtaskTicketTypes).value;

            const subtasks = await new TicketMonitoringTemplateBuilder(
                this.templatedContentService,
            ).buildRichContentSubtasks(account, requestDto, monitorInstance, clientType);

            const subtaskRequestDto = { ...requestDto, issueTypeId: subtaskTicketTypeId };
            for (const subtask of subtasks) {
                // eslint-disable-next-line no-await-in-loop
                await ticketService.createRichContentSubtask(subtaskRequestDto, parentTicket, {
                    fields: { description: subtask.description, summary: subtask.summary },
                });
            }

            return parentTicket;
        }

        return ticketService.createRichContentTicket(requestDto, fields);
    }

    private getContent(
        account: Account,
        requestDto: TicketsCreateRequestDto,
        clientType: ClientType,
    ): Promise<{
        summary: string;
        content: Record<string, unknown>;
        attachments?: FileBufferOnlyType[];
    }> {
        return requestDto.relatedEntityEnum !== TicketReferenceType.CONTROL
            ? this.getTestContent(account, requestDto, clientType)
            : this.getControlsContent(account, requestDto);
    }

    private async getControlsContent(
        account: Account,
        dto: TicketsCreateRequestDto,
    ): Promise<{ summary: string; content: Record<string, unknown> }> {
        let content: Record<string, unknown> = {};

        if (dto.relatedEntityEnum !== TicketReferenceType.CONTROL) {
            throw new BadRequestException('Given IDs must belong to controls.');
        }

        // Frontend is sending only one control ID
        const controlId = dto.relatedEntityIds[0];

        const control = await this.getControlById(controlId);

        const owners = await this.getOwnersControlByControlCode(account, control.code);

        const failedTest = await this.getFailedTest(account, control.id);

        const unapprovedPolicies = await this.getUnapprovedControlPolicies(controlId);

        const evidences = await this.getControlEvidence(controlId);

        const summary = `Drata | ${account.getCurrentProduct().name} | ${
            control.code
        } not ready | ${control.name}`;

        content = {
            ['Control description: ']: this.getFieldContentValue(
                replaceTarget(control.description, account.companyName),
            ),
            ['Control question: ']: this.getFieldContentValue(control.question),
            ['Owners: ']: this.getFieldContentValue(owners.map(user => user.email).join(', ')),
        };

        if (!isEmpty(failedTest)) {
            content['Failed test: '] = this.getFieldContentValue(
                failedTest.map(test => `${test.name}. Latest test result: Failed`).join(', '),
            );
        }

        if (!isEmpty(unapprovedPolicies)) {
            content['Unapproved policy: '] = this.getFieldContentValue(
                unapprovedPolicies.map(policy => policy.name).join(', '),
            );
        }

        if (!isEmpty(evidences)) {
            content['Expired evidence: '] = this.getFieldContentValue(
                evidences
                    .map(
                        evidence =>
                            `${evidence.name} Renew on: ${getEvidenceDateFormatted(
                                evidence.renewalDate,
                            )}`,
                    )
                    .join(', '),
            );
        }

        content['Control URL: '] = `${config.get(
            'url.webApp',
        )}/compliance/controls/details/id/${controlId}`;

        return { summary, content };
    }

    async getControlEvidence(id: number): Promise<ExternalEvidence[]> {
        let page = 1;
        const evidences: ExternalEvidence[] = [];

        do {
            // eslint-disable-next-line no-await-in-loop
            const { data } = await this.externalEvidenceCoreService.getControlEvidence(id, {
                page,
                limit: 20,
            } as ControlEvidenceRequestDto);
            if (isEmpty(data)) {
                break;
            }
            evidences.push(...data);
            page++;
        } while (true);

        return evidences;
    }

    private async getUnapprovedControlPolicies(id: number): Promise<Policy[]> {
        const policies = await this.getControlPolicies(id);

        return policies.filter(
            policy =>
                !isNil(
                    policy.versions.find(version => version.current && isNil(version.approvedAt)),
                ),
        );
    }

    async getControlPolicies(id: number): Promise<Policy[]> {
        let page = 1;
        const policies: Policy[] = [];

        do {
            // eslint-disable-next-line no-await-in-loop
            const { data } = await this.policiesCoreService.getControlPolicies(id, {
                page,
                limit: 20,
            } as ControlPoliciesRequestDto);
            if (isEmpty(data)) {
                break;
            }
            policies.push(...data);
            page++;
        } while (true);

        return policies;
    }

    getControlById(id: number): Promise<Control> {
        return this.controlService.getControlById(id);
    }

    getOwnersControlByControlCode(account: Account, code: string): Promise<User[]> {
        return this.usersCoreService.getControlOwnersByControlCode(account, code);
    }

    private async getFailedTest(
        account: Account,
        controlId: number,
    ): Promise<ControlTestInstance[]> {
        const failedTest = [];
        let page = 1;
        do {
            // eslint-disable-next-line no-await-in-loop
            const tests = await this.listMonitors(account, {
                controlId,
                includeArchivedControls: true,
                checkResultStatus: CheckResultStatus.FAILED,
                page: page++,
                limit: config.get('pagination.limit'),
            } as MonitorsRequestDto);

            if (isEmpty(tests)) {
                break;
            }

            failedTest.push(...tests);
        } while (true);

        return failedTest;
    }

    async listMonitors(account: Account, criteria: MonitorsRequestDto) {
        const { data } = await this.monitorsCoreService.listControlTestInstances(account, criteria);

        return data.map(controlTestInstance => controlTestInstance.test);
    }

    async findIssue(
        account: Account,
        externalTicketId: string,
        connection: ConnectionEntity,
    ): Promise<TicketingIssueType | undefined> {
        try {
            const ticketService = await this.ticketsCoreService.getTicketService(
                account,
                connection.id,
                connection,
            );
            return await ticketService?.findIssue(externalTicketId);
        } catch (error) {
            this.error(error, account, { externalTicketId });
        }
    }

    async getTicketWithUrl(ticket: Ticket, account: Account): Promise<TicketWithUrl> {
        const { externalTicketId, connection } = ticket;

        const issue = await this.findIssue(account, externalTicketId, connection);

        return {
            ...ticket,
            url: isNil(issue) ? '' : issue?.url,
        } as TicketWithUrl;
    }

    async searchByQuery(
        account: Account,
        requestDto: PaginationRequestDto,
        query: string,
        connection: ConnectionEntity,
        mapFullTicketContent?: boolean,
    ): Promise<PaginationType<TicketingIssueType | undefined>> {
        try {
            const ticketService = await this.ticketsCoreService.getTicketService(
                account,
                connection.id,
                connection,
            );

            if (!ticketService) {
                throw new NotFoundException(ErrorCode.CONNECTION_NOT_FOUND);
            }

            return await ticketService.searchPaginatedByQuery(
                requestDto,
                query,
                mapFullTicketContent,
            );
        } catch (error) {
            this.logger.error(
                PolloAdapter.acct('getExternalUsers: ' + error.message, account).setError(error),
            );
            throw new ServiceUnavailableException(error);
        }
    }

    @TenancyTransaction()
    async buildTicketsBeta(
        account: Account,
        originalTickets: Ticket[],
    ): Promise<{ tickets: TicketType[]; netChange: Ticket[] }> {
        if (isEmpty(originalTickets)) {
            return { tickets: [], netChange: [] };
        }

        let tickets = originalTickets;

        const mappedTickets = groupBy(tickets, 'connection.id');
        const mappedConnections = chain(tickets).map('connection').keyBy('id').value();

        const externalTickets = await (Promise as any).allSettled(
            map(mappedTickets, (item, key) => {
                const connection = mappedConnections[key];
                const ticketsIds = map(item, 'id');

                return this.getIssues(account, item).catch(e => {
                    tickets = filter(tickets, ({ id }) => !ticketsIds.includes(id));
                    throw new TicketConnectionError(e, connection);
                });
            }),
        );

        const mappedExternalTickets = this.handleExternalTickets(account, externalTickets);

        const { buildedTickets, updatedTickets } = reduce(
            tickets,
            ({ buildedTickets: bt, updatedTickets: ut }, ticket) => {
                const ticketingIssueType = mappedExternalTickets[ticket.externalTicketId] ?? null;

                if (isNil(ticketingIssueType)) {
                    ticket.deletedAt = new Date();
                    return {
                        buildedTickets: bt,
                        updatedTickets: [...ut, ticket],
                    };
                }

                const isEtd = isExternalTicketDone(ticket.connection, ticketingIssueType);

                if (ticket.isDone !== isEtd) {
                    ticket.lastSyncedAt = new Date();
                    ticket.completionDate = isEtd
                        ? (ticketingIssueType.fields?.completedAt ?? null)
                        : null;
                    ticket.isDone = isEtd;
                    ut.push(ticket);
                }

                return {
                    buildedTickets: [...bt, { ticket, ticketingIssueType }],
                    updatedTickets: ut,
                };
            },
            { buildedTickets: [], updatedTickets: [] } as {
                buildedTickets: TicketType[];
                updatedTickets: Ticket[];
            },
        );

        if (!isEmpty(updatedTickets)) {
            await this.ticketRepository.save(updatedTickets, { reload: false });
        }

        return { tickets: buildedTickets, netChange: updatedTickets };
    }

    @TenancyTransaction()
    async linkTicket(
        account: Account,
        ticketUrl: string,
        user: User,
        withDeleted = false,
    ): Promise<Ticket> {
        const connection = await this.getConnectionByUrl(new URL(ticketUrl).origin);
        const ticketNumber = getTicketNumberByUrl(connection.clientType, ticketUrl);

        const internalTicket = await this.ticketRepository.findTicketByExternalId(
            ticketNumber,
            connection.id,
            withDeleted,
        );

        if (!isNil(internalTicket)) {
            return internalTicket;
        }

        const externalTicket = await this.findIssue(account, ticketNumber, connection);
        if (isNil(externalTicket)) {
            throw new NotFoundException(ErrorCode.TICKET_NOT_FOUND);
        }

        const ticket = new Ticket();
        ticket.externalTicketId = externalTicket.ticketId;
        ticket.isDone = isExternalTicketDone(connection, externalTicket);
        ticket.completionDate = isExternalTicketDone(connection, externalTicket)
            ? externalTicket.fields.completedAt
            : null;
        ticket.user = user;
        ticket.connection = connection;
        ticket.linkingSource = TicketLinkingSourceType.MANUAL;

        return this.ticketRepository.save(ticket);
    }

    @TenancyTransaction()
    async linkExistingTicket(
        ticketingIssues: TicketingIssueType,
        user: User,
        connection: ConnectionEntity,
        withDeleted = false,
    ): Promise<Ticket> {
        const ticketNumber = getTicketNumberByUrl(connection.clientType, ticketingIssues.url);

        const internalTicket = await this.ticketRepository.findTicketByExternalId(
            ticketNumber,
            connection.id,
            withDeleted,
        );

        if (!isNil(internalTicket)) {
            return internalTicket;
        }

        const ticket = new Ticket();
        ticket.externalTicketId = ticketingIssues.ticketId;
        ticket.isDone = isExternalTicketDone(connection, ticketingIssues);
        ticket.completionDate = isExternalTicketDone(connection, ticketingIssues)
            ? ticketingIssues.fields.completedAt
            : null;
        ticket.user = user;
        ticket.linkingSource = TicketLinkingSourceType.AUTOMATED;
        ticket.connection = connection;

        return this.ticketRepository.save(ticket);
    }

    private handleExternalTickets(
        account: Account,
        externalTickets: PromiseSettled<TicketingIssueType>[],
    ): object {
        const { mappedExternalTickets, falsyConnections } = reduce(
            externalTickets,
            (acc, item) => {
                const { status, reason, value } = item;
                const invalidConnections = [];
                const ticketsForValidConnection = [];

                if (isEqual(status, 'rejected')) {
                    const httpStatus = get(reason, 'e.status');
                    const connection = get(reason, 'connection');
                    const e = get(reason, 'e');

                    if (HttpStatus.SERVICE_UNAVAILABLE === httpStatus) {
                        invalidConnections.push(item);
                    }

                    this.log('SDLC Service Unavailable', account, {
                        error: new ErrorCodeException(
                            'SDLC Service Unavailable',
                            HttpStatus.SERVICE_UNAVAILABLE,
                            ErrorCode.SDLC_SERVICE_UNAVAILABLE,
                        ),
                        connection: omit(connection, ['metadata']),
                        e,
                    });
                } else {
                    ticketsForValidConnection.push(...flatten(value));
                }

                return {
                    ...acc,
                    mappedExternalTickets: {
                        ...acc.mappedExternalTickets,
                        ...keyBy(ticketsForValidConnection, 'ticketId'),
                    },
                    falsyConnections: [...acc.falsyConnections, ...invalidConnections],
                };
            },
            { mappedExternalTickets: {}, falsyConnections: [] },
        );

        if (falsyConnections.length === externalTickets.length) {
            throw new ErrorCodeException(
                'SDLC Service Unavailable',
                HttpStatus.SERVICE_UNAVAILABLE,
                ErrorCode.SDLC_SERVICE_UNAVAILABLE,
            );
        }

        return mappedExternalTickets;
    }

    async buildTicketsWithAttachments(account: Account, tickets: Ticket[]): Promise<TicketType[]> {
        if (isEmpty(tickets)) {
            return [];
        }

        const mappedTickets = groupBy(tickets, 'connection.id');
        const mappedConnections = chain(tickets).map('connection').keyBy('id').value();

        const externalTickets = await (Promise as any).allSettled(
            map(mappedTickets, (item, key) => {
                const connection = mappedConnections[key];
                const issueIds = map(item, 'externalTicketId');
                const ticketsIds = map(item, 'id');
                return this.getTicketsWithAttachments(
                    account,
                    issueIds,
                    connection.id,
                    connection,
                ).catch(e => {
                    tickets = filter(tickets, ({ id }) => !ticketsIds.includes(id));
                    throw new TicketConnectionError(e, connection);
                });
            }),
        );

        const mappedExternalTickets = this.handleExternalTickets(account, externalTickets);

        return !isEmpty(mappedExternalTickets)
            ? map(tickets, ticket => ({
                  ticket,
                  ticketingIssueType: mappedExternalTickets[ticket.externalTicketId] ?? null,
              }))
            : [];
    }

    @TenancyTransaction()
    async closeTickets(ticketsIds: number[]): Promise<void> {
        await this.ticketRepository.update(
            {
                id: In(ticketsIds),
            },
            {
                lastSyncedAt: new Date(),
                isDone: true,
            },
        );
    }

    @TenancyTransaction()
    async reopenTickets(ticketsIds: number[]): Promise<void> {
        await this.ticketRepository.update(
            {
                id: In(ticketsIds),
            },
            {
                lastSyncedAt: new Date(),
                isDone: false,
            },
        );
    }

    async getIssues(account: Account, tickets: Ticket[]): Promise<TicketingIssueType[]> {
        if (isEmpty(tickets)) {
            return [];
        }

        const externalTickets: TicketingIssueType[] = [];
        const groupTickets = chain(tickets).groupBy('connection.id').values().value();

        const getIssues = async (gTickets: Ticket[]) => {
            const connection = gTickets[0].connection;
            const issueIds = gTickets.map(ticket => ticket.externalTicketId);

            try {
                const ticketService = await this.ticketsCoreService.getTicketService(
                    account,
                    connection.id,
                    connection,
                );
                if (!ticketService) {
                    this.logger.warn(
                        PolloAdapter.acct(
                            `Ticket service unavailable for connection ${connection.id}`,
                            account,
                        ),
                    );
                    return;
                }
                const issues = await ticketService.getIssues(issueIds);
                externalTickets.push(...issues);
            } catch (error) {
                this.logger.error(
                    PolloAdapter.acct(`getIssues: Error finding '${issueIds}'`, account)
                        .setIdentifier(error)
                        .setError(error),
                );
            }
        };

        await Promise.allSettled(groupTickets.map(getIssues));

        return externalTickets;
    }

    async buildTickets(
        account: Account,
        tickets: Ticket[],
        isCompleted: boolean,
    ): Promise<TicketType[]> {
        const data: TicketType[] = [];

        if (!isEmpty(tickets)) {
            const externalTickets = await this.getIssues(account, tickets);

            for (const ticket of tickets) {
                const externalTicket = externalTickets.find(
                    extTicket => extTicket.ticketId === ticket.externalTicketId,
                );

                if (isCompleted === ticket.isDone) {
                    data.push({
                        ticket: ticket,
                        ticketingIssueType: externalTicket,
                    });
                }
            }
        }

        return data;
    }

    async syncTickets(account: Account, tickets: Ticket[]): Promise<[number, number]> {
        const data: TicketType[] = [];
        const MAX_CONCURRENT_TICKET_SERVICES = 10;
        if (!isEmpty(tickets)) {
            const externalTickets = await this.getIssues(account, tickets);
            const groupTickets = chain(tickets).groupBy('connection.id').values().value();

            // Create a map of ticket services by connection ID to reuse instances
            const ticketServiceMap = new Map<number, ITicketService>();

            const processIssues = async (ticketService: ITicketService, ticket: Ticket) => {
                const externalTicket = externalTickets.find(
                    extTicket => extTicket.ticketId === ticket.externalTicketId,
                );

                if (isNil(externalTicket)) {
                    // if nothing came back from jira, the the ticket was not found
                    // we should update the drata ticket as deleted
                    await this.ticketsCoreService.deleteTicket(ticket);
                    return;
                }

                if (ticket.isDone !== ticketService.isTicketDone(externalTicket)) {
                    // always going to be subtracted cause we are only getting either the
                    // in_progress or done tickets. So if there is a change the total will be less.
                    await this.updateTicketStatus(externalTicket, ticket);
                }

                data.push({
                    ticket: ticket,
                    ticketingIssueType: externalTicket,
                });
            };
            // get unique list of Connections to get ticket services and avoid fetch the same connection multiple times
            const uniqueConnections = Object.values(
                groupTickets.reduce(
                    (acc, gTicket) => {
                        const connection = gTicket[0].connection;
                        acc[connection.id] = connection;
                        return acc;
                    },
                    {} as Record<string, ConnectionEntity>,
                ),
            );
            await batchIteration(
                uniqueConnections,
                async connection => {
                    const connectionId = connection.id;
                    const ticketService = await this.ticketsCoreService.getTicketService(
                        account,
                        connectionId,
                        connection,
                    );

                    if (!ticketService) {
                        this.logger.warn(
                            PolloAdapter.acct(
                                `Ticket service unavailable for connection ${connectionId}`,
                                account,
                            ),
                        );
                        return;
                    }

                    ticketServiceMap.set(connectionId, ticketService);
                },
                MAX_CONCURRENT_TICKET_SERVICES,
            );

            const syncIssues = async (gTickets: Ticket[]) => {
                const [
                    {
                        connection: { id: connectionId },
                    },
                ] = gTickets;

                try {
                    const ticketService = ticketServiceMap.get(connectionId);
                    if (!ticketService) {
                        this.logger.warn(
                            PolloAdapter.acct(
                                `Ticket service unavailable for connection ${connectionId}`,
                                account,
                            ),
                        );
                        return;
                    }

                    await Promise.allSettled(
                        gTickets.map(ticket => processIssues(ticketService, ticket)),
                    );
                } catch (error) {
                    this.logger.error(
                        PolloAdapter.acct('syncTickets: ' + error.message, account)
                            .setResult(gTickets)
                            .setError(error),
                    );
                }
            };

            await batchIteration(groupTickets, syncIssues, MAX_CONCURRENT_TICKET_SERVICES);
        }

        const filteredData = data.filter(ticket => !isNil(ticket.ticketingIssueType));

        if (!isEmpty(tickets) && isEmpty(filteredData)) {
            throw new ConnectionFailedException(
                'Could not retrieve tickets from providers due to connection issues',
            );
        }

        const totalInProgress = data.filter(({ ticket }) => !ticket.isDone).length;
        const totalInDone = data.filter(({ ticket }) => ticket.isDone).length;

        return [totalInProgress, totalInDone];
    }

    async createInternalTicket(
        account: Account,
        user: User,
        requestDto: TicketBaseRequestDto,
    ): Promise<Ticket> {
        this.validateCreateIssueFields(requestDto.fields);

        const externalTicket = await this.createExternalTicket(account, requestDto);
        const connection = await this.ticketsCoreService.getConnection(requestDto.connectionId);

        const ticket = new Ticket();
        ticket.externalTicketId = externalTicket.ticketId;
        ticket.isDone = false;
        ticket.user = user;
        ticket.connection = connection;

        return this.ticketRepository.save(ticket);
    }

    async getTicketsFiles(
        account: Account,
        personnelIds: number[],
        requestorFullname: User,
    ): Promise<FileBufferTicketType[]> {
        const personnelTickets =
            await this.personnelTicketRepository.getPersonnelTicketsByPersonnelIdsAndType(
                personnelIds,
                PersonnelTicketType.OFFBOARDING,
            );

        const mappedPersonnelTickets = groupBy(personnelTickets, 'ticket.externalTicketId');

        const buildedTickets = await this.buildTicketsWithAttachments(
            account,
            map(personnelTickets, 'ticket'),
        );

        const settledFiles = await (Promise as any).allSettled(
            flatMap(buildedTickets, externalBuildedTicket => {
                const { ticketingIssueType, ticket } = externalBuildedTicket;
                const { externalTicketId } = ticket;
                const { attachments = [] } = ticketingIssueType;
                const personnel = mappedPersonnelTickets[externalTicketId];
                return flatMap(personnel, async ({ personnel: p }) => {
                    const formattedAttachments = attachments.map(
                        ({ stream: attachmentStream, fileName: attachmentFilename }) => ({
                            stream: attachmentStream,
                            fileName: `${externalTicketId}/${sanitizeFileName(attachmentFilename)}`,
                            ticket,
                            personnel: p,
                        }),
                    );

                    const { filename: fileName, stream } =
                        await this.ticketsCoreService.buildTicketPdf(
                            account,
                            requestorFullname,
                            externalBuildedTicket,
                        );
                    return [
                        ...formattedAttachments,
                        {
                            stream,
                            filename: `${externalTicketId}/${sanitizeFileName(fileName)}`,

                            ticket,
                            personnel: p,
                        },
                    ];
                });
            }),
        );

        return chain(settledFiles)
            .filter(({ status }) => status === 'fulfilled')
            .map(({ value }) => value)
            .flatten()
            .value();
    }

    async getTicketsForMonitorInstance(
        account: Account,
        requestDto: TicketsMonitorInstancesRequestDto,
    ): Promise<PaginationType<TicketType>> {
        const { page, limit } = requestDto;

        this.logger.log(
            PolloAdapter.acct(
                `Request tickets from monitor instance with id ${requestDto.monitorInstanceId}`,
                account,
            ).setIdentifier({ requestDto }),
        );

        const [tickets, total] =
            await this.ticketRepository.getTicketsForMonitorInstance(requestDto);

        this.logger.log(
            PolloAdapter.acct(
                `${total} tickets retrieved in ${
                    requestDto.isCompleted ? 'completed' : 'pending'
                } status`,
                account,
            ),
        );

        const data = await this.buildTickets(account, tickets, requestDto.isCompleted);

        return {
            data,
            page,
            limit,
            total,
        };
    }

    async syncTicketsForMonitorInstance(
        account: Account,
        requestDto: TicketsMonitorInstancesRequestDto,
    ): Promise<{ inProgress: number; inDone: number; total: number }> {
        this.logger.log(
            PolloAdapter.acct(
                `Syncing tickets for monitor instance id ${requestDto.monitorInstanceId}`,
                account,
            ),
        );
        const [tickets, total] = await this.ticketRepository.getTicketsForMonitorInstance({
            monitorInstanceId: requestDto.monitorInstanceId,
            page: 1,
            limit: 1000,
        });
        this.logger.log(
            PolloAdapter.acct(`${total} tickets retrieved`, account).setIdentifier({
                totalInProgress: tickets.filter(ticket => ticket.isDone === false).length,
                totalInDone: tickets.filter(ticket => ticket.isDone === true).length,
            }),
        );

        const [totalInProgress, totalInDone] = await this.syncTickets(account, tickets);

        this.logger.log(
            PolloAdapter.acct(
                `${totalInProgress} tickets in progress, ${totalInDone} tickets in done`,
                account,
            ),
        );

        // update index since tickets may have changed states with regard to monitor instances
        await updateTicketsForMonitoringSummaryResultsIndexForAccount(
            this.monitoringSummaryIndexingCoreService,
            account,
        );

        return {
            total: total,
            inProgress: totalInProgress,
            inDone: totalInDone,
        };
    }

    private async getTicketsWithAttachments(
        account: Account,
        issueIds: string[],
        connectionId: number,
        conn?: ConnectionEntity,
    ): Promise<TicketingIssueType[]> {
        try {
            const ticketService = await this.ticketsCoreService.getTicketService(
                account,
                connectionId,
                conn,
            );
            if (!ticketService) {
                throw new Error(`Ticket Service not found for connectionId ${connectionId}`);
            }
            return await ticketService.buildIssuesWithAttachments(issueIds);
        } catch (error) {
            this.error(error, account, { issueIds });
            throw new ServiceUnavailableException(error);
        }
    }

    private async updateTicketStatus(
        newTicket: TicketingIssueType,
        oldTicket: Ticket,
    ): Promise<Ticket> {
        if (!isNil(newTicket)) {
            oldTicket.lastSyncedAt = new Date();
            oldTicket.isDone = isExternalTicketDone(oldTicket.connection, newTicket);
            oldTicket.completionDate = isExternalTicketDone(oldTicket.connection, newTicket)
                ? newTicket.fields.completedAt
                : null;

            return this.ticketRepository.save(oldTicket);
        }
    }

    private async createExternalTicket(
        account: Account,
        requestDto: TicketBaseRequestDto,
    ): Promise<CreatedIssueType> {
        const ticketService = await this.ticketsCoreService.getTicketService(
            account,
            requestDto.connectionId,
        );
        const externalTicket = await ticketService?.createTicket(requestDto);

        if (isNil(externalTicket)) {
            throw new BadRequestException();
        }

        return externalTicket;
    }

    private throwTicketsBadRequest(msg: string, errorCode: ErrorCode) {
        throw new ErrorCodeException(msg, HttpStatus.BAD_REQUEST, errorCode);
    }

    private validateCreateIssueFields(fields: TicketsCreateFieldRequestDto[]): boolean {
        let valid = true;

        fields.forEach(field => {
            switch (field.type) {
                case 'datetime':
                    const datetime = moment(field.value, 'YYYY-MM-DDTHH:mm:ss.SSSZ', true);
                    valid = valid && datetime.isValid();
                    break;
                case 'date': {
                    const date = moment(field.value, 'YYYY-MM-DD', true);
                    valid = valid && date.isValid();
                    break;
                }
                case 'labels':
                case 'user':
                case 'priority':
                case 'string':
                case 'text':
                    valid = valid && !isEmpty(field.value);
                    break;
                case 'option':
                case 'dropdown':
                    valid = valid && (!isNaN(Number(field.value)) || !isEmpty(field.value)); // ServiceNow allows string values
                    break;
            }

            if (!valid) {
                const error = `The field '${field.field}' value is not valid for expected type of '${field.type}'.`;

                this.logger.log(PolloMessage.msg(error));
                this.throwTicketsBadRequest(error, ErrorCode.CREATE_TICKET_FIELD_NOT_VALID);
            }
        });

        return valid;
    }

    private async getConnectionByUrl(ticketHost: string): Promise<ConnectionEntity> {
        return this.connectionsCoreService.getTicketingConnectionByDomain(ticketHost);
    }

    private async getTestContent(
        account: Account,
        dto: TicketsCreateRequestDto,
        clientType: ClientType,
    ): Promise<{
        summary: string;
        content: Record<string, unknown>;
        attachments: FileBufferOnlyType[];
    }> {
        if (dto.relatedEntityEnum !== TicketReferenceType.MONITOR) {
            throw new BadRequestException('Given IDs must belong to monitor instances.');
        }

        // Frontend is sending only one monitor instance ID
        const [monitorInstanceId] = dto.relatedEntityIds;

        const monitorInstance = await this.getMonitorInstanceByIdWithControlTest(monitorInstanceId);

        let summary: string;
        let content: Record<string, unknown>;
        if (dto.useRichContent) {
            ({ summary, content } = await this.getRichTestContent(
                account,
                monitorInstance,
                dto,
                clientType,
            ));
        } else {
            ({ summary, content } = this.getBasicTestContent(account, monitorInstance));
        }

        const attachments = await this.getMonitorAttachmentsForTicket(account, monitorInstance);

        return { summary, content, attachments };
    }

    private async getMonitorAttachmentsForTicket(
        account: Account,
        monitorInstance: MonitorInstance,
    ): Promise<FileBufferOnlyType[]> {
        const attachments: FileBufferOnlyType[] = [];

        const hasValidMonitorCheckType = monitorInstance.monitorInstanceCheckTypes.some(checkType =>
            MonitorConstants.AVAILABLE_MONITOR_TEST_REPORT_TYPES.includes(checkType.checkType),
        );

        const hasValidMonitorCheckResultStatus =
            MonitorConstants.AVAILABLE_MONITOR_TEST_CHECK_RESULT_STATUSES.includes(
                monitorInstance.checkResultStatus,
            );

        if (hasValidMonitorCheckType && hasValidMonitorCheckResultStatus) {
            const monitorTestReportZipFiles = await this.getMonitorTestReportZipFiles(
                account,
                monitorInstance,
            );

            if (!isEmpty(monitorTestReportZipFiles)) {
                attachments.push(...monitorTestReportZipFiles);
            }
        }

        return attachments;
    }

    private async getMonitorTestReportZipFiles(
        account: Account,
        monitorInstance: MonitorInstance,
    ): Promise<FileBufferOnlyType[]> {
        try {
            const fileBuffers: FileBufferOnlyType[] = [];
            const workspaceId = getProductId(account) ?? 1;
            const { controlTestInstance } = monitorInstance;

            /**
             * Only included resources (findings) are supported for now. We don't have a use case
             * for excluded resources for ticket automation yet.
             */
            const type = MonitorFindingsType.INCLUDED;

            for await (const checkType of monitorInstance.monitorInstanceCheckTypes) {
                const fileBuffer = await this.monitorsCoreService.generateMonitorTestReportZip(
                    account,
                    controlTestInstance.testId,
                    type,
                    CheckType[checkType.checkType],
                    workspaceId,
                );

                const stream = Buffer.isBuffer(fileBuffer.stream)
                    ? fileBuffer.stream
                    : await getBufferFromStream(fileBuffer.stream);

                fileBuffers.push({
                    stream,
                    filename: fileBuffer.filename,
                    fileType: MimeType.ZIP_FILE,
                });
            }

            return fileBuffers;
        } catch (error) {
            this.logger.error(
                PolloAdapter.acct(
                    `Failed to generate monitor test report zip for monitor instance ${monitorInstance.id}`,
                    account,
                )
                    .setContext(this.constructor.name)
                    .setSubContext(this.getMonitorTestReportZipFiles.name)
                    .setError(error),
            );
            return [];
        }
    }

    private getMonitorInstanceByIdWithControlTest(id: number): Promise<MonitorInstance> {
        return this.monitorsCoreService.getMonitorInstanceByIdWithControlTest(id);
    }

    private async getRichTestContent(
        account: Account,
        monitorInstance: MonitorInstance,
        dto: TicketsCreateRequestDto,
        clientType: ClientType,
    ): Promise<{
        summary: string;
        content: { description: string; summary: string };
        createSubtasks: boolean;
    }> {
        const controlTestInstance = monitorInstance?.controlTestInstance;

        if (controlTestInstance?.source !== TestSource.ACORN) {
            throw new NotImplementedException(
                `Rich content is not supported for ${
                    TestSource[monitorInstance.controlTestInstance.source]
                } control test instance sources.`,
            );
        }

        const controlFrameworks = await this.frameworkRepository.getFrameworksByControlIds([
            controlTestInstance.id,
        ]);

        const content = await new TicketMonitoringTemplateBuilder(
            this.templatedContentService,
        ).buildRichContentTicket(account, dto, monitorInstance, controlFrameworks, clientType);

        return {
            summary: content.summary,
            content: content,
            createSubtasks: content.createSubtasks,
        };
    }

    private getBasicTestContent(
        account: Account,
        monitorInstance: MonitorInstance,
    ): { summary: string; content: Record<string, unknown> } {
        const controlTestInstance = monitorInstance?.controlTestInstance;
        const isFailedTest = controlTestInstance.checkResultStatus === CheckResultStatus.FAILED;
        const summary = `Drata | ${account.getCurrentProduct().name} | ${
            isFailedTest ? 'Test failed' : 'Test Error'
        } | ${controlTestInstance.name}`;

        const content = {
            ['Test description: ']: this.getFieldContentValue(
                replaceTarget(monitorInstance.evidenceCollectionDescription, account.companyName),
            ),
            ['Last result: ']: isFailedTest ? 'Failed' : 'Error',
            ['Reason: ']: this.getFieldContentValue(
                replaceTarget(monitorInstance.failedTestDescription, account.companyName),
            ),
            ['Impacted controls: ']: this.getFieldContentValue(
                controlTestInstance.controls?.map(control => control.code).join(', '),
            ),
            ['Test URL: ']: `${config.get('url.webApp')}/compliance/monitoring/details/${
                controlTestInstance.testId
            }`,
        };

        if (!isNil(monitorInstance.url)) {
            content['Help Article URL: '] = this.getFieldContentValue(monitorInstance.url);
        }

        if (!isFailedTest) {
            delete content['Reason: '];
        }

        return { summary, content };
    }

    private getFieldContentValue(value: string): string {
        // Jira throws an ["INVALID_INPUT"] error if the text is empty
        // for a paragraph content of type text
        return isEmpty(value) ? ' ' : value;
    }

    private get ticketRepository(): TicketRepository {
        return this.getCustomTenantRepository(TicketRepository);
    }

    private get personnelTicketRepository(): PersonnelTicketRepository {
        return this.getCustomTenantRepository(PersonnelTicketRepository);
    }

    private get frameworkRepository(): FrameworkRepository {
        return this.getCustomTenantRepository(FrameworkRepository);
    }
}
