import { TestingModule } from '@nestjs/testing';
import { WorkspacesCoreService } from 'app/companies/products/services/workspaces-core.service';
import { TaskNotificationAction } from 'app/grc/enums/task-notification-action.enum';
import {
    TASKS_TO_SHOW_IN_REMINDERS,
    WORKSPACE_AWARE_TASK_TYPES,
} from 'app/grc/services/tasks.constants';
import { TasksCoreService } from 'app/tasks/core/services/tasks-core.service';
import { UpcomingTasksRemindersProviderService } from 'app/tasks/upcoming-tasks-reminders/services/upcoming-tasks-reminders-provider.service';
import { bulkTaskNotificationsWorkflowV1 } from 'app/worker/workflows';
import { TaskNotification } from 'app/worker/workflows/grc/types/task-notification.type';
import {
    FeatureTypeDataMap,
    GenericUserReminderProviderConfig,
} from 'app/worker/workflows/reminders/interfaces/user-reminder-provider-config.interface';
import { RemindersScheduleConfig } from 'app/worker/workflows/reminders/types/reminders-schedule-config.type';
import { Account } from 'auth/entities/account.entity';
import { AccountEntitlementType } from 'commons/enums/account-entitlement-type.enum';
import { FeatureType } from 'commons/enums/feature-toggling/feature.enum';
import { TaskStatus } from 'commons/enums/task-status.enum';
import { UpcomingTaskType } from 'commons/enums/upcoming-task-type-enum';
import { createAppTestingModule } from 'commons/helpers/app-testing-module.helper';
import { getTemporalClient } from 'commons/helpers/temporal/client';
import config from 'config';
import { mock, MockProxy } from 'jest-mock-extended';

// Mock external dependencies
jest.mock('commons/helpers/temporal/client');
jest.mock('app/worker/workflows');
jest.mock('config');

describe('UpcomingTasksRemindersProviderService', () => {
    let service: UpcomingTasksRemindersProviderService;
    let mockTasksCoreService: MockProxy<TasksCoreService>;
    let mockWorkspacesCoreService: MockProxy<WorkspacesCoreService>;
    let mockTemporalClient: MockProxy<any>;

    // Test constants
    const TEST_CONSTANTS = {
        ACCOUNT_ID: '********-1234-1234-1234-************',
        ACCOUNT_DOMAIN: 'test.com',
        USER_ID: 1,
        USER_EMAIL: '<EMAIL>',
        WORKSPACE_ID: 1,
        WORKSPACE_NAME: 'Test Workspace',
        TASK_ID: 1,
        TASK_NAME: 'Test Task',
        DUE_DATE: '2023-01-15',
        YEAR: 2023,
        MONTH: 0,
        DAY: 15,
        HOUR: 6,
        MINUTE: 0,
        TIMEZONE: 'America/Chicago',
    };

    const mockAccount: Account = {
        id: TEST_CONSTANTS.ACCOUNT_ID,
        domain: TEST_CONSTANTS.ACCOUNT_DOMAIN,
        entitlements: [{ type: AccountEntitlementType.RISK_MANAGEMENT } as any],
    } as Account;

    const mockRemindersScheduleConfig: RemindersScheduleConfig = {
        year: TEST_CONSTANTS.YEAR,
        month: TEST_CONSTANTS.MONTH,
        day: TEST_CONSTANTS.DAY,
        hour: TEST_CONSTANTS.HOUR,
        minute: TEST_CONSTANTS.MINUTE,
        timezone: TEST_CONSTANTS.TIMEZONE,
    };

    const mockTaskNotification: TaskNotification = {
        id: TEST_CONSTANTS.TASK_ID,
        workspaceId: TEST_CONSTANTS.WORKSPACE_ID,
        title: TEST_CONSTANTS.TASK_NAME,
        dueDate: TEST_CONSTANTS.DUE_DATE,
        taskType: UpcomingTaskType.CONTROL,
        assignedTo: {
            id: TEST_CONSTANTS.USER_ID,
            email: TEST_CONSTANTS.USER_EMAIL,
            firstName: 'Test',
            lastName: 'User',
        },
    };

    const mockRiskTaskNotification: TaskNotification = {
        ...mockTaskNotification,
        taskType: UpcomingTaskType.RISK,
    };

    const mockWorkspaces = [
        { id: TEST_CONSTANTS.WORKSPACE_ID, name: TEST_CONSTANTS.WORKSPACE_NAME },
        { id: 2, name: 'Workspace 2' },
    ];

    const mockTaskDetailsResponse = {
        data: [
            {
                id: TEST_CONSTANTS.TASK_ID,
                renewalDate: TEST_CONSTANTS.DUE_DATE,
                name: TEST_CONSTANTS.TASK_NAME,
                type: UpcomingTaskType.CONTROL,
                userIds: [TEST_CONSTANTS.USER_ID],
                completedAt: null,
            },
        ],
        total: 1,
        page: 1,
        limit: TASKS_TO_SHOW_IN_REMINDERS,
    };

    const mockFeatureTypeData: FeatureTypeDataMap[FeatureType.UPCOMING_TASKS_REMINDERS] = {
        userId: TEST_CONSTANTS.USER_ID,
        totalNumberOfTasks: 1,
        tasksToIncludeInTheMail: [mockTaskNotification],
        includeRiskTasks: true,
    };

    const mockNoRiskFeatureTypeData: FeatureTypeDataMap[FeatureType.UPCOMING_TASKS_REMINDERS] = {
        userId: TEST_CONSTANTS.USER_ID,
        totalNumberOfTasks: 2,
        tasksToIncludeInTheMail: [mockRiskTaskNotification, mockTaskNotification],
        includeRiskTasks: false,
    };

    const mockReminderConfig: GenericUserReminderProviderConfig[FeatureType.UPCOMING_TASKS_REMINDERS] =
        {
            ...mockRemindersScheduleConfig,
            featureType: FeatureType.UPCOMING_TASKS_REMINDERS,
            data: mockFeatureTypeData,
            user: {
                id: TEST_CONSTANTS.USER_ID,
                email: TEST_CONSTANTS.USER_EMAIL,
                firstName: 'Test',
                lastName: 'User',
            },
        };

    beforeEach(async () => {
        const module: TestingModule = await createAppTestingModule({
            providers: [
                UpcomingTasksRemindersProviderService,
                {
                    provide: TasksCoreService,
                    useValue: mock<TasksCoreService>(),
                },
                {
                    provide: WorkspacesCoreService,
                    useValue: mock<WorkspacesCoreService>(),
                },
            ],
        }).compile();

        service = module.get<UpcomingTasksRemindersProviderService>(
            UpcomingTasksRemindersProviderService,
        );
        mockTasksCoreService = module.get(TasksCoreService);
        mockWorkspacesCoreService = module.get(WorkspacesCoreService);

        // Setup temporal client mock
        mockTemporalClient = mock<any>();
        (getTemporalClient as jest.Mock).mockResolvedValue(mockTemporalClient);
        (config.get as jest.Mock).mockReturnValue('test-task-queue');
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    it('should be defined', () => {
        expect(service).toBeDefined();
    });

    describe('validateReminderToBeSent', () => {
        describe('when reminderConfig is null', () => {
            it('should return null', async () => {
                const result = await service.validateReminderToBeSent(mockFeatureTypeData, null);

                expect(result).toBeNull();
            });
        });

        describe('when reminderConfig is undefined', () => {
            it('should return null', async () => {
                const result = await service.validateReminderToBeSent(
                    mockFeatureTypeData,
                    undefined,
                );

                expect(result).toBeNull();
            });
        });

        describe('when reminderConfig is provided', () => {
            beforeEach(() => {
                mockWorkspacesCoreService.getAllWorkspaceNamesAndIds.mockResolvedValue(
                    mockWorkspaces,
                );
                mockTasksCoreService.getTaskDetailsWithRecurrence.mockResolvedValue(
                    mockTaskDetailsResponse,
                );
                mockTasksCoreService.getCustomTasksForTaskDetails.mockResolvedValue([
                    { taskDetail: mockTaskDetailsResponse.data[0], customTask: undefined },
                ]);
                mockTasksCoreService.adaptToTaskNotifications.mockReturnValue(mockTaskNotification);
                mockTasksCoreService.deduplicateTasks.mockImplementation(tasks => tasks);
            });

            describe('when user has tasks', () => {
                it('should return task reminder data', async () => {
                    const result = await service.validateReminderToBeSent(
                        mockFeatureTypeData,
                        mockRemindersScheduleConfig,
                    );

                    expect(result).toEqual({
                        userId: TEST_CONSTANTS.USER_ID,
                        totalNumberOfTasks: expect.any(Number),
                        tasksToIncludeInTheMail: expect.any(Array),
                        includeRiskTasks: expect.any(Boolean),
                    });
                    expect(result?.totalNumberOfTasks).toBeGreaterThan(0);
                });
            });

            describe('when user has no tasks', () => {
                beforeEach(() => {
                    mockTasksCoreService.getTaskDetailsWithRecurrence.mockResolvedValue({
                        data: [],
                        total: 0,
                        page: 1,
                        limit: TASKS_TO_SHOW_IN_REMINDERS,
                    });
                    mockTasksCoreService.getCustomTasksForTaskDetails.mockResolvedValue([]);
                });

                it('should return null', async () => {
                    const result = await service.validateReminderToBeSent(
                        mockFeatureTypeData,
                        mockRemindersScheduleConfig,
                    );

                    expect(result).toBeNull();
                });
            });
        });
    });

    describe('send', () => {
        it('should execute temporal workflow with correct parameters', async () => {
            await service.send(mockAccount, mockReminderConfig);

            expect(getTemporalClient).toHaveBeenCalledWith(mockAccount.domain);
            expect(mockTemporalClient.executeWorkflow).toHaveBeenCalledWith(
                bulkTaskNotificationsWorkflowV1,
                {
                    args: [
                        {
                            tasks: mockReminderConfig.data.tasksToIncludeInTheMail,
                            taskCount: mockReminderConfig.data.totalNumberOfTasks,
                            account: mockAccount,
                            action: TaskNotificationAction.TASK_UPCOMING,
                            user: mockReminderConfig.user,
                        },
                    ],
                    taskQueue: 'test-task-queue',
                    memo: {
                        accountId: mockAccount.id,
                        domain: mockAccount.domain,
                    },
                },
            );
        });

        describe('when temporal client throws error', () => {
            beforeEach(() => {
                mockTemporalClient.executeWorkflow.mockRejectedValue(new Error('Temporal error'));
            });

            it('should propagate the error', async () => {
                await expect(service.send(mockAccount, mockReminderConfig)).rejects.toThrow(
                    'Temporal error',
                );

                expect(getTemporalClient).toHaveBeenCalledWith(mockAccount.domain);
            });
        });
    });

    // Testing private methods through public interface - validateReminderToBeSent calls getUserTaskDataForReminder
    describe('getUserTaskDataForReminder (via validateReminderToBeSent)', () => {
        beforeEach(() => {
            mockWorkspacesCoreService.getAllWorkspaceNamesAndIds.mockResolvedValue(mockWorkspaces);
        });

        describe('when fetching workspace aware tasks', () => {
            beforeEach(() => {
                mockTasksCoreService.getTaskDetailsWithRecurrence.mockResolvedValue(
                    mockTaskDetailsResponse,
                );
                mockTasksCoreService.getCustomTasksForTaskDetails.mockResolvedValue([
                    { taskDetail: mockTaskDetailsResponse.data[0], customTask: undefined },
                ]);
                mockTasksCoreService.adaptToTaskNotifications.mockReturnValue(mockTaskNotification);
                mockTasksCoreService.deduplicateTasks.mockImplementation(tasks => tasks);
            });

            it('should call getTaskDetailsWithRecurrence with correct workspace aware parameters', async () => {
                await service.validateReminderToBeSent(
                    mockFeatureTypeData,
                    mockRemindersScheduleConfig,
                );

                expect(mockTasksCoreService.getTaskDetailsWithRecurrence).toHaveBeenCalledWith(
                    expect.objectContaining({
                        workspaceId: TEST_CONSTANTS.WORKSPACE_ID,
                        status: [TaskStatus.INCOMPLETE],
                        type: WORKSPACE_AWARE_TASK_TYPES,
                        userId: TEST_CONSTANTS.USER_ID,
                        startDate: expect.any(String),
                        endDate: expect.any(String),
                        limit: TASKS_TO_SHOW_IN_REMINDERS,
                        page: 1,
                    }),
                );
            });

            it('should call getCustomTasksForTaskDetails with task data', async () => {
                await service.validateReminderToBeSent(
                    mockFeatureTypeData,
                    mockRemindersScheduleConfig,
                );

                expect(mockTasksCoreService.getCustomTasksForTaskDetails).toHaveBeenCalledWith(
                    mockTaskDetailsResponse.data,
                );
            });

            it('should call adaptToTaskNotifications with correct parameters', async () => {
                await service.validateReminderToBeSent(
                    mockFeatureTypeData,
                    mockRemindersScheduleConfig,
                );

                expect(mockTasksCoreService.adaptToTaskNotifications).toHaveBeenCalledWith({
                    taskData: {
                        taskDetail: mockTaskDetailsResponse.data[0],
                        customTask: undefined,
                    },
                    workspaceId: TEST_CONSTANTS.WORKSPACE_ID,
                    assignedUserId: TEST_CONSTANTS.USER_ID,
                });
            });
        });

        describe('when account does not have risk management entitlement', () => {
            beforeEach(() => {
                mockTasksCoreService.getTaskDetailsWithRecurrence.mockResolvedValue({
                    data: [],
                    total: 0,
                    page: 1,
                    limit: TASKS_TO_SHOW_IN_REMINDERS,
                });
                mockTasksCoreService.getCustomTasksForTaskDetails.mockResolvedValue([]);
            });

            it('should filter out RISK tasks from workspace aware task types', async () => {
                await service.validateReminderToBeSent(
                    mockNoRiskFeatureTypeData,
                    mockRemindersScheduleConfig,
                );

                const expectedTaskTypes = WORKSPACE_AWARE_TASK_TYPES.filter(
                    taskType => taskType !== UpcomingTaskType.RISK,
                );

                expect(mockTasksCoreService.getTaskDetailsWithRecurrence).toHaveBeenCalledWith(
                    expect.objectContaining({
                        type: expectedTaskTypes,
                    }),
                );
            });
        });

        describe('when fetching vendor tasks', () => {
            beforeEach(() => {
                // Mock workspace aware tasks to return empty for both workspaces
                mockTasksCoreService.getTaskDetailsWithRecurrence
                    .mockResolvedValueOnce({
                        data: [],
                        total: 0,
                        page: 1,
                        limit: TASKS_TO_SHOW_IN_REMINDERS,
                    })
                    .mockResolvedValueOnce({
                        data: [],
                        total: 0,
                        page: 1,
                        limit: TASKS_TO_SHOW_IN_REMINDERS,
                    })
                    // Mock vendor tasks (only called once)
                    .mockResolvedValueOnce({
                        data: [
                            {
                                id: 2,
                                renewalDate: TEST_CONSTANTS.DUE_DATE,
                                name: 'Vendor Task',
                                type: UpcomingTaskType.VENDOR,
                                userIds: [TEST_CONSTANTS.USER_ID],
                                completedAt: null,
                            },
                        ],
                        total: 1,
                        page: 1,
                        limit: TASKS_TO_SHOW_IN_REMINDERS,
                    })
                    // Mock policy renewal tasks for both workspaces (return empty)
                    .mockResolvedValueOnce({
                        data: [],
                        total: 0,
                        page: 1,
                        limit: TASKS_TO_SHOW_IN_REMINDERS,
                    })
                    .mockResolvedValueOnce({
                        data: [],
                        total: 0,
                        page: 1,
                        limit: TASKS_TO_SHOW_IN_REMINDERS,
                    });
                mockTasksCoreService.getCustomTasksForTaskDetails.mockResolvedValue([]);
                mockTasksCoreService.adaptToTaskNotifications.mockReturnValue({
                    ...mockTaskNotification,
                    id: 2,
                    title: 'Vendor Task',
                    taskType: UpcomingTaskType.VENDOR,
                });
                mockTasksCoreService.deduplicateTasks.mockImplementation(tasks => tasks);
            });

            it('should call getTaskDetailsWithRecurrence with vendor task parameters', async () => {
                await service.validateReminderToBeSent(
                    mockFeatureTypeData,
                    mockRemindersScheduleConfig,
                );

                expect(mockTasksCoreService.getTaskDetailsWithRecurrence).toHaveBeenCalledWith({
                    workspaceId: TEST_CONSTANTS.WORKSPACE_ID,
                    status: [TaskStatus.INCOMPLETE],
                    type: [UpcomingTaskType.VENDOR],
                    userId: TEST_CONSTANTS.USER_ID,
                    startDate: expect.any(String),
                    endDate: expect.any(String),
                    limit: TASKS_TO_SHOW_IN_REMINDERS,
                    page: 1,
                    upcomingOnly: true,
                });
            });

            it('should only fetch vendor tasks once across all workspaces', async () => {
                await service.validateReminderToBeSent(
                    mockFeatureTypeData,
                    mockRemindersScheduleConfig,
                );

                // Should be called 5 times total: 2 workspace aware calls + 1 vendor call + 2 policy renewal calls
                expect(mockTasksCoreService.getTaskDetailsWithRecurrence).toHaveBeenCalledTimes(5);

                // Verify vendor task call only happens once
                const vendorCalls =
                    mockTasksCoreService.getTaskDetailsWithRecurrence.mock.calls.filter(call =>
                        call[0].type.includes(UpcomingTaskType.VENDOR),
                    );
                expect(vendorCalls).toHaveLength(1);
            });
        });

        describe('when fetching policy renewal tasks', () => {
            beforeEach(() => {
                // Mock workspace aware tasks to return empty for both workspaces
                mockTasksCoreService.getTaskDetailsWithRecurrence
                    .mockResolvedValueOnce({
                        data: [],
                        total: 0,
                        page: 1,
                        limit: TASKS_TO_SHOW_IN_REMINDERS,
                    })
                    .mockResolvedValueOnce({
                        data: [],
                        total: 0,
                        page: 1,
                        limit: TASKS_TO_SHOW_IN_REMINDERS,
                    })
                    // Mock vendor tasks to return empty
                    .mockResolvedValueOnce({
                        data: [],
                        total: 0,
                        page: 1,
                        limit: TASKS_TO_SHOW_IN_REMINDERS,
                    })
                    // Mock policy renewal tasks for first workspace
                    .mockResolvedValueOnce({
                        data: [
                            {
                                id: 3,
                                renewalDate: TEST_CONSTANTS.DUE_DATE,
                                name: 'Policy Renewal Task',
                                type: UpcomingTaskType.POLICY_RENEWALS,
                                userIds: [TEST_CONSTANTS.USER_ID],
                                completedAt: null,
                            },
                        ],
                        total: 1,
                        page: 1,
                        limit: TASKS_TO_SHOW_IN_REMINDERS,
                    })
                    // Mock policy renewal tasks for second workspace (empty)
                    .mockResolvedValueOnce({
                        data: [],
                        total: 0,
                        page: 1,
                        limit: TASKS_TO_SHOW_IN_REMINDERS,
                    });
                mockTasksCoreService.getCustomTasksForTaskDetails.mockResolvedValue([]);
                mockTasksCoreService.adaptToTaskNotifications.mockReturnValue({
                    ...mockTaskNotification,
                    id: 3,
                    title: 'Policy Renewal Task',
                    taskType: UpcomingTaskType.POLICY_RENEWALS,
                });
                mockTasksCoreService.deduplicateTasks.mockImplementation(tasks => tasks);
            });

            it('should call getTaskDetailsWithRecurrence with policy renewal parameters', async () => {
                await service.validateReminderToBeSent(
                    mockFeatureTypeData,
                    mockRemindersScheduleConfig,
                );

                expect(mockTasksCoreService.getTaskDetailsWithRecurrence).toHaveBeenCalledWith({
                    workspaceId: TEST_CONSTANTS.WORKSPACE_ID,
                    status: [TaskStatus.INCOMPLETE],
                    type: [UpcomingTaskType.POLICY_RENEWALS],
                    userId: TEST_CONSTANTS.USER_ID,
                    startDate: expect.any(String),
                    endDate: expect.any(String),
                    upcomingOnly: true,
                });
            });

            it('should process policy renewal tasks correctly', async () => {
                const result = await service.validateReminderToBeSent(
                    mockFeatureTypeData,
                    mockRemindersScheduleConfig,
                );

                // Verify that the service processes the request and returns a result
                expect(result).toBeDefined();
                expect(result?.totalNumberOfTasks).toBeGreaterThan(0);

                // Verify that policy renewal tasks endpoint was called
                const policyRenewalCalls =
                    mockTasksCoreService.getTaskDetailsWithRecurrence.mock.calls.filter(call =>
                        call[0]?.type?.includes(UpcomingTaskType.POLICY_RENEWALS),
                    );
                expect(policyRenewalCalls.length).toBeGreaterThan(0);
            });
        });

        describe('when tasks need sorting and slicing', () => {
            beforeEach(() => {
                const multipleTasks = Array.from({ length: 10 }, (_, index) => ({
                    id: index + 1,
                    renewalDate: `2023-01-${15 + index}`,
                    name: `Task ${index + 1}`,
                    type: UpcomingTaskType.CONTROL,
                    userIds: [TEST_CONSTANTS.USER_ID],
                    completedAt: null,
                }));

                mockTasksCoreService.getTaskDetailsWithRecurrence.mockResolvedValue({
                    data: multipleTasks,
                    total: 10,
                    page: 1,
                    limit: TASKS_TO_SHOW_IN_REMINDERS,
                });
                mockTasksCoreService.getCustomTasksForTaskDetails.mockResolvedValue(
                    multipleTasks.map(task => ({ taskDetail: task, customTask: undefined })),
                );
                mockTasksCoreService.adaptToTaskNotifications.mockImplementation(
                    ({ taskData }) => ({
                        ...mockTaskNotification,
                        id: taskData.taskDetail.id,
                        title: taskData.taskDetail.name,
                        dueDate: taskData.taskDetail.renewalDate,
                    }),
                );
                mockTasksCoreService.deduplicateTasks.mockImplementation(tasks => tasks);
            });

            it('should limit tasks to TASKS_TO_SHOW_IN_REMINDERS', async () => {
                const result = await service.validateReminderToBeSent(
                    mockFeatureTypeData,
                    mockRemindersScheduleConfig,
                );

                expect(result?.tasksToIncludeInTheMail).toHaveLength(TASKS_TO_SHOW_IN_REMINDERS);
            });

            it('should sort tasks by due date in ascending order', async () => {
                const result = await service.validateReminderToBeSent(
                    mockFeatureTypeData,
                    mockRemindersScheduleConfig,
                );

                const dueDates = result?.tasksToIncludeInTheMail.map(task => task.dueDate) || [];
                const sortedDueDates = [...dueDates].sort(
                    (dueDateA, dueDateB) =>
                        new Date(dueDateA).getTime() - new Date(dueDateB).getTime(),
                );
                expect(dueDates).toEqual(sortedDueDates);
            });
        });

        describe('when date calculation is performed', () => {
            beforeEach(() => {
                // Mock all the required calls for both workspaces
                mockTasksCoreService.getTaskDetailsWithRecurrence
                    .mockResolvedValueOnce({
                        data: [],
                        total: 0,
                        page: 1,
                        limit: TASKS_TO_SHOW_IN_REMINDERS,
                    })
                    .mockResolvedValueOnce({
                        data: [],
                        total: 0,
                        page: 1,
                        limit: TASKS_TO_SHOW_IN_REMINDERS,
                    })
                    .mockResolvedValueOnce({
                        data: [],
                        total: 0,
                        page: 1,
                        limit: TASKS_TO_SHOW_IN_REMINDERS,
                    })
                    .mockResolvedValueOnce({
                        data: [],
                        total: 0,
                        page: 1,
                        limit: TASKS_TO_SHOW_IN_REMINDERS,
                    })
                    .mockResolvedValueOnce({
                        data: [],
                        total: 0,
                        page: 1,
                        limit: TASKS_TO_SHOW_IN_REMINDERS,
                    });
                mockTasksCoreService.getCustomTasksForTaskDetails.mockResolvedValue([]);
                mockTasksCoreService.deduplicateTasks.mockImplementation(tasks => tasks);
            });

            it('should calculate correct start and end dates based on reminders config', async () => {
                await service.validateReminderToBeSent(
                    mockFeatureTypeData,
                    mockRemindersScheduleConfig,
                );

                const calls = mockTasksCoreService.getTaskDetailsWithRecurrence.mock.calls;
                const firstCall = calls[0][0];

                // Check that dates are strings (the actual format may vary based on toISO8601String implementation)
                expect(typeof firstCall.startDate).toBe('string');
                expect(typeof firstCall.endDate).toBe('string');
                expect(firstCall.startDate).toBeTruthy();
                expect(firstCall.endDate).toBeTruthy();

                // Verify end date is DATE_RANGE_FOR_UPCOMING_TASK_REMINDERS days after start date
                if (firstCall.startDate && firstCall.endDate) {
                    const startDate = new Date(firstCall.startDate);
                    const endDate = new Date(firstCall.endDate);
                    const daysDifference = Math.round(
                        (endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24),
                    );
                    expect(daysDifference).toBe(7); // DATE_RANGE_FOR_UPCOMING_TASK_REMINDERS
                }
            });
        });

        describe('when workspace service fails', () => {
            beforeEach(() => {
                mockWorkspacesCoreService.getAllWorkspaceNamesAndIds.mockRejectedValue(
                    new Error('Workspace service error'),
                );
            });

            it('should propagate the error', async () => {
                await expect(
                    service.validateReminderToBeSent(
                        mockFeatureTypeData,
                        mockRemindersScheduleConfig,
                    ),
                ).rejects.toThrow('Workspace service error');
            });
        });

        describe('when task core service fails', () => {
            beforeEach(() => {
                mockWorkspacesCoreService.getAllWorkspaceNamesAndIds.mockResolvedValue(
                    mockWorkspaces,
                );
                mockTasksCoreService.getTaskDetailsWithRecurrence.mockRejectedValue(
                    new Error('Task core service error'),
                );
            });

            it('should propagate the error', async () => {
                await expect(
                    service.validateReminderToBeSent(
                        mockFeatureTypeData,
                        mockRemindersScheduleConfig,
                    ),
                ).rejects.toThrow('Task core service error');
            });
        });
    });
});
