import { Injectable } from '@nestjs/common';
import { WorkspacesCoreService } from 'app/companies/products/services/workspaces-core.service';
import { TaskNotificationAction } from 'app/grc/enums/task-notification-action.enum';
import {
    DATE_RANGE_FOR_UPCOMING_TASK_REMINDERS,
    TASKS_TO_SHOW_IN_REMINDERS,
    WORKSPACE_AWARE_TASK_TYPES,
} from 'app/grc/services/tasks.constants';
import { TasksCoreService } from 'app/tasks/core/services/tasks-core.service';
import { bulkTaskNotificationsWorkflowV1 } from 'app/worker/workflows';
import { TaskNotification } from 'app/worker/workflows/grc/types/task-notification.type';
import { FeatureReminderProviderService } from 'app/worker/workflows/reminders/interfaces/feature-reminder-provider-service.interface';
import {
    FeatureTypeDataMap,
    GenericUserReminderProviderConfig,
} from 'app/worker/workflows/reminders/interfaces/user-reminder-provider-config.interface';
import { RemindersScheduleConfig } from 'app/worker/workflows/reminders/types/reminders-schedule-config.type';
import { Account } from 'auth/entities/account.entity';
import { PolloAdapter } from 'commons/adapters/pollo.adapter';
import { FeatureType } from 'commons/enums/feature-toggling/feature.enum';
import { TaskStatus } from 'commons/enums/task-status.enum';
import { UpcomingTaskType } from 'commons/enums/upcoming-task-type-enum';
import { getTemporalClient } from 'commons/helpers/temporal/client';
import { AppService } from 'commons/services/app.service';
import config from 'config';
import { PolloMessage } from 'pollo-logger/classes/pollo.message';

@Injectable()
export class UpcomingTasksRemindersProviderService
    extends AppService
    implements FeatureReminderProviderService<FeatureType.UPCOMING_TASKS_REMINDERS>
{
    constructor(
        private readonly tasksCoreService: TasksCoreService,
        private readonly workspacesCoreService: WorkspacesCoreService,
    ) {
        super();
    }

    private getUpcomingTasksDates(remindersConfig: RemindersScheduleConfig): {
        startDate: string;
        endDate: string;
    } {
        const startDate = new Date();
        startDate.setMonth(remindersConfig.month);
        startDate.setDate(remindersConfig.day);
        startDate.setFullYear(remindersConfig.year);
        startDate.setHours(remindersConfig.hour);
        startDate.setMinutes(remindersConfig.minute);
        startDate.setSeconds(0);

        const endDate = new Date(startDate);
        endDate.setDate(startDate.getDate() + DATE_RANGE_FOR_UPCOMING_TASK_REMINDERS);

        return { startDate: startDate.toISO8601String(), endDate: endDate.toISO8601String() };
    }

    private async getWorkspaceAwareTasks(
        workspaceId: number,
        userId: number,
        startDate: string,
        endDate: string,
        includeRiskTasks: boolean,
    ): Promise<[TaskNotification[], number]> {
        this.logger.log(
            PolloMessage.msg(
                `Fetching upcoming workspace aware tasks for workspace ${workspaceId} for user ${userId}`,
            )
                .setContext(this.constructor.name)
                .setSubContext(this.getWorkspaceAwareTasks.name)
                .addTemporalMetadata(),
        );

        let workspaceAwareTaskTypes = WORKSPACE_AWARE_TASK_TYPES;

        if (!includeRiskTasks) {
            workspaceAwareTaskTypes = WORKSPACE_AWARE_TASK_TYPES.filter(
                taskType => taskType !== UpcomingTaskType.RISK,
            );
        }

        const workspaceAwareTasks = await this.tasksCoreService.getTaskDetailsWithRecurrence({
            workspaceId,
            status: [TaskStatus.INCOMPLETE],
            type: workspaceAwareTaskTypes,
            userId,
            startDate,
            endDate,
            limit: TASKS_TO_SHOW_IN_REMINDERS,
            page: 1,
        });

        const workspaceTasksWithCustomTasks =
            await this.tasksCoreService.getCustomTasksForTaskDetails(workspaceAwareTasks.data);

        this.logger.log(
            PolloMessage.msg(
                `Fetched ${
                    workspaceAwareTasks.total || workspaceTasksWithCustomTasks.length
                } upcoming workspace aware tasks for workspace ${workspaceId} for user ${userId}`,
            )
                .setContext(this.constructor.name)
                .setSubContext(this.getWorkspaceAwareTasks.name)
                .addTemporalMetadata(),
        );

        return [
            workspaceTasksWithCustomTasks.map(taskData =>
                this.tasksCoreService.adaptToTaskNotifications({
                    taskData,
                    workspaceId,
                    assignedUserId: userId,
                }),
            ),
            workspaceAwareTasks.total || workspaceTasksWithCustomTasks.length,
        ];
    }

    private async getVendorTasks(
        workspaceId: number,
        userId: number,
        startDate: string,
        endDate: string,
    ): Promise<[TaskNotification[], number]> {
        this.logger.log(
            PolloMessage.msg(`Fetching upcoming vendor tasks for user ${userId}`)
                .setContext(this.constructor.name)
                .setSubContext(this.getVendorTasks.name)
                .addTemporalMetadata(),
        );

        const vendorTasks = await this.tasksCoreService.getTaskDetailsWithRecurrence({
            workspaceId,
            status: [TaskStatus.INCOMPLETE],
            type: [UpcomingTaskType.VENDOR],
            userId,
            startDate,
            endDate,
            limit: TASKS_TO_SHOW_IN_REMINDERS,
            page: 1,
            upcomingOnly: true,
        });

        this.logger.log(
            PolloMessage.msg(
                `Fetched ${
                    vendorTasks.total || vendorTasks.data.length
                } upcoming vendor tasks for user ${userId}`,
            )
                .setContext(this.constructor.name)
                .setSubContext(this.getVendorTasks.name)
                .addTemporalMetadata(),
        );

        return [
            vendorTasks.data.map(taskDetail =>
                this.tasksCoreService.adaptToTaskNotifications({
                    taskData: { taskDetail },
                    workspaceId,
                    assignedUserId: userId,
                }),
            ),
            vendorTasks.total || vendorTasks.data.length,
        ];
    }

    private async getPolicyRenewalTasks(
        workspaceId: number,
        userId: number,
        startDate: string,
        endDate: string,
        tasksWithPotentialDuplicates: TaskNotification[],
    ): Promise<TaskNotification[]> {
        this.logger.log(
            PolloMessage.msg(`Fetching upcoming policy renewal tasks for user ${userId}`)
                .setContext(this.constructor.name)
                .setSubContext(this.getPolicyRenewalTasks.name)
                .addTemporalMetadata(),
        );
        const policyTasks = await this.tasksCoreService.getTaskDetailsWithRecurrence({
            workspaceId,
            status: [TaskStatus.INCOMPLETE],
            type: [UpcomingTaskType.POLICY_RENEWALS],
            userId,
            startDate,
            endDate,
            upcomingOnly: true,
        });

        if (policyTasks.data.length > 0) {
            const policyTaskNotification = policyTasks.data.map(taskDetail =>
                this.tasksCoreService.adaptToTaskNotifications({
                    taskData: { taskDetail },
                    workspaceId,
                    assignedUserId: userId,
                }),
            );

            tasksWithPotentialDuplicates.push(...policyTaskNotification);

            tasksWithPotentialDuplicates = this.tasksCoreService.deduplicateTasks(
                tasksWithPotentialDuplicates,
            );
        }

        this.logger.log(
            PolloMessage.msg(
                `Fetched ${
                    policyTasks.total || policyTasks.data.length
                } upcoming policy renewal tasks for workspace ${workspaceId} for user ${userId}`,
            )
                .setContext(this.constructor.name)
                .setSubContext(this.getPolicyRenewalTasks.name)
                .addTemporalMetadata(),
        );

        return tasksWithPotentialDuplicates;
    }

    private sortAndSliceTasks(tasks: TaskNotification[]): TaskNotification[] {
        tasks.sort((a, b) => {
            const dateA = new Date(a.dueDate).getTime();
            const dateB = new Date(b.dueDate).getTime();
            return dateA - dateB;
        });

        return tasks.slice(0, TASKS_TO_SHOW_IN_REMINDERS);
    }

    private async getUserTaskDataForReminder(
        userId: number,
        remindersConfig: RemindersScheduleConfig,
        includeRiskTasks: boolean,
    ): Promise<FeatureTypeDataMap[FeatureType.UPCOMING_TASKS_REMINDERS]> {
        const { startDate, endDate } = this.getUpcomingTasksDates(remindersConfig);

        const workspaces = await this.workspacesCoreService.getAllWorkspaceNamesAndIds();

        // Due to how vendor tasks work we only need to fetch them once.
        let shouldFetchVendorTasks = true;
        let tasksToIncludeInTheMail: TaskNotification[] = [];
        let totalNumberOfTasks = 0;

        let tasksWithPotentialDuplicates: TaskNotification[] = [];

        for (const workspace of workspaces) {
            const { id: workspaceId } = workspace;

            const [workspaceAwareTasks, workspaceAwareTasksCount] =
                // eslint-disable-next-line no-await-in-loop
                await this.getWorkspaceAwareTasks(
                    workspaceId,
                    userId,
                    startDate,
                    endDate,
                    includeRiskTasks,
                );

            tasksToIncludeInTheMail.push(...workspaceAwareTasks);
            totalNumberOfTasks += workspaceAwareTasksCount;

            if (shouldFetchVendorTasks) {
                const [vendorTasks, vendorTasksCount] =
                    // eslint-disable-next-line no-await-in-loop
                    await this.getVendorTasks(workspaceId, userId, startDate, endDate);

                tasksToIncludeInTheMail.push(...vendorTasks);
                totalNumberOfTasks += vendorTasksCount;
                shouldFetchVendorTasks = false;
            }

            tasksToIncludeInTheMail = this.sortAndSliceTasks(tasksToIncludeInTheMail);

            // eslint-disable-next-line no-await-in-loop
            tasksWithPotentialDuplicates = await this.getPolicyRenewalTasks(
                workspaceId,
                userId,
                startDate,
                endDate,
                tasksWithPotentialDuplicates,
            );
        }

        totalNumberOfTasks += tasksWithPotentialDuplicates.length;

        tasksToIncludeInTheMail.push(...tasksWithPotentialDuplicates);

        tasksToIncludeInTheMail = this.sortAndSliceTasks(tasksToIncludeInTheMail);

        return {
            userId,
            totalNumberOfTasks,
            tasksToIncludeInTheMail,
            includeRiskTasks,
        };
    }

    async validateReminderToBeSent(
        data: FeatureTypeDataMap[FeatureType.UPCOMING_TASKS_REMINDERS],
        remindersScheduleConfig: RemindersScheduleConfig,
    ): Promise<FeatureTypeDataMap[FeatureType.UPCOMING_TASKS_REMINDERS] | null> {
        this.logger.log(
            PolloMessage.msg(`Validating reminder to be sent for user ${data.userId}`)
                .setContext(this.constructor.name)
                .setSubContext(this.validateReminderToBeSent.name)
                .addTemporalMetadata(),
        );

        const { userId, includeRiskTasks } = data;

        if (!remindersScheduleConfig) {
            return null;
        }

        const taskReminderData = await this.getUserTaskDataForReminder(
            userId,
            remindersScheduleConfig,
            includeRiskTasks,
        );

        return taskReminderData.totalNumberOfTasks > 0 ? taskReminderData : null;
    }

    public async send(
        account: Account,
        reminderConfig: GenericUserReminderProviderConfig[FeatureType.UPCOMING_TASKS_REMINDERS],
    ): Promise<void> {
        const temporalClient = await getTemporalClient(account.domain);

        this.logger.log(
            PolloAdapter.acct(
                `Starting bulkTaskNotificationsWorkflowV1 for account ${account.id} for user ${reminderConfig.user.id} for feature ${
                    FeatureType[reminderConfig.featureType]
                }`,
                account,
            )
                .setContext(this.constructor.name)
                .setSubContext(this.send.name)
                .addTemporalMetadata(),
        );

        await temporalClient.executeWorkflow(bulkTaskNotificationsWorkflowV1, {
            args: [
                {
                    tasks: reminderConfig.data.tasksToIncludeInTheMail,
                    taskCount: reminderConfig.data.totalNumberOfTasks,
                    account,
                    action: TaskNotificationAction.TASK_UPCOMING,
                    user: reminderConfig.user,
                },
            ],
            taskQueue: config.get('temporal.taskQueues.temporal-slow-pool'),
            memo: {
                accountId: account.id,
                domain: account.domain,
            },
        });

        this.logger.log(
            PolloAdapter.acct(
                `Completed bulkTaskNotificationsWorkflowV1 for account ${account.id} for user ${reminderConfig.user.id} for feature ${
                    FeatureType[reminderConfig.featureType]
                }`,
                account,
            )
                .setContext(this.constructor.name)
                .setSubContext(this.send.name)
                .addTemporalMetadata(),
        );
    }
}
