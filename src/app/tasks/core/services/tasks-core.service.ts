import { getScheduleDates } from '@drata/recurring-schedule';
import { Injectable } from '@nestjs/common';
import { CustomTaskConfiguration } from 'app/grc/entities/custom-task-configuration.entity';
import { CustomTask } from 'app/grc/entities/custom-task.entity';
import { CustomTaskConfigurationsRepository } from 'app/grc/repositories/custom-task-configuration.repository';
import { CustomTasksRepository } from 'app/grc/repositories/custom-task.repository';
import { UpcomingTaskRepository } from 'app/grc/repositories/upcoming-task.repository';
import { EVIDENCE_TASK_TYPES, TASK_RECURRING_TYPES } from 'app/grc/services/tasks.constants';
import { TaskIdsRestrictedView } from 'app/grc/types/task-ids-restricted-view.type';
import { UpcomingTaskDetails } from 'app/grc/types/upcoming-task-details.type';
import { UpcomingTaskForOverview } from 'app/grc/types/upcoming-task-for-overview.type';
import {
    UpcomingTaskOverview,
    UpcomingTaskOverviewV2,
} from 'app/grc/types/upcoming-task-overview.type';
import { SchedulerDefinition } from 'app/scheduler/entities/scheduler-definition.entity';
import { SchedulerWorkflowType } from 'app/scheduler/enums/scheduler-workflow-type.enum';
import { SchedulerDefinitionRepository } from 'app/scheduler/repositories/scheduler-definition.repository';
import {
    CreateTaskOverviewParams,
    GetOverviewServiceParams,
    GetRecurringTasksServiceParams,
    GetSchedulesBetweenDatesServiceParams,
    GetTaskDetailsByIdAndTypeParams,
    GetTaskDetailsServiceParams,
    GetTaskDetailsWithRecurrenceServiceParams,
    GetTasksOverviewWithRecurrenceServiceParams,
    GetTasksPerYearServiceParams,
    GetUserIdsWithTasksParams,
    TaskDetailsWithCustomTask,
} from 'app/tasks/core/types/tasks.type';
import { TaskNotification } from 'app/worker/workflows/grc/types/task-notification.type';
import { TaskStatus } from 'commons/enums/task-status.enum';
import { UpcomingTaskType } from 'commons/enums/upcoming-task-type-enum';
import { convertToISO8601String } from 'commons/helpers/date.helper';
import { shouldGetRecurringTasks } from 'commons/helpers/task.helper';
import { AppService } from 'commons/services/app.service';
import { PaginationType } from 'commons/types/pagination.type';
import { isEmpty, isNil, isNumber } from 'lodash';
import moment from 'moment';
import { Span } from 'nestjs-ddtrace';
import { PolloMessage } from 'pollo-logger/classes/pollo.message';
import { In, IsNull, Not } from 'typeorm';

@Injectable()
export class TasksCoreService extends AppService {
    constructor() {
        super();
    }

    /**
     * Retrieves a comprehensive overview of all tasks within a workspace.
     *
     * By default, this method returns information of ALL possible tasks including:
     * - Automated tasks (system-generated)
     * - Custom tasks (user-created)
     * - Recurring tasks (scheduled)
     *
     * The method applies intelligent defaults to provide the most inclusive view possible
     * while allowing fine-grained filtering through optional parameters.
     *
     * @param params - Configuration object containing workspace ID and optional filters
     * @param params.workspaceId - Required. The workspace id for which to retrieve tasks for
     * @param params.notCompletedOnly - Optional. Defaults to `false`.
     *   When `false`, includes both completed and incomplete tasks.
     *   When `true`, filters to show only tasks that are not marked as completed.
     * @param params.taskIds - Optional. Defaults to empty arrays for all task types.
     *   Provides fine-grained control over which specific tasks to include.
     *   Empty arrays mean "include all tasks of this type" (no restrictions).
     * @param params.upcoming - Optional. Defaults to empty array `[]`.
     *   Pre-seeded accumulator array list of upcoming tasks. Empty array means "fetch all upcoming tasks".
     *   The method will mutate the object.
     * @param params.type - Optional. No default (undefined).
     *   When provided, filters tasks to only include the specified task types.
     *   When undefined, includes all task types.
     * @param params.userId - Optional. No default (undefined).
     *   When provided, filters tasks to only include those assigned to the specified user.
     *   When undefined, includes tasks for all users.
     *
     * @returns Promise<UpcomingTaskOverview[]> - Array of task overview objects containing
     *   aggregated information about tasks, including counts, statuses, and metadata.
     */
    public async getTasksOverview(
        params: GetOverviewServiceParams,
    ): Promise<UpcomingTaskOverview[]> {
        const { notCompletedOnly = false, upcoming = [], ...paramsWithoutDefaultValue } = params;

        const types = paramsWithoutDefaultValue.type
            ? this.expandEvidenceTypes(paramsWithoutDefaultValue.type)
            : undefined;

        return this.upcomingTasksRepository.getOverview({
            ...paramsWithoutDefaultValue,
            type: types,
            upcoming,
            notCompletedOnly,
        });
    }

    public async getTaskDetailsByIdAndType({
        id,
        type,
    }: GetTaskDetailsByIdAndTypeParams): Promise<UpcomingTaskDetails> {
        //expandEvidenceTypes
        return this.upcomingTasksRepository.findOneByOrFail({ id, type });
    }

    /**
     * Check the JSDocString of the getTasksOverview method for more details on how the params are defaulted.
     */
    public async getTaskDetails(
        params: GetTaskDetailsServiceParams,
    ): Promise<PaginationType<UpcomingTaskDetails>> {
        const types = params.type ? this.expandEvidenceTypes(params.type) : undefined;

        return this.upcomingTasksRepository.getDetails({
            ...params,
            type: types,
        });
    }

    private getTasksDetailsStartAndEndDate(
        params: GetTaskDetailsServiceParams,
    ): Required<Pick<GetTaskDetailsServiceParams, 'startDate' | 'endDate'>> {
        const today = moment().startOf('day');
        let startDate = params.startDate ?? moment(today).add(-6, 'months').format('YYYY-MM-DD');
        let endDate = params.endDate ?? moment(today).add(6, 'months').format('YYYY-MM-DD');
        if (params.year && params.month) {
            startDate = moment([params.year, params.month - 1])
                .startOf('month')
                .format('YYYY-MM-DD');
            endDate = moment(startDate).endOf('month').format('YYYY-MM-DD');
        }

        return { startDate, endDate };
    }

    private getRecurringTasksEndDate(
        endDate?: string,
        taskStatuses?: TaskStatus[] | null,
    ): string | undefined {
        if (taskStatuses?.includes(TaskStatus.INCOMPLETE)) {
            return endDate;
        }

        if (taskStatuses?.includes(TaskStatus.PAST_DUE)) {
            const setDate = endDate ? moment(endDate) : moment();
            const pastDueDate = moment().subtract(1, 'days').startOf('day');

            const dateToReturn = setDate.isBefore(pastDueDate) ? setDate : pastDueDate;

            return dateToReturn.format('YYYY-MM-DD');
        }

        return endDate;
    }

    /**
     * Check the JSDocString of the getTasksOverview method for more details on how the params are defaulted.
     */
    public async getTaskDetailsWithRecurrence(
        params: GetTaskDetailsWithRecurrenceServiceParams,
    ): Promise<PaginationType<UpcomingTaskDetails>> {
        const { configurationIds = [], ...paramsWithoutDefaultValue } = params;

        const { limit, type, page, year, month, completed, userId, workspaceId, status } =
            paramsWithoutDefaultValue;

        const { startDate, endDate } = this.getTasksDetailsStartAndEndDate(params);

        // Use the core service to get task details with recurrence
        const types = paramsWithoutDefaultValue.type
            ? this.expandEvidenceTypes(paramsWithoutDefaultValue.type)
            : undefined;

        const nonRecurringTasks = await this.upcomingTasksRepository.getDetails({
            ...paramsWithoutDefaultValue,
            upcomingOnly: params.upcomingOnly,
            notCompletedOnly: params.notCompletedOnly,
            type: types,
            year,
            month,
            startDate,
            endDate,
        });

        // add schedule for any tasks types that can be recurring
        const taskIdsForSchedule = nonRecurringTasks.data
            .filter(task => TASK_RECURRING_TYPES.includes(Number(task.type)))
            .map(task => task.id);
        // find schedules for tasks
        if (!isEmpty(taskIdsForSchedule)) {
            const taskSchedules = await this.customTasksRepository.find({
                where: {
                    id: In(taskIdsForSchedule),
                },
                relations: {
                    schedulerDefinition: true,
                },
            });
            if (!isEmpty(taskSchedules)) {
                nonRecurringTasks.data.forEach(task => {
                    const taskSchedule = taskSchedules.find(
                        taskWithSchedule => taskWithSchedule.id === task.id,
                    );
                    if (taskSchedule?.schedulerDefinition) {
                        task.schedule = taskSchedule.schedulerDefinition.schedule;
                    }
                });
            }
        }

        let allTasks: PaginationType<UpcomingTaskDetails> = {
            data: [],
            total: 0,
            page: 1,
            limit: limit ?? 0,
        };

        if (shouldGetRecurringTasks({ completed, status })) {
            const recurringEndDate = this.getRecurringTasksEndDate(endDate, status);
            allTasks = await this.getDetailsForRecurringTasks(
                configurationIds,
                nonRecurringTasks,
                workspaceId,
                startDate || '',
                recurringEndDate || '',
                userId,
                type,
            );
        }

        const details = allTasks.data.length > 0 ? allTasks : nonRecurringTasks;

        // If limit is set, reduce the number of tasks to the limit after merging
        // copy limit from dto for possible reduction in limit
        if (limit) {
            const currentPage = page ?? 1;
            const start = (currentPage - 1) * limit;
            details.data = details.data.slice(start, start + limit);
        } else {
            details.total = details.data.length;
        }

        return details;
    }

    private async createTaskOverview({
        data,
        workspaceId,
        taskIds,
        userId,
    }: CreateTaskOverviewParams): Promise<UpcomingTaskOverviewV2[]> {
        const upcoming: UpcomingTaskOverviewV2[] = [];

        for (const row of data) {
            let rowFound = upcoming.find(up => row.year === up.year && up.month === row.month);
            if (isNil(rowFound)) {
                rowFound = {
                    year: row.year,
                    month: row.month,
                    policyRenewals: { minDate: null, count: 0, overdue: 0, completed: 0 },
                    vendor: { minDate: null, count: 0, overdue: 0, completed: 0 },
                    evidence: { minDate: null, count: 0, overdue: 0, completed: 0 },
                    general: { minDate: null, count: 0, overdue: 0, completed: 0 },
                    control: { minDate: null, count: 0, overdue: 0, completed: 0 },
                    risk: { minDate: null, count: 0, overdue: 0, completed: 0 },
                    controlApprovals: { minDate: null, count: 0, overdue: 0, completed: 0 },
                    policyApprovals: { minDate: null, count: 0, overdue: 0, completed: 0 },
                    overdue: 0,
                    total: 0,
                };
                upcoming.push(rowFound);
            }

            const newAdd = row.num;

            const category = rowFound[this.mapEnum(row.type)];

            if (!isNil(category)) {
                category.count += newAdd;

                category.minDate = isNil(category.minDate)
                    ? row.min_date
                    : this.getMinimumStringDate(category.minDate, row.min_date);

                if (row.overdue_count) {
                    rowFound.overdue += row.overdue_count;
                    category.overdue += row.overdue_count;
                } else if (moment(row.min_date).isBefore(moment(), 'day')) {
                    rowFound.overdue++;
                    category.overdue++;
                }

                rowFound.total += newAdd;
            }
        }

        await Promise.all(
            upcoming.map(async summary => {
                await this.updateCompletedTaskCounts(summary, workspaceId, userId, taskIds);
            }),
        );

        return upcoming.sort((a, b) => {
            if (a.year > b.year) {
                return 1;
            } else if (a.year < b.year) {
                return -1;
            } else {
                return a.month - b.month;
            }
        });
    }

    /**
     * Check the JSDocString of the getTasksOverview method for more details on how the params are defaulted.
     */
    public async getTasksOverviewWithRecurrence(
        params: GetTasksOverviewWithRecurrenceServiceParams,
    ): Promise<UpcomingTaskOverview[]> {
        const { configurationIds = [], ...paramsWithoutDefaultValue } = params;

        const { workspaceId, startDate, endDate, userId, type, status } = paramsWithoutDefaultValue;

        const types = paramsWithoutDefaultValue.type
            ? this.expandEvidenceTypes(paramsWithoutDefaultValue.type)
            : undefined;

        const viewData = await this.upcomingTasksRepository.getUpcomingTasksFromView({
            ...paramsWithoutDefaultValue,
            type: types,
        });

        let recurringTasks: UpcomingTaskForOverview[] = [];

        if (shouldGetRecurringTasks({ status })) {
            const recurringEndDate = this.getRecurringTasksEndDate(endDate, status);

            recurringTasks = await this.getRecurringTasks({
                configurationIds,
                workspaceId,
                startDate: startDate || '',
                endDate: recurringEndDate || '',
                userId,
                type,
            });
        }

        const upcomingWithRecurrence = [...viewData, ...recurringTasks];

        return this.createTaskOverview({
            data: upcomingWithRecurrence,
            workspaceId,
            taskIds: params.taskIds,
            userId,
        });
    }

    public getSkipDates(scheduleSkip: string[] = [], taskDueDates: string[] = []): string[] {
        return [...new Set([...scheduleSkip, ...taskDueDates])];
    }

    @Span()
    public async getSchedulesBetweenDates(
        params: GetSchedulesBetweenDatesServiceParams,
    ): Promise<SchedulerDefinition[]> {
        const { configurationIds, workflowType, workspaceId, startDate, endDate, userId, type } =
            params;
        const query = this.schedulerDefinitionRepository
            .createQueryBuilder('schedulerDefinition')
            .distinct(true)
            .innerJoinAndSelect(
                'schedulerDefinition.customTaskConfiguration',
                'customTaskConfiguration',
            )
            .innerJoin('customTaskConfiguration.product', 'product')
            .innerJoin('customTaskConfiguration.assignedTo', 'user')
            .leftJoinAndSelect('schedulerDefinition.customTasks', 'customTasks')
            .addSelect(['user.id', 'product.id']);

        if (!isNil(userId)) {
            query.where('customTaskConfiguration.assignedTo = :userId', { userId });
        }

        if (!isNil(type)) {
            query.andWhere('customTaskConfiguration.taskType IN(:...type)', { type });
        }

        // This should handle both risk and control restricted views
        if (!isEmpty(configurationIds)) {
            query.andWhere(`(customTaskConfiguration.id IN(:...configurationIds))`, {
                configurationIds,
            });
        }

        query
            .andWhere('product.id = :workspaceId', { workspaceId })
            .andWhere('schedulerDefinition.workflowType = :workflowType', { workflowType })
            .andWhere('schedulerDefinition.startDate <= :endDate', {
                endDate,
            })
            .andWhere(
                '(schedulerDefinition.endDate IS NULL OR schedulerDefinition.endDate >= :startDate)',
                {
                    startDate,
                },
            );
        const scheduleDefinitions = await query.getMany();
        return scheduleDefinitions.filter(scheduleDefinition => !scheduleDefinition.deletedAt);
    }

    public async getRecurringTasks(
        params: GetRecurringTasksServiceParams,
    ): Promise<UpcomingTaskForOverview[]> {
        const { configurationIds, workspaceId, startDate, endDate, userId, type: type } = params;
        const schedulerDefinitions: SchedulerDefinition[] = await this.getSchedulesBetweenDates({
            configurationIds,
            workflowType: SchedulerWorkflowType.RECURRING_TASKS,
            workspaceId,
            startDate,
            endDate,
            userId,
            type: type,
        });

        const recurringTasks: UpcomingTaskForOverview[] = [];

        schedulerDefinitions.forEach(schedulerDefinition => {
            // assuming all tasks are the same type for a schedule definition
            const taskType = schedulerDefinition.customTaskConfiguration?.taskType;
            if (!taskType) {
                return;
            }
            const schedule = schedulerDefinition.schedule;
            const start = startDate || convertToISO8601String(new Date());
            const taskDates = schedulerDefinition.customTasks?.map(task => task.dueDate);
            schedule.skip = this.getSkipDates(schedule.skip, taskDates);
            const occurrences = getScheduleDates(schedule, start, endDate);

            occurrences.forEach(occurrence => {
                // make sure the occurrence is within the date range
                if (occurrence <= endDate) {
                    const month = parseInt(occurrence.split('-')[1]);
                    const year = parseInt(occurrence.split('-')[0]);
                    const recurringTask = {
                        min_date: occurrence,
                        month,
                        year,
                        num: 1,
                        type: taskType,
                    };
                    recurringTasks.push(recurringTask);
                } else {
                    // We should not have any occurrences that are after the end date
                    this.logger.warn(
                        PolloMessage.msg(
                            `Occurrence ${occurrence} is after the end date ${endDate}`,
                        ),
                    );
                }
            });
        });

        return recurringTasks;
    }

    @Span()
    private async updateCompletedTaskCounts(
        summary: UpcomingTaskOverviewV2,
        workspaceId: number,
        userId?: number,
        taskIds?: TaskIdsRestrictedView,
    ): Promise<void> {
        await Promise.all(
            TASK_RECURRING_TYPES.map(async type => {
                const completed = await this.customTasksRepository.getCompletedTasks(
                    {
                        year: summary.year,
                        month: summary.month,
                        workspaceId,
                        userId,
                        type,
                    },
                    taskIds?.controlTasksIds || [],
                    taskIds?.riskTasksIds || [],
                );
                const summaryField = this.mapEnum(type);
                summary[summaryField].completed = completed?.count ?? 0;
            }),
        );
    }

    private mapEnum(num: number): string {
        switch (num) {
            case UpcomingTaskType.POLICY_RENEWALS:
                return 'policyRenewals';
            case UpcomingTaskType.EVIDENCE:
            case UpcomingTaskType.REPORT:
            case UpcomingTaskType.EXTERNAL_EVIDENCE:
                return 'evidence';
            case UpcomingTaskType.VENDOR:
                return 'vendor';
            case UpcomingTaskType.CONTROL:
                return 'control';
            case UpcomingTaskType.RISK:
                return 'risk';
            case UpcomingTaskType.CONTROL_APPROVALS:
                return 'controlApprovals';
            case UpcomingTaskType.POLICY_APPROVALS:
                return 'policyApprovals';
            case UpcomingTaskType.GENERAL:
            default:
                return 'general';
        }
    }

    private getMinimumStringDate(firstStringDate: string, secondStringDate: string): string {
        const renewalDateFormat = 'YYYY-MM-DD';
        const firstMomentDate = moment(firstStringDate, renewalDateFormat);
        const secondMomentDate = moment(secondStringDate, renewalDateFormat);

        const minimumMomentDate = firstMomentDate.isAfter(secondMomentDate)
            ? secondMomentDate
            : firstMomentDate;

        return minimumMomentDate.format(renewalDateFormat);
    }

    @Span()
    public async getDetailsForRecurringTasks(
        configurationIds: number[],
        taskDetails: PaginationType<UpcomingTaskDetails>,
        workspaceId: number,
        startDate: string,
        endDate: string,
        userId?: number,
        type?: UpcomingTaskType[],
    ): Promise<PaginationType<UpcomingTaskDetails>> {
        const schedulerDefinitions: SchedulerDefinition[] = await this.getSchedulesBetweenDates({
            configurationIds,
            workflowType: SchedulerWorkflowType.RECURRING_TASKS,
            workspaceId,
            startDate,
            endDate,
            userId: userId,
            type,
        });

        schedulerDefinitions.forEach(schedulerDefinition => {
            const schedule = schedulerDefinition.schedule;
            const start = startDate || convertToISO8601String(new Date());
            const taskDates = schedulerDefinition.customTasks?.map(task => task.dueDate);
            schedule.skip = this.getSkipDates(schedule.skip, taskDates);
            const occurrences = getScheduleDates(schedule, start, endDate);

            occurrences.forEach(occurrence => {
                const taskConfiguration: CustomTaskConfiguration | undefined =
                    schedulerDefinition.customTaskConfiguration;
                if (!taskConfiguration) {
                    this.logger.error(
                        PolloMessage.msg(
                            'Custom Task Configuration not linked to Scheduler Definition Entity',
                        ),
                    );
                    return;
                }
                const scheduleDate = moment(occurrence).format('YYYY-MM-DD');
                const assignee = taskConfiguration.assignedTo;
                const task: UpcomingTaskDetails = {
                    renewalDate: scheduleDate,
                    scheduleDate,
                    configurationId: taskConfiguration.id,
                    name: taskConfiguration.title,
                    type: taskConfiguration.taskType,
                    userIds: !isNil(assignee) ? [assignee.id] : [],
                    completedAt: null,
                    schedule: schedulerDefinition.schedule,
                };

                // Insert task into details.data in ascending order of renewalDate
                const index = taskDetails.data.findIndex(existingTask =>
                    moment(existingTask.renewalDate).isAfter(moment(task.renewalDate)),
                );
                if (index === -1) {
                    taskDetails.data.push(task);
                } else {
                    taskDetails.data.splice(index, 0, task);
                }

                // update the total count for every recurring task
                if (!isNil(taskDetails.total)) {
                    taskDetails.total++;
                }
            });
        });

        return taskDetails;
    }

    @Span()
    public async getTasksPerYear(params: GetTasksPerYearServiceParams): Promise<number[][]> {
        const types = params.type ? this.expandEvidenceTypes(params.type) : undefined;
        const viewData = await this.upcomingTasksRepository.getUpcomingTasksFromView({
            ...params,
            type: types,
        });

        const currentYear = this.getCurrentYear();
        const viewEnd = viewData[viewData.length - 1]?.year ?? currentYear;
        const end = Math.max(viewEnd, currentYear + 5);
        const viewStart = viewData[0]?.year ?? currentYear;
        const start = Math.min(viewStart, currentYear);

        const years = Array.from({ length: end - start + 1 }, (_, i) => +start + i);

        return Promise.all(
            years.map(async year => {
                const viewTasks = viewData.filter(task => task.year === year);

                let recurringTasks: UpcomingTaskForOverview[] = [];

                if (
                    shouldGetRecurringTasks({
                        status: params.status,
                    })
                ) {
                    const endDate = `${year}-12-31`;
                    const recurringEndDate = this.getRecurringTasksEndDate(endDate, params.status);

                    recurringTasks = await this.getRecurringTasks({
                        configurationIds: params.configurationIds,
                        workspaceId: params.workspaceId,
                        startDate: `${year}-01-01`,
                        endDate: recurringEndDate || endDate,
                        userId: params.userId,
                        type: params.type,
                    });
                }

                const tasksCount = viewTasks.reduce((acc, task) => acc + task.num, 0);
                const count = tasksCount + recurringTasks.length;
                return [year, count];
            }),
        );
    }

    public async getCustomTasksForTaskDetails(
        taskDetails: UpcomingTaskDetails[],
    ): Promise<TaskDetailsWithCustomTask[]> {
        const customTasks = await this.customTasksRepository.find({
            where: taskDetails.map(task => ({ id: task.id, taskType: task.type })),
            relations: {
                controls: true,
                risks: true,
                schedulerDefinition: {
                    customTaskConfiguration: true,
                },
            },
        });

        const customTasksMap = customTasks.reduce(
            (acc, task) => {
                acc[task.id] = task;
                return acc;
            },
            {} as Record<number, CustomTask>,
        );

        const customTaskConfigurationIds = taskDetails
            .filter(task => !isNil(task.configurationId))
            .map(task => task.configurationId);

        const customTaskConfigurations = await this.customTaskConfigurationRepository.find({
            where: {
                id: In(customTaskConfigurationIds),
            },
            relations: {
                control: true,
                risk: true,
            },
        });

        const customTaskConfigurationMap = customTaskConfigurations.reduce(
            (acc, taskConfig) => {
                acc[taskConfig.id] = taskConfig;
                return acc;
            },
            {} as Record<number, CustomTaskConfiguration>,
        );

        return taskDetails.map(taskDetail => {
            let customTask: CustomTask | undefined = undefined;

            if (taskDetail.id) {
                const customTaskWithSameId = customTasksMap[taskDetail.id];

                if (customTaskWithSameId?.taskType === taskDetail.type) {
                    customTask = customTaskWithSameId;
                }
            }

            let customTaskConfiguration: CustomTaskConfiguration | undefined = undefined;

            if (taskDetail.configurationId) {
                customTaskConfiguration = customTaskConfigurationMap[taskDetail.configurationId];
            }

            return { taskDetail, customTask, customTaskConfiguration };
        });
    }

    private expandEvidenceTypes(types: UpcomingTaskType[]): UpcomingTaskType[] {
        if (types.includes(UpcomingTaskType.EVIDENCE)) {
            types = types.filter(type => type !== UpcomingTaskType.EVIDENCE);
            types.push(...EVIDENCE_TASK_TYPES);
        }

        return types;
    }

    /**
     * Since tasks are created based on entities that are not workspace aware on the UI appear on
     * multiple workspaces but when sending a reminder we need to deduplicate them to avoid sending
     * multiple reminders for the same task. Unfortunately we can't base our deduplication logic on
     * the id of the task because since tasks are coming from different tables they might have the
     * same id but represent different tasks. Some tasks might not even have an id yet (recurring tasks).
     * In order to determine the unicity of the task we use a combination of the following elements:
     * - id -> For automated tasks such as policy renewals and vendor tasks across workspaces have the same id.
     * - title -> For both recurring and automated tasks the title will be the same
     * - taskType -> For both recurring and automated tasks the task type will be the same
     * - dueDate -> For automated tasks the due date will be the same across workspaces.
     *              For recurring tasks the due date will be different for each recurring task.
     * - configurationId -> For automated tasks the configuration id will be undefined.
     *              For recurring tasks the configuration id will be different for each recurring task although
     *              the rest of the task details will be the same.
     * - assignedTo.id -> For tasks assigned to multiple users, we want to send a reminder for each user.
     * @param tasks List of task for which to send a reminder that might have duplicates
     * @returns
     */
    public deduplicateTasks(tasks: TaskNotification[]): TaskNotification[] {
        const uniqueTasksMap = new Map<string, TaskNotification>();

        for (const task of tasks) {
            const keyObject = {
                id: task.id,
                title: task.title,
                taskType: task.taskType,
                dueDate: task.dueDate,
                configurationId: task.configurationId,
                assignedToId: task.assignedTo?.id,
            };

            const key = JSON.stringify(keyObject);
            const existingTask = uniqueTasksMap.get(key);

            // Keep task with lowest workspaceId
            if (!existingTask || task.workspaceId < existingTask.workspaceId) {
                uniqueTasksMap.set(key, task);
            }
        }

        return Array.from(uniqueTasksMap.values());
    }

    public adaptToTaskNotifications({
        taskData,
        workspaceId,
        assignedUserId,
    }: {
        taskData: TaskDetailsWithCustomTask;
        workspaceId: number;
        assignedUserId: number;
    }): TaskNotification {
        const { taskDetail, customTask, customTaskConfiguration } = taskData;

        const controls = customTask?.controls?.map(customTaskControl => ({
            code: customTaskControl.code,
            name: customTaskControl.name,
        }));

        let control: TaskNotification['control'] = undefined;

        const configurationControl =
            customTask?.schedulerDefinition?.customTaskConfiguration?.control ||
            customTaskConfiguration?.control;
        if (configurationControl) {
            control = {
                code: configurationControl.code,
                name: configurationControl.name,
            };
        }

        const risks = customTask?.risks?.map(customTaskRisk => ({
            riskId: customTaskRisk.riskId,
            currentVersion: { title: customTaskRisk.currentVersion.title },
        }));

        let risk: TaskNotification['risk'] = undefined;
        const configurationRisk =
            customTask?.schedulerDefinition?.customTaskConfiguration?.risk ||
            customTaskConfiguration?.risk;
        if (configurationRisk) {
            risk = {
                riskId: configurationRisk.riskId,
                currentVersion: { title: configurationRisk.currentVersion.title },
            };
        }

        const schedule = customTask?.schedulerDefinition?.schedule || taskDetail.schedule;

        return {
            id: customTask?.id || taskDetail.id,
            workspaceId,
            title: taskDetail.name,
            description: customTask?.description || customTaskConfiguration?.description,
            dueDate: taskDetail.renewalDate,
            taskType: taskDetail.type,
            configurationId: taskDetail.configurationId,
            assignedTo: {
                id: assignedUserId,
                email: '',
                firstName: '',
                lastName: '',
            },
            schedule: taskDetail.schedule,
            schedulerDefinition: schedule ? { schedule } : undefined,
            controls,
            control,
            risks,
            risk,
        };
    }

    public async getUserIdsWithTasks({
        startDate,
        endDate,
        status,
        type,
    }: GetUserIdsWithTasksParams): Promise<number[]> {
        const dataQuery = this.upcomingTasksRepository
            .createQueryBuilder()
            .select('user_ids')
            .where('renewal_date >= :startDate', { startDate })
            .andWhere('renewal_date <= :endDate', { endDate })
            .andWhere({
                completedAt: status === TaskStatus.COMPLETED ? Not(IsNull()) : IsNull(),
            })
            .andWhere({
                type: In(type),
            })
            .groupBy('user_ids');

        const data = await dataQuery.getRawMany();

        const schedulerDefinitions =
            await this.schedulerDefinitionRepository.getSchedulesBetweenDates(
                SchedulerWorkflowType.RECURRING_TASKS,
                startDate,
                endDate,
            );

        const customTaskConfigurations =
            schedulerDefinitions.length > 0
                ? await this.customTaskConfigurationRepository.find({
                      where: {
                          schedulerDefinition: {
                              id: In(schedulerDefinitions.map(def => def.id)),
                          },
                      },
                      relations: {
                          assignedTo: true,
                      },
                  })
                : [];

        return Array.from(
            new Set([
                ...data
                    .map((task: { user_ids: string | null }) => {
                        return (
                            task['user_ids']
                                ?.split('|')
                                .map((userIdString: string) => Number(userIdString))
                                .filter((userId: unknown) => isNumber(userId)) || []
                        );
                    })
                    .flat(),
                ...customTaskConfigurations.map(config => config.assignedTo.id),
            ]),
        ).sort((userIdA, userIdB) => userIdA - userIdB);
    }

    private getCurrentYear(): number {
        return new Date().getFullYear();
    }

    private get upcomingTasksRepository(): UpcomingTaskRepository {
        return this.getCustomTenantRepository(UpcomingTaskRepository);
    }

    private get customTasksRepository(): CustomTasksRepository {
        return this.getCustomTenantRepository(CustomTasksRepository);
    }

    private get schedulerDefinitionRepository(): SchedulerDefinitionRepository {
        return this.getCustomTenantRepository(SchedulerDefinitionRepository);
    }

    private get customTaskConfigurationRepository(): CustomTaskConfigurationsRepository {
        return this.getCustomTenantRepository(CustomTaskConfigurationsRepository);
    }
}
