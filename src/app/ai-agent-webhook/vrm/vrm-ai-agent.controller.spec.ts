import { TestingModule } from '@nestjs/testing';
import { INestApplication, ValidationPipe } from '@nestjs/common';
import { mock } from 'jest-mock-extended';
import request from 'supertest';
import { VendorAssessmentResultService } from 'app/ai-agent-webhook/vrm/services/vendor-assessment-result.service';
import { CriteriaCreationEventType } from 'app/ai-agent-webhook/vrm/enums/criteria-creation.enum';
import { VrmAiAgentController } from 'app/ai-agent-webhook/vrm/vrm-ai-agent.controller';
import { VrmAiAgentRoute } from 'app/ai-agent-webhook/vrm/vrm-ai-agent.routes';
import { PolloLogger } from 'pollo-logger/pollo.logger';
import { PolloMessage } from 'pollo-logger/classes/pollo.message';
import { createAppTestingModule } from 'commons/helpers/app-testing-module.helper';

describe('VrmAiAgentController', () => {
    let app: INestApplication;

    beforeEach(async () => {
        const module: TestingModule = await createAppTestingModule({
            controllers: [VrmAiAgentController],
            providers: [
                {
                    provide: VendorAssessmentResultService,
                    useValue: {},
                },
                {
                    provide: PolloLogger,
                    useValue: mock<PolloLogger<PolloMessage>>(),
                },
            ],
        }).compile();

        app = module.createNestApplication();
        app.useGlobalPipes(new ValidationPipe({ transform: true }));

        await app.init();
    });

    afterEach(async () => {
        await app.close();
    });

    describe('postCriteriaCreationProgressWebhook', () => {
        it('should handle PROCESSING_QUESTIONNAIRES event', async () => {
            // Arrange
            const payload = {
                jobId: 'c5155459-290a-46d0-8659-e8168ad4c45d',
                tenantId: 'c5155459-290a-46d0-8659-e8168ad4c45d',
                event: CriteriaCreationEventType.PROCESSING_QUESTIONNAIRES,
            };

            // Act & Assert
            const response = await request(app.getHttpServer())
                .post(VrmAiAgentRoute.POST_VRM_AI_AGENT_WEBHOOK_CRITERIA_CREATION_PROGRESS)
                .send(payload)
                .expect(200);

            expect(response.body).toEqual({});
        });

        it('should handle PROCESSING_QUESTIONS event', async () => {
            // Arrange
            const payload = {
                jobId: 'c5155459-290a-46d0-8659-e8168ad4c45d',
                tenantId: 'c5155459-290a-46d0-8659-e8168ad4c45d',
                event: CriteriaCreationEventType.PROCESSING_QUESTIONS,
            };

            // Act & Assert
            const response = await request(app.getHttpServer())
                .post(VrmAiAgentRoute.POST_VRM_AI_AGENT_WEBHOOK_CRITERIA_CREATION_PROGRESS)
                .send(payload)
                .expect(200);

            expect(response.body).toEqual({});
        });

        it('should handle CREATING_CRITERIA event with data', async () => {
            // Arrange
            const payload = {
                jobId: 'c5155459-290a-46d0-8659-e8168ad4c45d',
                tenantId: 'c5155459-290a-46d0-8659-e8168ad4c45d',
                event: CriteriaCreationEventType.CREATING_CRITERIA,
                data: {
                    groupCount: 25,
                },
            };

            // Act & Assert
            const response = await request(app.getHttpServer())
                .post(VrmAiAgentRoute.POST_VRM_AI_AGENT_WEBHOOK_CRITERIA_CREATION_PROGRESS)
                .send(payload)
                .expect(200);

            expect(response.body).toEqual({});
        });

        it('should throw error for CREATING_CRITERIA event without data', async () => {
            // Arrange
            const payload = {
                jobId: 'c5155459-290a-46d0-8659-e8168ad4c45d',
                tenantId: 'c5155459-290a-46d0-8659-e8168ad4c45d',
                event: CriteriaCreationEventType.CREATING_CRITERIA,
            };

            // Act & Assert
            const response = await request(app.getHttpServer())
                .post(VrmAiAgentRoute.POST_VRM_AI_AGENT_WEBHOOK_CRITERIA_CREATION_PROGRESS)
                .send(payload)
                .expect(400);

            expect(response.body).toEqual({
                statusCode: 400,
                message: ['data must be an object', 'data should not be empty'],
                error: 'Bad Request',
            });
        });
    });
});
