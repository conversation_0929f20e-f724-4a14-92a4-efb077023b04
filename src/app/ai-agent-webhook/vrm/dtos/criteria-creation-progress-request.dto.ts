import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsObject, IsUUID, ValidateIf } from 'class-validator';
import { RequestDto } from 'commons/dtos/request.dto';
import { CriteriaCreationEventType } from 'app/ai-agent-webhook/vrm/enums/criteria-creation.enum';

export class CriteriaCreationProgressRequestDto extends RequestDto {
    @ApiProperty({
        example: 'c5155459-290a-46d0-8659-e8168ad4c45d',
        description: 'ID of the AI job in progress',
        required: true,
        type: 'string',
    })
    @IsUUID()
    jobId: string;

    @ApiProperty({
        example: 'c5155459-290a-46d0-8659-e8168ad4c45d',
        description: 'ID of the tenant for this job',
        required: true,
        type: 'string',
    })
    @IsUUID()
    tenantId: string;

    @ApiProperty({
        example: CriteriaCreationEventType.CREATING_CRITERIA,
        description: 'Event that the webhook is reporting',
        required: true,
        type: 'string',
        enum: CriteriaCreationEventType,
    })
    @IsEnum(CriteriaCreationEventType)
    event: CriteriaCreationEventType;

    @ApiProperty({
        example: { groupCount: 25 },
        description: 'Required when event is CREATING_CRITERIA',
        required: false,
    })
    @ValidateIf(o => o.event === CriteriaCreationEventType.CREATING_CRITERIA)
    @IsNotEmpty()
    @IsObject()
    data?: unknown;
}
