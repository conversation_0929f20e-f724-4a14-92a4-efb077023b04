import { Body, Controller, HttpCode, HttpStatus, Post } from '@nestjs/common';
import {
    ApiBadRequestResponse,
    ApiForbiddenResponse,
    ApiOkResponse,
    ApiOperation,
    ApiTags,
} from '@nestjs/swagger';
import { CriteriaCreationProgressRequestDto } from 'app/ai-agent-webhook/vrm/dtos/criteria-creation-progress-request.dto';
import { VendorAssessmentResultRequestDto } from 'app/ai-agent-webhook/vrm/dtos/vendor-assessment-result-request.dto';
import { VendorAssessmentResultService } from 'app/ai-agent-webhook/vrm/services/vendor-assessment-result.service';
import { VrmAiAgentRoute } from 'app/ai-agent-webhook/vrm/vrm-ai-agent.routes';
import { BaseController } from 'commons/controllers/base.controller';
import { Area, ProductArea } from 'commons/decorators/product-area.decorator';
import { ExceptionResponseDto } from 'commons/dtos/exception-response.dto';
import { ApiResponse } from 'commons/enums/api-response.enum';
import { assertNever } from 'commons/helpers/type.helper';
import { PolloMessage } from 'pollo-logger/classes/pollo.message';
import { PolloLogger } from 'pollo-logger/pollo.logger';
import { CriteriaCreationEventType } from 'app/ai-agent-webhook/vrm/enums/criteria-creation.enum';

@ApiTags('AiAgentWebhook')
@Controller()
@ProductArea(Area.AI_ML)
export class VrmAiAgentController extends BaseController {
    private logger = PolloLogger.logger(this.constructor.name);
    constructor(private readonly vendorAssessmentResultService: VendorAssessmentResultService) {
        super();
    }

    @ApiOperation({
        description: 'AI Agent webhook target for criteria creation progress events',
    })
    @ApiOkResponse({
        description: ApiResponse.OK,
    })
    @ApiForbiddenResponse({
        description: ApiResponse.FORBIDDEN,
        type: ExceptionResponseDto,
    })
    @ApiBadRequestResponse({
        description: ApiResponse.BAD_REQUEST,
        type: ExceptionResponseDto,
    })
    @Post(VrmAiAgentRoute.POST_VRM_AI_AGENT_WEBHOOK_CRITERIA_CREATION_PROGRESS)
    @HttpCode(HttpStatus.OK)
    async postCriteriaCreationProgressWebhook(
        @Body() payload: CriteriaCreationProgressRequestDto,
    ): Promise<void> {
        this.logger.log(PolloMessage.msg('Received webhook payload').setMetadata({ payload }));

        switch (payload.event) {
            case CriteriaCreationEventType.CREATING_CRITERIA:
                this.logger.log(PolloMessage.msg('Handling creating criteria event'));
                break;
            case CriteriaCreationEventType.PROCESSING_QUESTIONNAIRES:
                this.logger.log(PolloMessage.msg('Handling processing questionnaires event'));
                break;
            case CriteriaCreationEventType.PROCESSING_QUESTIONS:
                this.logger.log(PolloMessage.msg('Handling analyzing extracted questions event'));
                break;
            default:
                assertNever(payload.event, 'Unhandled VRM criteria creation progress event type');
        }
    }

    @ApiOperation({
        description: 'Webhook to receive Vendor Assessment AI results',
    })
    @ApiOkResponse({
        description: ApiResponse.OK,
    })
    @ApiForbiddenResponse({
        description: ApiResponse.FORBIDDEN,
        type: ExceptionResponseDto,
    })
    @ApiBadRequestResponse({
        description: ApiResponse.BAD_REQUEST,
        type: ExceptionResponseDto,
    })
    @Post(VrmAiAgentRoute.POST_VRM_AI_AGENT_WEBHOOK_VENDOR_ASSESSMENT)
    @HttpCode(HttpStatus.OK)
    async vendorAssessmentResult(@Body() dto: VendorAssessmentResultRequestDto): Promise<void> {
        this.logger.log(
            PolloMessage.msg('Received VendorAssessmentResult payload').setMetadata({ dto }),
        );

        await this.vendorAssessmentResultService.handleVendorAssessmentResult(dto);
    }
}
