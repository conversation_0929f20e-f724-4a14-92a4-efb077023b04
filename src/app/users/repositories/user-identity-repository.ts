import { SortDir, SortType } from '@drata/enums';
import { AccessReviewApplicationUsersListRequestDto } from 'app/access-review/dtos/access-review-application-users-list-request.dto';
import { AccessReviewUserFromCsvRequestDto } from 'app/access-review/dtos/access-review-user-from-csv-request.dto';
import {
    resolveAccessReviewUserIdentitiesFilters,
    resolveAccessReviewUserIdentityRelations,
    resolveAccessReviewUserOrder,
} from 'app/access-review/helpers/access-review.helper';
import {
    filterAccessReviewUserList,
    resolveUserIdentityRelations,
    resolveUserIdentityWhere,
} from 'app/access-review/resolvers/access-application-user.query.resolver';
import { ConnectionEntity } from 'app/companies/connections/entities/connection.entity';
import { UserIdentitiesObservabilityReportRequestDto } from 'app/users/dtos/user-identity-observability/user-identities-observability-report-request.dto';
import { UserIdentityRequestDto } from 'app/users/dtos/user-identity-request.dto';
import { UserIdentityInfrastructure } from 'app/users/entities/user-identity-infrastructure.entity';
import { UserIdentityObservability } from 'app/users/entities/user-identity-observability.entity';
import { UserIdentityVersionControl } from 'app/users/entities/user-identity-version-control.entity';
import { UserIdentity } from 'app/users/entities/user-identity.entity';
import { User } from 'app/users/entities/user.entity';
import { IUserIdentityEntity } from 'app/users/interfaces/user-identity/user-identity-entity.interface';
import { Account } from 'auth/entities/account.entity';
import { getProviderTypeClientTypeMap } from 'auth/helpers/provider-type.helper';
import { UserIdentitiesRequestDto } from 'autopilot2/dtos/user-identities-request.dto';
import { PaginationRequestDto } from 'commons/dtos/pagination-request.dto';
import { ClientType } from 'commons/enums/auth/client-type.enum';
import { ProviderType } from 'commons/enums/auth/provider-type.enum';
import { ConnectionState } from 'commons/enums/auth/state-type.enum';
import { IdentityTypeEnum } from 'commons/enums/identity-type.enum';
import { like } from 'commons/helpers/database.helper';
import { getSkip } from 'commons/helpers/pagination.helper';
import { getSortDir } from 'commons/helpers/sort.helper';
import { BaseRepository } from 'commons/repositories/base.repository';
import { PaginationType } from 'commons/types/pagination.type';
import config from 'config';
import { CustomRepository } from 'database/typeorm/typeorm.extensions.decorator';
import { isEmpty, isNil } from 'lodash';
import { Brackets, In, IsNull, Not, Raw, SelectQueryBuilder } from 'typeorm';

@CustomRepository(UserIdentity)
export class UserIdentityRepository extends BaseRepository<UserIdentity> {
    async getUserIdentities(dto: UserIdentityRequestDto): Promise<PaginationType<UserIdentity>> {
        const { sort, sortDir, page, limit, q, clientType } = dto;

        const query = this.createQueryBuilder('UserIdentity');
        const skip = getSkip(page, limit);

        query
            .innerJoinAndSelect('UserIdentity.user', 'User')
            .innerJoinAndSelect('UserIdentity.connection', 'Connection')
            .leftJoinAndSelect('Connection.connectionProviderTypes', 'ConnectionProviderTypes')
            .leftJoinAndSelect('User.roles', 'UserRole')
            .leftJoinAndSelect('User.identities', 'NestedUserIdentity')
            .leftJoinAndSelect('NestedUserIdentity.connection', 'NestedConnection')
            .leftJoinAndSelect(
                'NestedConnection.connectionProviderTypes',
                'NestedConnectionProviderTypes',
            );

        if (isNil(clientType)) {
            const clientTypes = getProviderTypeClientTypeMap().get(ProviderType.HRIS);
            query.where('Connection.client_type IN (:...clientTypes)', {
                clientTypes,
            });
        } else {
            query.where('Connection.client_type = :clientType', {
                clientType,
            });
        }

        query.skip(skip).take(limit);

        this.resolveSearchQ(query, q);
        this.resolveSort(query, sort, sortDir);

        const [data, total] = await query.getManyAndCount();

        return {
            data,
            page,
            limit,
            total,
        };
    }

    async getUserIdentitiesByIdentityIds(
        identitiesIdentityIds: string[],
        connectionId: number,
    ): Promise<UserIdentity[]> {
        if (isEmpty(identitiesIdentityIds)) {
            return [];
        }

        const query = this.createQueryBuilder('UserIdentity');

        query
            .leftJoinAndSelect('UserIdentity.user', 'User')
            .innerJoinAndSelect('UserIdentity.connection', 'Connection')
            .where('Connection.id = :connectionId', {
                connectionId,
            })
            .andWhere('UserIdentity.identityId IN (:...identitiesIdentityIds)', {
                identitiesIdentityIds,
            });

        return query.getMany();
    }

    async getUserIdentityWithIdentityTypes(dto: UserIdentitiesRequestDto) {
        const queryBuilder = await this.createQueryBuilder('userIdentity')
            .leftJoinAndSelect('userIdentity.user', 'user')
            .leftJoinAndSelect('userIdentity.connection', 'connection')
            .leftJoinAndSelect('userIdentity.serviceAccountDesignator', 'serviceAccountDesignator')
            .skip(dto.offset)
            .take(dto.limit);

        let data;
        if (!dto.type) {
            data = await queryBuilder.getMany();
        } else {
            const { entity, attribute } = this.getUserIdentitiesTable(dto.type);
            data = await queryBuilder
                .innerJoinAndMapOne(
                    `userIdentity.${attribute}`,
                    entity,
                    attribute,
                    `${attribute}.userIdentity = userIdentity.id`,
                )
                .getMany();
        }

        data.forEach(identity => {
            identity.userId = identity.user && identity.user.id;
            identity.connectionId = identity.connection && identity.connection.id;
        });

        return data;
    }

    protected getUserIdentitiesTable(type) {
        const identityType = {
            [IdentityTypeEnum.OBSERVABILITY]: {
                entity: UserIdentityObservability,
                attribute: 'userIdentityObservability',
            },
            [IdentityTypeEnum.INFRASTRUCTURE]: {
                entity: UserIdentityInfrastructure,
                attribute: 'userIdentityInfrastructure',
            },
            [IdentityTypeEnum.VERSION_CONTROL]: {
                entity: UserIdentityVersionControl,
                attribute: 'userIdentityVersionControl',
            },
        };
        return identityType[type];
    }

    async getUserAccessList(
        dto: UserIdentitiesObservabilityReportRequestDto,
    ): Promise<IUserIdentityEntity[]> {
        const { clientType } = dto;
        const query = this.createQueryBuilder('UserIdentity')
            .distinct(true)
            // there should always be a connection
            .innerJoinAndSelect('UserIdentity.connection', 'Connection')
            .leftJoinAndSelect('UserIdentity.user', 'User')
            .leftJoinAndSelect('User.roles', 'UserRole')
            .andWhere('Connection.clientType = :clientType', {
                clientType,
            });

        this.resolveSort(query, SortType.USERNAME, SortDir.ASC);

        const data = await query.getMany();

        return data.map(user => ({
            id: user.id,
            updatedAt: user.updatedAt,
            createdAt: user.createdAt,
            userIdentity: user,
        }));
    }

    protected resolveSort(
        query: SelectQueryBuilder<UserIdentity>,
        sort: SortType,
        sortDir: SortDir,
    ): void {
        const order = getSortDir(sortDir);

        switch (sort) {
            case SortType.USER:
                query.orderBy('User.email', order);
                break;

            case SortType.CONNECTED_AT:
                query.orderBy('UserIdentity.connectedAt', order);
                break;

            case SortType.DISCONNECTED_AT:
                query.orderBy('UserIdentity.disconnectedAt', order);
                break;

            case SortType.HAS_MFA:
                query.orderBy('UserIdentity.hasMfa', order);
                break;

            case SortType.USERNAME:
            default:
                query.orderBy('UserIdentity.username', order);
                break;
        }
    }

    protected resolveSearchQ(query: SelectQueryBuilder<UserIdentity>, q: string): void {
        if (q) {
            query.andWhere(
                `(
                    UserIdentity.identityId ${like()} :both
                    OR UserIdentity.username ${like()} :both
                    OR UserIdentity.email ${like()} :both
                    OR User.email ${like()} :both
                )`,
                {
                    both: `%${q}%`,
                },
            );
        }
    }

    async getAccessReviewUserDetails(
        userIdentityId: number,
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        applicationId: number,
    ): Promise<UserIdentity> {
        const query = this.buildQuery({
            mainAlias: 'UserIdentity',
            where: {
                id: userIdentityId,
            },
            relations: resolveAccessReviewUserIdentityRelations({
                includeAccessReviewUsers: true,
                includeApplicationUserGroupList: true,
            }),
        });

        return query.getOne();
    }

    async getAccessReviewUserListByClientType(
        clientType: ClientType,
        dto: PaginationRequestDto,
    ): Promise<UserIdentity[]> {
        const query = this.buildQuery({
            mainAlias: 'UserIdentity',
            relations: resolveAccessReviewUserIdentityRelations({
                includeAccessReviewUsers: false,
                includeApplicationUserAccessList: false,
            }),
            select: {
                id: true,
                username: true,
                hasMfa: true,
                user: {
                    id: true,
                    entryId: true,
                    email: true,
                    firstName: true,
                    lastName: true,
                    jobTitle: true,
                    avatar: true,
                    drataTermsAgreedAt: true,
                    createdAt: true,
                    updatedAt: true,
                    personnel: {
                        employmentStatus: true,
                    },
                },
                connection: {
                    clientType: true,
                    clientAlias: true,
                    accountId: true,
                    clientId: true,
                },
            },
            skip: getSkip(dto.page, dto.limit),
            take: dto.limit,
            where: resolveAccessReviewUserIdentitiesFilters({
                clientType,
                limit: dto.limit,
                sort: SortType.EMPLOYMENT_STATUS,
                sortDir: SortDir.ASC,
                q: null,
            }),
        });

        return query.getMany();
    }

    async getAccessReviewUserList(
        requestDto: AccessReviewApplicationUsersListRequestDto,
    ): Promise<PaginationType<UserIdentity>> {
        const { limit, page, sort, sortDir } = requestDto;

        const query = filterAccessReviewUserList(
            requestDto,
            this.buildQuery({
                mainAlias: 'userIdentity',
                select: {
                    id: true,
                    identityId: true,
                    username: true,
                    email: true,
                    connectedAt: true,
                    disconnectedAt: true,
                    serviceAccount: true,
                    serviceAccountReason: true,
                    hasMfa: true,
                    lastCheckedAt: true,
                    avatarSource: true,
                    deletedAt: true,
                    createdAt: true,
                    updatedAt: true,
                    user: {
                        id: true,
                        entryId: true,
                        email: true,
                        firstName: true,
                        lastName: true,
                        jobTitle: true,
                        avatar: true,
                        drataTermsAgreedAt: true,
                        createdAt: true,
                        updatedAt: true,
                        personnel: {
                            employmentStatus: true,
                        },
                    },
                    connection: true,
                    applicationUserAccessList: {
                        id: true,
                        hasAdminAccess: true,
                        application: true,
                        accessData: true,
                    },
                },
                relations: { ...resolveUserIdentityRelations(), applicationGroupList: true },
                where: resolveUserIdentityWhere(requestDto.clientType, requestDto.applicationId),
                skip: page ? getSkip(page, limit) : undefined,
                take: limit,
            }),
        );
        resolveAccessReviewUserOrder(query, sort, sortDir);

        const [data, total] = await query.getManyAndCount();

        return {
            data,
            page,
            limit,
            total,
        };
    }

    async getAccessReviewApplicationGroupUserList(
        userIdentityIdList: number[],
        applicationId: number,
    ): Promise<UserIdentity[] | null> {
        if (isEmpty(userIdentityIdList)) {
            return null;
        }

        const query = this.buildQuery({
            mainAlias: 'userIdentity',
            select: {
                id: true,
            },
            relations: {
                applicationGroupList: true,
            },
            where: {
                id: { in$: userIdentityIdList },
                applicationGroupList: {
                    accessApplicationId: applicationId,
                },
            },
            order: [{ property: 'applicationGroupList.name', order: 'ASC' }],
        });

        const [data] = await query.getManyAndCount();

        return data;
    }

    async getUserIdentityWithMFAByUserIds(ids: number[]): Promise<UserIdentity[]> {
        return this.find({
            relations: ['user'],
            where: {
                deletedAt: IsNull(),
                hasMfa: true,
                user: {
                    id: In(ids),
                },
            },
        });
    }

    getConnectedUserIdentitiesByApplicationId(
        applicationId: number,
        startDate: Date,
        endDate: Date,
        page: number,
        limit: number,
    ): Promise<UserIdentity[]> {
        return this.createQueryBuilder('UserIdentity')
            .innerJoin('UserIdentity.applicationUserAccessList', 'ApplicationUserAccessList')
            .leftJoinAndSelect('UserIdentity.user', 'User')
            .where('ApplicationUserAccessList.accessApplicationId = :applicationId', {
                applicationId,
            })
            .andWhere(
                new Brackets(qb =>
                    qb
                        .where({
                            connectedAt: Raw(
                                alias =>
                                    `IF(${alias} > :endDate, ${alias} IS NOT NULL , ${alias} <= :endDate)`,
                                {
                                    endDate,
                                },
                            ),
                            disconnectedAt: Raw(
                                alias => `${alias} IS NULL OR ${alias} >= :startDate`,
                                {
                                    startDate,
                                },
                            ),
                        })
                        .orWhere({
                            connectedAt: IsNull(),
                            disconnectedAt: Raw(
                                alias => `${alias} IS NULL OR ${alias} >= :startDate`,
                                {
                                    startDate,
                                },
                            ),
                            user: IsNull(),
                            createdAt: Raw(
                                alias =>
                                    `IF(${alias} > :endDate, ${alias} IS NOT NULL , ${alias} <= :endDate)`,
                                {
                                    endDate,
                                },
                            ),
                        }),
                ),
            )
            .offset(getSkip(page, limit))
            .limit(limit)
            .getMany();
    }

    getManualApplicationUsers(
        accessApplicationId: number,
        connectionId: number,
        dto: PaginationRequestDto,
    ): Promise<UserIdentity[]> {
        const { limit = 20, page = 1 } = dto;

        return this.find({
            skip: getSkip(page, limit),
            take: limit,
            relations: ['user'],
            where: {
                disconnectedAt: IsNull(),
                email: Not(IsNull()),
                serviceAccount: IsNull(),
                applicationUserAccessList: { accessApplicationId },
                connection: {
                    id: connectionId,
                    clientType: ClientType.UAR_CSV,
                },
            },
        });
    }

    async disconnectByIds(ids: number[], disconnectedAt = new Date()): Promise<number> {
        const result = await this.update(ids, { disconnectedAt });
        return result.affected ?? 0;
    }

    async saveUserIdentityFromUarCsv(
        simplifiedUser: AccessReviewUserFromCsvRequestDto,
        connection: ConnectionEntity,
        storedUserIdentityMap: Map<string, UserIdentity>,
        getUser: (email: string) => User | null,
    ) {
        let updated = false;
        const { email } = simplifiedUser;
        const userIdentity = storedUserIdentityMap.get(email) ?? this.create();
        const storedUser = getUser(email);

        userIdentity.connectedAt = new Date();
        userIdentity.connection = connection;
        userIdentity.disconnectedAt = null;
        userIdentity.email = email;
        userIdentity.hasMfa = false;
        userIdentity.identityId = simplifiedUser.identityId;
        userIdentity.lastCheckedAt = new Date();
        userIdentity.username = email;
        if (storedUser) userIdentity.user = storedUser;
        if (userIdentity.id) updated = true;

        const result = await this.save(userIdentity);

        return { result, updated };
    }

    async getUniqueUserIdentitiesMap(connectionId: number): Promise<Map<string, number>> {
        // Get all the known unique active IDP identities
        const connectionIdentities = await this.createQueryBuilder('UserIdentity')
            .select('UserIdentity.id', 'id')
            .addSelect('UserIdentity.identityId', 'identityId')
            .addSelect('UserIdentity.connectionId', 'connectionId')
            .addSelect('UserIdentity.deletedAt', 'deletedAt')
            .where('UserIdentity.connectionId = :connectionId', { connectionId })
            .getRawMany();
        return new Map<string, number>(
            connectionIdentities.map(row => [
                `${row.identityId}-${row.connectionId}-${row.deletedAt ? Math.floor(new Date(row.deletedAt).getTime() / 1000) : 0}`,
                row.id,
            ]),
        );
    }

    async getUserIdentitiesForUserAndEntryManagementPhase(
        connectionId: number,
        pagination: PaginationRequestDto,
    ): Promise<PaginationType<UserIdentity>> {
        const { page = config.get('pagination.page'), limit = config.get('pagination.token.max') } =
            pagination;
        const skip = getSkip(page, limit);

        const query = this.createQueryBuilder('UserIdentity')
            .leftJoinAndSelect('UserIdentity.user', 'User')
            .innerJoinAndSelect('UserIdentity.connection', 'Connection')
            .where('Connection.id = :connectionId', { connectionId })
            .andWhere('Connection.failedAt IS NULL')
            .andWhere('Connection.providerType = :providerType', {
                providerType: ProviderType.IDENTITY,
            })
            .skip(skip)
            .take(limit);

        const data = await query.getMany();

        return {
            data,
            page,
            limit,
            total: data.length,
        };
    }

    getUserIdentitiesForHrisLinkingPhase(): Promise<UserIdentity[]> {
        return this.createQueryBuilder('UserIdentity')
            .leftJoinAndSelect('UserIdentity.user', 'User')
            .innerJoinAndSelect('UserIdentity.connection', 'Connection')
            .where('Connection.failedAt IS NULL')
            .andWhere('Connection.providerType = :providerType', {
                providerType: ProviderType.HRIS,
            })
            .getMany();
    }

    async getUserIdentitiesWithConnectionByUserId(userId: number) {
        return this.createQueryBuilder('UserIdentity')
            .innerJoinAndSelect('UserIdentity.connection', 'Connection')
            .innerJoinAndSelect('Connection.connectionProviderTypes', 'connectionProviderTypes')
            .where({
                userId,
                deletedAt: IsNull(),
                connection: {
                    connectedAt: Not(IsNull()),
                    state: ConnectionState.ACTIVE,
                },
            })
            .getMany();
    }

    async getUserIdentitiesByConnectionId(connectionId: number): Promise<UserIdentity[]> {
        return this.createQueryBuilder('UserIdentity')
            .innerJoin('UserIdentity.user', 'User')
            .where('UserIdentity.connectionId = :connectionId', { connectionId })
            .getMany();
    }

    async getHrisUserIdentitiesWithManagerId(account: Account): Promise<UserIdentity[]> {
        return this.createQueryBuilder('identity')
            .leftJoin('identity.connection', 'connection')
            .select(['identity', 'connection.accountId', 'connection.providerType'])
            .where('connection.accountId = :accountId', { accountId: account.id })
            .andWhere('connection.providerType = :providerType', {
                providerType: ProviderType.HRIS,
            })
            .andWhere('identity.managerId IS NOT NULL')
            .andWhere('identity.managerName IS NULL')
            .getMany();
    }

    async getAllHrisUserIdentities(account: Account): Promise<UserIdentity[]> {
        return this.createQueryBuilder('identity')
            .leftJoin('identity.connection', 'connection')
            .select(['identity', 'connection.accountId', 'connection.providerType'])
            .where('connection.accountId = :accountId', { accountId: account.id })
            .andWhere('connection.providerType = :providerType', {
                providerType: ProviderType.HRIS,
            })
            .getMany();
    }

    async getUserIdentityByThirdPartyAppAndConnection(
        identityId: string,
        connectionId: number,
        accessApplicationId: number,
    ): Promise<UserIdentity | null> {
        return this.createQueryBuilder('UserIdentity')
            .select([
                'UserIdentity.id',
                'applicationUserAccessList.accessData',
                'applicationUserAccessList.accessApplicationId',
                'applicationUserAccessList.id',
            ])
            .leftJoin('UserIdentity.applicationUserAccessList', 'applicationUserAccessList')
            .where('UserIdentity.identityId = :identityId', { identityId })
            .andWhere('UserIdentity.connectionId = :connectionId', { connectionId })
            .andWhere('applicationUserAccessList.accessApplicationId = :accessApplicationId', {
                accessApplicationId,
            })
            .getOne();
    }
}
