import { TestingModule } from '@nestjs/testing';
import { RoleEntity } from 'app/users/entities/role.entity';
import { User } from 'app/users/entities/user.entity';
import { UsersPermissions } from 'app/users/entities/users-permissions.entity';
import { RoleEntityRepository } from 'app/users/repositories/role-entity.repository';
import { UserDocumentRepository } from 'app/users/repositories/user-document.repository';
import { UserRoleRepository } from 'app/users/repositories/user-role.repository';
import { UserRepository } from 'app/users/repositories/user.repository';
import { UsersRolesRepository } from 'app/users/repositories/users-roles.repository';
import { UsersCoreService } from 'app/users/services/users-core.service';
import { Account } from 'auth/entities/account.entity';
import { CacheService } from 'cache/cache.service';
import { EmailConfig } from 'commons/configs/email.config';
import { Role } from 'commons/enums/users/role.enum';
import { UserDocumentType } from 'commons/enums/users/user-document-type.enum';
import { createAppTestingModule } from 'commons/helpers/app-testing-module.helper';
import { EmailService } from 'commons/services/email.service';
import { FeatureFlagService } from 'feature-flags/feature-flag.service';
import { TenancyContext } from 'tenancy/contexts/tenancy.context';
import { Repository } from 'typeorm';
import { v4 as uuidv4 } from 'uuid';

jest.mock('nestjs-ddtrace', () => ({
    Span: jest.fn(() => jest.fn()),
}));

jest.mock('cache/cache.module', () => ({
    CACHE_IS_UP: true,
}));

jest.mock('config', () => ({
    get: jest.fn().mockImplementation((key: string) => {
        if (key === 'cache.enabled') {
            return true;
        }
        if (key === 'cache.globalPrefix') {
            return 'globalstore';
        }
    }),
}));

describe('UsersCoreService', () => {
    let service: UsersCoreService;
    let userRepository: UserRepository;
    let userRoleRepository: UserRoleRepository;
    let usersRolesRepository: UsersRolesRepository;
    let usersPermissionsRepository: Repository<UsersPermissions>;
    let roleRepository: RoleEntityRepository;
    let userDocumentRepository: UserDocumentRepository;
    let tenancyContext: TenancyContext;

    const user = new User();
    user.id = 1;
    user.entryId = uuidv4();

    const cacheService = Object.assign(new CacheService({} as any), {
        get: jest.fn(() => Promise.resolve()),
        set: jest.fn(() => Promise.resolve()),
        del: jest.fn(() => Promise.resolve()),
        unlink: jest.fn(() => Promise.resolve()),
        keys: jest.fn(() => Promise.resolve([] as string[])),
        multiDel: jest.fn(),
        scanDel: jest.fn(),
        smembers: jest.fn(() => Promise.resolve([])),
    });

    beforeEach(async () => {
        const module: TestingModule = await createAppTestingModule({
            providers: [
                UsersCoreService,
                {
                    provide: CacheService,
                    useValue: cacheService,
                },
                {
                    provide: FeatureFlagService,
                    useValue: {},
                },
                {
                    provide: EmailConfig,
                    useValue: {},
                },
                {
                    provide: EmailService,
                    useValue: {},
                },
                {
                    provide: TenancyContext,
                    useValue: {
                        getCustomRepository: jest.fn().mockReturnValue({
                            findBy: jest.fn(),
                            findOneByEmailNoFail: jest.fn(),
                            getUserByEntryIdOrFail: jest.fn(),
                            softDelete: jest.fn(),
                            save: jest.fn(),
                        }),
                        getRepository: jest.fn().mockReturnValue({
                            findBy: jest.fn(),
                            softDelete: jest.fn(),
                        }),
                    },
                },
            ],
        }).compile();

        service = module.get<UsersCoreService>(UsersCoreService);
        tenancyContext = module.get<TenancyContext>(TenancyContext);
        userRepository = tenancyContext.getCustomRepository(UserRepository);
        userRoleRepository = tenancyContext.getCustomRepository(UserRoleRepository);
        usersRolesRepository = tenancyContext.getCustomRepository(UsersRolesRepository);
        usersPermissionsRepository = tenancyContext.getRepository(UsersPermissions);
        roleRepository = tenancyContext.getCustomRepository(RoleEntityRepository);
        userDocumentRepository = tenancyContext.getCustomRepository(UserDocumentRepository);
    });

    it('should be defined', () => {
        expect(service).toBeDefined();
    });

    it('should call userRepository.getUserByEntryIdOrFail with the correct entryId', async () => {
        const entryId = 'entryId123';
        const account = new Account();

        jest.spyOn(userRepository, 'getUserByEntryIdOrFail').mockResolvedValue(user);

        const result = await service.getUserByEntryIdOrFail(entryId, account);

        expect(result).toEqual(user);
    });

    it('should throw an error if userRepository.getUserByEntryIdOrFail throws an error', async () => {
        const entryId = 'entryId123';
        const account = new Account();
        const error = new Error('User not found');
        jest.spyOn(userRepository, 'getUserByEntryIdOrFail').mockRejectedValue(error);

        await expect(service.getUserByEntryIdOrFail(entryId, account)).rejects.toThrow(
            'User not found',
        );
    });

    it('should call userRepository.findOneByEmailNoFail with the correct email', async () => {
        const email = '<EMAIL>';

        await service.findOneByEmailNoFail(email);

        expect(userRepository.findOneByEmailNoFail).toHaveBeenCalledWith(email);
    });

    it('should call getUserByEntryIdAndConvertToEntity and return a User entity', async () => {
        const entryId = 'entryId123';
        const account = new Account();
        const userQuery = {
            ...user,
            drataTermsAgreedAt: '2023-01-01T00:00:00Z',
            jobTitleChangedAt: '2023-01-01T00:00:00Z',
        };
        //ignore errors since we need the date to be a string
        jest.spyOn(userRepository, 'getUserByEntryIdOrFail').mockResolvedValue(userQuery);
        jest.spyOn(User, 'create').mockReturnValue(userQuery);

        const result = await service.getUserByEntryIdAndConvertToEntity(entryId, account);

        expect(result.drataTermsAgreedAt).toBeInstanceOf(Date);
        expect(result.jobTitleChangedAt).toBeInstanceOf(Date);
    });

    it('should call getUserByEntryIdAndConvertToEntity and return a User entity', async () => {
        const entryId = 'entryId123';
        const account = new Account();
        const userQuery = { ...user, drataTermsAgreedAt: null, jobTitleChangedAt: null };
        //ignore errors since we need the date to be a string
        jest.spyOn(userRepository, 'getUserByEntryIdOrFail').mockResolvedValue(userQuery);
        jest.spyOn(User, 'create').mockReturnValue(userQuery);

        const result = await service.getUserByEntryIdAndConvertToEntity(entryId, account);

        expect(result.drataTermsAgreedAt).toBe(null);
        expect(result.jobTitleChangedAt).toBe(null);
    });

    it('should call removeUsersRolesAndPermissions', async () => {
        const account = new Account();
        const roles = [Role.TRUST_CENTER_MANAGER, Role.TRUST_CENTER_REVIEWER];
        const roleQuery = [
            {
                id: 1,
                role: roles[0],
                deletedAt: null,
                createdAt: new Date(),
                updatedAt: new Date(),
            } as unknown as RoleEntity,
            {
                id: 2,
                role: roles[1],
                deletedAt: null,
                createdAt: new Date(),
                updatedAt: new Date(),
            } as unknown as RoleEntity,
        ];

        jest.spyOn(roleRepository, 'findBy').mockResolvedValue(roleQuery);

        await service.removeUsersRolesAndPermissions(account, [user], roles);

        expect(roleRepository.findBy).toHaveBeenCalled();
        expect(usersPermissionsRepository.softDelete).toHaveBeenCalled();
        expect(usersRolesRepository.softDelete).toHaveBeenCalled();
        expect(userRoleRepository.softDelete).toHaveBeenCalled();
    });

    describe('saveUserDocument', () => {
        beforeEach(() => {
            jest.clearAllMocks();
        });

        it('should save user document successfully', async () => {
            const uploadedFile = {
                key: 'test-key',
                fileName: 'test.pdf',
            };
            const type = UserDocumentType.MFA_EVIDENCE;

            jest.spyOn(userDocumentRepository, 'save').mockResolvedValue(null as any);

            await service.saveUserDocument(user, uploadedFile, type);

            expect(userDocumentRepository.save).toHaveBeenCalled();
        });
    });
});
