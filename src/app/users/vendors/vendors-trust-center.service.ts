import {
    BadRequestException,
    Inject,
    Injectable,
    InternalServerErrorException,
    NotFoundException,
} from '@nestjs/common';
import { User } from 'app/users/entities/user.entity';
import { EMPTY_TOKEN } from 'app/users/vendors/constants/vendor-trust-center.constants';
import { VendorTrustCenterAccessRequest } from 'app/users/vendors/entities/vendor-trust-center-access-request.entity';
import { Vendor } from 'app/users/vendors/entities/vendor.entity';
import { getSBVendorTrustCenterAccessRequestStatus } from 'app/users/vendors/helpers/get-sb-vendor-trust-center-access-request-status.helper';
import { isTokenExpired } from 'app/users/vendors/helpers/vendors-trust-center.helper';
import { VendorRepository } from 'app/users/vendors/repositories/vendor.repository';
import {
    VendorTrustCenterAccessRequestRequirements,
    VendorTrustCenterAccessRequirementsField,
} from 'app/users/vendors/types/vendor-trust-center-access-requirements.type';
import { VendorTrustCenterCertification } from 'app/users/vendors/types/vendor-trust-center-certification.type';
import { VendorTrustCenterDocument } from 'app/users/vendors/types/vendor-trust-center-document.type';
import {
    VendorTrustCenterItem,
    VendorTrustCenterItemsByCategory,
} from 'app/users/vendors/types/vendor-trust-center-items-by-category.type';
import { VendorTrustCenterOverview } from 'app/users/vendors/types/vendor-trust-center-overview.type';
import { VendorTrustCenterSubProcessor } from 'app/users/vendors/types/vendor-trust-center-subprocessor.type';
import { VendorTrustCenter } from 'app/users/vendors/types/vendor-trust-center.type';
import { VendorTrustCenterAccessRequestRepository } from 'app/vendor-access-requests/repositories/vendor-trust-center-access-request.repository';
import { Account } from 'auth/entities/account.entity';
import { NotContentException } from 'commons/exceptions/not-content.exception';
import { markdownToPlainText } from 'commons/helpers/markdown-to-plain-text.helper';
import { getDomain } from 'commons/helpers/url.helper';
import { AppService } from 'commons/services/app.service';
import { SBItemType } from 'dependencies/safebase/item-type.enum';
import { SBOrganizationDocumentAccessType } from 'dependencies/safebase/organization-document-access-type.enum';
import { SafebaseVendorTrustCenterMaturity } from 'dependencies/safebase/safebase-vendor-trust-center-maturity.enum';
import { SafeBaseVendorTrustCenterSdk } from 'dependencies/safebase/safebase-vendor-trust-center.sdk';
import {
    SBAccessRequestRequirementsField,
    SBBaseItem,
    SBSelectFieldOption,
    SBTrustCenterAccessRequestPayload,
    SBTrustCenterAccessRequestResponse,
    SBTrustCenterDocumentDownload,
} from 'dependencies/safebase/safebase-vendor-trust-center.types';
import { VendorDirectoryItemV2 } from 'dependencies/vendor-directory/types/vendor-directory-item.type';
import { VendorDirectoryFactory } from 'dependencies/vendor-directory/vendor-directory-factory';
import { head, isArray, isEmpty, isNil, omit } from 'lodash';
import { PolloMessage } from 'pollo-logger/classes/pollo.message';

@Injectable()
export class VendorsTrustCenterService extends AppService {
    constructor(
        @Inject(SafeBaseVendorTrustCenterSdk)
        private readonly safeBaseSdk: SafeBaseVendorTrustCenterSdk,
        private readonly vendorDirectoryFactory: VendorDirectoryFactory,
    ) {
        super();
    }

    /**
     * Get vendor trust center information by extracting organizationId from Cloudflare vendors API and calling SafeBase
     * @param account - The account context
     * @param vendorId - The vendor ID
     * @returns Promise<VendorTrustCenter>
     * @throws NotContentException if vendor trust center information is not found
     */
    async getVendorTrustCenter(account: Account, vendorId: number): Promise<VendorTrustCenter> {
        try {
            const organizationId = await this.getOrganizationIdFromVendor(vendorId);
            const trustCenterInfo = await this.safeBaseSdk.getTrustCenterInfo(organizationId);

            this.logger.log(
                PolloMessage.msg(
                    `getVendorTrustCenter trustCenterInfo: ${trustCenterInfo}`,
                ).setIdentifier({
                    vendorId,
                    trustCenterInfo,
                }),
            );

            return {
                name: trustCenterInfo.name,
                organizationId,
                trustCenterUrl: trustCenterInfo.trustCenterUrl,
                products: this.safeBaseSdk.getProductsName(trustCenterInfo.products),
            };
        } catch (error) {
            this.error(error, account, { vendorId });
            throw new NotContentException(
                'Something went wrong while fetching Trust center information from safebase',
            );
        }
    }

    /**
     * Get vendor trust center overview by extracting organizationId from Cloudflare vendors API and calling SafeBase
     * @param account - The account context
     * @param vendorId - The vendor ID
     * @returns Promise<VendorTrustCenterOverview[]>
     * @throws NotContentException if vendor trust center overview is not found
     */
    async getVendorTrustCenterOverview(
        account: Account,
        vendorId: number,
    ): Promise<VendorTrustCenterOverview> {
        try {
            const vendorDirectoryItem = await this.getVendorDirectoryData(account, vendorId);

            const organizationId = this.extractOrganizationIdFromVendor(vendorDirectoryItem);
            const product = this.extractProductSlugFromVendor(vendorDirectoryItem);

            const trustCenterOverviewItems = await this.safeBaseSdk.getTrustCenterOverviewItems(
                organizationId,
                [product],
            );

            return {
                organizationId,
                overview: trustCenterOverviewItems[0].explanation,
            };
        } catch (error) {
            this.error(error, account, { vendorId });
            throw new NotContentException(
                'Something went wrong while fetching Trust center overview from safebase',
            );
        }
    }

    /**
     * Get vendor trust center certifications by extracting organizationId from Cloudflare vendors API and calling SafeBase
     * @param account - The account context
     * @param vendorId - The vendor ID
     * @returns Promise<VendorTrustCenterCertification[]>
     * @throws NotContentException if vendor trust center certifications are not found
     */
    async getVendorTrustCenterCertifications(
        account: Account,
        vendorId: number,
        user: User,
    ): Promise<VendorTrustCenterCertification[]> {
        try {
            const vendorDirectoryItem = await this.getVendorDirectoryData(account, vendorId);

            const organizationId = this.extractOrganizationIdFromVendor(vendorDirectoryItem);
            const product = this.extractProductSlugFromVendor(vendorDirectoryItem);

            const token = await this.processAccessTokenValidation(user, vendorId);

            const trustCenterComplianceItems = await this.safeBaseSdk.getTrustCenterComplianceItems(
                organizationId,
                [product],
                token,
            );

            return trustCenterComplianceItems.map(item => ({
                id: item.id,
                name: item.title,
                type: item.itemType,
                imgUrl: (item.logo as string) || '',
                maturityLevel: item.maturity ?? SafebaseVendorTrustCenterMaturity.NA,
            }));
        } catch (error) {
            this.error(error, account, { vendorId });
            throw new NotContentException(
                'Something went wrong while fetching Trust center certifications from safebase',
            );
        }
    }

    /**
     * Get vendor trust center sub-processors by extracting organizationId from Cloudflare vendors API and calling SafeBase
     * @param account - The account context
     * @param vendorId - The vendor ID
     * @returns Promise<VendorTrustCenterSubProcessor[]>
     * @throws NotContentException if vendor trust center certifications are not found
     */
    async getVendorTrustCenterSubProcessors(
        account: Account,
        vendorId: number,
        user: User,
    ): Promise<VendorTrustCenterSubProcessor[]> {
        try {
            const vendorDirectoryItem = await this.getVendorDirectoryData(account, vendorId);

            const organizationId = this.extractOrganizationIdFromVendor(vendorDirectoryItem);
            const product = this.extractProductSlugFromVendor(vendorDirectoryItem);

            const token = await this.processAccessTokenValidation(user, vendorId);

            const legalItems = await this.safeBaseSdk.getTrustCenterLegalItems(
                organizationId,
                [product],
                token,
            );

            const subProcessorItems = legalItems.filter(
                item => item.itemType === SBItemType.LEGAL_SUB_PROCESSORS,
            );

            return subProcessorItems.flatMap(
                item =>
                    item?.listEntries?.map(entry => ({
                        company: entry.company,
                        location: entry.location,
                        additionalDetails: entry.additionalDetails,
                    })) ?? [],
            );
        } catch (error) {
            this.error(error, account, { vendorId });
            throw new NotContentException(
                'Something went wrong while fetching Trust center certifications from safebase',
            );
        }
    }

    /**
     * Get vendor trust center documents by extracting organizationId from Cloudflare vendors API and calling SafeBase
     * @param account - The account context
     * @param vendorId - The vendor ID
     * @returns Promise<VendorTrustCenterDocument[]>
     * @throws NotContentException if vendor trust center documents are not found
     */
    async getVendorTrustCenterDocuments(
        account: Account,
        vendorId: number,
        user: User,
    ): Promise<VendorTrustCenterDocument[]> {
        try {
            const vendorDirectoryItem = await this.getVendorDirectoryData(account, vendorId);

            const organizationId = this.extractOrganizationIdFromVendor(vendorDirectoryItem);
            const product = this.extractProductSlugFromVendor(vendorDirectoryItem);

            const token = await this.processAccessTokenValidation(user, vendorId);

            const trustCenterDocuments = await this.safeBaseSdk.getTrustCenterDocuments(
                organizationId,
                [product],
                token,
            );

            const documentsById: Record<string, VendorTrustCenterDocument> = {};
            const documentsNamesById: Record<string, string> = {};

            for (const document of trustCenterDocuments) {
                const existing = documentsById[document.id];
                const hasExistingPublic = existing && !existing.isAccessRequired;

                // If document is public in this instance, it should be public overall
                // If document was already public from a previous instance, keep it public
                const isPublicAccess = document.publicAccess !== false || hasExistingPublic;
                const lastItemName = document.publicAccess
                    ? document.itemName
                    : hasExistingPublic
                      ? existing.itemName
                      : document.itemName;

                if (document.name) {
                    documentsNamesById[document.id] = document.name;
                }

                documentsById[document.id] = {
                    id: document.id,
                    name: documentsNamesById[document.id] ?? document.name,
                    itemName: lastItemName,
                    isViewOnly:
                        document.documentAccessType === SBOrganizationDocumentAccessType.ViewOnly,
                    isAccessRequired: !isPublicAccess,
                };
            }

            return Object.values(documentsById);
        } catch (error) {
            this.error(error, account, { vendorId });
            throw new NotContentException(
                'Something went wrong while fetching Trust center documents from safebase',
            );
        }
    }

    /**
     * Get vendor trust center document url by extracting organizationId from Cloudflare vendors API and calling SafeBase
     * @param account - The account context
     * @param vendorId - The vendor ID
     * @param documentId - The document ID
     * @returns Promise<SBTrustCenterDocumentDownload[]>
     * @throws NotContentException if vendor trust center document is not found
     */
    async getVendorTrustCenterDocumentsById(
        account: Account,
        vendorId: number,
        documentId: string,
        user: User,
    ): Promise<SBTrustCenterDocumentDownload> {
        try {
            const vendorDirectoryItem = await this.getVendorDirectoryData(account, vendorId);

            const organizationId = this.extractOrganizationIdFromVendor(vendorDirectoryItem);
            const product = this.extractProductSlugFromVendor(vendorDirectoryItem);

            const token = await this.processAccessTokenValidation(user, vendorId);

            const trustCenterDocument = await this.safeBaseSdk.getTrustCenterDocumentsById(
                organizationId,
                documentId,
                [product],
                token,
            );

            const documentDownloadData = head(trustCenterDocument);

            if (!documentDownloadData) {
                throw new NotFoundException('Document not found');
            }

            if (!documentDownloadData.url) {
                throw new NotContentException(
                    'Something went wrong while fetching Trust center document URL from safebase',
                );
            }

            return {
                url: documentDownloadData.url,
                fileName: documentDownloadData?.fileName ?? '',
            };
        } catch (error) {
            this.error(error, account, { vendorId });
            throw new NotContentException(
                'Something went wrong while fetching Trust center documents from safebase',
            );
        }
    }

    /**
     * Get vendor trust center items by category by extracting organizationId from Cloudflare vendors API and calling SafeBase
     * @param account - The account context
     * @param vendorId - The vendor ID
     * @returns Promise<VendorTrustCenterItemsByCategory[]>
     * @throws NotContentException if vendor trust center items are not found
     */
    async getVendorTrustCenterItemsByCategory(
        account: Account,
        vendorId: number,
        user: User,
    ): Promise<VendorTrustCenterItemsByCategory[]> {
        try {
            const vendorDirectoryItem = await this.getVendorDirectoryData(account, vendorId);

            const organizationId = this.extractOrganizationIdFromVendor(vendorDirectoryItem);
            const product = this.extractProductSlugFromVendor(vendorDirectoryItem);

            const token = await this.processAccessTokenValidation(user, vendorId);

            const SBItems = await this.safeBaseSdk.getTrustCenterItems(
                organizationId,
                [product],
                token,
            );

            return SBItems.filter(
                SBItem => SBItem.itemType !== SBItemType.LEGAL_SUB_PROCESSORS,
            ).reduce((acc, SBItem) => {
                const category = acc.find(c => c.type === SBItem.categoryType);

                const mappedItem = this.mapToVendorTrustCenterItem(SBItem);

                if (isNil(category)) {
                    acc.push({
                        type: SBItem.categoryType,
                        title: SBItem.categoryTitle,
                        items: [mappedItem],
                    });
                } else {
                    category.items.push(mappedItem);
                }

                return acc;
            }, [] as VendorTrustCenterItemsByCategory[]);
        } catch (error) {
            this.error(error, account, { vendorId });
            throw new NotContentException(
                'Something went wrong while fetching Trust center items from safebase',
            );
        }
    }

    /**
     * Get vendor trust center access request requirements by extracting organizationId from Cloudflare vendors API and calling SafeBase
     * @param account - The account context
     * @param user - The user context
     * @param vendorId - The vendor ID
     * @returns Promise<VendorTrustCenterAccessRequestRequirements> - Access request requirements data
     * @throws NotFoundException if vendor trust center access request requirements are not found
     * @throws InternalServerErrorException if any unexpected error occurs
     */
    async getVendorTrustCenterAccessRequestRequirements(
        account: Account,
        user: User,
        vendorId: number,
    ): Promise<VendorTrustCenterAccessRequestRequirements> {
        try {
            const vendorDirectoryItem = await this.getVendorDirectoryData(account, vendorId);
            const organizationId = this.extractOrganizationIdFromVendor(vendorDirectoryItem);
            const product = this.extractProductSlugFromVendor(vendorDirectoryItem);
            const token = await this.processAccessTokenValidation(user, vendorId);

            const requirements = await this.safeBaseSdk.getAccessRequestRequirements(
                organizationId,
                user.email,
                product,
                token,
            );

            const processedFields = requirements.fields
                .map(field => this.applyFieldModifications(field))
                // Process markdown in field labels to extract links and convert to plain text
                .map(field => this.processAccessRequirementsField(field));

            return {
                ...requirements,
                fields: processedFields,
            };
        } catch (error) {
            this.error(error, account, { vendorId });

            // Re-throw expected/safe errors that are already properly typed
            if (error instanceof NotFoundException) {
                throw error;
            }

            // For any unexpected errors, throw 500 to prevent sensitive data exposure
            throw new InternalServerErrorException(
                'Something went wrong while fetching Trust center access request requirements from safebase',
            );
        }
    }

    /**
     * Get vendor trust center access request information by vendor ID and user email
     * @param account - The account context
     * @param user - The user context
     * @param vendorId - The vendor ID
     * @returns Promise<VendorTrustCenterAccessRequest>
     * @throws NotContentException if vendor or access request is not found
     */
    async getVendorTrustCenterAccessRequest(
        account: Account,
        user: User,
        vendorId: number,
    ): Promise<VendorTrustCenterAccessRequest> {
        try {
            this.logger.log(
                PolloMessage.msg(
                    `getVendorTrustCenterAccessRequest vendorId: ${vendorId}`,
                ).setIdentifier({
                    vendorId,
                }),
            );

            // First verify the vendor exists and belongs to this tenant
            const vendor = await this.vendorRepository.findOne({
                where: { id: vendorId },
                select: ['id'],
            });

            if (isNil(vendor)) {
                throw new NotFoundException('Vendor not found');
            }

            // Find the most recent access request for this vendor and user
            const accessRequest = await this.vendorTrustCenterAccessRequestRepository.findOne({
                where: { vendor: { id: vendor.id }, requestedBy: user.email },
                relations: ['vendor'],
                order: { createdAt: 'DESC' },
            });

            if (isNil(accessRequest)) {
                throw new NotFoundException('Access request not found');
            }

            this.logger.log(
                PolloMessage.msg(
                    `getVendorTrustCenterAccessRequest accessRequest found`,
                ).setIdentifier({
                    vendorId,
                    accessRequestId: accessRequest.id,
                }),
            );

            return accessRequest;
        } catch (error) {
            this.error(error, account, { vendorId });
            throw new NotContentException(
                'Something went wrong while fetching Trust center access request',
            );
        }
    }

    /**
     * Get organization ID from vendor by following the complete lookup chain
     * @param account - The account context
     * @param vendorId - The vendor ID
     * @returns Promise<string> - The organization ID
     */
    private async getOrganizationIdFromVendor(vendorId: number): Promise<string> {
        this.logger.log(
            PolloMessage.msg(`getOrganizationIdFromVendor vendorId: ${vendorId}`).setIdentifier({
                vendorId,
            }),
        );

        const vendorUrl = await this.getVendorUrlById(vendorId);
        this.logger.log(
            PolloMessage.msg(`getOrganizationIdFromVendor vendorUrl: ${vendorUrl}`).setIdentifier({
                vendorId,
                vendorUrl,
            }),
        );

        const domain = getDomain(vendorUrl);
        this.logger.log(
            PolloMessage.msg(`getOrganizationIdFromVendor domain: ${domain}`).setIdentifier({
                vendorId,
                domain,
            }),
        );

        const organizationId = await this.getOrganizationIdByDomain(domain);
        this.logger.log(
            PolloMessage.msg(
                `getOrganizationIdFromVendor organizationId: ${organizationId}`,
            ).setIdentifier({
                vendorId,
                organizationId,
            }),
        );
        return organizationId;
    }

    /**
     * Get vendor directory data by following the complete lookup chain
     * @param account - The account context
     * @param vendorId - The vendor ID
     * @returns Promise<VendorDirectoryItemV2> - The vendor directory items
     */
    private async getVendorDirectoryData(
        account: Account,
        vendorId: number,
    ): Promise<VendorDirectoryItemV2> {
        this.logger.log(
            PolloMessage.msg(`getVendorDirectoryData vendorId: ${vendorId}`).setIdentifier({
                vendorId,
            }),
        );

        const vendorUrl = await this.getVendorUrlById(vendorId);
        this.logger.log(
            PolloMessage.msg(`getVendorDirectoryData vendorUrl: ${vendorUrl}`).setIdentifier({
                vendorId,
                vendorUrl,
            }),
        );

        const domain = getDomain(vendorUrl);
        this.logger.log(
            PolloMessage.msg(`getVendorDirectoryData domain: ${domain}`).setIdentifier({
                vendorId,
                domain,
            }),
        );

        const vendorDirectoryItem = await this.getVendorDirectoryItemByDomain(domain);

        this.logger.log(
            PolloMessage.msg(
                `getVendorDirectoryData vendorDirectoryItem = ${vendorDirectoryItem}`,
            ).setIdentifier({
                vendorDirectoryItem,
                domain,
            }),
        );

        if (isEmpty(vendorDirectoryItem)) {
            throw new NotFoundException(
                'There is not at least one vendor in vendors directory API for the given domain',
            );
        }

        return vendorDirectoryItem;
    }

    /**
     *
     * @param account
     * @param vendorId
     * @param body - The access request payload
     * @returns Promise<> - The access request result
     */
    async postVendorTrustCenterAccessRequest(
        account: Account,
        vendorId: number,
        body: SBTrustCenterAccessRequestPayload,
    ): Promise<SBTrustCenterAccessRequestResponse> {
        try {
            const vendorDirectoryItem = await this.getVendorDirectoryData(account, vendorId);
            const organizationId = this.extractOrganizationIdFromVendor(vendorDirectoryItem);
            const product = this.extractProductSlugFromVendor(vendorDirectoryItem);

            const accessRequestResponse = await this.safeBaseSdk.postVendorTrustCenterAccessRequest(
                organizationId,
                {
                    ...body,
                    fields: {
                        ...body.fields,
                        productId: body.fields.productId ?? product,
                    },
                },
            );

            const accessRequestResults = accessRequestResponse.result;

            await Promise.all(
                accessRequestResults.map(async result => {
                    if (!result.id) {
                        return;
                    }

                    const existingAccessRequest =
                        await this.vendorTrustCenterAccessRequestRepository.findOne({
                            where: { sbAccessRequestId: result.id },
                        });

                    if (existingAccessRequest) {
                        // update existing record in case status has changed
                        existingAccessRequest.status =
                            getSBVendorTrustCenterAccessRequestStatus(result);
                        await this.vendorTrustCenterAccessRequestRepository.save(
                            existingAccessRequest,
                        );
                    } else {
                        // create new record
                        const vendorTrustCenterAccessRequest = new VendorTrustCenterAccessRequest();
                        vendorTrustCenterAccessRequest.requestedBy = body.fields.workEmail;
                        vendorTrustCenterAccessRequest.vendor = { id: vendorId } as Vendor;
                        vendorTrustCenterAccessRequest.sbAccessRequestId = result.id;
                        vendorTrustCenterAccessRequest.status =
                            getSBVendorTrustCenterAccessRequestStatus(result);
                        vendorTrustCenterAccessRequest.expiresAt = new Date(
                            '1970-01-01T00:00:00.000Z',
                        );
                        await this.vendorTrustCenterAccessRequestRepository.save(
                            vendorTrustCenterAccessRequest,
                        );
                    }
                }),
            );

            return accessRequestResponse;
        } catch (error) {
            this.error(error, account, { vendorId });
            throw new BadRequestException(
                'Something went wrong while posting Trust Center Access Request to Safebase',
            );
        }
    }

    private async getVendorUrlById(vendorId: number): Promise<string> {
        const vendor = await this.vendorRepository.findOne({
            where: { id: vendorId },
            select: ['id', 'url'],
        });

        if (isNil(vendor)) {
            throw new NotFoundException('Vendor not found');
        }

        if (isNil(vendor.url)) {
            throw new Error('Vendor URL is not defined for the vendor');
        }

        return vendor.url;
    }

    /**
     * Resolve a valid SafeBase access token for the given vendor/user.
     * - Returns decrypted token string if present and not expired
     * - If token is expired, soft-deletes the access request and returns empty string
     */
    private async processAccessTokenValidation(user: User, vendorId: number): Promise<string> {
        const accessRequest =
            await this.vendorTrustCenterAccessRequestRepository.findAccessRequestByVendorIdAndRequestedId(
                user.email,
                vendorId,
            );

        if (!accessRequest) {
            this.logger.log(
                PolloMessage.msg('Access request does not exist').setIdentifier({
                    vendorId,
                    reason: 'Access request does not exist',
                }),
            );
            return EMPTY_TOKEN;
        }

        if (isTokenExpired(accessRequest)) {
            await this.handleExpiredToken(user.email, vendorId, accessRequest.expiresAt);
            return EMPTY_TOKEN;
        }

        if (!accessRequest.token) {
            this.logger.log(
                PolloMessage.msg(
                    'Token does not exist for access request or is invalid',
                ).setIdentifier({
                    vendorId,
                    reason: 'Token does not exist',
                }),
            );
            return EMPTY_TOKEN;
        }

        return accessRequest.token;
    }

    /**
     * Handle expired token by cleaning up and logging
     */
    private async handleExpiredToken(
        email: string,
        vendorId: number,
        expiresAt: Date,
    ): Promise<void> {
        await this.vendorTrustCenterAccessRequestRepository.softDeleteAccessRequestByVendorAndEmail(
            email,
            vendorId,
        );

        this.logger.log(
            PolloMessage.msg('Invalidating vendor trust center access token').setIdentifier({
                vendorId,
                reason: 'Expired',
                expiresAt,
            }),
        );
    }

    private async getOrganizationIdByDomain(domain: string): Promise<string> {
        const vendorDirectoryItem = await this.getVendorDirectoryItemByDomain(domain);

        return this.extractOrganizationIdFromVendor(vendorDirectoryItem);
    }

    private async getVendorDirectoryItemByDomain(domain: string): Promise<VendorDirectoryItemV2> {
        const vendorDirectoryItem = await this.vendorDirectoryFactory.create().searchVendor(domain);

        if (isNil(vendorDirectoryItem)) {
            throw new NotFoundException(
                'No vendor found in vendors directory API for the given vendorDirectoryItem',
            );
        }

        return vendorDirectoryItem;
    }

    private extractOrganizationIdFromVendor(vendorDirectoryItem: VendorDirectoryItemV2): string {
        this.assertVendorDirectoryItemIdFormat(vendorDirectoryItem.id);

        return vendorDirectoryItem.id.split(':')[1];
    }

    private extractProductSlugFromVendor(vendorDirectoryItem: VendorDirectoryItemV2): string {
        this.assertVendorDirectoryItemIdFormat(vendorDirectoryItem.id);

        return vendorDirectoryItem.id.split(':')[2];
    }

    private assertVendorDirectoryItemIdFormat(id: string) {
        if (typeof id !== 'string' || !id.startsWith('SB:') || id.split(':').length !== 3) {
            throw new Error('Invalid vendor directory item ID format');
        }
    }

    private mapToVendorTrustCenterItem(rawItem: SBBaseItem): VendorTrustCenterItem {
        // Destructure to separate core properties from additional fields
        const {
            id,
            title,
            itemType,
            question,
            explanation,
            categoryType,
            maturity,
            ...additionalFields
        } = rawItem;

        const coreItem: VendorTrustCenterItem = {
            id,
            name: title,
            type: itemType,
            categoryType,
            maturity,
            description: question || explanation || '',
        };

        // Only add additionalFields if there are any
        if (!isEmpty(additionalFields)) {
            coreItem.additionalFields = omit(additionalFields, 'categoryTitle');
        }

        return coreItem;
    }

    /**
     * Process an access request field by extracting markdown links and converting to URL objects
     * @param field - The raw access request field from SafeBase
     * @returns Processed field with URL objects containing href and optional mask
     */
    private processAccessRequirementsField(
        field: SBAccessRequestRequirementsField,
    ): VendorTrustCenterAccessRequirementsField {
        const { plainText, links } = markdownToPlainText(field.label);
        const { urls, ...rest } = field;

        const currentUrlObjects = isArray(urls)
            ? urls.map(link => ({ href: link.url, mask: link.label }))
            : [];

        const markdownUrlObjects = links.map(link => ({
            href: link.url,
            mask: link.text,
        }));

        const allUrlObjects = [...currentUrlObjects, ...markdownUrlObjects];
        const uniqueUrlObjects = allUrlObjects.filter(
            (urlObj, index, array) => array.findIndex(item => item.href === urlObj.href) === index,
        );

        const result: VendorTrustCenterAccessRequirementsField = {
            ...rest,
            label: plainText,
        };

        if (!isEmpty(uniqueUrlObjects)) {
            result.urls = uniqueUrlObjects;
        }

        return result;
    }

    private get vendorRepository(): VendorRepository {
        return this.getCustomTenantRepository(VendorRepository);
    }

    private get vendorTrustCenterAccessRequestRepository(): VendorTrustCenterAccessRequestRepository {
        return this.getCustomTenantRepository(VendorTrustCenterAccessRequestRepository);
    }

    /**
     * Apply field-specific modifications to access request fields
     * @param field - The field to modify
     * @returns A new field object with modifications applied
     */
    private applyFieldModifications(
        field: SBAccessRequestRequirementsField,
    ): SBAccessRequestRequirementsField {
        if (field.name === 'countriesDropdown' && field.options) {
            return {
                ...field,
                options: this.sortCountriesDropdownOptions(field.options),
            };
        }
        return field;
    }

    /**
     * Sort countries dropdown options with prioritized countries at the top
     * @param options - The options array to sort
     * @returns Sorted options array with prioritized countries first
     */
    private sortCountriesDropdownOptions(options: SBSelectFieldOption[]): SBSelectFieldOption[] {
        const prioritizedCountries = new Set(['United States']);

        // Create a new sorted array without mutating the original
        return [...options].sort((a, b) => {
            const aIsPrioritized = prioritizedCountries.has(a.value);
            const bIsPrioritized = prioritizedCountries.has(b.value);

            if (aIsPrioritized && !bIsPrioritized) {
                return -1;
            }

            if (!aIsPrioritized && bIsPrioritized) {
                return 1;
            }
            return a.value.localeCompare(b.value);
        });
    }
}
