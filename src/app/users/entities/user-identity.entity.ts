import { AccessReviewPeriodApplicationUser } from 'app/access-review/entities/access-review-period-application-user.entity';
import { ApplicationGroup } from 'app/access-review/entities/application-group.entity';
import { ApplicationUserAccess } from 'app/access-review/entities/application-user-access.entity';
import { ConnectionEntity } from 'app/companies/connections/entities/connection.entity';
import { User } from 'app/users/entities/user.entity';
import { IsBoolean, IsDate, IsEmail, IsNotEmpty, IsOptional, MaxLength } from 'class-validator';
import { BaseEntity } from 'commons/entities/base.entity';
import config from 'config';
import {
    Column,
    CreateDateColumn,
    DeleteDateColumn,
    Entity,
    Index,
    JoinColumn,
    ManyToMany,
    ManyToOne,
    OneToMany,
    PrimaryGeneratedColumn,
    Relation,
    UpdateDateColumn,
} from 'typeorm';

@Entity()
@Index('IDX_user_identity_application_user_access', [
    'connectionId',
    'userId',
    'deletedAt',
    'disconnectedAt',
    'serviceAccount',
])
@Index('idx_identity_connection_active', ['identityId', 'connectionId'], { unique: true })
export class UserIdentity extends BaseEntity {
    static INDEX_UNIQUE_IDENTITY_CONNECTION = 'idx_identity_connection_active';
    static PATHS_INDEX_UNIQUE_IDENTITY_CONNECTION = ['identityId', 'connection'];

    @PrimaryGeneratedColumn({
        type: BaseEntity.integer(),
        unsigned: BaseEntity.unsigned(),
    })
    id: number;

    @Index()
    @IsNotEmpty()
    @MaxLength(config.get('validation.maxVarcharText'))
    @Column({ name: 'identity_id', length: config.get('db.varcharLength') })
    identityId: string;

    @IsOptional()
    @Index()
    @IsNotEmpty()
    @MaxLength(config.get('validation.maxVarcharText'))
    @Column({
        name: 'username',
        type: BaseEntity.varchar(),
        length: config.get('db.varcharLength'),
        nullable: true,
    })
    username?: string | null;

    @IsOptional()
    @IsEmail()
    @MaxLength(config.get('validation.maxVarcharText'))
    @Column({
        type: BaseEntity.varchar(),
        length: config.get('db.varcharLength'),
        nullable: true,
    })
    email?: string | null;

    @IsOptional()
    @IsDate()
    @Column({
        name: 'connected_at',
        type: BaseEntity.timestamp(),
        precision: config.get('db.dateTimePrecision'),
        nullable: true,
    })
    connectedAt?: Date | null;

    @IsOptional()
    @IsDate()
    @Column({
        name: 'disconnected_at',
        type: BaseEntity.timestamp(),
        precision: config.get('db.dateTimePrecision'),
        nullable: true,
    })
    disconnectedAt?: Date | null;

    /**
     * Boolean expressed as a timestamp.
     * If there is a timestamp, this
     * is a service account
     */
    @IsOptional()
    @IsDate()
    @Column({
        name: 'service_account',
        type: BaseEntity.timestamp(),
        precision: config.get('db.dateTimePrecision'),
        nullable: true,
    })
    serviceAccount?: Date | null;

    @IsOptional()
    @IsNotEmpty()
    @MaxLength(config.get('validation.maxLongText'))
    @Column({ name: 'service_account_reason', type: 'text', nullable: true })
    serviceAccountReason?: string | null;

    @IsBoolean()
    @IsOptional()
    @Column({
        name: 'has_mfa',
        width: config.get('db.booleanLength'),
        type: 'boolean',
        default: false,
        nullable: true,
    })
    hasMfa?: boolean | null;

    @IsDate()
    @Column({
        name: 'last_checked_at',
        type: BaseEntity.timestamp(),
        precision: config.get('db.dateTimePrecision'),
    })
    lastCheckedAt: Date;

    @IsOptional()
    @IsNotEmpty()
    @MaxLength(config.get('validation.maxVarcharLongText'))
    @Column({
        name: 'avatar_source',
        type: BaseEntity.varchar(),
        length: config.get('db.varcharLongLength'),
        nullable: true,
    })
    avatarSource?: string | null;

    @IsOptional()
    @IsNotEmpty()
    @MaxLength(config.get('validation.maxVarcharLongText'))
    @Column({
        name: 'user_avatar',
        type: BaseEntity.varchar(),
        length: config.get('db.varcharLongLength'),
        nullable: true,
    })
    userAvatar?: string | null;

    @Index()
    @DeleteDateColumn({ type: BaseEntity.timestamp(), name: 'deleted_at' })
    deletedAt: Date | null;

    @CreateDateColumn({ type: BaseEntity.timestamp(), name: 'created_at' })
    createdAt: Date;

    @UpdateDateColumn({ type: BaseEntity.timestamp(), name: 'updated_at' })
    updatedAt: Date;

    @Column({ name: 'fk_user_id', unsigned: true, nullable: true })
    userId: number;

    @ManyToOne(() => User, user => user.identities, {
        eager: true,
        nullable: true,
    })
    @JoinColumn({ name: 'fk_user_id' })
    user: Relation<User>;

    @Column({ name: 'fk_connection_id', unsigned: true })
    connectionId: number;

    @ManyToOne(() => ConnectionEntity, connection => connection.identities, {
        eager: false,
        nullable: false,
    })
    @JoinColumn({ name: 'fk_connection_id' })
    connection: Relation<ConnectionEntity>;

    @ManyToOne(() => User, user => user.markedServiceUserIdentities, {
        eager: false,
        nullable: true,
    })
    @JoinColumn({ name: 'fk_service_account_designator_id' })
    serviceAccountDesignator: Relation<User>;

    @OneToMany(
        () => AccessReviewPeriodApplicationUser,
        reviewApplicationUsers => reviewApplicationUsers.userIdentity,
        { eager: false },
    )
    reviewApplicationUsers: AccessReviewPeriodApplicationUser[];

    @OneToMany(
        () => ApplicationUserAccess,
        applicationUserAccess => applicationUserAccess.userIdentity,
    )
    applicationUserAccessList: ApplicationUserAccess[];

    @ManyToMany(() => ApplicationGroup, group => group.userIdentities, {
        eager: false,
    })
    applicationGroupList: ApplicationGroup[] | null;

    @IsOptional()
    @IsDate()
    @Column({
        name: 'unlinked_at',
        type: BaseEntity.timestamp(),
        nullable: true,
    })
    unlinkedAt: Date | null;

    @IsOptional()
    @IsEmail()
    @MaxLength(config.get('validation.maxVarcharText'))
    @Column({
        name: 'secondary_email',
        type: BaseEntity.varchar(),
        length: config.get('db.varcharLength'),
        nullable: true,
    })
    secondaryEmail?: string | null;

    @IsOptional()
    @MaxLength(config.get('validation.maxVarcharText'))
    @Column({
        name: 'firstname',
        type: BaseEntity.varchar(),
        length: config.get('db.varcharLength'),
        nullable: true,
    })
    firstName?: string | null;

    @IsOptional()
    @MaxLength(config.get('validation.maxVarcharText'))
    @Column({
        name: 'lastname',
        type: BaseEntity.varchar(),
        length: config.get('db.varcharLength'),
        nullable: true,
    })
    lastName?: string | null;

    @IsOptional()
    @IsDate()
    @Column({
        name: 'started_at',
        type: BaseEntity.timestamp(),
        nullable: true,
    })
    startedAt?: Date | null;

    @IsOptional()
    @IsDate()
    @Column({
        name: 'separated_at',
        type: BaseEntity.timestamp(),
        nullable: true,
    })
    separatedAt?: Date | null;

    @IsBoolean()
    @IsOptional()
    @Column({
        name: 'is_contractor',
        width: config.get('db.booleanLength'),
        type: 'boolean',
        nullable: true,
    })
    isContractor?: boolean | null;

    @IsOptional()
    @MaxLength(config.get('validation.maxVarcharText'))
    @Column({
        name: 'job_title',
        type: BaseEntity.varchar(),
        length: config.get('db.varcharLength'),
        nullable: true,
    })
    jobTitle?: string | null;

    @IsOptional()
    @MaxLength(config.get('validation.maxVarcharText'))
    @Column({
        name: 'manager_id',
        type: BaseEntity.varchar(),
        length: config.get('db.varcharLength'),
        nullable: true,
    })
    managerId?: string | null;

    @IsOptional()
    @MaxLength(config.get('validation.maxVarcharText'))
    @Column({
        name: 'manager_name',
        type: BaseEntity.varchar(),
        length: config.get('db.varcharLength'),
        nullable: true,
    })
    managerName?: string | null;
}
