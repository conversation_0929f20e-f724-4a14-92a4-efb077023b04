import { IsBoolean, IsDate, IsEmail, IsNotEmpty, IsOptional, MaxLength } from 'class-validator';
import { BaseEntity } from 'commons/entities/base.entity';
import {
    Column,
    Entity,
    Index,
    PrimaryGeneratedColumn,
} from 'typeorm';

@Entity('user_identity_archive')
@Index('idx_identity_connection_archive', ['identityId', 'connectionId'])
@Index('idx_archived_at', ['archivedAt'])
@Index('idx_original_id', ['originalId'])
@Index('idx_archive_reason', ['archiveReason'])
export class UserIdentityArchive extends BaseEntity {
    @PrimaryGeneratedColumn({
        type: BaseEntity.integer(),
        unsigned: BaseEntity.unsigned(),
    })
    id: number;

    // Original user_identity columns
    @IsNotEmpty()
    @MaxLength(255)
    @Column({ name: 'identity_id', type: BaseEntity.varchar(), length: 255 })
    identityId: string;

    @IsOptional()
    @MaxLength(255)
    @Column({ name: 'username', type: BaseEntity.varchar(), length: 255, nullable: true })
    username?: string | null;

    @IsOptional()
    @IsEmail()
    @MaxLength(255)
    @Column({ name: 'email', type: BaseEntity.varchar(), length: 255, nullable: true })
    email?: string | null;

    @IsOptional()
    @IsDate()
    @Column({ name: 'connected_at', type: BaseEntity.timestamp(), nullable: true })
    connectedAt?: Date | null;

    @IsOptional()
    @IsDate()
    @Column({ name: 'disconnected_at', type: BaseEntity.timestamp(), nullable: true })
    disconnectedAt?: Date | null;

    /**
     * Boolean expressed as a timestamp.
     * If there is a timestamp, this
     * is a service account
     */
    @IsOptional()
    @Column({
        name: 'service_account',
        type: 'tinyint',
        nullable: true,
    })
    serviceAccount?: number | null;

    @IsOptional()
    @Column({ name: 'service_account_reason', type: 'text', nullable: true })
    serviceAccountReason?: string | null;

    @IsOptional()
    @IsBoolean()
    @Column({
        name: 'has_mfa',
        type: 'tinyint',
        nullable: true
    })
    hasMfa?: number | null;

    @IsOptional()
    @IsDate()
    @Column({ name: 'last_checked_at', type: BaseEntity.timestamp(), nullable: true })
    lastCheckedAt?: Date | null;

    @IsOptional()
    @MaxLength(1024)
    @Column({ name: 'avatar_source', type: BaseEntity.varchar(), length: 1024, nullable: true })
    avatarSource?: string | null;

    @IsOptional()
    @MaxLength(1024)
    @Column({ name: 'user_avatar', type: BaseEntity.varchar(), length: 1024, nullable: true })
    userAvatar?: string | null;

    @Column({ name: 'created_at', type: BaseEntity.timestamp(), precision: 6 })
    createdAt: Date;

    @Column({ name: 'updated_at', type: BaseEntity.timestamp(), precision: 6 })
    updatedAt: Date;

    @IsOptional()
    @IsDate()
    @Column({ name: 'deleted_at', type: BaseEntity.timestamp(), nullable: true })
    deletedAt?: Date | null;

    @IsOptional()
    @Column({
        name: 'fk_user_id',
        type: BaseEntity.integer(),
        unsigned: BaseEntity.unsigned(),
        nullable: true,
    })
    userId?: number;

    @Column({
        name: 'fk_connection_id',
        type: BaseEntity.integer(),
        unsigned: BaseEntity.unsigned(),
    })
    connectionId: number;

    @IsOptional()
    @Column({
        name: 'fk_service_account_marked_by_id',
        type: BaseEntity.integer(),
        unsigned: BaseEntity.unsigned(),
        nullable: true,
    })
    serviceAccountMarkedById?: number;

    @IsOptional()
    @IsDate()
    @Column({ name: 'unlinked_at', type: BaseEntity.timestamp(), nullable: true })
    unlinkedAt?: Date | null;

    @IsOptional()
    @IsEmail()
    @MaxLength(255)
    @Column({ name: 'secondary_email', type: BaseEntity.varchar(), length: 255, nullable: true })
    secondaryEmail?: string | null;

    @IsOptional()
    @MaxLength(255)
    @Column({ name: 'firstname', type: BaseEntity.varchar(), length: 255, nullable: true })
    firstName?: string | null;

    @IsOptional()
    @MaxLength(255)
    @Column({ name: 'lastname', type: BaseEntity.varchar(), length: 255, nullable: true })
    lastName?: string | null;

    @IsOptional()
    @IsDate()
    @Column({ name: 'started_at', type: BaseEntity.timestamp(), nullable: true })
    startedAt?: Date | null;

    @IsOptional()
    @IsDate()
    @Column({ name: 'separated_at', type: BaseEntity.timestamp(), nullable: true })
    separatedAt?: Date | null;

    @IsOptional()
    @IsBoolean()
    @Column({
        name: 'is_contractor',
        type: 'tinyint',
        nullable: true
    })
    isContractor?: number | null;

    @IsOptional()
    @MaxLength(255)
    @Column({ name: 'job_title', type: BaseEntity.varchar(), length: 255, nullable: true })
    jobTitle?: string | null;

    @IsOptional()
    @MaxLength(255)
    @Column({ name: 'manager_id', type: BaseEntity.varchar(), length: 255, nullable: true })
    managerId?: string | null;

    @IsOptional()
    @MaxLength(255)
    @Column({ name: 'manager_name', type: BaseEntity.varchar(), length: 255, nullable: true })
    managerName?: string | null;

    // Archive-specific columns
    @Column({
        name: 'archived_at',
        type: BaseEntity.timestamp(),
        precision: 6,
        default: () => 'CURRENT_TIMESTAMP(6)'
    })
    archivedAt: Date;

    @IsNotEmpty()
    @MaxLength(100)
    @Column({ name: 'archive_reason', type: BaseEntity.varchar(), length: 100 })
    archiveReason: string;

    @IsOptional()
    @Column({
        name: 'original_id',
        type: BaseEntity.integer(),
        unsigned: BaseEntity.unsigned(),
        nullable: true,
        comment: 'Original user_identity.id before archiving',
    })
    originalId?: number;
}
