import { CspmIssue } from 'app/cloud-secure-posture-management/entities/cspm-issue.entity';
import { CspmTest } from 'app/cloud-secure-posture-management/types/cspm-test.type';
import { ConnectionEntity } from 'app/companies/connections/entities/connection.entity';
import { BaseRepository } from 'commons/repositories/base.repository';
import { CustomRepository } from 'database/typeorm/typeorm.extensions.decorator';
import { isEmpty } from 'lodash';
import { Brackets, In } from 'typeorm';

@CustomRepository(CspmIssue)
export class CspmIssueRepository extends BaseRepository<CspmIssue> {
    async getIssuesByConnectionAndExternalId(
        connection: ConnectionEntity,
        externalIds: string[],
    ): Promise<[CspmIssue[], number]> {
        return this.findAndCount({
            where: {
                connection: {
                    id: connection.id,
                },
                externalId: In(externalIds),
            },
        });
    }

    async listCspmIssues(connection: ConnectionEntity, dto: CspmTest): Promise<CspmIssue[]> {
        const { riskType, criteria, limit } = dto;

        // 1) Select only IDs with tight filters on the base table (no joins)
        const issuesIds = this.createQueryBuilder('CspmIssue')
            .select('CspmIssue.id', 'id')
            .where('CspmIssue.riskType = :riskType', { riskType })
            .andWhere('CspmIssue.fk_connection_id = :connId', { connId: connection.id });

        if (!isEmpty(criteria?.status)) {
            issuesIds.andWhere('CspmIssue.status IN (:...status)', { status: criteria.status });
        }
        if (!isEmpty(criteria?.subscriptionIds)) {
            // subscriptionId is a single value on the issue; use IN for efficient matching
            issuesIds.andWhere('CspmIssue.subscription_id IN (:...subs)', {
                subs: criteria.subscriptionIds,
            });
        }
        if (!isEmpty(criteria?.issuesIds)) {
            issuesIds.andWhere('CspmIssue.external_id IN (:...issueIds)', {
                issueIds: criteria.issuesIds,
            });
        }
        if (!isEmpty(criteria?.projectIds)) {
            issuesIds.andWhere(
                new Brackets(qb => {
                    criteria.projectIds?.forEach((projectId, index) => {
                        qb.orWhere('FIND_IN_SET(:projectId' + index + ', CspmIssue.projects) > 0', {
                            [`projectId${index}`]: projectId,
                        });
                    });
                }),
            );
        }

        issuesIds.orderBy('CspmIssue.updatedAt', 'DESC');
        if (limit && Number.isFinite(limit) && limit > 0) {
            issuesIds.take(limit);
        }

        const rawIds = await issuesIds.getRawMany<{ id: string | number }>();
        const ids = rawIds.map(r => Number(r.id)).filter(id => !isNaN(id));
        if (ids.length === 0) {
            return [];
        }

        // 2) Load full entities with relations for the small filtered set of IDs
        const query = this.createQueryBuilder('CspmIssue')
            .innerJoinAndSelect('CspmIssue.catalogEntries', 'entriesMap')
            .leftJoinAndSelect('CspmIssue.serviceTickets', 'tickets')
            .innerJoinAndSelect('entriesMap.catalogEntry', 'entries')
            .leftJoinAndSelect('tickets.serviceTicket', 'servicesTicket')
            .where('CspmIssue.id IN (:...ids)', { ids })
            .orderBy('CspmIssue.updatedAt', 'DESC');

        return query.getMany();
    }

    async listCspmIssuesExternalIds(
        connection: ConnectionEntity,
        dto: CspmTest,
    ): Promise<CspmIssue[]> {
        const { riskType, criteria } = dto;
        const query = this.createQueryBuilder('CspmIssue')
            .select(['CspmIssue.externalId'])
            .where('CspmIssue.riskType = :riskType AND CspmIssue.status IN (:...status)', {
                riskType: riskType,
                status: criteria.status?.map(status => status),
            })
            .andWhere('CspmIssue.fk_connection_id = :connId', {
                connId: connection.id,
            });

        if (!isEmpty(criteria?.subscriptionIds)) {
            // subscriptionId is a single value on the issue; use IN for efficient matching
            query.andWhere('CspmIssue.subscription_id IN (:...subs)', {
                subs: criteria.subscriptionIds,
            });
        }

        if (!isEmpty(criteria?.projectIds)) {
            query.andWhere(
                new Brackets(qb => {
                    criteria.projectIds?.forEach((projectId, index) => {
                        qb.orWhere('FIND_IN_SET(:projectId' + index + ', CspmIssue.projects) > 0', {
                            [`projectId${index}`]: projectId,
                        });
                    });
                }),
            );
        }

        return query.getMany();
    }
}
