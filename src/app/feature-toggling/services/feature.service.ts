import { ErrorCode, FeatureGroup } from '@drata/enums';
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import {
    FEATURES_WITH_DEFAULT_VALUES,
    FEATURE_DEFAULT_VALUE_CONFIG,
} from 'app/feature-toggling/constants/user-feature.constants';
import { Feature } from 'app/feature-toggling/entities/feature.entity';
import { UserFeature } from 'app/feature-toggling/entities/user-feature.entity';
import {
    FEATURE_GROUP_SLUGS_MAP,
    FeatureGroupSlugs,
} from 'app/feature-toggling/helper/feature-group-slugs.map';
import { DisableUserFeature } from 'app/feature-toggling/observables/events/disable-user-feature.event';
import { PreviewAccessOptedInEvent } from 'app/feature-toggling/observables/events/preview-access-opted-in.event';
import { PreviewAccessOptedOutEvent } from 'app/feature-toggling/observables/events/preview-access-opted-out.event';
import { UserFeatureRepository } from 'app/feature-toggling/repositories/user-feature.repository';
import { UserConfigurableSettingGroupUpdateRequestDto } from 'app/users/dtos/user-settings/user-configurable-setting-group-update-request.dto';
import { UserConfigurableSettingUpdateRequestDto } from 'app/users/dtos/user-settings/user-configurable-setting-update-request.dto';
import { User } from 'app/users/entities/user.entity';
import { UserRepository } from 'app/users/repositories/user.repository';
import { Account } from 'auth/entities/account.entity';
import { DrataDataSource } from 'commons/classes/drata-data-source.class';
import { FeatureAccess } from 'commons/enums/feature-toggling/feature-access.enum';
import { FeatureType } from 'commons/enums/feature-toggling/feature.enum';
import { NotFoundException } from 'commons/exceptions/not-found.exception';
import { AppService } from 'commons/services/app.service';
import { WorkspacesBaseService } from 'commons/services/workspaces-base.service';
import config from 'config';
import { FeatureFlagCategory } from 'feature-flags/enums/feature-flag-category.enum';
import { FeatureFlag } from 'feature-flags/feature-flag.enum';
import { FeatureFlagService } from 'feature-flags/feature-flag.service';
import { isEmpty, isNil } from 'lodash';
import { FeatureTemplate } from 'site-admin/entities/feature-template.entity';
import { Repository } from 'typeorm';

@Injectable()
export class FeatureService extends AppService {
    constructor(
        @InjectRepository(FeatureTemplate)
        private readonly featureTemplateRepository: Repository<FeatureTemplate>,
        private readonly featureFlagService: FeatureFlagService,
        private readonly workspaceBaseService: WorkspacesBaseService,
    ) {
        super();
    }

    async canRun(
        connectivity: Account | DrataDataSource,
        userId: number,
        featureType: FeatureType,
    ): Promise<boolean> {
        const userFeature = await this.userFeatureRepository.findOne({
            where: {
                user: {
                    id: userId,
                },
                feature: {
                    featureType,
                },
            },
        });

        return !isNil(userFeature?.enabledAt) || false;
    }

    async getUserFeatureByType(
        userId: number,
        featureType: FeatureType,
    ): Promise<UserFeature | null> {
        return this.userFeatureRepository.findOne({
            where: {
                user: {
                    id: userId,
                },
                feature: {
                    featureType,
                },
            },
        });
    }

    async createAccountFeatures(): Promise<void> {
        const featureTemplates = await this.featureTemplateRepository.find();
        const featuresToSave: Feature[] = [];

        for (const featureTemplate of featureTemplates) {
            const featureToSave = new Feature();
            featureToSave.access = featureTemplate.access;
            featureToSave.featureType = featureTemplate.featureType;
            featureToSave.description = featureTemplate.description;
            featureToSave.group = featureTemplate.group;
            featureToSave.defaultValue = featureTemplate.defaultValue;
            featureToSave.valueType = featureTemplate.valueType;
            featureToSave.defaultIsEnabled = featureTemplate.defaultIsEnabled;

            featuresToSave.push(featureToSave);
        }

        if (isEmpty(featuresToSave)) {
            return;
        }

        const savedFeatures = await this.featureRepository.save(featuresToSave);
        const users = await this.userRepository.find();
        const userFeatures: Partial<UserFeature>[] = [];

        for (const user of users) {
            for (const feature of savedFeatures) {
                const userFeature = {
                    user,
                    feature,
                    enabledAt: feature.defaultIsEnabled ? new Date() : null,
                    value: feature.defaultValue,
                };

                userFeatures.push(userFeature);
            }
        }

        await this.userFeatureRepository.save(userFeatures);
    }

    async createUserFeatures(account: Account, user: User): Promise<void> {
        const features = await this.featureRepository.find();

        if (isEmpty(features)) {
            return;
        }

        const userFeatures: Partial<UserFeature>[] = [];

        for (const feature of features) {
            let value = feature.defaultValue;

            if (
                feature.featureType === FeatureType.READINESS_BY_CONTROL &&
                config.get('frameworks.controlReadinessDate') <
                    new Date(account.createdAt).toISOString()
            ) {
                value = 'control';
            }

            const userFeature = {
                user,
                feature,
                enabledAt: feature.defaultIsEnabled ? new Date() : null,
                value,
            };

            userFeatures.push(userFeature);
        }

        await this.userFeatureRepository.save(userFeatures);
    }

    private async createDefaultUserFeature({
        featureType,
        userFeatures,
        user,
        account,
        featureGroup,
    }: {
        featureType: FeatureType;
        userFeatures: UserFeature[];
        user: User;
        account: Account;
        featureGroup: FeatureGroup;
    }): Promise<UserFeature | null> {
        const defaultFeatureConfig = FEATURE_DEFAULT_VALUE_CONFIG[featureType];

        if (!defaultFeatureConfig) {
            return null;
        }

        if (featureGroup !== FEATURE_GROUP_SLUGS_MAP[defaultFeatureConfig.featureGroup]) {
            return null;
        }

        const userFeatureToCreate = userFeatures.find(
            feature => feature.feature.featureType === featureType,
        );

        if (isNil(userFeatureToCreate)) {
            const featureFlagConfig = defaultFeatureConfig.featureFlagConfig;

            if (!isNil(featureFlagConfig)) {
                const isFeatureFlagEnabled = await this.featureFlagService.evaluate(
                    {
                        category: FeatureFlagCategory.NONE,
                        name: FeatureFlag.RELEASE_OVERDUE_TASKS,
                        defaultValue: false,
                    },
                    user,
                    account,
                );

                if (!isFeatureFlagEnabled) {
                    return null;
                }
            }

            const feature = await this.featureRepository.findOne({
                where: { featureType },
            });

            if (!isNil(feature)) {
                const newUserFeature = new UserFeature();
                newUserFeature.user = user;
                newUserFeature.feature = feature;
                newUserFeature.value = feature.defaultValue || defaultFeatureConfig.defaultValue;
                newUserFeature.enabledAt = new Date();

                await this.userFeatureRepository.save(newUserFeature);

                return newUserFeature;
            }
        }

        return null;
    }

    /**
     *
     * @param account
     * @param user
     * @param dto
     */
    async getFeaturesByGroup(
        account: Account,
        user: User,
        settingSlug: FeatureGroupSlugs,
    ): Promise<UserFeature[]> {
        const featureGroup = this.getFeatureGroup(settingSlug);

        const userFeatures = await this.userFeatureRepository.find({
            where: {
                user: {
                    id: user.id,
                },
                feature: {
                    group: featureGroup,
                    access: FeatureAccess.PUBLIC,
                },
            },
        });

        // accounts for when the user feature value hasn't been backfilled
        let readinessUserFeatureExists = false;
        for (const userFeature of userFeatures) {
            if (userFeature.feature.featureType === FeatureType.READINESS_BY_CONTROL) {
                readinessUserFeatureExists = true;
                break;
            }
        }

        if (!readinessUserFeatureExists) {
            const readinessFeature = await this.featureRepository.findOne({
                where: { featureType: FeatureType.READINESS_BY_CONTROL },
            });

            if (!isNil(readinessFeature)) {
                const readinessUserFeature = new UserFeature();
                readinessUserFeature.user = user;
                readinessUserFeature.feature = readinessFeature;

                // if account was created past a certain date, the default is control, old accounts are requirement
                if (
                    config.get('frameworks.controlReadinessDate') <
                    new Date(account.createdAt).toISOString()
                ) {
                    readinessUserFeature.value = 'control';
                } else {
                    readinessUserFeature.value = 'requirement';
                }

                userFeatures.push(readinessUserFeature);
            }
        }

        await Promise.all(
            FEATURES_WITH_DEFAULT_VALUES.map(async featureType => {
                const overdueTasksFeature = await this.createDefaultUserFeature({
                    userFeatures,
                    user,
                    account,
                    featureGroup,
                    featureType,
                });

                if (!isNil(overdueTasksFeature)) {
                    userFeatures.push(overdueTasksFeature);
                }
            }),
        );

        return userFeatures;
    }

    /**
     *
     * @param account
     * @param user
     * @param dto
     * @param id
     */
    async updateUserFeature(
        account: Account,
        user: User,
        dto: UserConfigurableSettingGroupUpdateRequestDto,
        settingSlug: FeatureGroupSlugs,
    ): Promise<UserFeature[]> {
        const featureGroup = this.getFeatureGroup(settingSlug);

        const userFeatures = await this.userFeatureRepository.find({
            where: {
                user: {
                    id: user.id,
                },
                feature: {
                    group: featureGroup,
                    access: FeatureAccess.PUBLIC,
                },
            },
        });

        for (const userFeature of userFeatures) {
            userFeature.enabledAt = dto.isEnabled ? new Date() : null;
        }

        return this.userFeatureRepository.save(userFeatures);
    }

    /**
     *
     * @param account
     * @param user
     * @param dto
     */
    async updateFeatureSetting(
        account: Account,
        user: User,
        dto: UserConfigurableSettingUpdateRequestDto,
    ): Promise<UserFeature> {
        // if working off a default value, we'll need to create the userFeature first
        let queryOptions = {};
        let usingType = false;

        if (!isNil(dto.featureId)) {
            queryOptions = {
                where: {
                    user: {
                        id: user.id,
                    },
                    id: dto.featureId,
                },
            };
        } else {
            usingType = true;
            queryOptions = {
                where: {
                    user: {
                        id: user.id,
                    },
                    feature: { featureType: dto.featureType },
                },
            };
        }

        let userFeature = await this.userFeatureRepository.findOne(queryOptions);

        if (isNil(userFeature)) {
            if (!usingType) {
                throw new NotFoundException(ErrorCode.SETTING_NOT_FOUND);
            }
            this.log(`UserFeature ${dto.featureType} not found with id ${dto.featureId}, creating`);

            const feature = await this.featureRepository.findOneOrFail({
                where: { featureType: dto.featureType },
            });

            userFeature = new UserFeature();
            userFeature.user = user;
            userFeature.feature = feature;
        }

        userFeature.enabledAt = dto.isEnabled ? new Date() : null;

        if (isNil(userFeature.enabledAt)) {
            this._eventBus.publish(new DisableUserFeature(account, user, userFeature));
        }

        if (!isNil(dto.value)) {
            if (dto.value === true) userFeature.value = '1';
            else if (dto.value === false) userFeature.value = '0';
            else userFeature.value = String(dto.value);
        }

        const productId = await this.workspaceBaseService.getPrimaryProductId();

        if (dto.featureId) {
            const constellationFeature = await this.userFeatureRepository.findOne({
                where: {
                    id: dto.featureId,
                    feature: {
                        featureType: FeatureType.CONSTELLATION_BETA_ACCESS,
                    },
                },
            });

            if (!isNil(constellationFeature) && dto.value) {
                this._eventBus.publish(new PreviewAccessOptedInEvent(account, user, productId));
            }
        }

        if (dto.featureType === FeatureType.CONSTELLATION_BETA_ACCESS && !dto.value) {
            this._eventBus.publish(new PreviewAccessOptedOutEvent(account, user, productId));
        }

        return this.userFeatureRepository.save(userFeature);
    }

    public findOneTenantFeatureByType(featureType: FeatureType): Promise<Feature | null> {
        return this.featureRepository.findOne({
            where: { featureType },
        });
    }

    public async createTenantFeatureEntryOrFail(featureType: FeatureType): Promise<Feature | null> {
        const featureTemplate = await this.featureTemplateRepository.findOne({
            where: { featureType },
        });

        if (isNil(featureTemplate)) {
            return null;
        }

        const feature = new Feature();
        feature.featureType = featureTemplate.featureType;
        feature.access = featureTemplate.access;
        feature.description = featureTemplate.description;
        feature.group = featureTemplate.group;
        feature.defaultValue = featureTemplate.defaultValue;
        feature.valueType = featureTemplate.valueType;
        feature.defaultIsEnabled = featureTemplate.defaultIsEnabled;

        await this.featureRepository.save(feature);

        return feature;
    }

    private async buildUserFeatureRepository(
        connectivity: Account | DrataDataSource,
        userFeatureRepository?: Repository<UserFeature>,
    ): Promise<Repository<UserFeature> | undefined> {
        if (userFeatureRepository) return userFeatureRepository;

        if (connectivity instanceof Account) {
            return this.userFeatureRepository;
        }

        if (connectivity instanceof DrataDataSource) {
            return connectivity.getRepository(UserFeature);
        }
    }

    private getFeatureGroup(settingSlug: FeatureGroupSlugs) {
        const featureGroup = FEATURE_GROUP_SLUGS_MAP[settingSlug];

        if (isNil(featureGroup)) {
            throw new NotFoundException(ErrorCode.SETTING_NOT_FOUND);
        }
        return featureGroup;
    }

    private get userRepository(): UserRepository {
        return this.getCustomTenantRepository(UserRepository);
    }

    private get featureRepository(): Repository<Feature> {
        return this.getTenantRepository(Feature);
    }

    private get userFeatureRepository(): UserFeatureRepository {
        return this.getCustomTenantRepository(UserFeatureRepository);
    }
}
