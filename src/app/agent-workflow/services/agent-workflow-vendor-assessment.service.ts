import { ErrorCode } from '@drata/enums';
import { Injectable, UnprocessableEntityException } from '@nestjs/common';
import { CreateAgentWorkflowDto } from 'app/agent-workflow/dtos/create-agent-workflow.dto';
import { GetVendorAssessmentAgentWorkflowsDto } from 'app/agent-workflow/dtos/get-vendor-assessment-agent-workflows.dto';
import { AgentWorkflow } from 'app/agent-workflow/entities';
import {
    AgentType,
    AgentWorkflowGeneralStepName,
    AgentWorkflowStatus,
    AgentWorkflowType,
    AgentWorkflowVendorAssessmentStepName,
    AgentWorkflowVendorType,
} from 'app/agent-workflow/enums';
import { AgentWorkflowOrchestratorService } from 'app/agent-workflow/services/agent-workflow-orchestrator.service';

import { AgentWorkflowService } from 'app/agent-workflow/services/agent-workflow.service';
import { VendorAssessmentAgentWorkflowType } from 'app/agent-workflow/types/vendor-assessment-agent-workflow.type';
import { WorkflowExecutionResult } from 'app/agent-workflow/types/workflow-execution-result.type';
import { User } from 'app/users/entities/user.entity';
import { VendorSecurityReview } from 'app/users/vendors/entities/vendor-security-review.entity';
import { VendorsSecurityRiskCoreService } from 'app/users/vendors/services/vendors-security-risk-core.service';
import { Account } from 'auth/entities/account.entity';
import { VendorSecurityReviewStatus } from 'commons/enums/vendor/vendor-security-review-status.enum';
import { VendorSecurityReviewType } from 'commons/enums/vendor/vendor-security-review-type.enum';
import { VendorStatus } from 'commons/enums/vendor/vendor-status.enum';
import { ValidationException } from 'commons/exceptions/validation.exception';
import { AppService } from 'commons/services/app.service';
import { PaginationType } from 'commons/types/pagination.type';
import config from 'config';
import { isEmpty, isNil } from 'lodash';
import { In, Not } from 'typeorm';

/**
 * Agent Workflow Vendor Assesment Service
 *
 * Acts as the main coordinator for all vendor assesment operations and handles the complete execution flow.
 * This service integrates the step executor service, step message service, and state machine service
 * to execute workflows step-by-step until manual input is required.
 *
 * Manages the full execution cycle: message generation → step execution → state transition → continuation evaluation.
 */
@Injectable()
export class AgentWorkflowVendorAssessmentService extends AppService {
    constructor(
        private readonly agentWorkflowService: AgentWorkflowService,
        private readonly vendorsSecurityRiskCoreService: VendorsSecurityRiskCoreService,
        private readonly agentWorkflowOrchestratorService: AgentWorkflowOrchestratorService,
    ) {
        super();
    }

    /**
     *
     */
    async getVendorAssessmentWorkflows(
        account: Account,
        agentType: AgentType,
        agentWorkflowType: AgentWorkflowType,
        dto: GetVendorAssessmentAgentWorkflowsDto,
    ): Promise<PaginationType<VendorAssessmentAgentWorkflowType>> {
        // retrieve all the agent workflow types ids
        const workflowTypeIds = await this.agentWorkflowService.getAgentWorkflowTypeIds(
            account,
            agentType,
            agentWorkflowType,
            dto,
        );

        const vendorSecurityReviews = await this.getSecurityReportsVendor(
            dto.vendorIds,
            workflowTypeIds.map(w => Number(w)),
        );

        if (isEmpty(vendorSecurityReviews)) {
            return {
                data: [],
                page: dto.page,
                limit: dto.limit ?? config.get('pagination.limit'),
                total: 0,
            };
        }

        const assessmentWorkflowsPage = await this.agentWorkflowService.getAgentWorkflows(
            account,
            agentType,
            agentWorkflowType,
            { ...dto, workflowTypeIds: vendorSecurityReviews.map(v => v.id.toString()) },
        );

        const transformedData = assessmentWorkflowsPage.data.map(assessmentWorkflow => {
            const vendorSecurityReview = vendorSecurityReviews.find(
                v => v.id === Number(assessmentWorkflow.workflowTypeId),
            );
            const extendedWorkflow =
                this.agentWorkflowOrchestratorService.buildAgentWorkflowExtended(
                    assessmentWorkflow,
                );
            const assessmentStartedStep = assessmentWorkflow.steps.find(
                s => s.stepName === AgentWorkflowVendorAssessmentStepName.SEND_DOCUMENTS_TO_AI,
            );
            const assessmentStartedDate = assessmentStartedStep?.createdAt ?? null;

            return Object.assign(extendedWorkflow, {
                vendorId: vendorSecurityReview?.vendor.id ?? null,
                vendorName: vendorSecurityReview?.vendor.name ?? '',
                assessmentStarted: !isNil(assessmentStartedStep),
                assessmentStartedDate,
            });
        });

        return {
            data: transformedData,
            page: assessmentWorkflowsPage.page,
            limit: assessmentWorkflowsPage.limit,
            total: assessmentWorkflowsPage.total,
        };
    }

    private getSecurityReportsVendor(
        vendorIds?: number[] | null,
        vendorSecurityReviews?: number[] | null,
    ): Promise<VendorSecurityReview[]> {
        if (isNil(vendorIds) && isNil(vendorSecurityReviews)) {
            throw new UnprocessableEntityException('No vendor ids or vendor security reviews');
        }

        const whereStatement = {
            ...(vendorIds && { vendor: { id: In(vendorIds) } }),
            ...(vendorSecurityReviews && { id: In(vendorSecurityReviews) }),
        };
        return this.vendorSecurityReviewRepository.find({
            where: whereStatement,
            relations: {
                vendor: true,
            },
        });
    }

    private get vendorSecurityReviewRepository() {
        return this.getTenantRepository(VendorSecurityReview);
    }

    /**
     * Start a vendor assessment workflow with comprehensive validation and setup.
     * This method serves as the entry point for vendor assessment workflows and handles:
     * - Active workflow validation
     * - Security review creation/validation
     * - Workflow creation and execution
     *
     * @param account - The account context for logging and permissions
     * @param user - The user creating the workflow
     * @param dto - Workflow creation data containing vendorId and optional workflowTypeId
     * @returns Promise<WorkflowExecutionResult> - Comprehensive execution result
     */
    async startVendorAssessment(
        account: Account,
        user: User,
        dto: CreateAgentWorkflowDto,
    ): Promise<WorkflowExecutionResult> {
        this.log(`Starting vendor assessment workflow for vendor ${dto.vendorId}`, account, {
            vendorId: dto.vendorId,
            workflowTypeId: dto.workflowTypeId,
        });

        try {
            // Step: Handle security review (create or validate existing)
            const securityReview = await this.handleSecurityReview(account, user, dto);

            // Step: Create the agent workflow with the security review ID
            const workflowDto: CreateAgentWorkflowDto = {
                ...dto,
                workflowTypeId: securityReview.id.toString(),
            };

            const workflow = await this.agentWorkflowService.createAgentWorkflow(
                account,
                user,
                AgentType.VENDOR_RISK_MANAGEMENT,
                AgentWorkflowVendorType.VENDOR_ASSESSMENT,
                workflowDto,
            );

            // Step: Add the PROCESS_START step
            const processStartStep = await this.agentWorkflowService.createAgentWorkflowStep(
                account,
                AgentType.VENDOR_RISK_MANAGEMENT,
                AgentWorkflowVendorType.VENDOR_ASSESSMENT,
                workflow.id,
                100,
                {
                    stepName: AgentWorkflowGeneralStepName.PROCESS_START,
                },
            );

            // Refresh the workflow entity to include the newly created step
            const currentWorkflow = await this.agentWorkflowService.getAgentWorkflow(
                account,
                workflow.id,
            );

            // Emit step created event for the PROCESS_START step
            await this.agentWorkflowOrchestratorService.emitStepCreatedEvent(
                account,
                currentWorkflow,
                processStartStep,
            );

            // Step: Start workflow execution from PROCESS_START
            const result = await this.agentWorkflowOrchestratorService.startWorkflow(
                account,
                currentWorkflow,
            );

            this.log(`Vendor assessment workflow ${workflow.id} started successfully`, account, {
                workflowId: workflow.id,
                vendorId: dto.vendorId,
                securityReviewId: securityReview.id,
                success: result.success,
            });

            return result;
        } catch (error) {
            this.error(error, account, {
                vendorId: dto.vendorId,
                workflowTypeId: dto.workflowTypeId,
            });
            throw error;
        }
    }

    /**
     * Handle security review creation or validation based on workflowTypeId presence and vendor status.
     *
     * This method handles three cases:
     * 1. If workflowTypeId is provided: Validate existing security review
     * 2. If vendor is prospective: Check for existing security review to avoid duplicates
     * 3. Otherwise: Create new security review
     *
     * @param account - The account context
     * @param user - The user creating the workflow
     * @param dto - Workflow creation data
     * @returns Promise<VendorSecurityReview> - The security review to use
     */
    private async handleSecurityReview(
        account: Account,
        user: User,
        dto: CreateAgentWorkflowDto,
    ): Promise<VendorSecurityReview> {
        if (dto.workflowTypeId) {
            // Case 1: Validate existing security review
            return this.validateExistingSecurityReview(account, dto.vendorId, dto.workflowTypeId);
        }

        // Case 2: Check for prospective vendor with existing security review in a single query
        const existProspectiveReviews = await this.vendorSecurityReviewRepository.exists({
            where: {
                vendor: { id: dto.vendorId, status: VendorStatus.PROSPECTIVE },
            },
        });

        if (existProspectiveReviews) {
            // For "Start Assessment" with review creation, throw error if prospective vendor already has a review
            throw new ValidationException(
                `Cannot start new assessment: Prospective vendor ${dto.vendorId} already has an existing security review. ` +
                    `Please use the existing review or complete the current assessment first.`,
            );
        }

        // Case 3: Create new security review (either prospective vendor without existing review or non-prospective vendor)
        return this.createNewSecurityReview(account, user, dto.vendorId);
    }

    /**
     * Validate an existing security review belongs to the specified vendor and check for active workflows.
     *
     * @param account - The account context
     * @param vendorId - The vendor ID
     * @param workflowTypeId - The security review ID
     * @returns Promise<VendorSecurityReview> - The validated security review
     * @throws ValidationException if security review doesn't exist, vendor mismatch, or active workflow exists
     */
    private async validateExistingSecurityReview(
        account: Account,
        vendorId: number,
        workflowTypeId: string,
    ): Promise<VendorSecurityReview> {
        this.log(`Validating security review ${workflowTypeId} for vendor ${vendorId}`, account);

        try {
            const securityReviewId = parseInt(workflowTypeId, 10);
            if (isNaN(securityReviewId)) {
                throw new ValidationException(
                    `Invalid workflowTypeId format: ${workflowTypeId}`,
                    ErrorCode.VALIDATION,
                );
            }

            const securityReview =
                await this.vendorsSecurityRiskCoreService.getVendorsSecurityReview(
                    securityReviewId,
                );

            // Validate that the security review belongs to the specified vendor
            if (isNil(securityReview.vendor) || securityReview.vendor.id !== vendorId) {
                throw new ValidationException(
                    `Security review ${workflowTypeId} does not belong to vendor ${vendorId}`,
                    ErrorCode.VALIDATION,
                );
            }

            // Check if security review already has an active agent workflow
            const existingWorkflow = await this.agentWorkflowRepository.exists({
                where: {
                    agentType: AgentType.VENDOR_RISK_MANAGEMENT,
                    agentWorkflowType: AgentWorkflowVendorType.VENDOR_ASSESSMENT,
                    workflowTypeId: securityReview.id.toString(),
                    status: Not(In([AgentWorkflowStatus.COMPLETED, AgentWorkflowStatus.CANCELLED])),
                },
            });

            if (existingWorkflow) {
                throw new ValidationException(
                    `Cannot start new assessment: Security review ${workflowTypeId} already has an active workflow. ` +
                        `Please complete or cancel the existing workflow before starting a new assessment.`,
                    ErrorCode.VALIDATION,
                );
            }

            return securityReview;
        } catch (error) {
            if (error instanceof ValidationException) {
                throw error;
            }
            throw new ValidationException(
                `Security review with ID ${workflowTypeId} was not found`,
                ErrorCode.VALIDATION,
            );
        }
    }

    /**
     * Create a new security review for the specified vendor.
     *
     * @param account - The account context
     * @param user - The user creating the security review
     * @param vendorId - The vendor ID
     * @returns Promise<VendorSecurityReview> - The newly created security review
     */
    private async createNewSecurityReview(
        account: Account,
        user: User,
        vendorId: number,
    ): Promise<VendorSecurityReview> {
        this.log(`Creating new security review for vendor ${vendorId}`, account);

        // Create a new security review with default values
        const securityReviewData = {
            title: `Agent Workflow Security Review - ${new Date().toISOString()}`,
            requestedAt: new Date(),
            reviewDeadlineAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
            securityReviewStatus: VendorSecurityReviewStatus.IN_PROGRESS,
            securityReviewType: VendorSecurityReviewType.SECURITY,
            requesterUserId: user.id,
        };

        return this.vendorsSecurityRiskCoreService.createVendorSecurityReview(
            user,
            account,
            securityReviewData,
            vendorId,
        );
    }

    private get agentWorkflowRepository() {
        return this.getTenantRepository(AgentWorkflow);
    }
}
