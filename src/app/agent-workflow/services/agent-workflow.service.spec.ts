import { TestingModule } from '@nestjs/testing';
import { CreateAgentWorkflowStepDto } from 'app/agent-workflow/dtos/create-agent-workflow-step.dto';
import { CreateAgentWorkflowDto } from 'app/agent-workflow/dtos/create-agent-workflow.dto';
import { GetAgentWorkflowsDto } from 'app/agent-workflow/dtos/get-agent-workflows.dto';
import { UpdateAgentWorkflowRequestDto } from 'app/agent-workflow/dtos/update-agent-workflow-request.dto';
import {
    AgentWorkflow,
    AgentWorkflowStep,
    AgentWorkflowStepMessage,
} from 'app/agent-workflow/entities';
import {
    AgentType,
    AgentWorkflowStatus,
    AgentWorkflowVendorAssessmentStepName,
    AgentWorkflowVendorType,
} from 'app/agent-workflow/enums';
import { AgentWorkflowMessageFactory } from 'app/agent-workflow/message/factories/agent-workflow-message-factory';
import { AgentWorkflowService } from 'app/agent-workflow/services/agent-workflow.service';
import { AgentWorkflowStepMessageDataType } from 'app/agent-workflow/types';
import { getUserMock } from 'app/users/entities/mocks/user.mock';
import { getAccountMock } from 'auth/mocks/account.mock';
import { ValidationException } from 'commons/exceptions/validation.exception';
import { createAppTestingModule } from 'commons/helpers/app-testing-module.helper';

describe('AgentWorkflowService', () => {
    let service: AgentWorkflowService;

    const mockAccount = getAccountMock();
    const mockUser = getUserMock();

    const mockRepository = {
        createWorkflow: jest.fn(),
        getWorkflows: jest.fn(),
        getWorkflowById: jest.fn(),
        createStep: jest.fn(),
        updateWorkflow: jest.fn(),
    };

    const mockMessageDataFactory = {
        generateMessageData: jest.fn(),
    };

    beforeEach(async () => {
        const module: TestingModule = await createAppTestingModule({
            providers: [
                AgentWorkflowService,
                {
                    provide: AgentWorkflowMessageFactory,
                    useValue: mockMessageDataFactory,
                },
            ],
        }).compile();

        service = module.get<AgentWorkflowService>(AgentWorkflowService);

        // Mock the getCustomTenantRepository method to return our mock repository
        jest.spyOn(service as any, 'getCustomTenantRepository').mockImplementation(
            (repositoryClass: any) => {
                if (repositoryClass.name === 'AgentWorkflowRepository') {
                    return mockRepository;
                }
                return mockRepository; // fallback
            },
        );
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    beforeEach(() => {
        // Reset all mocks before each test
        jest.clearAllMocks();
    });

    it('should be defined', () => {
        expect(service).toBeDefined();
    });

    describe('createAgentWorkflow', () => {
        it('should create an agent workflow and return mocked response', async () => {
            const agentType = AgentType.VENDOR_RISK_MANAGEMENT;
            const agentWorkflowType = AgentWorkflowVendorType.VENDOR_ASSESSMENT;
            const dto: CreateAgentWorkflowDto = {
                workflowTypeId: 'test-workflow-123',
                vendorId: 1,
                workspaceId: 1,
            };
            const mockWorkflow = { id: 1, agentType, status: AgentWorkflowStatus.PENDING };

            mockRepository.createWorkflow.mockResolvedValue(mockWorkflow);

            const result = await service.createAgentWorkflow(
                mockAccount,
                mockUser,
                agentType,
                agentWorkflowType,
                dto,
            );

            expect(result).toEqual(mockWorkflow);
            expect(mockRepository.createWorkflow).toHaveBeenCalledWith(
                mockUser,
                agentType,
                agentWorkflowType,
                dto,
            );
        });
    });

    describe('createAgentWorkflowStep', () => {
        it('should create a workflow step and return mocked response', async () => {
            const agentType = AgentType.VENDOR_RISK_MANAGEMENT;
            const agentWorkflowType = AgentWorkflowVendorType.VENDOR_ASSESSMENT;
            const workflowId = 1;
            const dto: CreateAgentWorkflowStepDto = {
                stepName: AgentWorkflowVendorAssessmentStepName.SEND_DOCUMENTS_TO_AI,
            };
            const mockStep = { id: 1, workflowId, status: 'PENDING' };

            mockRepository.createStep.mockResolvedValue(mockStep);

            const result = await service.createAgentWorkflowStep(
                mockAccount,
                agentType,
                agentWorkflowType,
                workflowId,
                100,
                dto,
            );

            expect(result).toEqual(mockStep);
            expect(mockRepository.createStep).toHaveBeenCalledWith(
                workflowId,
                agentType,
                agentWorkflowType,
                100,
                dto,
            );
        });
    });

    describe('getAgentWorkflows', () => {
        it('should get agent workflows and return paginated response', async () => {
            const agentType = AgentType.VENDOR_RISK_MANAGEMENT;
            const agentWorkflowType = AgentWorkflowVendorType.VENDOR_ASSESSMENT;
            const dto: GetAgentWorkflowsDto = {};
            const mockWorkflows = [{ id: 1, agentType, agentWorkflowType, status: 'PENDING' }];
            const mockPaginatedResult = {
                data: mockWorkflows,
                page: 1,
                limit: 20,
                total: 1,
            };

            mockRepository.getWorkflows.mockResolvedValue(mockPaginatedResult);

            const result = await service.getAgentWorkflows(
                mockAccount,
                agentType,
                agentWorkflowType,
                dto,
            );

            expect(result).toEqual(mockPaginatedResult);
            expect(result.data).toEqual(mockWorkflows);
            expect(result.page).toBe(1);
            expect(result.limit).toBe(20);
            expect(result.total).toBe(1);
            expect(mockRepository.getWorkflows).toHaveBeenCalledWith(
                agentType,
                agentWorkflowType,
                dto,
            );
        });
    });

    describe('getAgentWorkflow', () => {
        it('should get a specific agent workflow and return mocked response', async () => {
            const agentType = AgentType.VENDOR_RISK_MANAGEMENT;
            const workflowId = 1;

            const mockWorkflow = { id: workflowId, agentType, status: 'PENDING' };

            mockRepository.getWorkflowById.mockResolvedValue(mockWorkflow);

            const result = await service.getAgentWorkflow(mockAccount, workflowId);

            expect(result).toEqual(mockWorkflow);
            expect(mockRepository.getWorkflowById).toHaveBeenCalledWith(workflowId);
        });
    });

    describe('updateAgentWorkflow', () => {
        it('should update agent workflow successfully when valid data provided', async () => {
            // Arrange
            const account = getAccountMock();
            account.id = '********-1234-1234-1234-********9012';
            const agentType = AgentType.VENDOR_RISK_MANAGEMENT;
            const workflowId = 1;
            const dto: UpdateAgentWorkflowRequestDto = {
                status: AgentWorkflowStatus.CANCELLED,
            };
            const currentWorkflow = {
                id: workflowId,
                agentType,
                status: AgentWorkflowStatus.PENDING,
            };
            const updatedWorkflow = {
                id: workflowId,
                agentType,
                status: AgentWorkflowStatus.CANCELLED,
                updatedAt: new Date(),
            };

            // Mock first call to get current workflow for validation
            mockRepository.getWorkflowById.mockResolvedValueOnce(currentWorkflow);
            // Mock update call
            mockRepository.updateWorkflow.mockResolvedValue(updatedWorkflow);
            // Mock second call to get updated workflow with steps loaded
            mockRepository.getWorkflowById.mockResolvedValueOnce(updatedWorkflow);

            // Act
            const result = await service.updateAgentWorkflow(account, agentType, workflowId, dto);

            // Assert
            expect(result).toEqual(updatedWorkflow);
            expect(mockRepository.getWorkflowById).toHaveBeenCalledWith(workflowId);
            expect(mockRepository.getWorkflowById).toHaveBeenCalledTimes(2);
            expect(mockRepository.updateWorkflow).toHaveBeenCalledWith(workflowId, dto);
            expect(mockRepository.updateWorkflow).toHaveBeenCalledTimes(1);
        });

        it('should propagate repository errors when updateWorkflow fails', async () => {
            // Arrange
            const account = getAccountMock();
            account.id = '********-1234-1234-1234-********9012';
            const agentType = AgentType.VENDOR_RISK_MANAGEMENT;
            const workflowId = 1;
            const dto: UpdateAgentWorkflowRequestDto = {
                status: AgentWorkflowStatus.CANCELLED,
            };
            const currentWorkflow = {
                id: workflowId,
                agentType,
                status: AgentWorkflowStatus.PENDING, // allowed to transition to CANCELLED
            };
            const repositoryError = new Error('Repository error');

            mockRepository.getWorkflowById.mockResolvedValue(currentWorkflow);
            mockRepository.updateWorkflow.mockRejectedValue(repositoryError);

            // Act & Assert
            await expect(
                service.updateAgentWorkflow(account, agentType, workflowId, dto),
            ).rejects.toThrow('Repository error');

            expect(mockRepository.getWorkflowById).toHaveBeenCalledWith(workflowId);
            expect(mockRepository.updateWorkflow).toHaveBeenCalledWith(workflowId, dto);
        });

        it('should call repository with correct parameters', async () => {
            // Arrange
            const account = getAccountMock();
            account.id = '********-1234-1234-1234-********9012';
            const agentType = AgentType.VENDOR_RISK_MANAGEMENT;
            const workflowId = 123;
            const dto: UpdateAgentWorkflowRequestDto = {
                status: AgentWorkflowStatus.CANCELLED,
            };
            const currentWorkflow = {
                id: workflowId,
                agentType,
                status: AgentWorkflowStatus.PENDING,
            };
            const mockWorkflow = { id: workflowId, status: AgentWorkflowStatus.CANCELLED };

            mockRepository.getWorkflowById.mockResolvedValue(currentWorkflow);
            mockRepository.updateWorkflow.mockResolvedValue(mockWorkflow);

            // Act
            await service.updateAgentWorkflow(account, agentType, workflowId, dto);

            // Assert
            expect(mockRepository.getWorkflowById).toHaveBeenCalledWith(123);
            expect(mockRepository.updateWorkflow).toHaveBeenCalledWith(123, dto);
        });

        it('should throw ConflictException when trying to cancel a completed workflow', async () => {
            // Arrange
            const account = getAccountMock();
            account.id = '********-1234-1234-1234-********9012';
            const agentType = AgentType.VENDOR_RISK_MANAGEMENT;
            const workflowId = 1;
            const dto: UpdateAgentWorkflowRequestDto = {
                status: AgentWorkflowStatus.CANCELLED,
            };
            const currentWorkflow = {
                id: workflowId,
                agentType,
                status: AgentWorkflowStatus.COMPLETED,
            };

            // Reset mocks to ensure clean state
            jest.clearAllMocks();
            mockRepository.getWorkflowById.mockResolvedValue(currentWorkflow);

            // Act & Assert
            await expect(
                service.updateAgentWorkflow(account, agentType, workflowId, dto),
            ).rejects.toThrow('Workflow status cannot be updated from COMPLETED to CANCELLED');

            expect(mockRepository.getWorkflowById).toHaveBeenCalledWith(workflowId);
            expect(mockRepository.updateWorkflow).not.toHaveBeenCalled();
        });
    });

    describe('createStepMessages', () => {
        let mockStepMessageRepository: any;
        let getTenantRepositorySpy: jest.SpyInstance;

        beforeEach(() => {
            mockStepMessageRepository = {
                create: jest.fn(),
                save: jest.fn(),
            };

            // Mock getTenantRepository to return our mock repository for AgentWorkflowStepMessage
            getTenantRepositorySpy = jest
                .spyOn(service as any, 'getTenantRepository')
                .mockReturnValue(mockStepMessageRepository);
        });

        afterEach(() => {
            getTenantRepositorySpy.mockRestore();
        });

        it('should create step messages successfully when workflow contains the step', async () => {
            // Arrange
            const stepName = AgentWorkflowVendorAssessmentStepName.SEND_DOCUMENTS_TO_AI;
            const mockStep = {
                id: 1,
                stepName,
                workflowId: 1,
            } as AgentWorkflowStep;

            const mockWorkflow = {
                id: 1,
                steps: [mockStep],
            } as AgentWorkflow;

            const messageDataArray: AgentWorkflowStepMessageDataType[] = [
                {
                    caller: 'AGENT' as any,
                    title: [{ text: 'Test Title' }],
                    body: [{ text: 'Test Body' }],
                    actions: [],
                },
                {
                    caller: 'USER' as any,
                    title: [{ text: 'Another Title' }],
                    body: [{ text: 'Another Body' }],
                    actions: [],
                },
            ];

            const mockCreatedMessages = [
                { id: 1, workflowStepId: 1, data: messageDataArray[0], isVisible: true },
                { id: 2, workflowStepId: 1, data: messageDataArray[1], isVisible: true },
            ] as AgentWorkflowStepMessage[];

            mockStepMessageRepository.create.mockImplementation((data: any) => data);
            mockStepMessageRepository.save.mockResolvedValue(mockCreatedMessages);

            // Act
            const result = await service.createStepMessages(
                mockAccount,
                mockWorkflow,
                stepName,
                messageDataArray,
            );

            // Assert
            expect(result).toEqual(mockCreatedMessages);
            expect(mockStepMessageRepository.create).toHaveBeenCalledTimes(2);
            expect(mockStepMessageRepository.create).toHaveBeenNthCalledWith(1, {
                workflowStepId: 1,
                data: messageDataArray[0],
                isVisible: true,
            });
            expect(mockStepMessageRepository.create).toHaveBeenNthCalledWith(2, {
                workflowStepId: 1,
                data: messageDataArray[1],
                isVisible: true,
            });
            expect(mockStepMessageRepository.save).toHaveBeenCalledTimes(1);
        });

        it('should throw ValidationException when workflow does not contain the specified step', async () => {
            // Arrange
            const stepName = AgentWorkflowVendorAssessmentStepName.SEND_DOCUMENTS_TO_AI;
            const differentStepName =
                AgentWorkflowVendorAssessmentStepName.EVALUATE_SECURITY_POSTURE;

            const mockStep = {
                id: 1,
                stepName: differentStepName,
                workflowId: 1,
            } as AgentWorkflowStep;

            const mockWorkflow = {
                id: 1,
                steps: [mockStep],
            } as AgentWorkflow;

            const messageDataArray: AgentWorkflowStepMessageDataType[] = [
                {
                    caller: 'AGENT' as any,
                    title: [{ text: 'Test Title' }],
                    body: [{ text: 'Test Body' }],
                    actions: [],
                },
            ];

            // Act & Assert
            await expect(
                service.createStepMessages(mockAccount, mockWorkflow, stepName, messageDataArray),
            ).rejects.toThrow(ValidationException);

            expect(mockStepMessageRepository.create).not.toHaveBeenCalled();
            expect(mockStepMessageRepository.save).not.toHaveBeenCalled();
        });

        it('should handle empty message data array', async () => {
            // Arrange
            const stepName = AgentWorkflowVendorAssessmentStepName.SEND_DOCUMENTS_TO_AI;
            const mockStep = {
                id: 1,
                stepName,
                workflowId: 1,
            } as AgentWorkflowStep;

            const mockWorkflow = {
                id: 1,
                steps: [mockStep],
            } as AgentWorkflow;

            const messageDataArray: AgentWorkflowStepMessageDataType[] = [];
            const mockCreatedMessages: AgentWorkflowStepMessage[] = [];

            mockStepMessageRepository.save.mockResolvedValue(mockCreatedMessages);

            // Act
            const result = await service.createStepMessages(
                mockAccount,
                mockWorkflow,
                stepName,
                messageDataArray,
            );

            // Assert
            expect(result).toEqual(mockCreatedMessages);
            expect(mockStepMessageRepository.create).not.toHaveBeenCalled();
            expect(mockStepMessageRepository.save).toHaveBeenCalledWith([]);
        });

        it('should handle workflow with no steps', async () => {
            // Arrange
            const stepName = AgentWorkflowVendorAssessmentStepName.SEND_DOCUMENTS_TO_AI;
            const mockWorkflow = {
                id: 1,
                steps: [],
            } as unknown as AgentWorkflow;

            const messageDataArray: AgentWorkflowStepMessageDataType[] = [
                {
                    caller: 'AGENT' as any,
                    title: [{ text: 'Test Title' }],
                    body: [{ text: 'Test Body' }],
                    actions: [],
                },
            ];

            // Act & Assert
            await expect(
                service.createStepMessages(mockAccount, mockWorkflow, stepName, messageDataArray),
            ).rejects.toThrow(ValidationException);

            expect(mockStepMessageRepository.create).not.toHaveBeenCalled();
            expect(mockStepMessageRepository.save).not.toHaveBeenCalled();
        });
    });
});
