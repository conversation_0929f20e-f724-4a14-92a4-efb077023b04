import { TestingModule } from '@nestjs/testing';
import { CreateAgentWorkflowDto } from 'app/agent-workflow/dtos/create-agent-workflow.dto';
import {
    AgentType,
    AgentWorkflowVendorAssessmentStepName,
    AgentWorkflowVendorType,
} from 'app/agent-workflow/enums';
import { AgentWorkflowOrchestratorService } from 'app/agent-workflow/services/agent-workflow-orchestrator.service';
import { AgentWorkflowVendorAssessmentService } from 'app/agent-workflow/services/agent-workflow-vendor-assessment.service';
import { AgentWorkflowService } from 'app/agent-workflow/services/agent-workflow.service';
import { VendorSecurityReview } from 'app/users/vendors/entities/vendor-security-review.entity';
import { VendorsSecurityRiskCoreService } from 'app/users/vendors/services/vendors-security-risk-core.service';
import { Account } from 'auth/entities/account.entity';
import { getAccountMock } from 'auth/mocks/account.mock';
import { VendorStatus } from 'commons/enums/vendor/vendor-status.enum';
import { ValidationException } from 'commons/exceptions/validation.exception';
import { createAppTestingModule } from 'commons/helpers/app-testing-module.helper';
import { MockProxy, mock } from 'jest-mock-extended';
import { TenancyContextMockType } from 'tenancy/contexts/__mocks__/tenancy.context';
import { TenancyContext } from 'tenancy/contexts/tenancy.context';
import { Repository } from 'typeorm';

jest.mock('pollo-logger/pollo.logger', () => ({
    PolloLogger: {
        logger: jest.fn(() => ({
            error: jest.fn(),
            warn: jest.fn(),
            log: jest.fn(),
            debug: jest.fn(),
        })),
    },
}));

describe('AgentWorkflowVendorAssessmentService', () => {
    let service: AgentWorkflowVendorAssessmentService;
    let tenancyContextMock: TenancyContextMockType;
    let vendorSecurityReviewRepository: MockProxy<Repository<VendorSecurityReview>>;
    let agentWorkflowRepository: MockProxy<Repository<any>>;
    let vendorsSecurityRiskCoreService: MockProxy<VendorsSecurityRiskCoreService>;
    let agentWorkflowService: MockProxy<AgentWorkflowService>;
    let agentWorkflowOrchestratorService: MockProxy<AgentWorkflowOrchestratorService>;

    let mockAccount: Account;

    beforeEach(async () => {
        vendorsSecurityRiskCoreService = mock<VendorsSecurityRiskCoreService>();
        agentWorkflowService = mock<AgentWorkflowService>();
        agentWorkflowOrchestratorService = mock<AgentWorkflowOrchestratorService>();

        const module: TestingModule = await createAppTestingModule({
            providers: [
                AgentWorkflowVendorAssessmentService,
                {
                    provide: VendorsSecurityRiskCoreService,
                    useValue: vendorsSecurityRiskCoreService,
                },
                {
                    provide: AgentWorkflowService,
                    useValue: agentWorkflowService,
                },
                {
                    provide: AgentWorkflowOrchestratorService,
                    useValue: agentWorkflowOrchestratorService,
                },
            ],
        }).compile();

        service = module.get<AgentWorkflowVendorAssessmentService>(
            AgentWorkflowVendorAssessmentService,
        );
        tenancyContextMock = module.get<TenancyContextMockType>(TenancyContext);
        vendorSecurityReviewRepository = tenancyContextMock.getRepository(VendorSecurityReview);
        agentWorkflowRepository = tenancyContextMock.getRepository('AgentWorkflow');

        mockAccount = getAccountMock();
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    it('should be defined', () => {
        expect(service).toBeDefined();
    });

    describe('getVendorAssessmentWorkflows', () => {
        const mockDto = {
            page: 1,
            limit: 10,
            vendorIds: [123],
        };

        it('should return paginated vendor assessment workflows', async () => {
            const mockWorkflowTypeIds = ['1', '2'];
            const mockVendorSecurityReviews = [
                {
                    id: 1,
                    vendor: { id: 123, name: 'Test Vendor' },
                },
            ];
            const mockWorkflows = [
                {
                    id: 1,
                    workflowTypeId: '1',
                    steps: [
                        {
                            stepName: AgentWorkflowVendorAssessmentStepName.SEND_DOCUMENTS_TO_AI,
                            createdAt: new Date(),
                        },
                    ],
                },
            ];
            const mockExtendedWorkflow = {
                id: 1,
                workflowTypeId: '1',
                steps: [],
            };
            const mockPaginatedResult = {
                data: mockWorkflows,
                page: 1,
                limit: 10,
                total: 1,
            };

            agentWorkflowService.getAgentWorkflowTypeIds.mockResolvedValue(mockWorkflowTypeIds);
            vendorSecurityReviewRepository.find.mockResolvedValue(mockVendorSecurityReviews as any);
            agentWorkflowService.getAgentWorkflows.mockResolvedValue(mockPaginatedResult as any);
            agentWorkflowOrchestratorService.buildAgentWorkflowExtended.mockReturnValue(
                mockExtendedWorkflow as any,
            );

            const result = await service.getVendorAssessmentWorkflows(
                mockAccount,
                AgentType.VENDOR_RISK_MANAGEMENT,
                AgentWorkflowVendorType.VENDOR_ASSESSMENT,
                mockDto,
            );

            expect(result).toBeDefined();
            expect(result.data).toHaveLength(1);
            expect(result.data[0]).toHaveProperty('vendorName', 'Test Vendor');
            expect(result.data[0]).toHaveProperty('assessmentStarted', true);
        });

        it('should return empty result when no vendor security reviews found', async () => {
            const mockWorkflowTypeIds = ['1', '2'];

            agentWorkflowService.getAgentWorkflowTypeIds.mockResolvedValue(mockWorkflowTypeIds);
            vendorSecurityReviewRepository.find.mockResolvedValue([]);

            const result = await service.getVendorAssessmentWorkflows(
                mockAccount,
                AgentType.VENDOR_RISK_MANAGEMENT,
                AgentWorkflowVendorType.VENDOR_ASSESSMENT,
                mockDto,
            );

            expect(result).toEqual({
                data: [],
                page: 1,
                limit: 10,
                total: 0,
            });
        });
    });

    describe('startVendorAssessment', () => {
        const mockUser = { id: 1 } as any;
        const outerMockDto = {
            vendorId: 123,
            workflowTypeId: undefined,
        } as CreateAgentWorkflowDto;

        it('should create new security review and start workflow when workflowTypeId is not provided', async () => {
            const mockSecurityReview = { id: 1, vendor: { id: 123 } };
            const mockWorkflow = { id: 1, workflowTypeId: '1' };
            const mockStep = { id: 1, stepName: 'PROCESS_START' };
            const mockCurrentWorkflow = { id: 1, steps: [] };
            const mockResult = { success: true };

            // Mock the prospective vendor exists query to return false (no prospective vendor found)
            vendorSecurityReviewRepository.exists.mockResolvedValue(false);
            vendorsSecurityRiskCoreService.createVendorSecurityReview.mockResolvedValue(
                mockSecurityReview as any,
            );
            agentWorkflowService.createAgentWorkflow.mockResolvedValue(mockWorkflow as any);
            agentWorkflowService.createAgentWorkflowStep.mockResolvedValue(mockStep as any);
            agentWorkflowService.getAgentWorkflow.mockResolvedValue(mockCurrentWorkflow as any);
            agentWorkflowOrchestratorService.startWorkflow.mockResolvedValue(mockResult as any);
            agentWorkflowOrchestratorService.emitStepCreatedEvent.mockResolvedValue();

            const result = await service.startVendorAssessment(mockAccount, mockUser, outerMockDto);

            expect(vendorSecurityReviewRepository.exists).toHaveBeenCalledWith({
                where: { vendor: { id: 123, status: VendorStatus.PROSPECTIVE } },
            });
            expect(vendorsSecurityRiskCoreService.createVendorSecurityReview).toHaveBeenCalled();
            expect(agentWorkflowService.createAgentWorkflow).toHaveBeenCalledWith(
                mockAccount,
                mockUser,
                AgentType.VENDOR_RISK_MANAGEMENT,
                AgentWorkflowVendorType.VENDOR_ASSESSMENT,
                expect.objectContaining({
                    vendorId: 123,
                    workflowTypeId: '1',
                }),
            );
            expect(agentWorkflowOrchestratorService.emitStepCreatedEvent).toHaveBeenCalledWith(
                mockAccount,
                mockCurrentWorkflow,
                mockStep,
            );
            expect(result).toEqual(mockResult);
        });

        it('should validate existing security review and start workflow when workflowTypeId is provided and no active workflow exists', async () => {
            const mockDtoWithWorkflowTypeId = {
                vendorId: 123,
                workflowTypeId: '1',
            } as CreateAgentWorkflowDto;
            const mockSecurityReview = { id: 1, vendor: { id: 123 } };
            const mockWorkflow = { id: 1, workflowTypeId: '1' };
            const mockStep = { id: 1, stepName: 'PROCESS_START' };
            const mockCurrentWorkflow = { id: 1, steps: [] };
            const mockResult = { success: true };

            vendorsSecurityRiskCoreService.getVendorsSecurityReview.mockResolvedValue(
                mockSecurityReview as any,
            );
            // Mock no existing agent workflow found
            agentWorkflowRepository.exists.mockResolvedValue(false);
            agentWorkflowService.createAgentWorkflow.mockResolvedValue(mockWorkflow as any);
            agentWorkflowService.createAgentWorkflowStep.mockResolvedValue(mockStep as any);
            agentWorkflowService.getAgentWorkflow.mockResolvedValue(mockCurrentWorkflow as any);
            agentWorkflowOrchestratorService.startWorkflow.mockResolvedValue(mockResult as any);
            agentWorkflowOrchestratorService.emitStepCreatedEvent.mockResolvedValue();

            const result = await service.startVendorAssessment(
                mockAccount,
                mockUser,
                mockDtoWithWorkflowTypeId,
            );

            expect(vendorsSecurityRiskCoreService.getVendorsSecurityReview).toHaveBeenCalledWith(1);
            expect(agentWorkflowRepository.exists).toHaveBeenCalledWith({
                where: {
                    agentType: AgentType.VENDOR_RISK_MANAGEMENT,
                    agentWorkflowType: AgentWorkflowVendorType.VENDOR_ASSESSMENT,
                    workflowTypeId: '1',
                    status: expect.any(Object), // Not(In([COMPLETED, CANCELLED]))
                },
            });
            expect(
                vendorsSecurityRiskCoreService.createVendorSecurityReview,
            ).not.toHaveBeenCalled();
            expect(agentWorkflowOrchestratorService.emitStepCreatedEvent).toHaveBeenCalledWith(
                mockAccount,
                mockCurrentWorkflow,
                mockStep,
            );
            expect(result).toEqual(mockResult);
        });

        it('should throw ValidationException when prospective vendor already has existing security review', async () => {
            const innerMockDto = {
                vendorId: 123,
                workflowTypeId: undefined,
            } as CreateAgentWorkflowDto;

            // Mock the exists query to return true (prospective vendor has existing security review)
            vendorSecurityReviewRepository.exists.mockResolvedValue(true);

            await expect(
                service.startVendorAssessment(mockAccount, mockUser, innerMockDto),
            ).rejects.toThrow(ValidationException);

            expect(vendorSecurityReviewRepository.exists).toHaveBeenCalledWith({
                where: { vendor: { id: 123, status: VendorStatus.PROSPECTIVE } },
            });
            expect(
                vendorsSecurityRiskCoreService.createVendorSecurityReview,
            ).not.toHaveBeenCalled();
            expect(agentWorkflowService.createAgentWorkflow).not.toHaveBeenCalled();
        });

        it('should throw ValidationException when workflowTypeId is provided and active workflow already exists', async () => {
            const mockDtoWithWorkflowTypeId = {
                vendorId: 123,
                workflowTypeId: '1',
            } as CreateAgentWorkflowDto;
            const mockSecurityReview = { id: 1, vendor: { id: 123 } };
            vendorsSecurityRiskCoreService.getVendorsSecurityReview.mockResolvedValue(
                mockSecurityReview as any,
            );
            // Mock existing agent workflow found
            agentWorkflowRepository.exists.mockResolvedValue(true);

            await expect(
                service.startVendorAssessment(mockAccount, mockUser, mockDtoWithWorkflowTypeId),
            ).rejects.toThrow(ValidationException);

            expect(vendorsSecurityRiskCoreService.getVendorsSecurityReview).toHaveBeenCalledWith(1);
            expect(agentWorkflowRepository.exists).toHaveBeenCalledWith({
                where: {
                    agentType: AgentType.VENDOR_RISK_MANAGEMENT,
                    agentWorkflowType: AgentWorkflowVendorType.VENDOR_ASSESSMENT,
                    workflowTypeId: '1',
                    status: expect.any(Object), // Not(In([COMPLETED, CANCELLED]))
                },
            });
            expect(agentWorkflowService.createAgentWorkflow).not.toHaveBeenCalled();
        });

        it('should create new security review for prospective vendor when none exists', async () => {
            const innerMockDto = {
                vendorId: 123,
                workflowTypeId: undefined,
            } as CreateAgentWorkflowDto;

            const mockNewSecurityReview = { id: 2, vendor: { id: 123 } };
            const mockWorkflow = { id: 1, workflowTypeId: '2' };
            const mockCurrentWorkflow = { id: 1, steps: [] };
            const mockResult = { success: true };

            // Mock the exists query to return false (no existing review for prospective vendor)
            vendorSecurityReviewRepository.exists.mockResolvedValue(false);
            // Mock no existing agent workflow found
            agentWorkflowRepository.findOne.mockResolvedValue(null);
            vendorsSecurityRiskCoreService.createVendorSecurityReview.mockResolvedValue(
                mockNewSecurityReview as any,
            );
            agentWorkflowService.createAgentWorkflow.mockResolvedValue(mockWorkflow as any);
            agentWorkflowService.createAgentWorkflowStep.mockResolvedValue({} as any);
            agentWorkflowService.getAgentWorkflow.mockResolvedValue(mockCurrentWorkflow as any);
            agentWorkflowOrchestratorService.startWorkflow.mockResolvedValue(mockResult as any);

            const result = await service.startVendorAssessment(mockAccount, mockUser, innerMockDto);

            expect(vendorSecurityReviewRepository.exists).toHaveBeenCalledWith({
                where: { vendor: { id: 123, status: VendorStatus.PROSPECTIVE } },
            });
            expect(vendorsSecurityRiskCoreService.createVendorSecurityReview).toHaveBeenCalled();
            expect(result).toEqual(mockResult);
        });

        it('should create new security review for non-prospective vendor', async () => {
            const innerMockDto = {
                vendorId: 123,
                workflowTypeId: undefined,
            } as CreateAgentWorkflowDto;

            const mockSecurityReview = { id: 1, vendor: { id: 123 } };
            const mockWorkflow = { id: 1, workflowTypeId: '1' };
            const mockCurrentWorkflow = { id: 1, steps: [] };
            const mockResult = { success: true };

            // Mock the prospective vendor exists query to return false (no prospective vendor found)
            vendorSecurityReviewRepository.exists.mockResolvedValue(false);
            // Mock no existing agent workflow found
            agentWorkflowRepository.findOne.mockResolvedValue(null);
            vendorsSecurityRiskCoreService.createVendorSecurityReview.mockResolvedValue(
                mockSecurityReview as any,
            );
            agentWorkflowService.createAgentWorkflow.mockResolvedValue(mockWorkflow as any);
            agentWorkflowService.createAgentWorkflowStep.mockResolvedValue({} as any);
            agentWorkflowService.getAgentWorkflow.mockResolvedValue(mockCurrentWorkflow as any);
            agentWorkflowOrchestratorService.startWorkflow.mockResolvedValue(mockResult as any);

            const result = await service.startVendorAssessment(mockAccount, mockUser, innerMockDto);

            expect(vendorSecurityReviewRepository.exists).toHaveBeenCalledWith({
                where: { vendor: { id: 123, status: VendorStatus.PROSPECTIVE } },
            });
            expect(vendorsSecurityRiskCoreService.createVendorSecurityReview).toHaveBeenCalled();
            expect(result).toEqual(mockResult);
        });
    });
});
