import { ErrorCode } from '@drata/enums';
import { Injectable, Scope } from '@nestjs/common';
import { CreateAgentWorkflowDto } from 'app/agent-workflow/dtos/create-agent-workflow.dto';
import { GetVendorCriteriaCreationAgentWorkflowsDto } from 'app/agent-workflow/dtos/get-vendor-criteria-creation-agent-workflows.dto';
import {
    AgentType,
    AgentWorkflowGeneralStepName,
    AgentWorkflowType,
    AgentWorkflowVendorType
} from 'app/agent-workflow/enums';
import { AgentWorkflowOrchestratorService } from 'app/agent-workflow/services/agent-workflow-orchestrator.service';
import { AgentWorkflowService } from 'app/agent-workflow/services/agent-workflow.service';
import { VendorCriteriaCreationAgentWorkflowType } from 'app/agent-workflow/types/vendor-criteria-creation-agent-workflow.type';
import { WorkflowExecutionResult } from 'app/agent-workflow/types/workflow-execution-result.type';
import { User } from 'app/users/entities/user.entity';
import { Account } from 'auth/entities/account.entity';
import { ErrorCodeException } from 'commons/exceptions/error-code.exception';
import { InternalServerErrorException } from 'commons/exceptions/internal-server-error.exception';
import { AppService } from 'commons/services/app.service';
import { PaginationType } from 'commons/types/pagination.type';

/**
 * Agent Workflow Vendor Criteria Creation Service
 *
 * Acts as the main coordinator for all vendor criteria creation operations and handles the complete execution flow.
 *
 * Manages the full execution cycle: message generation → step execution → state transition → continuation evaluation.
 */
@Injectable({ scope: Scope.REQUEST })
export class AgentWorkflowVendorCriteriaCreationService extends AppService {
    constructor(
        private readonly agentWorkflowService: AgentWorkflowService,
        private readonly agentWorkflowOrchestratorService: AgentWorkflowOrchestratorService,
    ) {
        super();
    }

    /**
     * Start a vendor criteria creation workflow.
     *
     * @param account - The account context for logging and permissions
     * @param user - The user creating the workflow
     * @param dto - Workflow creation data
     * @returns Promise<WorkflowExecutionResult> - Comprehensive execution result
     */
    async startCriteriaCreation(
        account: Account,
        user: User,
        dto: CreateAgentWorkflowDto,
    ): Promise<WorkflowExecutionResult> {
        this.log('Starting vendor criteria creation workflow', account, {
            workflowTypeId: dto.workflowTypeId,
        });

        try {
            const workflow = await this.agentWorkflowService.createAgentWorkflow(
                account,
                user,
                AgentType.VENDOR_RISK_MANAGEMENT,
                AgentWorkflowVendorType.VENDORS_CRITERIA,
                dto,
            );

            // Add the PROCESS_START step
            const processStartStep = await this.agentWorkflowService.createAgentWorkflowStep(
                account,
                AgentType.VENDOR_RISK_MANAGEMENT,
                AgentWorkflowVendorType.VENDORS_CRITERIA,
                workflow.id,
                100,
                {
                    stepName: AgentWorkflowGeneralStepName.PROCESS_START,
                },
            );

            // Refresh the workflow entity to include the newly created step
            const currentWorkflow = await this.agentWorkflowService.getAgentWorkflow(
                account,
                workflow.id,
            );

            // Emit step created event for the PROCESS_START step
            await this.agentWorkflowOrchestratorService.emitStepCreatedEvent(
                account,
                currentWorkflow,
                processStartStep,
            );

            // Start workflow execution from PROCESS_START
            const result = await this.agentWorkflowOrchestratorService.startWorkflow(
                account,
                currentWorkflow,
            );

            this.log(`Vendor criteria creation workflow ${workflow.id} started successfully`, account, {
                workflowId: workflow.id,
                success: result.success,
            });

            return result;
        } catch (error) {
            this.error(error, account, {
                workflowTypeId: dto.workflowTypeId,
                error: error.message,
            });

            // Preserve the original error type if it's already an ErrorCodeException
            // This maintains appropriate HTTP status codes for different error types
            if (error instanceof ErrorCodeException) {
                throw error;
            }

            // Wrap other errors with context about the criteria creation failure
            throw new InternalServerErrorException(
                `Failed to start vendor criteria creation workflow: ${error.message}`,
                ErrorCode.INTERNAL_SERVER_ERROR,
            );
        }
    }

    /**
     * Get vendor criteria creation workflows with pagination.
     *
     * @param account - The account context
     * @param agentType - The agent type
     * @param agentWorkflowType - The workflow type
     * @param dto - Query parameters for filtering and pagination
     * @returns Promise<PaginationType<VendorCriteriaCreationAgentWorkflowType>> - Paginated workflows
     */
    async getVendorCriteriaCreationWorkflows(
        account: Account,
        agentType: AgentType,
        agentWorkflowType: AgentWorkflowType,
        dto: GetVendorCriteriaCreationAgentWorkflowsDto,
    ): Promise<PaginationType<VendorCriteriaCreationAgentWorkflowType>> {
        this.log('Getting vendor criteria creation workflows', account);

        // Basic implementation without complex filtering
        const workflowsPage = await this.agentWorkflowService.getAgentWorkflows(
            account,
            agentType,
            agentWorkflowType,
            dto,
        );

        const transformedData: VendorCriteriaCreationAgentWorkflowType[] = workflowsPage.data.map(workflow => {
            const extendedWorkflow = this.agentWorkflowOrchestratorService.buildAgentWorkflowExtended(workflow);
            const vendorCriteriaWorkflow: VendorCriteriaCreationAgentWorkflowType = extendedWorkflow;
            return vendorCriteriaWorkflow;
        });

        return {
            data: transformedData,
            page: workflowsPage.page,
            limit: workflowsPage.limit,
            total: workflowsPage.total,
        };
    }
}
