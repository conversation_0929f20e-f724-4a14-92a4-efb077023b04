import { ErrorCode } from '@drata/enums';
import { TestingModule } from '@nestjs/testing';
import { CreateAgentWorkflowDto } from 'app/agent-workflow/dtos/create-agent-workflow.dto';
import { AgentType, AgentWorkflowVendorType } from 'app/agent-workflow/enums';
import { AgentWorkflowOrchestratorService } from 'app/agent-workflow/services/agent-workflow-orchestrator.service';
import { AgentWorkflowVendorCriteriaCreationService } from 'app/agent-workflow/services/agent-workflow-vendor-criteria-creation.service';
import { AgentWorkflowService } from 'app/agent-workflow/services/agent-workflow.service';
import { getUserMock } from 'app/users/entities/mocks/user.mock';
import { User } from 'app/users/entities/user.entity';
import { Account } from 'auth/entities/account.entity';
import { getAccountMock } from 'auth/mocks/account.mock';
import { BadRequestException } from 'commons/exceptions/bad-request.exception';
import { InternalServerErrorException } from 'commons/exceptions/internal-server-error.exception';
import { createAppTestingModule } from 'commons/helpers/app-testing-module.helper';
import { MockProxy, mock } from 'jest-mock-extended';

jest.mock('pollo-logger/pollo.logger', () => ({
    PolloLogger: {
        logger: jest.fn(() => ({
            error: jest.fn(),
            warn: jest.fn(),
            log: jest.fn(),
            debug: jest.fn(),
        })),
    },
}));

describe('AgentWorkflowVendorCriteriaCreationService', () => {
    let service: AgentWorkflowVendorCriteriaCreationService;
    let agentWorkflowService: MockProxy<AgentWorkflowService>;
    let agentWorkflowOrchestratorService: MockProxy<AgentWorkflowOrchestratorService>;
    let mockAccount: Account;
    let mockUser: User;

    beforeEach(async () => {
        agentWorkflowService = mock<AgentWorkflowService>();
        agentWorkflowOrchestratorService = mock<AgentWorkflowOrchestratorService>();

        const module: TestingModule = await createAppTestingModule({
            providers: [
                AgentWorkflowVendorCriteriaCreationService,
                {
                    provide: AgentWorkflowService,
                    useValue: agentWorkflowService,
                },
                {
                    provide: AgentWorkflowOrchestratorService,
                    useValue: agentWorkflowOrchestratorService,
                },
            ],
        }).compile();

        service = await module.resolve<AgentWorkflowVendorCriteriaCreationService>(
            AgentWorkflowVendorCriteriaCreationService,
        );
        mockAccount = getAccountMock();
        mockUser = getUserMock();
    });

    describe('startCriteriaCreation', () => {
        it('should successfully create workflow, step, and emit step created event', async () => {
            // Arrange
            const dto: CreateAgentWorkflowDto = {
                vendorId: 1,
                workspaceId: 1,
                workflowTypeId: '123',
            };
            const mockWorkflow = { id: 1 };
            const mockStep = { id: 1, stepName: 'PROCESS_START' };
            const mockCurrentWorkflow = { id: 1, steps: [mockStep] };
            const mockResult = { success: true, workflowId: 1 };

            agentWorkflowService.createAgentWorkflow.mockResolvedValue(mockWorkflow as any);
            agentWorkflowService.createAgentWorkflowStep.mockResolvedValue(mockStep as any);
            agentWorkflowService.getAgentWorkflow.mockResolvedValue(mockCurrentWorkflow as any);
            agentWorkflowOrchestratorService.startWorkflow.mockResolvedValue(mockResult as any);
            agentWorkflowOrchestratorService.emitStepCreatedEvent.mockResolvedValue();

            // Act
            const result = await service.startCriteriaCreation(mockAccount, mockUser, dto);

            // Assert
            expect(result).toBe(mockResult);
            expect(agentWorkflowService.createAgentWorkflow).toHaveBeenCalledWith(
                mockAccount,
                mockUser,
                AgentType.VENDOR_RISK_MANAGEMENT,
                AgentWorkflowVendorType.VENDORS_CRITERIA,
                dto,
            );
            expect(agentWorkflowService.createAgentWorkflowStep).toHaveBeenCalledWith(
                mockAccount,
                AgentType.VENDOR_RISK_MANAGEMENT,
                AgentWorkflowVendorType.VENDORS_CRITERIA,
                mockWorkflow.id,
                100,
                { stepName: 'PROCESS_START' },
            );
            expect(agentWorkflowOrchestratorService.emitStepCreatedEvent).toHaveBeenCalledWith(
                mockAccount,
                mockCurrentWorkflow,
                mockStep,
            );
            expect(agentWorkflowOrchestratorService.startWorkflow).toHaveBeenCalledWith(
                mockAccount,
                mockCurrentWorkflow,
            );
        });

        it('should wrap generic errors with InternalServerErrorException', async () => {
            // Arrange
            const dto: CreateAgentWorkflowDto = {
                vendorId: 1,
                workspaceId: 1,
                workflowTypeId: '123',
            };
            const originalError = new Error('Database connection failed');

            agentWorkflowService.createAgentWorkflow.mockRejectedValue(originalError);

            // Act & Assert
            await expect(service.startCriteriaCreation(mockAccount, mockUser, dto))
                .rejects
                .toThrow(new InternalServerErrorException(
                    'Failed to start vendor criteria creation workflow: Database connection failed',
                    ErrorCode.INTERNAL_SERVER_ERROR,
                ));

            expect(agentWorkflowService.createAgentWorkflow).toHaveBeenCalledWith(
                mockAccount,
                mockUser,
                AgentType.VENDOR_RISK_MANAGEMENT,
                AgentWorkflowVendorType.VENDORS_CRITERIA,
                dto,
            );
        });

        it('should preserve original error type when error is ErrorCodeException', async () => {
            // Arrange
            const dto: CreateAgentWorkflowDto = {
                vendorId: 1,
                workspaceId: 1,
                workflowTypeId: '456',
            };
            const originalError = new BadRequestException(
                'Validation failed: workflowTypeId is invalid',
                ErrorCode.INVALID_ARGUMENT,
            );

            agentWorkflowService.createAgentWorkflow.mockRejectedValue(originalError);

            // Act & Assert
            try {
                await service.startCriteriaCreation(mockAccount, mockUser, dto);
                fail('Expected method to throw');
            } catch (error) {
                expect(error).toBeInstanceOf(BadRequestException);
                expect(error.message).toBe('Validation failed: workflowTypeId is invalid');
                expect(error.getCode()).toBe(ErrorCode.INVALID_ARGUMENT);
            }
        });
    });
});
