import { AgentType } from 'app/agent-workflow/enums/agent-type.enum';
import { AgentWorkflowVendorType } from 'app/agent-workflow/enums/agent-workflow-vendor-type.enum';
import { AgentWorkflowAuditTrailFactory } from 'app/agent-workflow/factories/agent-workflow-audit-trail.factory';
import { VendorRiskManagementAuditTrailMappingFactory } from 'app/agent-workflow/factories/vendor-risk-management-audit-trail-mapping.factory';
import { mock, MockProxy } from 'jest-mock-extended';

describe('AgentWorkflowAuditTrailFactory', () => {
    let factory: AgentWorkflowAuditTrailFactory;
    let mockVendorRiskManagementMappingFactory: MockProxy<VendorRiskManagementAuditTrailMappingFactory>;

    beforeEach(() => {
        mockVendorRiskManagementMappingFactory =
            mock<VendorRiskManagementAuditTrailMappingFactory>();
        factory = new AgentWorkflowAuditTrailFactory(mockVendorRiskManagementMappingFactory);
    });

    describe('getAuditTrailMapping', () => {
        it('should return vendor assessment mapping for VRM vendor assessment', () => {
            // Arrange
            const mockMapping = new Map([
                ['PROCESS_START', { displayName: 'Started security review' }],
                ['SAFEBASE_ACCESS_GRANTED', { displayName: 'Access Granted' }],
                ['SAFEBASE_SEND_DOCUMENTS_TO_AI', { displayName: 'Documents collected' }],
                ['RETRIEVE_SECURITY_POSTURE', { displayName: 'Criteria assessment results' }],
                ['SECURITY_QUESTIONNAIRE_SENT', { displayName: 'Follow up questionnaire' }],
            ]);
            mockVendorRiskManagementMappingFactory.getMapping.mockReturnValue(mockMapping);

            // Act
            const mapping = factory.getAuditTrailMapping(
                AgentType.VENDOR_RISK_MANAGEMENT,
                AgentWorkflowVendorType.VENDOR_ASSESSMENT,
            );

            // Assert
            expect(mockVendorRiskManagementMappingFactory.getMapping).toHaveBeenCalledWith(
                AgentWorkflowVendorType.VENDOR_ASSESSMENT,
            );
            expect(mapping).toBe(mockMapping);
            expect(mapping.size).toBe(5);
        });

        it('should return vendor criteria mapping for VRM vendors criteria', () => {
            // Arrange
            const mockMapping = new Map([
                ['PROCESS_START', { displayName: 'Started criteria creation' }],
                ['SEND_CRITERIA_QUESTIONNAIRE_TO_AI', { displayName: 'Questionnaire sent to AI' }],
                ['ANALYZING_CRITERIA_QUESTIONNAIRE', { displayName: 'Analyzing questionnaire' }],
                ['GENERATING_CRITERIA', { displayName: 'Generating criteria' }],
                ['CRITERIA_GENERATION_FINALIZED', { displayName: 'Criteria generation completed' }],
                ['SAVE_GENERATED_CRITERIA', { displayName: 'Criteria saved' }],
                ['RESTORE_DRATA_CRITERIA', { displayName: 'Restored default criteria' }],
                ['FINALIZE', { displayName: 'Workflow completed' }],
            ]);
            mockVendorRiskManagementMappingFactory.getMapping.mockReturnValue(mockMapping);

            // Act
            const mapping = factory.getAuditTrailMapping(
                AgentType.VENDOR_RISK_MANAGEMENT,
                AgentWorkflowVendorType.VENDORS_CRITERIA,
            );

            // Assert
            expect(mockVendorRiskManagementMappingFactory.getMapping).toHaveBeenCalledWith(
                AgentWorkflowVendorType.VENDORS_CRITERIA,
            );
            expect(mapping).toBe(mockMapping);
            expect(mapping.size).toBe(8);
        });

        it('should throw error for unsupported agent type', () => {
            // Act & Assert
            expect(() => {
                factory.getAuditTrailMapping(
                    'UNSUPPORTED_AGENT_TYPE' as AgentType,
                    AgentWorkflowVendorType.VENDOR_ASSESSMENT,
                );
            }).toThrow('Unsupported agent type for audit trail: UNSUPPORTED_AGENT_TYPE');
        });

        it('should throw error for unsupported workflow type', () => {
            // Arrange
            mockVendorRiskManagementMappingFactory.getMapping.mockImplementation(() => {
                throw new Error(
                    'Unsupported workflow type for Vendor Risk Management audit trail: UNSUPPORTED_WORKFLOW_TYPE',
                );
            });

            // Act & Assert
            expect(() => {
                factory.getAuditTrailMapping(
                    AgentType.VENDOR_RISK_MANAGEMENT,
                    'UNSUPPORTED_WORKFLOW_TYPE' as any,
                );
            }).toThrow(
                'Unsupported workflow type for Vendor Risk Management audit trail: UNSUPPORTED_WORKFLOW_TYPE',
            );
        });
    });

    describe('display names', () => {
        it('should have correct display names for all vendor assessment steps', () => {
            // Arrange
            const mockMapping = new Map([
                ['PROCESS_START', { displayName: 'Started security review' }],
                ['SAFEBASE_ACCESS_GRANTED', { displayName: 'Access Granted' }],
                ['SAFEBASE_SEND_DOCUMENTS_TO_AI', { displayName: 'Documents collected' }],
                ['RETRIEVE_SECURITY_POSTURE', { displayName: 'Criteria assessment results' }],
                ['SECURITY_QUESTIONNAIRE_SENT', { displayName: 'Follow up questionnaire' }],
            ]);
            mockVendorRiskManagementMappingFactory.getMapping.mockReturnValue(mockMapping);

            // Act
            const mapping = factory.getAuditTrailMapping(
                AgentType.VENDOR_RISK_MANAGEMENT,
                AgentWorkflowVendorType.VENDOR_ASSESSMENT,
            );

            // Assert
            expect(mapping.get('PROCESS_START')?.displayName).toBe('Started security review');
            expect(mapping.get('SAFEBASE_ACCESS_GRANTED')?.displayName).toBe('Access Granted');
            expect(mapping.get('SAFEBASE_SEND_DOCUMENTS_TO_AI')?.displayName).toBe(
                'Documents collected',
            );
            expect(mapping.get('RETRIEVE_SECURITY_POSTURE')?.displayName).toBe(
                'Criteria assessment results',
            );
            expect(mapping.get('SECURITY_QUESTIONNAIRE_SENT')?.displayName).toBe(
                'Follow up questionnaire',
            );
        });
    });
});
