import { Injectable } from '@nestjs/common';
import { AgentWorkflowType } from 'app/agent-workflow/enums/agent-workflow-type.enum';
import { AgentWorkflowVendorType } from 'app/agent-workflow/enums/agent-workflow-vendor-type.enum';
import { AuditTrailStepConfig } from 'app/agent-workflow/types/audit-trail-step-config.type';
import {
    VENDOR_ASSESSMENT_AUDIT_TRAIL_STEP_MAPPING,
    VENDOR_CRITERIA_AUDIT_TRAIL_STEP_MAPPING,
} from 'app/ai/constants/audit-trails/vendor-risk-management-audit-trail.constant';
@Injectable()
export class VendorRiskManagementAuditTrailMappingFactory {
    /**
     * Get audit trail mapping for Vendor Risk Management workflows
     * @param workflowType - The workflow type
     * @returns Map<string, AuditTrailStepConfig> - The audit trail mapping
     */
    getMapping(workflowType: AgentWorkflowType): Map<string, AuditTrailStepConfig> {
        if (workflowType === AgentWorkflowVendorType.VENDOR_ASSESSMENT) {
            return VENDOR_ASSESSMENT_AUDIT_TRAIL_STEP_MAPPING;
        }

        if (workflowType === AgentWorkflowVendorType.VENDORS_CRITERIA) {
            return VENDOR_CRITERIA_AUDIT_TRAIL_STEP_MAPPING;
        }

        throw new Error(
            `Unsupported workflow type for Vendor Risk Management audit trail: ${workflowType}`,
        );
    }
}
