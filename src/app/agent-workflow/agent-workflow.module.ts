import { Module } from '@nestjs/common';
import { AgentWorkflowOrchestratorModule } from 'app/agent-workflow/agent-workflow-orchestrator.module';
import { AgentWorkflowVendorAssessmentController } from 'app/agent-workflow/controllers/agent-workflow-vendor-assessment.controller';
import { AgentWorkflowVendorCriteriaCreationController } from 'app/agent-workflow/controllers/agent-workflow-vendor-criteria-creation.controller';
import { AiModule } from 'app/ai/ai.module';
import { AppModule } from 'app/app.module';
import { VendorsCoreModule } from 'app/users/vendors-core.module';
import { ModuleType, ModuleTypes } from 'commons/decorators/module-type.decorator';

@ModuleType(ModuleTypes.COMMERCE)
@Module({
    imports: [AgentWorkflowOrchestratorModule, VendorsCoreModule, AiModule],
    controllers: [AgentWorkflowVendorAssessmentController, AgentWorkflowVendorCriteriaCreationController],
    providers: [],
    exports: [],
})
export class Agent<PERSON>or<PERSON>flowModule extends AppModule {}
