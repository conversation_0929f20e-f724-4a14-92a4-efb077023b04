import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import {
    AgentWorkflow,
    AgentWorkflowStep,
    AgentWorkflowStepMessage,
} from 'app/agent-workflow/entities';
import { AgentWorkflowAuditTrailFactory } from 'app/agent-workflow/factories/agent-workflow-audit-trail.factory';
import { VendorRiskManagementAuditTrailMappingFactory } from 'app/agent-workflow/factories/vendor-risk-management-audit-trail-mapping.factory';
import { AgentWorkflowMessageFactory } from 'app/agent-workflow/message/factories/agent-workflow-message-factory';
import { VendorAssessmentMessageGeneratorFactory } from 'app/agent-workflow/message/factories/vendor-assessment-message-generator-factory';
import { VendorCriteriaMessageGeneratorFactory } from 'app/agent-workflow/message/factories/vendor-criteria-message-generator-factory';
import { VendorRiskManagementMessageFactory } from 'app/agent-workflow/message/factories/vendor-risk-management-message-factory';
import { EvaluateSecurityPostureMessageGenerator } from 'app/agent-workflow/message/generators/vendor-risk-management/vendor-assessment/evaluate-security-posture.generator';
import { ProcessStartMessageGenerator } from 'app/agent-workflow/message/generators/vendor-risk-management/vendor-assessment/process-start.generator';
import { RetrieveSecurityPostureMetMessageGenerator } from 'app/agent-workflow/message/generators/vendor-risk-management/vendor-assessment/retrieve-security-posture-met.generator';
import { RetrieveSecurityPostureNotMetMessageGenerator } from 'app/agent-workflow/message/generators/vendor-risk-management/vendor-assessment/retrieve-security-posture-not-met.generator';
import { RetrieveSecurityPostureMessageGenerator } from 'app/agent-workflow/message/generators/vendor-risk-management/vendor-assessment/retrieve-security-posture.generator';
import { SecurityQuestionnaireSentAutoOnMessageGenerator } from 'app/agent-workflow/message/generators/vendor-risk-management/vendor-assessment/security-questionnaire-sent-auto-on.generator';
import { SecurityQuestionnaireSentAutoMessageGenerator } from 'app/agent-workflow/message/generators/vendor-risk-management/vendor-assessment/security-questionnaire-sent-auto.generator';
import { SecurityQuestionnaireSentMessageGenerator } from 'app/agent-workflow/message/generators/vendor-risk-management/vendor-assessment/security-questionnaire-sent.generator';
import { SecurityReviewDocumentsCollectedMessageGenerator } from 'app/agent-workflow/message/generators/vendor-risk-management/vendor-assessment/security-review-documents-collected.generator';
import { SecurityReviewMissingDocumentsMessageGenerator } from 'app/agent-workflow/message/generators/vendor-risk-management/vendor-assessment/security-review-missing-documents.generator';
import { SendDocumentsToAiMessageGenerator } from 'app/agent-workflow/message/generators/vendor-risk-management/vendor-assessment/send-documents-to-ai.generator';
import { AnalyzingCriteriaQuestionnaireMessageGenerator } from 'app/agent-workflow/message/generators/vendor-risk-management/vendors-criteria/analyzing-criteria-questionnaire.generator';
import { FinalizedGeneratingCriteriaMessageGenerator } from 'app/agent-workflow/message/generators/vendor-risk-management/vendors-criteria/finalized-criteria-generation.generator';
import { FinalizingGeneratingCriteriaMessageGenerator } from 'app/agent-workflow/message/generators/vendor-risk-management/vendors-criteria/finalizing-generating-criteria.generator';
import { GeneratingCriteriaMessageGenerator } from 'app/agent-workflow/message/generators/vendor-risk-management/vendors-criteria/generating-criteria.generator';
import { RestoreDrataCriteriaMessageGenerator } from 'app/agent-workflow/message/generators/vendor-risk-management/vendors-criteria/restore-drata-criteria.generator';
import { ReviewAndModifyCriteriaMessageGenerator } from 'app/agent-workflow/message/generators/vendor-risk-management/vendors-criteria/review-and-modify-criteria.generator';
import { SavedGeneratedCriteriaMessageGenerator } from 'app/agent-workflow/message/generators/vendor-risk-management/vendors-criteria/saved-generated-criteria.generator';
import { SendCriteriaToAiMessageGenerator } from 'app/agent-workflow/message/generators/vendor-risk-management/vendors-criteria/send-criteria-to-ai.generator';
import { AgentWorkflowStepCompletedSocketHandler } from 'app/agent-workflow/observables/handlers/agent-workflow-step-completed.handler';
import { AgentWorkflowStepCreatedSocketHandler } from 'app/agent-workflow/observables/handlers/agent-workflow-step-created.handler';
import { AgentWorkflowStepUpdatedSocketHandler } from 'app/agent-workflow/observables/handlers/agent-workflow-step-updated.handler';
import { AgentWorkflowAuditTrailService } from 'app/agent-workflow/services/agent-workflow-audit-trail.service';
import { AgentWorkflowOrchestratorService } from 'app/agent-workflow/services/agent-workflow-orchestrator.service';
import { AgentWorkflowStateMachineService } from 'app/agent-workflow/services/agent-workflow-state-machine.service';
import { AgentWorkflowStepExecutorService } from 'app/agent-workflow/services/agent-workflow-step-executor.service';
import { AgentWorkflowStepMessageService } from 'app/agent-workflow/services/agent-workflow-step-message.service';
import { AgentWorkflowVendorAssessmentService } from 'app/agent-workflow/services/agent-workflow-vendor-assessment.service';
import { AgentWorkflowVendorCriteriaCreationService } from 'app/agent-workflow/services/agent-workflow-vendor-criteria-creation.service';
import { AgentWorkflowService } from 'app/agent-workflow/services/agent-workflow.service';
import { AgentWorkflowStateMachineFactory } from 'app/agent-workflow/state-machines/agent-workflow-state-machine.factory';
import { VendorRiskManagementStateMachineFactory } from 'app/agent-workflow/state-machines/vendor-risk-management/agent-workflow-state-machine.factory';
import { EvaluateSecurityPostureExecutor } from 'app/agent-workflow/step-executor/executors/vendor-risk-management/vendor-assessment/evaluate-security-posture.executor';
import { FinalizeExecutor } from 'app/agent-workflow/step-executor/executors/vendor-risk-management/vendor-assessment/finalize.executor';
import { ProcessStartSafebaseExecutor } from 'app/agent-workflow/step-executor/executors/vendor-risk-management/vendor-assessment/process-start-safebase.executor';
import { ProcessStartExecutor } from 'app/agent-workflow/step-executor/executors/vendor-risk-management/vendor-assessment/process-start.executor';
import { RetrieveSecurityPostureMetExecutor } from 'app/agent-workflow/step-executor/executors/vendor-risk-management/vendor-assessment/retrieve-security-posture-met.executor';
import { RetrieveSecurityPostureNotMetExecutor } from 'app/agent-workflow/step-executor/executors/vendor-risk-management/vendor-assessment/retrieve-security-posture-not-met.executor';
import { RetrieveSecurityPostureExecutor } from 'app/agent-workflow/step-executor/executors/vendor-risk-management/vendor-assessment/retrieve-security-posture.executor';
import { SafebaseAccessDeniedExecutor } from 'app/agent-workflow/step-executor/executors/vendor-risk-management/vendor-assessment/safebase-access-denied.executor';
import { SafebaseAccessGrantedExecutor } from 'app/agent-workflow/step-executor/executors/vendor-risk-management/vendor-assessment/safebase-access-granted.executor';
import { SafebaseRequestAccessAutoExecutor } from 'app/agent-workflow/step-executor/executors/vendor-risk-management/vendor-assessment/safebase-request-access-auto.executor';
import { SafebaseRequestAccessManualExecutor } from 'app/agent-workflow/step-executor/executors/vendor-risk-management/vendor-assessment/safebase-request-access-manual.executor';
import { SafebaseRequestAccessExecutor } from 'app/agent-workflow/step-executor/executors/vendor-risk-management/vendor-assessment/safebase-request-access.executor';
import { SafebaseSendDocumentsToAiExecutor } from 'app/agent-workflow/step-executor/executors/vendor-risk-management/vendor-assessment/safebase-send-documents-to-ai.executor';
import { SecurityQuestionnaireSentAutoOnExecutor } from 'app/agent-workflow/step-executor/executors/vendor-risk-management/vendor-assessment/security-questionnaire-sent-auto-on.executor';
import { SecurityQuestionnaireSentAutoExecutor } from 'app/agent-workflow/step-executor/executors/vendor-risk-management/vendor-assessment/security-questionnaire-sent-auto.executor';
import { SecurityQuestionnaireSentExecutor } from 'app/agent-workflow/step-executor/executors/vendor-risk-management/vendor-assessment/security-questionnaire-sent.executor';
import { SecurityReviewDocumentsCollectedExecutor } from 'app/agent-workflow/step-executor/executors/vendor-risk-management/vendor-assessment/security-review-documents-collected.executor';
import { SecurityReviewMissingDocumentsExecutor } from 'app/agent-workflow/step-executor/executors/vendor-risk-management/vendor-assessment/security-review-missing-documents.executor';
import { SendDocumentsToAiExecutor } from 'app/agent-workflow/step-executor/executors/vendor-risk-management/vendor-assessment/send-documents-to-ai.executor';
import { AnalyzingCriteriaQuestionnaireExecutor } from 'app/agent-workflow/step-executor/executors/vendor-risk-management/vendors-criteria/analyzing-criteria-questionnaire.executor';
import { CriteriaGenerationFinalizedExecutor } from 'app/agent-workflow/step-executor/executors/vendor-risk-management/vendors-criteria/criteria-generation-finalized.executor';
import { CriteriaGenerationFinalizingExecutor } from 'app/agent-workflow/step-executor/executors/vendor-risk-management/vendors-criteria/criteria-generation-finalizing.executor';
import { GeneratingCriteriaExecutor } from 'app/agent-workflow/step-executor/executors/vendor-risk-management/vendors-criteria/generating-criteria.executor';
import { CriteriaCreationProcessStartExecutor } from 'app/agent-workflow/step-executor/executors/vendor-risk-management/vendors-criteria/process-start.executor';
import { RestoreDrataCriteriaExecutor } from 'app/agent-workflow/step-executor/executors/vendor-risk-management/vendors-criteria/restore-drata-criteria.executor';
import { ReviewAndModifyCriteriaExecutor } from 'app/agent-workflow/step-executor/executors/vendor-risk-management/vendors-criteria/review-and-modify-criteria.executor';
import { SaveGeneratedCriteriaExecutor } from 'app/agent-workflow/step-executor/executors/vendor-risk-management/vendors-criteria/save-generated-criteria.executor';
import { SendCriteriaToAiExecutor } from 'app/agent-workflow/step-executor/executors/vendor-risk-management/vendors-criteria/send-criteria-to-ai.executor';
import { AgentWorkflowStepExecutorFactory } from 'app/agent-workflow/step-executor/factories/agent-workflow-step-executor-factory';
import { VendorRiskManagementStepExecutorFactory } from 'app/agent-workflow/step-executor/factories/vendor-risk-management-step-executor-factory';
import { VendorAssessmentStepExecutorMapper } from 'app/agent-workflow/step-executor/mappers/vendor-risk-management/vendor-assessment-step-executor.mapper';
import { VendorsCriteriaStepExecutorMapper } from 'app/agent-workflow/step-executor/mappers/vendor-risk-management/vendors-criteria-step-executor.mapper';
import { FinalizeStepExecutor } from 'app/agent-workflow/step-executor/shared/finalize.executor';
import { AppModule } from 'app/app.module';
import { VendorsCoreModule } from 'app/users/vendors-core.module';
import { ModuleType, ModuleTypes } from 'commons/decorators/module-type.decorator';

@ModuleType(ModuleTypes.ORCHESTRATION)
@Module({
    imports: [
        TypeOrmModule.forFeature([AgentWorkflow, AgentWorkflowStep, AgentWorkflowStepMessage]),
        VendorsCoreModule,
    ],
    providers: [
        AgentWorkflowService,
        AgentWorkflowAuditTrailService,
        AgentWorkflowStateMachineService,
        AgentWorkflowStepExecutorService,
        AgentWorkflowStepMessageService,
        AgentWorkflowOrchestratorService,
        AgentWorkflowStateMachineFactory,
        AgentWorkflowVendorAssessmentService,
        AgentWorkflowVendorCriteriaCreationService,
        VendorRiskManagementStateMachineFactory,
        AgentWorkflowMessageFactory,
        VendorRiskManagementMessageFactory,
        VendorAssessmentMessageGeneratorFactory,
        VendorCriteriaMessageGeneratorFactory,
        // Message Generators
        ProcessStartMessageGenerator,
        SecurityReviewMissingDocumentsMessageGenerator,
        SecurityReviewDocumentsCollectedMessageGenerator,
        SendDocumentsToAiMessageGenerator,
        EvaluateSecurityPostureMessageGenerator,
        RetrieveSecurityPostureMessageGenerator,
        RetrieveSecurityPostureNotMetMessageGenerator,
        RetrieveSecurityPostureMetMessageGenerator,
        SecurityQuestionnaireSentMessageGenerator,
        SecurityQuestionnaireSentAutoMessageGenerator,
        SecurityQuestionnaireSentAutoOnMessageGenerator,
        SendCriteriaToAiMessageGenerator,
        AgentWorkflowAuditTrailFactory,
        VendorRiskManagementAuditTrailMappingFactory,
        AgentWorkflowStepExecutorFactory,
        VendorRiskManagementStepExecutorFactory,
        AnalyzingCriteriaQuestionnaireMessageGenerator,
        GeneratingCriteriaMessageGenerator,
        FinalizingGeneratingCriteriaMessageGenerator,
        FinalizedGeneratingCriteriaMessageGenerator,
        ReviewAndModifyCriteriaMessageGenerator,
        SavedGeneratedCriteriaMessageGenerator,
        RestoreDrataCriteriaMessageGenerator,
        // Step Executor Mappers
        VendorAssessmentStepExecutorMapper,
        VendorsCriteriaStepExecutorMapper,
        // Vendor Assessment Executors
        ProcessStartExecutor,
        ProcessStartSafebaseExecutor,
        SecurityReviewMissingDocumentsExecutor,
        SecurityReviewDocumentsCollectedExecutor,
        SendDocumentsToAiExecutor,
        EvaluateSecurityPostureExecutor,
        RetrieveSecurityPostureExecutor,
        RetrieveSecurityPostureNotMetExecutor,
        RetrieveSecurityPostureMetExecutor,
        SecurityQuestionnaireSentExecutor,
        SecurityQuestionnaireSentAutoExecutor,
        SecurityQuestionnaireSentAutoOnExecutor,
        // Safebase Executors
        SafebaseAccessDeniedExecutor,
        SafebaseAccessGrantedExecutor,
        SafebaseRequestAccessExecutor,
        SafebaseRequestAccessAutoExecutor,
        SafebaseRequestAccessManualExecutor,
        SafebaseSendDocumentsToAiExecutor,
        FinalizeExecutor,
        // Vendors Criteria Executors
        CriteriaCreationProcessStartExecutor,
        SendCriteriaToAiExecutor,
        AnalyzingCriteriaQuestionnaireExecutor,
        GeneratingCriteriaExecutor,
        CriteriaGenerationFinalizingExecutor,
        CriteriaGenerationFinalizedExecutor,
        ReviewAndModifyCriteriaExecutor,
        SaveGeneratedCriteriaExecutor,
        RestoreDrataCriteriaExecutor,
        FinalizeStepExecutor,
        // Event Handlers
        AgentWorkflowStepCreatedSocketHandler,
        AgentWorkflowStepUpdatedSocketHandler,
        AgentWorkflowStepCompletedSocketHandler,
    ],
    exports: [
        AgentWorkflowService,
        AgentWorkflowAuditTrailService,
        AgentWorkflowStateMachineService,
        AgentWorkflowStepExecutorService,
        AgentWorkflowStepMessageService,
        AgentWorkflowOrchestratorService,
        AgentWorkflowStateMachineFactory,
        AgentWorkflowVendorAssessmentService,
        AgentWorkflowVendorCriteriaCreationService,
        VendorRiskManagementStateMachineFactory,
    ],
})
export class AgentWorkflowOrchestratorModule extends AppModule {}
