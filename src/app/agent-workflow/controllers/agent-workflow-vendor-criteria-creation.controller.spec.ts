import { TestingModule } from '@nestjs/testing';
import { AgentWorkflowVendorCriteriaCreationController } from 'app/agent-workflow/controllers/agent-workflow-vendor-criteria-creation.controller';
import { CreateAgentWorkflowStepDto } from 'app/agent-workflow/dtos/create-agent-workflow-step.dto';
import { CreateAgentWorkflowDto } from 'app/agent-workflow/dtos/create-agent-workflow.dto';
import { GetVendorCriteriaCreationAgentWorkflowsDto } from 'app/agent-workflow/dtos/get-vendor-criteria-creation-agent-workflows.dto';
import { UpdateAgentWorkflowRequestDto } from 'app/agent-workflow/dtos/update-agent-workflow-request.dto';
import { AgentType } from 'app/agent-workflow/enums/agent-type.enum';
import { AgentWorkflowStatus } from 'app/agent-workflow/enums/agent-workflow-status.enum';
import { AgentWorkflowStepStatus } from 'app/agent-workflow/enums/agent-workflow-step-status.enum';
import { AgentWorkflowVendorCriteriaStepName } from 'app/agent-workflow/enums/agent-workflow-vendor-criteria-step-name.enum';
import { AgentWorkflowVendorType } from 'app/agent-workflow/enums/agent-workflow-vendor-type.enum';
import { AgentWorkflowAuditTrailService } from 'app/agent-workflow/services/agent-workflow-audit-trail.service';
import { AgentWorkflowOrchestratorService } from 'app/agent-workflow/services/agent-workflow-orchestrator.service';
import { AgentWorkflowVendorCriteriaCreationService } from 'app/agent-workflow/services/agent-workflow-vendor-criteria-creation.service';
import { AgentWorkflowService } from 'app/agent-workflow/services/agent-workflow.service';
import { getUserMock } from 'app/users/entities/mocks/user.mock';
import { getAccountMock } from 'auth/mocks/account.mock';
import { createAppTestingModule } from 'commons/helpers/app-testing-module.helper';
import { MockFunctionMetadata, ModuleMocker } from 'jest-mock';
import { mock, MockProxy } from 'jest-mock-extended';

const moduleMocker = new ModuleMocker(global);

describe('AgentWorkflowVendorCriteriaCreationController', () => {
    let controller: AgentWorkflowVendorCriteriaCreationController;
    let mockAgentWorkflowService: MockProxy<AgentWorkflowService>;
    let mockAuditTrailService: MockProxy<AgentWorkflowAuditTrailService>;
    let mockOrchestratorService: MockProxy<AgentWorkflowOrchestratorService>;
    let mockVendorCriteriaCreationService: MockProxy<AgentWorkflowVendorCriteriaCreationService>;

    const mockAccount = getAccountMock();
    const mockUser = getUserMock();

    beforeEach(async () => {
        mockAgentWorkflowService = mock<AgentWorkflowService>();
        mockAuditTrailService = mock<AgentWorkflowAuditTrailService>();
        mockOrchestratorService = mock<AgentWorkflowOrchestratorService>();
        mockVendorCriteriaCreationService = mock<AgentWorkflowVendorCriteriaCreationService>();

        // Set up default mock return values
        mockAgentWorkflowService.createAgentWorkflow.mockResolvedValue({} as any);
        mockAgentWorkflowService.createAgentWorkflowStep.mockResolvedValue({} as any);
        mockAgentWorkflowService.getAgentWorkflows.mockResolvedValue([] as any);
        mockAgentWorkflowService.getAgentWorkflow.mockResolvedValue({} as any);
        mockAgentWorkflowService.updateAgentWorkflow.mockResolvedValue({} as any);
        mockAuditTrailService.getWorkflowAuditTrail.mockResolvedValue([] as any);
        mockOrchestratorService.continueWorkflow.mockResolvedValue({} as any);
        mockOrchestratorService.buildAgentWorkflowExtended.mockReturnValue({} as any);
        mockVendorCriteriaCreationService.startCriteriaCreation.mockResolvedValue({} as any);
        mockVendorCriteriaCreationService.getVendorCriteriaCreationWorkflows.mockResolvedValue([] as any);

        const module: TestingModule = await createAppTestingModule({
            controllers: [AgentWorkflowVendorCriteriaCreationController],
            providers: [
                {
                    provide: AgentWorkflowService,
                    useValue: mockAgentWorkflowService,
                },
                {
                    provide: AgentWorkflowAuditTrailService,
                    useValue: mockAuditTrailService,
                },
                {
                    provide: AgentWorkflowOrchestratorService,
                    useValue: mockOrchestratorService,
                },
                {
                    provide: AgentWorkflowVendorCriteriaCreationService,
                    useValue: mockVendorCriteriaCreationService,
                },
            ],
        })
            .useMocker(token => {
                if (token === AgentWorkflowService) {
                    return mockAgentWorkflowService;
                }
                if (token === AgentWorkflowAuditTrailService) {
                    return mockAuditTrailService;
                }
                if (token === AgentWorkflowOrchestratorService) {
                    return mockOrchestratorService;
                }
                if (token === AgentWorkflowVendorCriteriaCreationService) {
                    return mockVendorCriteriaCreationService;
                }
                // Mock all other dependencies automatically
                if (typeof token === 'function') {
                    const mockMetadata = moduleMocker.getMetadata(token) as MockFunctionMetadata<
                        any,
                        any
                    >;
                    const Mock = moduleMocker.generateFromMetadata(mockMetadata);
                    return new Mock();
                }
                return {};
            })
            .compile();

        controller = module.get<AgentWorkflowVendorCriteriaCreationController>(
            AgentWorkflowVendorCriteriaCreationController,
        );
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    it('should be defined', () => {
        expect(controller).toBeDefined();
    });

    describe('createWorkflow', () => {
        it('should create a vendor criteria creation workflow', async () => {
            const dto: CreateAgentWorkflowDto = {
                workflowTypeId: 'test-criteria-workflow-123',
                vendorId: 1,
                workspaceId: 1,
            };

            const result = await controller.createWorkflow(mockAccount, mockUser, dto);

            expect(mockVendorCriteriaCreationService.startCriteriaCreation).toHaveBeenCalledWith(
                mockAccount,
                mockUser,
                dto,
            );
            expect(result).toEqual({});
        });
    });

    describe('createWorkflowStep', () => {
        it('should create a workflow step using continueWorkflow', async () => {
            const workflowId = 1;
            const dto: CreateAgentWorkflowStepDto = {
                stepName: AgentWorkflowVendorCriteriaStepName.SEND_CRITERIA_QUESTIONNAIRE_TO_AI,
                resumeWorkflow: false,
            };

            const result = await controller.createWorkflowStep(mockAccount, workflowId, dto);

            expect(mockOrchestratorService.continueWorkflow).toHaveBeenCalledWith(
                mockAccount,
                workflowId,
                dto,
            );
            expect(result).toEqual({});
        });

        it('should handle resumeWorkflow omitted', async () => {
            const workflowId = 1;
            const dto: CreateAgentWorkflowStepDto = {
                stepName: AgentWorkflowVendorCriteriaStepName.GENERATING_CRITERIA,
            };

            const result = await controller.createWorkflowStep(mockAccount, workflowId, dto);

            expect(mockOrchestratorService.continueWorkflow).toHaveBeenCalledWith(
                mockAccount,
                workflowId,
                dto,
            );
            expect(result).toEqual({});
        });

        it('should handle resumeWorkflow true', async () => {
            const workflowId = 1;
            const dto: CreateAgentWorkflowStepDto = {
                stepName: AgentWorkflowVendorCriteriaStepName.REVIEW_AND_MODIFY_CRITERIA,
                resumeWorkflow: true,
            };

            const result = await controller.createWorkflowStep(mockAccount, workflowId, dto);

            expect(mockOrchestratorService.continueWorkflow).toHaveBeenCalledWith(
                mockAccount,
                workflowId,
                dto,
            );
            expect(result).toEqual({});
        });
    });

    describe('getWorkflows', () => {
        it('should get workflows and return paginated result', async () => {
            const dto: GetVendorCriteriaCreationAgentWorkflowsDto = {};
            const mockPaginatedResult = {
                data: [],
                page: 1,
                limit: 20,
                total: 0,
            };

            mockVendorCriteriaCreationService.getVendorCriteriaCreationWorkflows.mockResolvedValue(
                mockPaginatedResult,
            );

            const result = await controller.getWorkflows(mockAccount, dto);

            expect(mockVendorCriteriaCreationService.getVendorCriteriaCreationWorkflows).toHaveBeenCalledWith(
                mockAccount,
                AgentType.VENDOR_RISK_MANAGEMENT,
                AgentWorkflowVendorType.VENDORS_CRITERIA,
                dto,
            );
            expect(result).toEqual(mockPaginatedResult);
            expect(result.data).toEqual([]);
            expect(result.page).toBe(1);
            expect(result.limit).toBe(20);
            expect(result.total).toBe(0);
        });
    });

    describe('getWorkflow', () => {
        it('should get a workflow', async () => {
            const workflowId = 1;

            const result = await controller.getWorkflow(mockAccount, workflowId);

            expect(mockAgentWorkflowService.getAgentWorkflow).toHaveBeenCalledWith(mockAccount, workflowId);
            expect(result).toEqual({});
        });
    });

    describe('updateWorkflow', () => {
        it('should update a workflow', async () => {
            const workflowId = 1;
            const dto: UpdateAgentWorkflowRequestDto = {
                status: AgentWorkflowStatus.CANCELLED,
            };

            const result = await controller.updateWorkflow(mockAccount, workflowId, dto);

            expect(mockAgentWorkflowService.updateAgentWorkflow).toHaveBeenCalledWith(
                mockAccount,
                AgentType.VENDOR_RISK_MANAGEMENT,
                workflowId,
                dto,
            );
            expect(result).toEqual({});
        });
    });

    describe('getWorkflowAuditTrail', () => {
        it('should get audit trail for a workflow', async () => {
            const workflowId = 1;
            const mockAuditTrailData = [
                {
                    id: 1,
                    displayName: 'Started criteria generation',
                    status: AgentWorkflowStepStatus.COMPLETED,
                    createdAt: new Date('2024-01-01T10:00:00Z'),
                },
                {
                    id: 2,
                    displayName: 'Analyzing criteria questionnaire',
                    status: AgentWorkflowStepStatus.IN_PROGRESS,
                    createdAt: new Date('2024-01-01T11:00:00Z'),
                },
            ];

            mockAuditTrailService.getWorkflowAuditTrail.mockResolvedValue(mockAuditTrailData);

            const result = await controller.getWorkflowAuditTrail(mockAccount, workflowId);

            expect(mockAuditTrailService.getWorkflowAuditTrail).toHaveBeenCalledWith(
                mockAccount,
                workflowId,
            );
            expect(result).toEqual(mockAuditTrailData);
            expect(result).toHaveLength(2);
            expect(result[0].displayName).toBe('Started criteria generation');
            expect(result[0].status).toBe(AgentWorkflowStepStatus.COMPLETED);
            expect(result[1].displayName).toBe('Analyzing criteria questionnaire');
            expect(result[1].status).toBe(AgentWorkflowStepStatus.IN_PROGRESS);
        });

        it('should return empty array when workflow has no audit trail steps', async () => {
            const workflowId = 1;
            mockAuditTrailService.getWorkflowAuditTrail.mockResolvedValue([]);

            const result = await controller.getWorkflowAuditTrail(mockAccount, workflowId);

            expect(mockAuditTrailService.getWorkflowAuditTrail).toHaveBeenCalledWith(
                mockAccount,
                workflowId,
            );
            expect(result).toEqual([]);
            expect(result).toHaveLength(0);
        });
    });
});
