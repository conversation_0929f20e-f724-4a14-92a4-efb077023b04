import { MongoAbility } from '@casl/ability';
import {
    Body,
    Controller,
    Get,
    HttpCode,
    HttpStatus,
    Param,
    ParseIntPipe,
    Post,
    Put,
    Query,
} from '@nestjs/common';
import {
    ApiBadRequestResponse,
    ApiCreatedResponse,
    ApiOkResponse,
    ApiOperation,
    ApiTags
} from '@nestjs/swagger';
import { AgentWorkflowVendorCriteriaCreationRoute } from 'app/agent-workflow/agent-workflow-vendor-criteria-creation.routes';
import { AuditTrailStepsResponseDto } from 'app/agent-workflow/dtos/audit-trail-steps-response.dto';
import { CreateAgentWorkflowStepDto } from 'app/agent-workflow/dtos/create-agent-workflow-step.dto';
import { CreateAgentWorkflowDto } from 'app/agent-workflow/dtos/create-agent-workflow.dto';
import { GetVendorCriteriaCreationAgentWorkflowsDto } from 'app/agent-workflow/dtos/get-vendor-criteria-creation-agent-workflows.dto';
import { UpdateAgentWorkflowRequestDto } from 'app/agent-workflow/dtos/update-agent-workflow-request.dto';
import { VendorCriteriaCreationAgentWorkflowResponseDto } from 'app/agent-workflow/dtos/vendor-criteria-creation-agent-workflow-response.dto';
import { VendorCriteriaCreationAgentWorkflowResultResponseDto } from 'app/agent-workflow/dtos/vendor-criteria-creation-agent-workflow-result-response.dto';
import { VendorCriteriaCreationAgentWorkflowsResponseDto } from 'app/agent-workflow/dtos/vendor-criteria-creation-agent-workflows-response.dto';
import { AgentType } from 'app/agent-workflow/enums/agent-type.enum';
import { AgentWorkflowVendorType } from 'app/agent-workflow/enums/agent-workflow-vendor-type.enum';
import { AgentWorkflowAuditTrailService } from 'app/agent-workflow/services/agent-workflow-audit-trail.service';
import { AgentWorkflowOrchestratorService } from 'app/agent-workflow/services/agent-workflow-orchestrator.service';
import { AgentWorkflowVendorCriteriaCreationService } from 'app/agent-workflow/services/agent-workflow-vendor-criteria-creation.service';
import { AgentWorkflowService } from 'app/agent-workflow/services/agent-workflow.service';
import { AgentWorkflowExtended } from 'app/agent-workflow/types/agent-workflow.type';
import { AuditTrailStep } from 'app/agent-workflow/types/audit-trail-step.type';
import { VendorCriteriaCreationAgentWorkflowType } from 'app/agent-workflow/types/vendor-criteria-creation-agent-workflow.type';
import { WorkflowExecutionResult } from 'app/agent-workflow/types/workflow-execution-result.type';
import { AppController } from 'app/app.controller';
import { User } from 'app/users/entities/user.entity';
import { Account } from 'auth/entities/account.entity';
import { CheckAbilities } from 'casl/ability.handlers';
import { Action } from 'casl/actions';
import { Dto } from 'commons/decorators/dto.decorator';
import { GetAccount } from 'commons/decorators/get-account.decorator';
import { GetUser } from 'commons/decorators/get-user.decorator';
import { Area, ProductArea } from 'commons/decorators/product-area.decorator';
import { ExceptionResponseDto } from 'commons/dtos/exception-response.dto';
import { AccountEntitlementType } from 'commons/enums/account-entitlement-type.enum';
import { ApiResponse } from 'commons/enums/api-response.enum';
import { Subject } from 'commons/enums/users/subject.enum';
import { PaginationType } from 'commons/types/pagination.type';
import { RequireAccountEntitlement } from 'entitlements/decorators/require-account-entitlement.decorator';


@ApiTags('Agent Workflow - Vendor Criteria Creation')
@Controller()
@ProductArea(Area.TBD)
export class AgentWorkflowVendorCriteriaCreationController extends AppController {
    constructor(
        private readonly agentWorkflowService: AgentWorkflowService,
        private readonly auditTrailService: AgentWorkflowAuditTrailService,
        private readonly agentWorkflowVendorCriteriaCreationService: AgentWorkflowVendorCriteriaCreationService,
        private readonly agentWorkflowOrchestratorService: AgentWorkflowOrchestratorService,
    ) {
        super();
    }

    @ApiOperation({
        description: 'Create a new vendor criteria creation workflow',
    })
    @ApiCreatedResponse({
        description: 'Workflow created successfully',
        type: VendorCriteriaCreationAgentWorkflowResultResponseDto,
    })
    @ApiBadRequestResponse({
        description: ApiResponse.BAD_REQUEST,
        type: ExceptionResponseDto,
    })
    @Dto(VendorCriteriaCreationAgentWorkflowResultResponseDto)
    @HttpCode(HttpStatus.CREATED)
    @Post(AgentWorkflowVendorCriteriaCreationRoute.POST_VENDOR_CRITERIA_CREATION)
    @RequireAccountEntitlement(AccountEntitlementType.TPRM_PRO)
    @CheckAbilities((ability: MongoAbility) => ability.can(Action.Create, Subject[Subject.Vendor]))
    async createWorkflow(
        @GetAccount() account: Account,
        @GetUser() user: User,
        @Body() dto: CreateAgentWorkflowDto,
    ): Promise<WorkflowExecutionResult> {
        return this.agentWorkflowVendorCriteriaCreationService.startCriteriaCreation(account, user, dto);
    }

    @ApiOperation({
        description: 'Create a new step for a vendor criteria creation workflow',
    })
    @ApiCreatedResponse({
        description: 'Workflow step created successfully',
        type: VendorCriteriaCreationAgentWorkflowResultResponseDto,
    })
    @ApiBadRequestResponse({
        description: ApiResponse.BAD_REQUEST,
        type: ExceptionResponseDto,
    })
    @Dto(VendorCriteriaCreationAgentWorkflowResultResponseDto)
    @HttpCode(HttpStatus.CREATED)
    @Post(AgentWorkflowVendorCriteriaCreationRoute.POST_VENDOR_CRITERIA_CREATION_STEP)
    @RequireAccountEntitlement(AccountEntitlementType.TPRM_PRO)
    @CheckAbilities((ability: MongoAbility) => ability.can(Action.Create, Subject[Subject.Vendor]))
    async createWorkflowStep(
        @GetAccount() account: Account,
        @Param('id', ParseIntPipe) id: number,
        @Body() dto: CreateAgentWorkflowStepDto,
    ): Promise<WorkflowExecutionResult> {
        return this.agentWorkflowOrchestratorService.continueWorkflow(account, id, dto);
    }

    @ApiOperation({
        description: 'Get all vendor criteria creation workflows',
    })
    @ApiOkResponse({
        description: 'List of workflows',
        type: VendorCriteriaCreationAgentWorkflowsResponseDto,
    })
    @ApiBadRequestResponse({
        description: ApiResponse.BAD_REQUEST,
        type: ExceptionResponseDto,
    })
    @Dto(VendorCriteriaCreationAgentWorkflowsResponseDto)
    @HttpCode(HttpStatus.OK)
    @Get(AgentWorkflowVendorCriteriaCreationRoute.GET_VENDOR_CRITERIA_CREATION)
    @RequireAccountEntitlement(AccountEntitlementType.TPRM_PRO)
    @CheckAbilities((ability: MongoAbility) => ability.can(Action.Read, Subject[Subject.Vendor]))
    async getWorkflows(
        @GetAccount() account: Account,
        @Query() dto: GetVendorCriteriaCreationAgentWorkflowsDto,
    ): Promise<PaginationType<VendorCriteriaCreationAgentWorkflowType>> {
        return this.agentWorkflowVendorCriteriaCreationService.getVendorCriteriaCreationWorkflows(
            account,
            AgentType.VENDOR_RISK_MANAGEMENT,
            AgentWorkflowVendorType.VENDORS_CRITERIA,
            dto,
        );
    }

    @ApiOperation({
        description: 'Get a specific vendor criteria creation workflow',
    })
    @ApiOkResponse({
        description: 'Workflow details',
        type: VendorCriteriaCreationAgentWorkflowResponseDto,
    })
    @ApiBadRequestResponse({
        description: ApiResponse.BAD_REQUEST,
        type: ExceptionResponseDto,
    })
    @Dto(VendorCriteriaCreationAgentWorkflowResponseDto)
    @HttpCode(HttpStatus.OK)
    @Get(AgentWorkflowVendorCriteriaCreationRoute.GET_VENDOR_CRITERIA_CREATION_BY_ID)
    @RequireAccountEntitlement(AccountEntitlementType.TPRM_PRO)
    @CheckAbilities((ability: MongoAbility) => ability.can(Action.Read, Subject[Subject.Vendor]))
    async getWorkflow(
        @GetAccount() account: Account,
        @Param('id', ParseIntPipe) id: number,
    ): Promise<AgentWorkflowExtended> {
        const agentWorkflow = await this.agentWorkflowService.getAgentWorkflow(account, id);
        return this.agentWorkflowOrchestratorService.buildAgentWorkflowExtended(agentWorkflow);
    }

    @ApiOperation({
        description: 'Get audit trail for a vendor criteria creation workflow',
    })
    @ApiOkResponse({
        description: 'Workflow audit trail',
        type: AuditTrailStepsResponseDto,
    })
    @ApiBadRequestResponse({
        description: ApiResponse.BAD_REQUEST,
        type: ExceptionResponseDto,
    })
    @Dto(AuditTrailStepsResponseDto)
    @HttpCode(HttpStatus.OK)
    @Get(AgentWorkflowVendorCriteriaCreationRoute.GET_VENDOR_CRITERIA_CREATION_AUDIT_TRAIL)
    @RequireAccountEntitlement(AccountEntitlementType.TPRM_PRO)
    @CheckAbilities((ability: MongoAbility) => ability.can(Action.Read, Subject[Subject.Vendor]))
    async getWorkflowAuditTrail(
        @GetAccount() account: Account,
        @Param('id', ParseIntPipe) id: number,
    ): Promise<AuditTrailStep[]> {
        return this.auditTrailService.getWorkflowAuditTrail(account, id);
    }

    @ApiOperation({
        description: 'Update a vendor criteria creation workflow',
    })
    @ApiOkResponse({
        description: 'Workflow updated successfully',
        type: VendorCriteriaCreationAgentWorkflowResponseDto,
    })
    @ApiBadRequestResponse({
        description: ApiResponse.BAD_REQUEST,
        type: ExceptionResponseDto,
    })
    @Dto(VendorCriteriaCreationAgentWorkflowResponseDto)
    @HttpCode(HttpStatus.OK)
    @Put(AgentWorkflowVendorCriteriaCreationRoute.PUT_VENDOR_CRITERIA_CREATION)
    @RequireAccountEntitlement(AccountEntitlementType.TPRM_PRO)
    @CheckAbilities((ability: MongoAbility) => ability.can(Action.Update, Subject[Subject.Vendor]))
    async updateWorkflow(
        @GetAccount() account: Account,
        @Param('id', ParseIntPipe) id: number,
        @Body() dto: UpdateAgentWorkflowRequestDto,
    ): Promise<AgentWorkflowExtended> {
        const agentWorkflow = await this.agentWorkflowService.updateAgentWorkflow(
            account,
            AgentType.VENDOR_RISK_MANAGEMENT,
            id,
            dto,
        );

        return this.agentWorkflowOrchestratorService.buildAgentWorkflowExtended(agentWorkflow);
    }
}
