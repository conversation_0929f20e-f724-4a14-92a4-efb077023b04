import { ApiProperty } from '@nestjs/swagger';
import { AgentWorkflowStepResponseDto } from 'app/agent-workflow/dtos/agent-workflow-step-response.dto';
import { AgentWorkflowGeneralStepName } from 'app/agent-workflow/enums';
import { AgentType } from 'app/agent-workflow/enums/agent-type.enum';
import { AgentWorkflowStatus } from 'app/agent-workflow/enums/agent-workflow-status.enum';
import { AgentWorkflowStepName } from 'app/agent-workflow/enums/agent-workflow-step-name.enum';
import { AgentWorkflowVendorType } from 'app/agent-workflow/enums/agent-workflow-vendor-type.enum';
import { WorkflowExecutionMetadata } from 'app/agent-workflow/types/workflow-execution-metadata.type';
import { WorkflowExecutionResult } from 'app/agent-workflow/types/workflow-execution-result.type';
import { ResponseDto } from 'commons/dtos/response.dto';
import config from 'config';

/**
 * Base response DTO for agent workflow result operations.
 * Contains common properties shared across all agent workflow result DTOs.
 */
export abstract class AgentWorkflowResultBaseResponseDto extends ResponseDto {
    @ApiProperty({
        type: 'number',
        example: 1,
        description: 'The workflow ID',
    })
    workflowId: number;

    @ApiProperty({
        enum: AgentType,
        example: AgentType.VENDOR_RISK_MANAGEMENT,
        description: 'The agent type',
    })
    agentType: AgentType;

    @ApiProperty({
        enum: AgentWorkflowVendorType,
        example: AgentWorkflowVendorType.VENDOR_ASSESSMENT,
        description: 'The workflow type',
    })
    agentWorkflowType: AgentWorkflowVendorType;

    @ApiProperty({
        type: 'string',
        example: '123',
        description: 'The workflow type identifier',
    })
    workflowTypeId: string;

    @ApiProperty({
        enum: AgentWorkflowStatus,
        example: AgentWorkflowStatus.IN_PROGRESS,
        description: 'The workflow status',
    })
    status: AgentWorkflowStatus;

    @ApiProperty({
        type: 'string',
        format: 'date-time',
        example: config.get('swagger.examples.dateTime'),
        description: 'Workflow created date timestamp',
    })
    workflowCreatedAt: Date;

    @ApiProperty({
        type: 'string',
        format: 'date-time',
        example: config.get('swagger.examples.dateTime'),
        description: 'Workflow updated date timestamp',
    })
    workflowUpdatedAt: Date;

    @ApiProperty({
        type: () => AgentWorkflowStepResponseDto,
        nullable: true,
        example: null,
        description: 'The current step after execution',
    })
    currentStep: AgentWorkflowStepResponseDto | null;

    @ApiProperty({
        type: 'string',
        nullable: true,
        example: AgentWorkflowGeneralStepName[AgentWorkflowGeneralStepName.PROCESS_START],
        description:
            'The next step name if automatic progression is possible, null if manual input required',
    })
    nextStepName: AgentWorkflowStepName | null;

    @ApiProperty({
        type: 'boolean',
        example: true,
        description: 'Whether the workflow execution completed successfully',
    })
    success: boolean;

    @ApiProperty({
        type: 'number',
        example: 3,
        description: 'Number of steps executed in this execution cycle',
    })
    stepsExecuted: number;

    @ApiProperty({
        type: 'boolean',
        example: false,
        description: 'Whether the workflow requires manual input to continue',
    })
    requiresManualInput: boolean;

    @ApiProperty({
        type: 'boolean',
        example: false,
        description: 'Whether the workflow has reached a terminal state (completed/cancelled)',
    })
    isTerminal: boolean;

    @ApiProperty({
        type: 'string',
        nullable: true,
        example: 'Step execution failed due to invalid input',
        description: 'Optional error message if execution failed',
    })
    error: string | null;

    @ApiProperty({
        type: 'object',
        additionalProperties: true,
        example: { executionId: 'exec-123', startTime: '2025-01-01T10:00:00Z' },
        description: 'Execution metadata for debugging and monitoring',
    })
    executionMetadata: WorkflowExecutionMetadata;

    /**
     * Common build logic for mapping WorkflowExecutionResult to response DTO.
     * Subclasses can override this method to add specific behavior.
     */
    protected buildBase(result: WorkflowExecutionResult): void {
        // Map workflow properties directly
        this.workflowId = result.workflow.id;
        this.agentType = result.workflow.agentType;
        this.agentWorkflowType = result.workflow.agentWorkflowType;
        this.workflowTypeId = result.workflow.workflowTypeId;
        this.status = result.workflow.status;
        this.workflowCreatedAt = result.workflow.createdAt;
        this.workflowUpdatedAt = result.workflow.updatedAt;

        // Map execution result properties
        this.currentStep = result.currentStep
            ? new AgentWorkflowStepResponseDto().build(result.currentStep)
            : null;

        this.nextStepName = result.nextStepName;
        this.success = result.success;
        this.stepsExecuted = result.stepsExecuted;
        this.requiresManualInput = result.requiresManualInput;
        this.isTerminal = result.isTerminal;
        this.error = result.error || null;
        this.executionMetadata = result.executionMetadata;
    }
}
