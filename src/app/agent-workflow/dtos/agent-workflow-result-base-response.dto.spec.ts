import { AgentWorkflowResultBaseResponseDto } from 'app/agent-workflow/dtos/agent-workflow-result-base-response.dto';
import { VendorAssessmentAgentWorkflowResultResponseDto } from 'app/agent-workflow/dtos/vendor-assesment-agent-workflow-result-response.dto';
import { VendorCriteriaCreationAgentWorkflowResultResponseDto } from 'app/agent-workflow/dtos/vendor-criteria-creation-agent-workflow-result-response.dto';
import { AgentType } from 'app/agent-workflow/enums/agent-type.enum';
import { AgentWorkflowStatus } from 'app/agent-workflow/enums/agent-workflow-status.enum';
import { AgentWorkflowVendorType } from 'app/agent-workflow/enums/agent-workflow-vendor-type.enum';
import { WorkflowExecutionResult } from 'app/agent-workflow/types/workflow-execution-result.type';

describe('AgentWorkflowResultBaseResponseDto', () => {
    const mockWorkflowExecutionResult: WorkflowExecutionResult = {
        workflow: {
            id: 1,
            agentType: AgentType.VENDOR_RISK_MANAGEMENT,
            agentWorkflowType: AgentWorkflowVendorType.VENDORS_CRITERIA,
            workflowTypeId: 'test-123',
            status: AgentWorkflowStatus.IN_PROGRESS,
            createdAt: new Date('2023-01-01'),
            updatedAt: new Date('2023-01-02'),
        },
        currentStep: null,
        nextStepName: 'NEXT_STEP',
        success: true,
        stepsExecuted: 2,
        requiresManualInput: false,
        isTerminal: false,
        error: null,
        executionMetadata: {
            executedSteps: ['STEP_1', 'STEP_2'],
            warnings: [],
        },
    } as WorkflowExecutionResult;

    describe('VendorCriteriaCreationAgentWorkflowResultResponseDto', () => {
        it('should build correctly using base class logic', () => {
            const dto = new VendorCriteriaCreationAgentWorkflowResultResponseDto();
            const result = dto.build(mockWorkflowExecutionResult);

            expect(result).toBeInstanceOf(VendorCriteriaCreationAgentWorkflowResultResponseDto);
            expect(result.workflowId).toBe(1);
            expect(result.agentType).toBe(AgentType.VENDOR_RISK_MANAGEMENT);
            expect(result.agentWorkflowType).toBe(AgentWorkflowVendorType.VENDORS_CRITERIA);
            expect(result.workflowTypeId).toBe('test-123');
            expect(result.status).toBe(AgentWorkflowStatus.IN_PROGRESS);
            expect(result.success).toBe(true);
            expect(result.stepsExecuted).toBe(2);
            expect(result.requiresManualInput).toBe(false);
            expect(result.isTerminal).toBe(false);
            expect(result.error).toBeNull();
            expect(result.executionMetadata).toEqual({
                executedSteps: ['STEP_1', 'STEP_2'],
                warnings: [],
            });
        });
    });

    describe('VendorAssessmentAgentWorkflowResultResponseDto', () => {
        it('should build correctly using base class logic', () => {
            const dto = new VendorAssessmentAgentWorkflowResultResponseDto();
            const result = dto.build(mockWorkflowExecutionResult);

            expect(result).toBeInstanceOf(VendorAssessmentAgentWorkflowResultResponseDto);
            expect(result.workflowId).toBe(1);
            expect(result.agentType).toBe(AgentType.VENDOR_RISK_MANAGEMENT);
            expect(result.agentWorkflowType).toBe(AgentWorkflowVendorType.VENDORS_CRITERIA);
            expect(result.workflowTypeId).toBe('test-123');
            expect(result.status).toBe(AgentWorkflowStatus.IN_PROGRESS);
            expect(result.success).toBe(true);
            expect(result.stepsExecuted).toBe(2);
            expect(result.requiresManualInput).toBe(false);
            expect(result.isTerminal).toBe(false);
            expect(result.error).toBeNull();
            expect(result.executionMetadata).toEqual({
                executedSteps: ['STEP_1', 'STEP_2'],
                warnings: [],
            });
        });
    });

    describe('Base class inheritance', () => {
        it('should inherit from base class correctly', () => {
            const criteriaDto = new VendorCriteriaCreationAgentWorkflowResultResponseDto();
            const assessmentDto = new VendorAssessmentAgentWorkflowResultResponseDto();

            // Check that both DTOs inherit from the base class
            expect(criteriaDto).toBeInstanceOf(AgentWorkflowResultBaseResponseDto);
            expect(assessmentDto).toBeInstanceOf(AgentWorkflowResultBaseResponseDto);
        });

        it('should have common properties after building', () => {
            const criteriaDto = new VendorCriteriaCreationAgentWorkflowResultResponseDto();
            const assessmentDto = new VendorAssessmentAgentWorkflowResultResponseDto();

            // Build the DTOs to populate properties
            criteriaDto.build(mockWorkflowExecutionResult);
            assessmentDto.build(mockWorkflowExecutionResult);

            // Check that common properties exist and have the same values
            expect(criteriaDto.workflowId).toBe(assessmentDto.workflowId);
            expect(criteriaDto.agentType).toBe(assessmentDto.agentType);
            expect(criteriaDto.status).toBe(assessmentDto.status);
            expect(criteriaDto.success).toBe(assessmentDto.success);
        });
    });
});
