import { AgentWorkflowResultBaseResponseDto } from 'app/agent-workflow/dtos/agent-workflow-result-base-response.dto';
import { WorkflowExecutionResult } from 'app/agent-workflow/types/workflow-execution-result.type';

export class VendorCriteriaCreationAgentWorkflowResultResponseDto extends AgentWorkflowResultBaseResponseDto {

    build(result: WorkflowExecutionResult): VendorCriteriaCreationAgentWorkflowResultResponseDto {
        // Use base class build logic for common properties
        this.buildBase(result);

        return this.send();
    }
}
