import { AgentWorkflowResponseDto } from 'app/agent-workflow/dtos/agent-workflow-response.dto';
import { VendorCriteriaCreationAgentWorkflowType } from 'app/agent-workflow/types/vendor-criteria-creation-agent-workflow.type';

export class VendorCriteriaCreationAgentWorkflowResponseDto extends AgentWorkflowResponseDto {
    // Add criteria-specific properties

    build(data: VendorCriteriaCreationAgentWorkflowType): VendorCriteriaCreationAgentWorkflowResponseDto {
        super.build(data);
        return this.send();
    }
}
