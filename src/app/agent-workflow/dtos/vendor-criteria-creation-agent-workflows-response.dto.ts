import { Type } from '@nestjs/common';
import { ApiProperty } from '@nestjs/swagger';
import { VendorCriteriaCreationAgentWorkflowResponseDto } from 'app/agent-workflow/dtos/vendor-criteria-creation-agent-workflow-response.dto';
import { AgentWorkflow } from 'app/agent-workflow/entities';
import { PaginationResponseDto } from 'commons/dtos/pagination-response.dto';
import { ResponseDto } from 'commons/dtos/response.dto';

export class VendorCriteriaCreationAgentWorkflowsResponseDto extends PaginationResponseDto<AgentWorkflow> {
    @ApiProperty({
        type: VendorCriteriaCreationAgentWorkflowResponseDto,
        isArray: true,
        example: [],
        description: 'List of Vendor Criteria Creation agent workflows',
    })
    data: VendorCriteriaCreationAgentWorkflowResponseDto[];

    protected getDtoType(): Type<ResponseDto> {
        return VendorCriteriaCreationAgentWorkflowResponseDto;
    }
}
