import { TestingModule } from '@nestjs/testing';
import { AgentWorkflowStep } from 'app/agent-workflow/entities/agent-workflow-step.entity';
import { AgentWorkflow } from 'app/agent-workflow/entities/agent-workflow.entity';
import {
    AgentType,
    AgentWorkflowStepStatus,
    AgentWorkflowVendorCriteriaStepName,
    AgentWorkflowVendorType,
} from 'app/agent-workflow/enums';
import { CriteriaCreationProcessStartExecutor } from 'app/agent-workflow/step-executor/executors/vendor-risk-management/vendors-criteria/process-start.executor';
import { VendorAssessmentCriteriaCoreService } from 'app/users/vendors/services/vendor-assessment-criteria-core.service';
import { getAccountMock } from 'auth/mocks/account.mock';
import { createAppTestingModule } from 'commons/helpers/app-testing-module.helper';
import { MockFunctionMetadata, ModuleMocker } from 'jest-mock';
import { TenancyContext } from 'tenancy/contexts/tenancy.context';

const moduleMocker = new ModuleMocker(global);

describe('ProcessStartExecutor (Vendor Criteria)', () => {
    let executor: CriteriaCreationProcessStartExecutor;

    const mockAccount = getAccountMock();

    beforeEach(async () => {
        const module: TestingModule = await createAppTestingModule({
            providers: [
                CriteriaCreationProcessStartExecutor,
                {
                    provide: TenancyContext,
                    useValue: {
                        getTenantRepository: jest.fn(),
                    },
                },
                {
                    provide: VendorAssessmentCriteriaCoreService,
                    useValue: {
                        // Mock methods as needed
                    },
                },
            ],
        })
            .useMocker(token => {
                if (typeof token === 'function') {
                    const mockMetadata = moduleMocker.getMetadata(token) as MockFunctionMetadata<
                        any,
                        any
                    >;
                    const Mock = moduleMocker.generateFromMetadata(mockMetadata);
                    return new Mock();
                }
                return {};
            })
            .compile();

        executor = module.get<CriteriaCreationProcessStartExecutor>(CriteriaCreationProcessStartExecutor);

        // Mock the saveStep method
        jest.spyOn(executor as any, 'saveStep').mockResolvedValue(undefined);
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    it('should be defined', () => {
        expect(executor).toBeDefined();
    });

    describe('canExecute', () => {
        it('should return true for VENDORS_CRITERIA workflow type', async () => {
            const workflow = {
                agentWorkflowType: AgentWorkflowVendorType.VENDORS_CRITERIA,
            } as AgentWorkflow;

            const result = await executor.canExecute(workflow);

            expect(result).toBe(true);
        });

        it('should return false for non-VENDORS_CRITERIA workflow type', async () => {
            const workflow = {
                agentWorkflowType: AgentWorkflowVendorType.VENDOR_ASSESSMENT,
            } as AgentWorkflow;

            const result = await executor.canExecute(workflow);

            expect(result).toBe(false);
        });
    });

    describe('execute', () => {
        it('should mark step as completed', async () => {
            const workflow = {
                id: 1,
                agentType: AgentType.VENDOR_RISK_MANAGEMENT,
                agentWorkflowType: AgentWorkflowVendorType.VENDORS_CRITERIA,
            } as AgentWorkflow;

            const step = {
                id: 1,
                status: AgentWorkflowStepStatus.IN_PROGRESS,
            } as AgentWorkflowStep;

            await executor.execute(mockAccount, workflow, step);

            expect(step.status).toBe(AgentWorkflowStepStatus.COMPLETED);
            expect(executor['saveStep']).toHaveBeenCalledWith(step);
        });
    });

    describe('nextStep', () => {
        it('should return SEND_CRITERIA_QUESTIONNAIRE_TO_AI as next step', async () => {
            const workflow = {
                id: 1,
                agentType: AgentType.VENDOR_RISK_MANAGEMENT,
                agentWorkflowType: AgentWorkflowVendorType.VENDORS_CRITERIA,
            } as AgentWorkflow;

            const result = await executor.nextStep(mockAccount, workflow);

            expect(result).toBe(AgentWorkflowVendorCriteriaStepName.SEND_CRITERIA_QUESTIONNAIRE_TO_AI);
        });
    });
});
