import { Test, TestingModule } from '@nestjs/testing';
import { AgentWorkflowGeneralStepName } from 'app/agent-workflow/enums/agent-workflow-general-step-name.enum';
import { AgentWorkflowVendorCriteriaStepName } from 'app/agent-workflow/enums/agent-workflow-vendor-criteria-step-name.enum';
import { AnalyzingCriteriaQuestionnaireExecutor } from 'app/agent-workflow/step-executor/executors/vendor-risk-management/vendors-criteria/analyzing-criteria-questionnaire.executor';
import { CriteriaGenerationFinalizedExecutor } from 'app/agent-workflow/step-executor/executors/vendor-risk-management/vendors-criteria/criteria-generation-finalized.executor';
import { CriteriaGenerationFinalizingExecutor } from 'app/agent-workflow/step-executor/executors/vendor-risk-management/vendors-criteria/criteria-generation-finalizing.executor';
import { GeneratingCriteriaExecutor } from 'app/agent-workflow/step-executor/executors/vendor-risk-management/vendors-criteria/generating-criteria.executor';
import { CriteriaCreationProcessStartExecutor } from 'app/agent-workflow/step-executor/executors/vendor-risk-management/vendors-criteria/process-start.executor';
import { RestoreDrataCriteriaExecutor } from 'app/agent-workflow/step-executor/executors/vendor-risk-management/vendors-criteria/restore-drata-criteria.executor';
import { ReviewAndModifyCriteriaExecutor } from 'app/agent-workflow/step-executor/executors/vendor-risk-management/vendors-criteria/review-and-modify-criteria.executor';
import { SaveGeneratedCriteriaExecutor } from 'app/agent-workflow/step-executor/executors/vendor-risk-management/vendors-criteria/save-generated-criteria.executor';
import { SendCriteriaToAiExecutor } from 'app/agent-workflow/step-executor/executors/vendor-risk-management/vendors-criteria/send-criteria-to-ai.executor';
import { VendorsCriteriaStepExecutorMapper } from 'app/agent-workflow/step-executor/mappers/vendor-risk-management/vendors-criteria-step-executor.mapper';
import { FinalizeStepExecutor } from 'app/agent-workflow/step-executor/shared/finalize.executor';
import { MockProxy, mockDeep } from 'jest-mock-extended';

describe('VendorsCriteriaStepExecutorMapper', () => {
    let mapper: VendorsCriteriaStepExecutorMapper;
    let mockProcessStartExecutor: MockProxy<CriteriaCreationProcessStartExecutor>;
    let mockSendCriteriaToAiExecutor: MockProxy<SendCriteriaToAiExecutor>;
    let mockFinalizeStepExecutor: MockProxy<FinalizeStepExecutor>;
    let mockAnalyzingCriteriaQuestionnaireExecutor: MockProxy<AnalyzingCriteriaQuestionnaireExecutor>;
    let mockGeneratingCriteriaExecutor: MockProxy<GeneratingCriteriaExecutor>;
    let mockCriteriaGenerationFinalizingExecutor: MockProxy<CriteriaGenerationFinalizingExecutor>;
    let mockCriteriaGenerationFinalizedExecutor: MockProxy<CriteriaGenerationFinalizedExecutor>;
    let mockReviewAndModifyCriteriaExecutor: MockProxy<ReviewAndModifyCriteriaExecutor>;
    let mockSaveGeneratedCriteriaExecutor: MockProxy<SaveGeneratedCriteriaExecutor>;
    let mockRestoreDrataCriteriaExecutor: MockProxy<RestoreDrataCriteriaExecutor>;

    beforeEach(async () => {
        mockProcessStartExecutor = mockDeep<CriteriaCreationProcessStartExecutor>();
        mockSendCriteriaToAiExecutor = mockDeep<SendCriteriaToAiExecutor>();
        mockFinalizeStepExecutor = mockDeep<FinalizeStepExecutor>();
        mockAnalyzingCriteriaQuestionnaireExecutor = mockDeep<AnalyzingCriteriaQuestionnaireExecutor>();
        mockGeneratingCriteriaExecutor = mockDeep<GeneratingCriteriaExecutor>();
        mockCriteriaGenerationFinalizingExecutor = mockDeep<CriteriaGenerationFinalizingExecutor>();
        mockCriteriaGenerationFinalizedExecutor = mockDeep<CriteriaGenerationFinalizedExecutor>();
        mockReviewAndModifyCriteriaExecutor = mockDeep<ReviewAndModifyCriteriaExecutor>();
        mockSaveGeneratedCriteriaExecutor = mockDeep<SaveGeneratedCriteriaExecutor>();
        mockRestoreDrataCriteriaExecutor = mockDeep<RestoreDrataCriteriaExecutor>();

        const module: TestingModule = await Test.createTestingModule({
            providers: [
                VendorsCriteriaStepExecutorMapper,
                { provide: CriteriaCreationProcessStartExecutor, useValue: mockProcessStartExecutor },
                { provide: SendCriteriaToAiExecutor, useValue: mockSendCriteriaToAiExecutor },
                { provide: AnalyzingCriteriaQuestionnaireExecutor, useValue: mockAnalyzingCriteriaQuestionnaireExecutor },
                { provide: GeneratingCriteriaExecutor, useValue: mockGeneratingCriteriaExecutor },
                { provide: CriteriaGenerationFinalizingExecutor, useValue: mockCriteriaGenerationFinalizingExecutor },
                { provide: CriteriaGenerationFinalizedExecutor, useValue: mockCriteriaGenerationFinalizedExecutor },
                { provide: ReviewAndModifyCriteriaExecutor, useValue: mockReviewAndModifyCriteriaExecutor },
                { provide: SaveGeneratedCriteriaExecutor, useValue: mockSaveGeneratedCriteriaExecutor },
                { provide: RestoreDrataCriteriaExecutor, useValue: mockRestoreDrataCriteriaExecutor },
                { provide: FinalizeStepExecutor, useValue: mockFinalizeStepExecutor },
            ],
        }).compile();

        mapper = module.get(VendorsCriteriaStepExecutorMapper);
    });

    describe('getExecutor', () => {
        it('should return CriteriaCreationProcessStartExecutor for PROCESS_START step', () => {
            const result = mapper.getExecutor(AgentWorkflowGeneralStepName.PROCESS_START);
            expect(result).toBe(mockProcessStartExecutor);
        });

        it('should return SendCriteriaToAiExecutor for SEND_CRITERIA_QUESTIONNAIRE_TO_AI step', () => {
            const result = mapper.getExecutor(AgentWorkflowVendorCriteriaStepName.SEND_CRITERIA_QUESTIONNAIRE_TO_AI);
            expect(result).toBe(mockSendCriteriaToAiExecutor);
        });

        it('should return FinalizeStepExecutor for FINALIZE step', () => {
            const result = mapper.getExecutor(AgentWorkflowGeneralStepName.FINALIZE);
            expect(result).toBe(mockFinalizeStepExecutor);
        });

        it('should throw error for unknown step', () => {
            expect(() => mapper.getExecutor('UNKNOWN_STEP')).toThrow(
                'No step executor found for Vendors Criteria step: UNKNOWN_STEP',
            );
        });
    });
});
