import { Injectable } from '@nestjs/common';
import { AgentWorkflowGeneralStepName } from 'app/agent-workflow/enums/agent-workflow-general-step-name.enum';
import { AgentWorkflowVendorCriteriaStepName } from 'app/agent-workflow/enums/agent-workflow-vendor-criteria-step-name.enum';
import { AnalyzingCriteriaQuestionnaireExecutor } from 'app/agent-workflow/step-executor/executors/vendor-risk-management/vendors-criteria/analyzing-criteria-questionnaire.executor';
import { CriteriaGenerationFinalizedExecutor } from 'app/agent-workflow/step-executor/executors/vendor-risk-management/vendors-criteria/criteria-generation-finalized.executor';
import { CriteriaGenerationFinalizingExecutor } from 'app/agent-workflow/step-executor/executors/vendor-risk-management/vendors-criteria/criteria-generation-finalizing.executor';
import { GeneratingCriteriaExecutor } from 'app/agent-workflow/step-executor/executors/vendor-risk-management/vendors-criteria/generating-criteria.executor';
import { CriteriaCreationProcessStartExecutor } from 'app/agent-workflow/step-executor/executors/vendor-risk-management/vendors-criteria/process-start.executor';
import { RestoreDrataCriteriaExecutor } from 'app/agent-workflow/step-executor/executors/vendor-risk-management/vendors-criteria/restore-drata-criteria.executor';
import { ReviewAndModifyCriteriaExecutor } from 'app/agent-workflow/step-executor/executors/vendor-risk-management/vendors-criteria/review-and-modify-criteria.executor';
import { SaveGeneratedCriteriaExecutor } from 'app/agent-workflow/step-executor/executors/vendor-risk-management/vendors-criteria/save-generated-criteria.executor';
import { SendCriteriaToAiExecutor } from 'app/agent-workflow/step-executor/executors/vendor-risk-management/vendors-criteria/send-criteria-to-ai.executor';
import { FinalizeStepExecutor } from 'app/agent-workflow/step-executor/shared/finalize.executor';
import { IStepExecutor } from 'app/agent-workflow/step-executor/types/step-executor.interface';

/**
 * Mapper for vendors criteria step executors.
 * Handles the mapping between step names and their corresponding executor instances.
 */
@Injectable()
export class VendorsCriteriaStepExecutorMapper {
    constructor(
        private readonly processStartExecutor: CriteriaCreationProcessStartExecutor,
        private readonly sendCriteriaToAiExecutor: SendCriteriaToAiExecutor,
        private readonly analyzingCriteriaQuestionnaireExecutor: AnalyzingCriteriaQuestionnaireExecutor,
        private readonly generatingCriteriaExecutor: GeneratingCriteriaExecutor,
        private readonly criteriaGenerationFinalizingExecutor: CriteriaGenerationFinalizingExecutor,
        private readonly criteriaGenerationFinalizedExecutor: CriteriaGenerationFinalizedExecutor,
        private readonly reviewAndModifyCriteriaExecutor: ReviewAndModifyCriteriaExecutor,
        private readonly saveGeneratedCriteriaExecutor: SaveGeneratedCriteriaExecutor,
        private readonly restoreDrataCriteriaExecutor: RestoreDrataCriteriaExecutor,
        private readonly finalizeStepExecutor: FinalizeStepExecutor,
    ) {}

    /**
     * Gets the appropriate executor for a vendors criteria step.
     * @param stepName - The name of the step to get an executor for
     * @returns The executor instance for the given step
     * @throws Error if no executor is found for the step
     */
    getExecutor(stepName: string): IStepExecutor {
        switch (stepName) {
            case AgentWorkflowGeneralStepName.PROCESS_START:
                return this.processStartExecutor;
            case AgentWorkflowVendorCriteriaStepName.SEND_CRITERIA_QUESTIONNAIRE_TO_AI:
                return this.sendCriteriaToAiExecutor;
            case AgentWorkflowVendorCriteriaStepName.ANALYZING_CRITERIA_QUESTIONNAIRE:
                return this.analyzingCriteriaQuestionnaireExecutor;
            case AgentWorkflowVendorCriteriaStepName.GENERATING_CRITERIA:
                return this.generatingCriteriaExecutor;
            case AgentWorkflowVendorCriteriaStepName.CRITERIA_GENERATION_FINALIZING:
                return this.criteriaGenerationFinalizingExecutor;
            case AgentWorkflowVendorCriteriaStepName.CRITERIA_GENERATION_FINALIZED:
                return this.criteriaGenerationFinalizedExecutor;
            case AgentWorkflowVendorCriteriaStepName.REVIEW_AND_MODIFY_CRITERIA:
                return this.reviewAndModifyCriteriaExecutor;
            case AgentWorkflowVendorCriteriaStepName.SAVE_GENERATED_CRITERIA:
                return this.saveGeneratedCriteriaExecutor;
            case AgentWorkflowVendorCriteriaStepName.RESTORE_DRATA_CRITERIA:
                return this.restoreDrataCriteriaExecutor;
            case AgentWorkflowGeneralStepName.FINALIZE:
                return this.finalizeStepExecutor;
            default:
                throw new Error(
                    `No step executor found for Vendors Criteria step: ${String(stepName)}`,
                );
        }
    }
}
