/* eslint-disable @typescript-eslint/no-duplicate-enum-values */
export enum AgentWorkflowVendorCriteriaCreationRoute {
    // Vendor Criteria Creation routes
    POST_VENDOR_CRITERIA_CREATION = '/agent-workflow/vendor-criteria-creation',
    POST_VENDOR_CRITERIA_CREATION_STEP = '/agent-workflow/vendor-criteria-creation/:id/step',
    GET_VENDOR_CRITERIA_CREATION = '/agent-workflow/vendor-criteria-creation',
    GET_VENDOR_CRITERIA_CREATION_BY_ID = '/agent-workflow/vendor-criteria-creation/:id',
    PUT_VENDOR_CRITERIA_CREATION = '/agent-workflow/vendor-criteria-creation/:id',
    GET_VENDOR_CRITERIA_CREATION_AUDIT_TRAIL = '/agent-workflow/vendor-criteria-creation/:id/audit-trail',
}
