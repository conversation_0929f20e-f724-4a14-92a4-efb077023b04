import { HttpStatus, Injectable } from '@nestjs/common';
import { AccessApplication } from 'app/access-review/entities/access-application.entity';
import { AccessReviewPeriodApplicationUser } from 'app/access-review/entities/access-review-period-application-user.entity';
import { AccessReviewPeriodApplication } from 'app/access-review/entities/access-review-period-application.entity';
import { ApplicationGroup } from 'app/access-review/entities/application-group.entity';
import { ApplicationUserAccess } from 'app/access-review/entities/application-user-access.entity';
import { ApplicationSource } from 'app/access-review/enums/application-source.enum';
import {
    cleanAccessReviewDataRaw,
    connectionNeedsFullUARSync,
} from 'app/access-review/helpers/access-review.helper';
import { AccessApplicationRepository } from 'app/access-review/repositories/access-application.repository';
import { AccessReviewPeriodApplicationUserRepository } from 'app/access-review/repositories/access-review-period-application-user.repository';
import { AccessReviewPeriodApplicationRepository } from 'app/access-review/repositories/access-review-period-application.repository';
import { ApplicationGroupRepository } from 'app/access-review/repositories/application-group.repository';
import { ApplicationUserAccessRepository } from 'app/access-review/repositories/application-user-access.repository';
import { AccessReviewApplicationCoreService } from 'app/access-review/services/access-review-application-core.service';
import { ApiClientService } from 'app/api-client/api-client.service';
import { IAccessReviewService } from 'app/apis/interfaces/access-review-user-application.interface';
import { IApiServices } from 'app/apis/interfaces/api-services.interface';
import { IdentityGroup } from 'app/apis/interfaces/identity-group.interface';
import { IThirdPartyAppUser } from 'app/apis/interfaces/third-party-app-user.interface';
import { IThirdPartyApp } from 'app/apis/interfaces/third-party-app.interface';
import { IUserAccessReviewServiceUser } from 'app/apis/interfaces/user-access-review-service-user.interface';
import { IUserAccessReviewServices } from 'app/apis/interfaces/user-access-review-services.interface';
import { ConnectionEntity } from 'app/companies/connections/entities/connection.entity';
import { ConnectionsCoreService } from 'app/companies/connections/services/connections-core.service';
import { Company } from 'app/companies/entities/company.entity';
import { CompaniesCoreService } from 'app/companies/services/companies-core.service';
import { ISynchronization } from 'app/synchronizations/interfaces/synchronization.interface';
import { BaseSynchronizationService } from 'app/synchronizations/services/base-synchronization.service';
import { DEFAULT_TC_WORKSPACE_ID } from 'app/trust-center/trust-center.constants';
import { UserIdentity } from 'app/users/entities/user-identity.entity';
import { User } from 'app/users/entities/user.entity';
import { UserIdentityRepository } from 'app/users/repositories/user-identity-repository';
import { UsersCoreService } from 'app/users/services/users-core.service';
import { VendorToDiscover } from 'app/users/vendors/types/vendor-to-discover.type';
import { VendorsOrchestrationService } from 'app/users/vendors/vendors-orchestration.service';
import { Account } from 'auth/entities/account.entity';
import { isEmail } from 'class-validator';
import { PolloAdapter } from 'commons/adapters/pollo.adapter';
import { Benchmark } from 'commons/benchmark/benchmark';
import { ApplicationUserStatus } from 'commons/enums/application-user-status.enum';
import { ClientType } from 'commons/enums/auth/client-type.enum';
import { ProviderType } from 'commons/enums/auth/provider-type.enum';
import { LogIdentifierRecordType } from 'commons/enums/log-identifier-record-type.enum';
import { Role } from 'commons/enums/users/role.enum';
import { batch, paginateItems, tokenize } from 'commons/helpers/pagination/pagination.helper';
import { hasRole } from 'commons/helpers/user.helper';
import config from 'config';
import { filter, isEmpty, isNil } from 'lodash';
import { Span } from 'nestjs-ddtrace';
import { PolloMessage } from 'pollo-logger/classes/pollo.message';
import { IsNull } from 'typeorm';

@Injectable()
export class UserAccessReviewSynchronizationService
    extends BaseSynchronizationService
    implements ISynchronization
{
    constructor(
        private readonly provider: ApiClientService,
        private readonly companiesCoreService: CompaniesCoreService,
        private readonly connectionsCoreService: ConnectionsCoreService,
        private readonly vendorsOrchestrationService: VendorsOrchestrationService,
        private readonly usersCoreService: UsersCoreService,
        private readonly accessReviewApplicationCoreService: AccessReviewApplicationCoreService,
    ) {
        super();
    }

    getProviderType(): ProviderType {
        return ProviderType.USER_ACCESS_REVIEW;
    }

    getProvider(
        account: Account,
        connection: ConnectionEntity,
    ): Promise<IUserAccessReviewServices> {
        return this.provider.get(connection, account);
    }

    async willStartSynchronization(account: Account): Promise<void> {}

    didFinishSynchronization(account: Account): void {
        this.finalize(account);
    }

    finalize(account: Account): void {}

    protected async runSync(account: Account, connections?: ConnectionEntity[]): Promise<void> {
        const isFeatureEnabled =
            await this.accessReviewApplicationCoreService.isFeatureEnabled(account);

        if (!isFeatureEnabled) {
            this.log(
                `Entitlement for access reviews is not enabled for '${account.domain}'`,
                account,
            );
            return;
        }

        await this.willStartSynchronization(account);

        const company = await this.companiesCoreService.getCompanyByAccountId(account.id);

        const benchmark = new Benchmark();
        const conns = await this.getConnectionsByProviderType(account, connections);

        this.logSyncStart(account, conns);

        for await (const conn of conns) {
            await this.syncConnection(account, this.connectionsCoreService, company, conn);
        }

        this.logSyncFinish(account, conns, benchmark);
    }

    @Span()
    private async syncConnection(
        account: Account,
        connectionsCoreService: ConnectionsCoreService,
        company: Company,
        connection: ConnectionEntity,
    ): Promise<void> {
        const connectionBenchmark = new Benchmark();
        const logData = this.getLogData(connection);

        let uarApi: IUserAccessReviewServices | null = null;

        try {
            const isConnectionAllowed =
                await this.accessReviewApplicationCoreService.isConnectionAllowed(
                    account,
                    connection,
                );

            if (!isConnectionAllowed) {
                this.log(
                    // prettier-ignore
                    `Access review is not allowed for '${ClientType[connection.clientType]}' clientType`,
                    account,
                    logData,
                );
                return;
            }

            const apiProvider = await this.getProvider(account, connection);
            uarApi = apiProvider;

            const shouldRunUserSync = await this.shouldRunUserSync(
                apiProvider,
                connectionsCoreService,
                connection,
            );

            if (!shouldRunUserSync) {
                this.log(
                    `cannot sync ${logData.providerType} for ${logData.clientType}: api.shouldRunUserSync returned false`,
                    account,
                    logData,
                );
                return;
            }

            await this.logConnectionSyncStart(
                account,
                connection,
                connectionBenchmark,
                LogIdentifierRecordType.ACCESS_REVIEW_APP_USER,
            );

            await this.disconnectUserIdentityFromConnection(account, connection, logData);

            const accessApplication = await this.getDirectAccessApplicationByClientType(
                account,
                connection.clientType,
            );

            const accessReviewPeriodApplication = await this.getAccessPeriodApplicationById(
                accessApplication.id,
            );

            for await (const batchItems of batch(
                paginateItems(nextPageToken =>
                    this.getUserIdentities(
                        account,
                        connection,
                        apiProvider,
                        !isNil(company?.multiDomain),
                        logData,
                        nextPageToken,
                    ),
                ),
                2,
            )) {
                await Promise.all(
                    batchItems.map((identityUser: IUserAccessReviewServiceUser) =>
                        this.processUserIdentity(
                            account,
                            connection,
                            accessApplication,
                            accessReviewPeriodApplication,
                            identityUser,
                            logData,
                        ),
                    ),
                );
            }

            await this.syncThirdPartyApplications(
                account,
                connection,
                apiProvider,
                !isNil(company?.multiDomain),
                logData,
            );
            await this.processAccessReviewGroups(account, connection, accessApplication, logData);

            await this.logConnectionSyncSuccess(
                account,
                connection,
                connectionBenchmark,
                LogIdentifierRecordType.ACCESS_REVIEW_APP_USER,
            );
        } catch (error) {
            await this.logConnectionSyncFailure(
                error,
                account,
                connection,
                connectionBenchmark,
                LogIdentifierRecordType.ACCESS_REVIEW_APP_USER,
            );
        } finally {
            this.stopObserverIfImplemented(uarApi);
        }
    }

    private async getConnectionsByProviderType(
        account: Account,
        connections?: ConnectionEntity[],
    ): Promise<ConnectionEntity[]> {
        try {
            if (!isNil(connections) && !isEmpty(connections)) {
                return this.filterOutCsvConnection(account, connections);
            }

            connections = await this.connectionsCoreService.getConnectionsByProviderType(
                this.getProviderType(),
            );

            return this.filterOutCsvConnection(account, connections);
        } catch (error) {
            this.error(error, account, {
                method: this.getConnectionsByProviderType.name,
                provider: ProviderType[this.getProviderType()],
            });
            return [];
        }
    }

    private async getDirectAccessApplicationByClientType(
        account: Account,
        clientType: ClientType,
    ): Promise<AccessApplication> {
        try {
            return await this.accessApplicationRepository.findOneOrFail({
                where: {
                    clientType: clientType,
                    source: ApplicationSource.DIRECT_CONNECTION,
                },
            });
        } catch {
            return this.accessApplicationRepository.createApplicationByClientType(clientType);
        }
    }

    private async getAccessPeriodApplicationById(
        accessApplicationId: number,
    ): Promise<AccessReviewPeriodApplication | null> {
        return this.accessReviewPeriodApplicationRepository.getAccessPeriodApplicationById(
            accessApplicationId,
        );
    }

    private async getUserIdentities(
        account: Account,
        connection: ConnectionEntity,
        apiProvider: IUserAccessReviewServices,
        hasMultiDomain: boolean,
        logData: any,
        nextPageToken: string | null,
    ) {
        try {
            this.log(`Fetching users with page token '${nextPageToken}'`, account, logData);

            const response = await apiProvider.getUserIdentities(
                account,
                config.get('sync.maxResults'),
                nextPageToken,
                null,
                hasMultiDomain,
            );

            this.logConnectionSyncProgress(
                account,
                connection,
                LogIdentifierRecordType.ACCESS_REVIEW_APP_USER,
                {
                    records: response?.data?.length ?? 0,
                },
            );

            return tokenize(
                response.data,
                !isNil(response.token) && !isEmpty(response.token) ? response.token : null,
            );
        } catch (error) {
            if (error.response?.status !== HttpStatus.UNAUTHORIZED) {
                this.error(error, account, {
                    method: this.getUserIdentities.name,
                    ...logData,
                    nextPageToken,
                });
            }
            return tokenize([], null);
        }
    }

    private async getThirdPartyApplications(
        account: Account,
        connection: ConnectionEntity,
        apiProvider: IAccessReviewService,
        logData: any,
        nextPageToken: string | null,
    ) {
        try {
            this.log(`Fetching applications with page token '${nextPageToken}'`, account, logData);

            const response = await apiProvider.getApplications(
                account.domain,
                config.get('sync.maxResults'),
                nextPageToken,
            );

            this.logConnectionSyncProgress(
                account,
                connection,
                LogIdentifierRecordType.ACCESS_REVIEW_APP,
                {
                    records: response?.data?.length,
                },
            );

            const thirdPartyApplications = response?.data ?? [];

            const accessApps =
                await this.accessReviewApplicationCoreService.savePartnerApplications(
                    account,
                    connection,
                    thirdPartyApplications,
                );

            await this.oktaApplicationVendorDiscovered(
                account,
                connection,
                thirdPartyApplications,
                logData,
            );

            return tokenize(
                accessApps,
                !isNil(response.token) && !isEmpty(response.token) ? response.token : null,
            );
        } catch (error) {
            this.error(error, account, {
                method: this.getThirdPartyApplications.name,
                ...logData,
                nextPageToken,
            });

            return tokenize([], null);
        }
    }

    private async getThirdPartyApplicationUsers(
        account: Account,
        connection: ConnectionEntity,
        apiProvider: IAccessReviewService,
        application: AccessApplication,
        logData: any,
        nextPageToken: string | null,
    ) {
        try {
            this.log(
                `Fetching application users for application '${application.name}' with page token '${nextPageToken}'`,
                account,
                logData,
            );

            const externalId = application.externalId ?? '';
            if (isEmpty(externalId)) {
                this.log(
                    `No externalId found for application '${application.name}'`,
                    account,
                    logData,
                );
                return {
                    data: [],
                    nextPageToken: null,
                };
            }

            const response = await apiProvider.getApplicationUsers(
                account.domain,
                config.get('sync.maxResults'),
                externalId,
                nextPageToken,
            );

            this.logConnectionSyncProgress(
                account,
                connection,
                LogIdentifierRecordType.ACCESS_REVIEW_APP_USER,
                {
                    records: response?.data?.length,
                },
            );

            return tokenize(
                response.data,
                !isNil(response.token) && !isEmpty(response.token) ? response.token : null,
            );
        } catch (error) {
            this.error(error, account, {
                method: this.getThirdPartyApplicationUsers.name,
                ...logData,
                nextPageToken,
            });

            return tokenize([], null);
        }
    }

    private async getThirdPartyApplicationGroups(
        account: Account,
        connection: ConnectionEntity,
        apiProvider: IAccessReviewService,
        thirdPartyApp: AccessApplication,
        logData: any,
        nextPageToken: string | null,
    ) {
        try {
            this.log(
                `Fetching groups for application '${thirdPartyApp.name}' with page token '${nextPageToken}'`,
                account,
                logData,
            );

            const response = await apiProvider.getApplicationGroups(
                account.domain,
                config.get('sync.maxResults'),
                thirdPartyApp.externalId ?? '',
                nextPageToken,
            );

            this.logConnectionSyncProgress(
                account,
                connection,
                LogIdentifierRecordType.ACCESS_REVIEW_APP_GROUP,
                {
                    records: response?.data?.length,
                    context: logData,
                },
            );

            return tokenize(
                response.data,
                !isNil(response.token) && !isEmpty(response.token) ? response.token : null,
            );
        } catch (error) {
            this.error(error, account, {
                method: this.getThirdPartyApplicationGroups.name,
                ...logData,
                nextPageToken,
            });
            return tokenize([], null);
        }
    }

    private async getAccessApplicationGroupMembers(
        account: Account,
        connection: ConnectionEntity,
        apiService: IAccessReviewService,
        group: IdentityGroup,
        logData: any,
        thirdPartyApp: AccessApplication,
        nextPageToken: string | null,
        isMultiDomain: boolean,
    ) {
        try {
            this.log(
                `Fetching application group members for group '${group.name}' with page token ${nextPageToken}`,
                account,
                logData,
            );

            const response = await apiService.getApplicationGroupMembers(
                account.domain,
                config.get('sync.maxResults'),
                group,
                thirdPartyApp,
                nextPageToken,
                isMultiDomain,
            );

            this.logConnectionSyncProgress(
                account,
                connection,
                LogIdentifierRecordType.ACCESS_REVIEW_APP_GROUP_MEMBER,
                {
                    records: response?.data?.length,
                    context: logData,
                },
            );

            return tokenize(
                response.data,
                !isNil(response.token) && !isEmpty(response.token) ? response.token : null,
            );
        } catch (error) {
            this.error(error, account, {
                method: this.getAccessApplicationGroupMembers.name,
                ...logData,
                nextPageToken,
            });
            return tokenize([], null);
        }
    }

    @Span()
    private async processAccessReviewGroups(
        account: Account,
        connection: ConnectionEntity,
        accessApplication: AccessApplication,
        logData: any,
    ) {
        try {
            this.log('Processing group access data', account, logData);

            await this.accessReviewApplicationCoreService.processUserGroupsAccessData(
                account,
                connection,
                accessApplication,
            );
        } catch (error) {
            this.logger.error(
                PolloAdapter.acct(
                    `Error syncing UAR identities groups access data: ${error.message}`,
                    account,
                )
                    .setSubContext(this.processAccessReviewGroups.name)
                    .setError(error)
                    .setIdentifier(logData),
            );
        }
    }

    @Span()
    private async syncThirdPartyApplications(
        account: Account,
        connection: ConnectionEntity,
        apiService: IApiServices,
        isMultiDomain: boolean,
        logData: any,
    ): Promise<void> {
        if (!this.instanceOfIAccessReviewService(apiService)) {
            return;
        }

        try {
            const accessReviewService = apiService as IAccessReviewService;

            this.log('Processing access review third party applications', account, logData);

            await this.accessReviewApplicationCoreService.disconnectThirdPartyApplications(
                connection,
            );

            for await (const batchItems of batch(
                paginateItems(nextPageToken =>
                    this.getThirdPartyApplications(
                        account,
                        connection,
                        accessReviewService,
                        logData,
                        nextPageToken,
                    ),
                ),
                2,
            )) {
                await Promise.all(
                    batchItems.map((app: AccessApplication) =>
                        this.processThirdPartyApplication(
                            account,
                            connection,
                            accessReviewService,
                            app,
                            isMultiDomain,
                            logData,
                        ),
                    ),
                );
            }

            const totalDeleted =
                await this.accessReviewApplicationCoreService.removeThirdPartyReviewPeriodApplications(
                    connection,
                );

            this.log(`Removing ${totalDeleted} review period applications`, account, logData);
        } catch (error) {
            this.error(error, account, {
                method: this.syncThirdPartyApplications.name,
                provider: ProviderType[this.getProviderType()],
                ...logData,
            });
        }
    }

    private async processThirdPartyApplication(
        account: Account,
        connection: ConnectionEntity,
        apiService: IAccessReviewService,
        thirdPartyApp: AccessApplication,
        isMultiDomain: boolean,
        logData: any,
    ): Promise<void> {
        logData = {
            ...logData,
            applicationId: thirdPartyApp.id,
            applicationName: thirdPartyApp.name,
            applicationExternalId: thirdPartyApp.externalId,
            applicationSource: ApplicationSource[thirdPartyApp.source],
        };

        try {
            const accessReviewPeriodApplication = await this.getAccessPeriodApplicationById(
                thirdPartyApp.id,
            );

            await this.accessReviewApplicationCoreService.softDeleteAllPeriodApplicationUsers(
                accessReviewPeriodApplication,
            );

            await this.accessReviewApplicationCoreService.disconnectPartnerApplicationUsers(
                connection,
                [thirdPartyApp],
            );

            this.log(`Fetching users for application '${thirdPartyApp.name}'`, account, logData);

            for await (const batchItems of batch(
                paginateItems(nextPageToken =>
                    this.getThirdPartyApplicationUsers(
                        account,
                        connection,
                        apiService,
                        thirdPartyApp,
                        logData,
                        nextPageToken,
                    ),
                ),
                2,
            )) {
                await Promise.all(
                    batchItems.map((thirdPartyAppUser: IThirdPartyAppUser) =>
                        this.processThirdPartyAppUser(
                            account,
                            connection,
                            thirdPartyApp,
                            accessReviewPeriodApplication,
                            thirdPartyAppUser,
                            logData,
                        ),
                    ),
                );
            }

            await this.removeThirdPartyApplicationGroups(account, thirdPartyApp.id);

            this.log(`Fetching groups for application '${thirdPartyApp.name}'`, account, logData);
            for await (const batchItems of batch(
                paginateItems(nextPageToken =>
                    this.getThirdPartyApplicationGroups(
                        account,
                        connection,
                        apiService,
                        thirdPartyApp,
                        logData,
                        nextPageToken,
                    ),
                ),
                2,
            )) {
                await Promise.all(
                    batchItems.map((group: IdentityGroup) =>
                        this.syncAccessApplicationGroupMembers(
                            account,
                            connection,
                            apiService,
                            thirdPartyApp,
                            accessReviewPeriodApplication,
                            group,
                            isMultiDomain,
                            logData,
                        ),
                    ),
                );
            }
        } catch (error) {
            this.error(error, account, {
                method: this.processThirdPartyApplication.name,
                provider: ProviderType[this.getProviderType()],
                ...logData,
            });
        }
    }

    private async syncAccessApplicationGroupMembers(
        account: Account,
        connection: ConnectionEntity,
        apiService: IAccessReviewService,
        thirdPartyApp: AccessApplication,
        accessReviewPeriodApplication: AccessReviewPeriodApplication | null,
        group: IdentityGroup,
        isMultiDomain: boolean,
        logData: any,
    ): Promise<void> {
        logData = {
            ...logData,
            groupId: group.externalId,
            groupName: group.name,
            groupDescription: group.description,
        };
        try {
            for await (const batchItems of batch(
                paginateItems(nextPageToken =>
                    this.getAccessApplicationGroupMembers(
                        account,
                        connection,
                        apiService,
                        group,
                        logData,
                        thirdPartyApp,
                        nextPageToken,
                        isMultiDomain,
                    ),
                ),
                2,
            )) {
                await Promise.all(
                    batchItems.map(async (applicationGroupUsers: IThirdPartyAppUser) => {
                        await this.processThirdPartyAppUser(
                            account,
                            connection,
                            thirdPartyApp,
                            accessReviewPeriodApplication,
                            applicationGroupUsers,
                            logData,
                        );
                        await this.processThirdPartyAppUserGroup(
                            account,
                            connection,
                            thirdPartyApp,
                            applicationGroupUsers,
                            group,
                            logData,
                        );
                    }),
                );
            }
        } catch (error) {
            this.error(error, account, {
                method: this.syncAccessApplicationGroupMembers.name,
                provider: ProviderType[this.getProviderType()],
                ...logData,
            });
        }
    }

    @Span()
    private async processUserIdentity(
        account: Account,
        connection: ConnectionEntity,
        accessApplication: AccessApplication,
        accessReviewPeriodApplication: AccessReviewPeriodApplication | null,
        userIdentity: IUserAccessReviewServiceUser,
        logData: any,
    ): Promise<void> {
        const logIdentifier = this.formatIdentifier(userIdentity);
        logData = {
            ...logData,
            userIdentityId: userIdentity.getId(),
            userIdentityEmail: userIdentity.getEmail(),
        };

        try {
            let identity = await this.userIdentityRepository.findOne({
                where: {
                    identityId: userIdentity.getId(),
                    connection: { id: connection.id },
                },
            });

            const email = userIdentity.getEmail() ?? identity?.email;
            if (!isEmpty(email) && !isEmail(email)) {
                this.log(
                    `Identity with incorrect format email detected. Skipping saving process for: ${logIdentifier}`,
                    account,
                    logData,
                );
                return;
            }

            if (isNil(identity)) {
                identity = new UserIdentity();
                identity.identityId = userIdentity.getId();
                identity.connection = connection;
                if (userIdentity.isServiceAccount()) {
                    identity.serviceAccount = new Date();
                    identity.serviceAccountReason =
                        'It was identified as a service account during the UAR sync process.';
                }
            }
            identity.lastCheckedAt = new Date();
            identity.email = email;
            identity.username = userIdentity.getUsername() ?? identity.username ?? identity.email;

            if (isEmpty(identity.username)) {
                this.logger.warn(
                    PolloMessage.msg(
                        `Identity username is null or empty for: ${identity.identityId}`,
                    )
                        .setDomain(account.domain)
                        .setContext(this.constructor.name)
                        .setSubContext(this.processUserIdentity.name)
                        .setIdentifier(logData),
                );
                // NOTE: ensuring that the username is null instead of an empty string.
                identity.username = null;
            }

            if (isEmpty(identity.email)) {
                this.logger.warn(
                    PolloMessage.msg(`Identity email is null or empty for: ${identity.identityId}`)
                        .setDomain(account.domain)
                        .setContext(this.constructor.name)
                        .setSubContext(this.processUserIdentity.name)
                        .setIdentifier(logData),
                );
                // NOTE: ensuring that the email is null instead of an empty string.
                identity.email = null;
            }

            if (isNil(identity.hasMfa)) {
                identity.hasMfa = userIdentity.hasMultiFactorAuthentication();
            }

            if (isNil(identity.user)) {
                const user = await this.attemptMatchUser(
                    account,
                    userIdentity,
                    !connectionNeedsFullUARSync(connection),
                );
                if (!isNil(user)) {
                    identity.user = user;
                    identity.connectedAt = new Date();
                }
            }

            if (connectionNeedsFullUARSync(connection)) {
                identity.disconnectedAt = userIdentity.isActive() ? null : new Date();
            }

            if (isNil(identity.id)) {
                this.log(`Creating ${logIdentifier}`, account, logData);
            } else {
                this.log(`Updating ${logIdentifier}`, account, logData);
            }

            await this.userIdentityRepository.save(identity);

            await this.saveApplicationUserAccess(
                account,
                accessApplication,
                accessReviewPeriodApplication,
                identity,
                userIdentity,
                logIdentifier,
                logData,
            );
        } catch (error) {
            this.error(error, account, {
                method: this.processUserIdentity.name,
                ...logData,
            });
        }
    }

    private async processThirdPartyAppUserGroup(
        account: Account,
        connection: ConnectionEntity,
        accessApplication: AccessApplication,
        thirdPartyAppUser: IThirdPartyAppUser,
        group: IdentityGroup,
        logData: any,
    ) {
        logData = {
            ...logData,
            thirdPartyAppUserId: thirdPartyAppUser.getId(),
            thirdPartyAppUserEmail: thirdPartyAppUser.getPrimaryEmail(),
        };

        try {
            const identity =
                await this.userIdentityRepository.getUserIdentityByThirdPartyAppAndConnection(
                    thirdPartyAppUser.getId(),
                    connection.id,
                    accessApplication.id,
                );

            if (isNil(identity) || isNil(group.name)) {
                return;
            }

            const applicationUserAccess = identity.applicationUserAccessList?.find(
                a => a.accessApplicationId === accessApplication.id,
            );

            if (isNil(applicationUserAccess)) {
                return;
            }

            let groupToAdd: ApplicationGroup;

            const applicationGroup = await this.applicationGroupRepository.findOne({
                where: {
                    accessApplicationId: accessApplication.id,
                    name: group.name,
                },
                relations: ['userIdentities'],
            });

            if (isNil(applicationGroup)) {
                const newGroup = new ApplicationGroup();
                newGroup.accessApplicationId = accessApplication.id;
                newGroup.connectionId = connection.id;
                newGroup.name = group.name;

                groupToAdd = await this.applicationGroupRepository.save(newGroup);
            } else {
                groupToAdd = applicationGroup;
            }

            const uniqueUserIdentities =
                groupToAdd.userIdentities?.filter(
                    userIdentity => userIdentity.id !== identity.id,
                ) ?? [];

            groupToAdd.userIdentities = [...uniqueUserIdentities, identity];

            applicationUserAccess.accessData.groups = [
                ...(applicationUserAccess.accessData.groups ?? []),
                groupToAdd.name,
            ];

            await this.applicationUserAccessRepository.update(applicationUserAccess.id, {
                accessData: applicationUserAccess.accessData,
            });

            await this.applicationGroupRepository.save(groupToAdd);
        } catch (error) {
            this.error(error, account, {
                method: this.processThirdPartyAppUser.name,
                ...logData,
            });
        }
    }

    private async processThirdPartyAppUser(
        account: Account,
        connection: ConnectionEntity,
        accessApplication: AccessApplication,
        accessReviewPeriodApplication: AccessReviewPeriodApplication | null,
        thirdPartyAppUser: IThirdPartyAppUser,
        logData: any,
    ): Promise<void> {
        logData = {
            ...logData,
            thirdPartyAppUserId: thirdPartyAppUser.getId(),
            thirdPartyAppUserEmail: thirdPartyAppUser.getPrimaryEmail(),
        };
        try {
            const identity = await this.userIdentityRepository.findOne({
                where: {
                    identityId: thirdPartyAppUser.getId(),
                    connection: { id: connection.id },
                },
            });

            if (isNil(identity)) {
                this.log(
                    `Identity not found for third party app user ${thirdPartyAppUser.getId()}`,
                    account,
                    { ...logData, connectionId: connection.id },
                );
                return;
            }

            await this.saveApplicationUserAccess(
                account,
                accessApplication,
                accessReviewPeriodApplication,
                identity,
                thirdPartyAppUser,
                this.formatThirdPartyAppUserIdentifier(thirdPartyAppUser),
                logData,
            );
        } catch (error) {
            this.error(error, account, {
                method: this.processThirdPartyAppUser.name,
                ...logData,
            });
        }
    }

    private async saveApplicationUserAccess(
        account: Account,
        accessApplication: AccessApplication,
        accessReviewPeriodApplication: AccessReviewPeriodApplication | null,
        identity: UserIdentity,
        userIdentity: IUserAccessReviewServiceUser | IThirdPartyAppUser,
        logIdentifier: string,
        logData: any,
    ): Promise<void> {
        let applicationUserAccess = await this.applicationUserAccessRepository.findOne({
            where: {
                accessApplicationId: accessApplication.id,
                userIdentityId: identity.id,
            },
            withDeleted: true,
        });

        if (isNil(applicationUserAccess)) {
            applicationUserAccess = new ApplicationUserAccess();
            applicationUserAccess.application = accessApplication;
            applicationUserAccess.userIdentity = identity;
        }
        applicationUserAccess.deletedAt = userIdentity.hasUARAccessTerminated() ? new Date() : null;
        applicationUserAccess.hasAdminAccess = userIdentity.hasAdminAccess() ?? false;
        applicationUserAccess.accessData = cleanAccessReviewDataRaw(
            userIdentity.getAccessData() ?? {},
        );

        if (isNil(applicationUserAccess.id)) {
            this.log(`Creating ApplicationUserAccess for ${logIdentifier}`, account, logData);
        } else {
            this.log(`Updating ApplicationUserAccess for ${logIdentifier}`, account, logData);
        }

        await this.applicationUserAccessRepository.save(applicationUserAccess);

        if (!isNil(accessReviewPeriodApplication)) {
            await this.saveAccessReviewPeriodApplicationUser(
                identity,
                accessReviewPeriodApplication,
                applicationUserAccess,
                logIdentifier,
                account,
                logData,
            );
        }
    }

    private async saveAccessReviewPeriodApplicationUser(
        identity: UserIdentity,
        accessReviewPeriodApplication: AccessReviewPeriodApplication,
        applicationUserAccess: ApplicationUserAccess,
        logIdentifier: string,
        account: Account,
        logData: any,
    ) {
        let periodApplicationUser = await this.accessReviewPeriodApplicationUserRepository.findOne({
            where: {
                userIdentity: { id: identity.id },
                reviewApplicationId: accessReviewPeriodApplication.id,
            },
            withDeleted: true,
        });

        if (isNil(periodApplicationUser)) {
            periodApplicationUser = new AccessReviewPeriodApplicationUser();
            periodApplicationUser.status = ApplicationUserStatus.NOT_REVIEWED;
            periodApplicationUser.userIdentity = identity;
            periodApplicationUser.initialAccessData = applicationUserAccess.accessData;
            periodApplicationUser.initialJobTitle = identity.user?.jobTitle;
            periodApplicationUser.reviewApplication = accessReviewPeriodApplication;
        }
        periodApplicationUser.deletedAt = null;

        if (isNil(periodApplicationUser.id)) {
            this.log(
                `Creating AccessReviewPeriodApplicationUser for ${logIdentifier}`,
                account,
                logData,
            );
        } else {
            this.log(
                `Updating AccessReviewPeriodApplicationUser for ${logIdentifier}`,
                account,
                logData,
            );
        }

        await this.accessReviewPeriodApplicationUserRepository.save(periodApplicationUser);
    }

    private async oktaApplicationVendorDiscovered(
        account: Account,
        connection: ConnectionEntity,
        applications: IThirdPartyApp[],
        logData: any,
    ) {
        if (connection.clientType !== ClientType.OKTA_IDENTITY || isEmpty(applications)) {
            return;
        }

        logData = {
            ...logData,
            applications: applications.map(app => ({
                id: app.getId(),
                name: app.getName(),
                domain: app.getDomain(),
            })),
        };

        try {
            this.log('processing okta applications vendor to discover', account, logData);

            const results = await Promise.allSettled(
                applications.map(async app => {
                    const sharedAccount =
                        await this.vendorsOrchestrationService.validateSharedAccountFromDomain(
                            app.getDomain(),
                            DEFAULT_TC_WORKSPACE_ID,
                        );

                    return {
                        domain: app.getDomain(),
                        externalId: app.getId(),
                        isDrataUser: !isNil(app.getDomain()) && !isNil(sharedAccount),
                    };
                }),
            );

            const vendorsToDiscover = results
                .filter(result => result.status === 'fulfilled')
                .map(
                    (fulfilledPromise: PromiseFulfilledResult<VendorToDiscover>) =>
                        fulfilledPromise.value,
                );

            this.log(`VendorToDiscover results ${results?.length}`, account, logData);

            if (vendorsToDiscover.length !== results.length) {
                for (const result of results) {
                    if (result.status === 'rejected') {
                        this.error(
                            new TypeError(
                                `Error while creating vendor discovered: ${result.reason}`,
                            ),
                            account,
                            logData,
                        );
                    }
                }
            }

            await this.vendorsOrchestrationService.createVendorsDiscovered(
                account,
                vendorsToDiscover,
                ClientType.OKTA_IDENTITY,
            );

            this.log(`VendorToDiscover fulfilled ${vendorsToDiscover?.length}`, account, logData);
        } catch (error) {
            this.error(error, account, {
                method: this.oktaApplicationVendorDiscovered.name,
                ...logData,
            });
        }
    }

    @Span()
    private async disconnectUserIdentityFromConnection(
        account: Account,
        connection: ConnectionEntity,
        logData: any,
    ): Promise<void> {
        if (!connectionNeedsFullUARSync(connection)) {
            return;
        }

        const { affected } = await this.userIdentityRepository.update(
            {
                connection: { id: connection.id },
                disconnectedAt: IsNull(),
                deletedAt: IsNull(),
            },
            { disconnectedAt: new Date() },
        );

        this.log(`Disconnected '${affected}' User Access Review identities`, account, logData);
    }

    private async attemptMatchUser(
        account: Account,
        userIdentity: IUserAccessReviewServiceUser,
        onlyByEmail = false,
    ): Promise<User | null> {
        let user: User | null = null;

        // sanity check in case identityUser does not have email
        const fuzzyEmail = userIdentity.getEmail()?.split('@')[0] ?? '';

        const idpUser = {
            id: userIdentity.getId(),
            firstName: userIdentity.getFirstName(),
            lastName: userIdentity.getLastName(),
            email: userIdentity.getEmail(),
            username: userIdentity.getUsername(),
            fuzzyEmail,
        };

        if (!isNil(idpUser.email)) {
            user = await this.usersCoreService.getUserByEmailNoFail(idpUser.email);
            if (!isNil(user) && !hasRole(user, [Role.SERVICE_USER])) {
                this.log("Found user by email, let's use it", account, {
                    idpUser,
                    user,
                });
                return user;
            }
        }

        if (onlyByEmail) {
            return null;
        }

        if (!isNil(idpUser.fuzzyEmail)) {
            user = await this.usersCoreService.getUserByFuzzyEmailNoFail(idpUser.fuzzyEmail);
            if (!isNil(user) && !hasRole(user, [Role.SERVICE_USER])) {
                this.log("Found user by fuzzy email, let's use it", account, {
                    idpUser,
                    user,
                });
                return user;
            }
        }

        if (!isNil(idpUser.username)) {
            user = await this.usersCoreService.getUserByFuzzyEmailNoFail(idpUser.username);
            if (!isNil(user) && !hasRole(user, [Role.SERVICE_USER])) {
                this.log("Found user by username, let's use it", account, {
                    idpUser,
                    user,
                });
                return user;
            }
        }

        this.log('No user found', account, {
            idpUser,
        });

        return null;
    }

    private filterOutCsvConnection(
        account: Account,
        connections: ConnectionEntity[],
    ): ConnectionEntity[] {
        return filter(connections, conn => conn.clientType !== ClientType.UAR_CSV);
    }

    private instanceOfIAccessReviewService(apiService: any): apiService is IAccessReviewService {
        return (
            !isNil(apiService?.getApplications) &&
            !isNil(apiService?.getApplicationUsers) &&
            !isNil(apiService?.getApplicationGroups) &&
            !isNil(apiService?.getApplicationGroupMembers)
        );
    }
    private formatIdentifier(identityUser: IUserAccessReviewServiceUser): string {
        return `email: ${identityUser.getEmail()}, username: '${identityUser.getUsername()}', userIdentityId: ${identityUser.getId()}`;
    }

    private formatThirdPartyAppUserIdentifier(identityUser: IThirdPartyAppUser): string {
        return `email: ${identityUser.getPrimaryEmail()}, userIdentityId: ${identityUser.getId()}`;
    }

    private getLogData(connection: ConnectionEntity) {
        return {
            providerType: ProviderType[this.getProviderType()],
            clientType: ClientType[connection.clientType],
            connectionId: connection?.id,
        };
    }

    private async removeThirdPartyApplicationGroups(account: Account, accessApplicationId: number) {
        return this.applicationGroupRepository.softDelete({ accessApplicationId });
    }

    private get accessApplicationRepository(): AccessApplicationRepository {
        return this.getCustomTenantRepository(AccessApplicationRepository);
    }

    private get accessReviewPeriodApplicationRepository(): AccessReviewPeriodApplicationRepository {
        return this.getCustomTenantRepository(AccessReviewPeriodApplicationRepository);
    }

    private get userIdentityRepository(): UserIdentityRepository {
        return this.getCustomTenantRepository(UserIdentityRepository);
    }

    private get applicationGroupRepository(): ApplicationGroupRepository {
        return this.getCustomTenantRepository(ApplicationGroupRepository);
    }

    private get applicationUserAccessRepository(): ApplicationUserAccessRepository {
        return this.getCustomTenantRepository(ApplicationUserAccessRepository);
    }

    private get accessReviewPeriodApplicationUserRepository(): AccessReviewPeriodApplicationUserRepository {
        return this.getCustomTenantRepository(AccessReviewPeriodApplicationUserRepository);
    }
}
