---
auto_deploy: true
context: release-rl4vgs
domain: envs.drata.net
repo_name: drata/api
rules:
  - service: api
    hostnames:
      - api-${env_id}.${domain}
    path: "/"
    visibility: private
  - service: api-public
    hostnames:
      - api-public-${env_id}.${domain}
    path: "/"
    visibility: private
  - service: temporal-ui
    hostnames:
      - temporal-${env_id}.${domain}
    path: "/"
    visibility: private
service_accounts:
  - name: drata-api
    cloud_role: arn:aws:iam::************:role/dt-ephemeral-envs-relesehub-iam-role
environment_templates:
  - name: ephemeral
    datasets:
      - name: drata-global-and-tenant
  - name: permanent
    datasets:
      - name: drata-global-and-tenant
app_imports:
  - name: admin
  - name: web
  - name: multiverse
parallelize_app_imports: true
resources:
  cpu:
  memory:
    limits: 6Gi
    requests: 4Gi
  replicas: 1
services:
  - name: api
    has_repo: true
    build:
      dockerfile: Dockerfile
      args:
        - RELEASE_BUILD=true
    static: false
    args:
      - "/usr/local/bin/startup.sh"
    service_account_name: drata-api
    depends_on:
      - redis
      - temporal
    ports:
      - type: node_port
        port: "3000"
        loadbalancer: false
  - name: waas-worker
    has_repo: true
    build:
      dockerfile: Dockerfile
      args:
        - RELEASE_BUILD=true
    static: false
    args:
      - node
      - dist/app/worker/main.js
    service_account_name: drata-api
    depends_on:
      - redis
      - temporal
  - name: api-public
    has_repo: true
    build:
      dockerfile: Dockerfile
      args:
        - RELEASE_BUILD=true
    static: false
    args:
      - "/usr/local/bin/startup.sh"
    service_account_name: drata-api
    depends_on:
      - redis
    ports:
      - type: node_port
        port: "3000"
        loadbalancer: false
  - name: redis
    image: redis:7.0
    has_repo: false
    volumes:
      - name: redisdata
        type: persistent
        mount_path: "/data"
    ports:
      - type: node_port
        target_port: "6379"
        port: "6379"
        loadbalancer: false
    storage:
      size: 20Gi
      type: aws-efs
  - name: temporal-mysql
    image: mysql:8.0.32
    stateful: true
    has_repo: false
    ports:
      - type: container_port
        port: "3306"
    volumes:
      - type: persistent
        name: temporal-mysql-data
        mount_path: "/var/lib/mysql"
    storage:
      size: 10Gi
      type: aws-efs
  - name: temporal
    image: temporalio/auto-setup:1.27.1
    has_repo: false
    depends_on:
      - temporal-mysql
    ports:
      - type: node_port
        target_port: "7233"
        port: "7233"
        loadbalancer: false
  - name: temporal-ui
    image: temporalio/ui:2.36.0
    has_repo: false
    depends_on:
      - temporal
    ports:
      - type: node_port
        target_port: "8080"
        port: "8080"
        loadbalancer: false
workflows:
  - name: setup
    parallelize:
      - step: services-gitops-0
        tasks:
          - services.redis
          - services.temporal-mysql
      - step: services-gitops-1
        tasks:
          - services.temporal
      - step: services-1
        tasks:
          - services.api
          - services.waas-worker
          - services.api-public
          - services.temporal-ui
          - jobs.setup_dns_in_db
          - jobs.setup_dns_api_pub
  - name: patch
    parallelize:
      - step: services-gitops-0
        tasks:
          - services.api
          - services.waas-worker
          - services.api-public
  - name: teardown
    parallelize:
      - step: remove-environment
        tasks:
          - release.remove_environment
jobs:
  - name: setup_dns_in_db
    command:
      - node
      - "/usr/src/app/dist/database/helpers/releasehub.helper.js"
    from_services: api
  - name: setup_dns_api_pub
    command:
      - node
      - "/usr/src/app/dist/database/helpers/releasehub.helper.js"
    from_services: api-public
