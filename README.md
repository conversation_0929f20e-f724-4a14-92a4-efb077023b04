# Drata

![alt text](https://cdn.drata.com/icon/icon_fwhite_bblue_128.png)

## Description

This is the Drata Rest API built on top of the [NestJS](https://nestjs.com) framework.

The API documentation runs in [Swagger](https://swagger.io) at the root of the API domain (per your environment).

## Dependencies

- [Git](https://git-scm.com) - Version Control Manager
- [NodeJS](https://nodejs.org) - Core server-side programming language
- [NVM](https://github.com/nvm-sh/nvm) - Node Version Manager
    - We run the [latest LTS version](https://nodejs.org/en/about/releases/) of Node, which is v12 until Oct. 20, 2020
- [Yarn](https://classic.yarnpkg.com) - Dependency management
    - We use `yarn` for dependency commands, do not use `npm` directly.
- [MySQL](https://www.mysql.com) - Core persistant data storage layer
    - There is an accessible database for development (see `config/default.yml`)
    - There is an accessible database for e2e tests (see `config/test.yml`)
- [Redis](https://redis.io) - Used for data caching
    - There is an accessible Redis server for development (see `config/default.yml`)
- [Docker](https://www.docker.com/) - Used for running stack in docker, including OpenSearch. Install Rancher from Kandji Self-Service

## Tech Stack

- [NodeJS](https://nodejs.org) - Core server-side programming language
- [NestJS](https://nestjs.com) - REST framework in NodeJS
    - Familiarize yourself with the [Docs](https://docs.nestjs.com)!!!
- [Yarn](https://classic.yarnpkg.com) - Dependency management
- [MySQL](https://www.mysql.com) - Core persistant data storage layer
- [TypeORM](https://typeorm.io) - ORM into the SQL layer
    - Familiarize yourself with the [Docs](https://typeorm.io)!!!
- [Redis](https://redis.io) - Used for data caching
- [AWS](https://aws.amazon.com) - Infrastructure
    - We use the NPM package [aws-sdk](https://www.npmjs.com/package/aws-sdk) to communicate to the AWS services.
    - For anything CLI driven, we use the [AWS CLI](https://aws.amazon.com/cli) which is a Python package.
- [Terraform](https://www.terraform.io) - Infrastructure as code
    - Please see [https://github.com/drata/terraform](https://github.com/drata/terraform)

## Installation

Make sure you've followed one of the laptop setup guides that walk through all the prerequisites:

- ### [macOS](https://www.notion.so/drata/Engineering-Laptop-Setup-macOS-2989402500a34ba1b6775a1edd5f1c12)
- ### [Linux - Ubuntu 18.04 | 20.04](https://www.notion.so/drata/Engineering-Laptop-Setup-Linux-0e9774958cfc4ce2bd1b823ce8e96d7c)

Once you have the code, install the dependencies...

```bash
$ corepack enable
$ yarn install
```

For local email testing install mailpit (modern replacement for deprecated MailHog), you can view emails at http://localhost:8025/

```bash
$ brew install mailpit
$ brew services start mailpit
```

For local testing of features that depend on OpenSearch infrastructure

```bash
$ docker-compose up opensearch opensearch-dashboards
```

Run next commands in your terminal to generate a file called localdev.gen.yml that has all the latest secrets.

```bash
# connect to aws
$ dev

# generate latest secrets file and replace current config
$ yarn localdev:build:replace
```

## Running the app

```bash
# clear out any previous cached code
$ yarn prebuild

# development
$ yarn start:swc
# Then go to http://localhost:3000

# production mode
$ yarn start:prod

# public api server (separate terminal)
$ yarn start:public:swc
```

## Seeding your local database

```bash
# First you need to log into aws cli using
$ dev

# This will only work on NON-PROD
# For engineers going through onboarding - nukeAndPave is the correct database seed to run
$ bin/drata-cli nukeAndPave
# Use to download aws latest backup:
$ bin/drata-cli nukeAndPave -s3
# Use or create local latest backup:
$ bin/drata-cli nukeAndPave -l

# Check more options:
$ bin/drata-cli nukeAndPave -h

# Seeding with no tenant
$ bin/drata-cli nukeAndPaveEmpty -s3

more info: https://www.notion.so/drata/nukeAndPave-cached-and-Custom-Backups-14824465681e4f28b971248c8f2c1de3
```

### Creating and Seeding OpenSearch Indexes

If you ran the nukeAndPave script above and noticed the following message `opensearch is disabled in configuration`, you will need to run the following commands:

```bash
# 1. Enable OpenSearch in config (if disabled)
# In api/config/localdev.yml, ensure: opensearch.enable: true
yq '.opensearch.enable=true' -i ~/projects/drata/api/config/localdev.yml

# 2. Start OpenSearch (if not already running)
docker-compose up opensearch opensearch-dashboards

# you can now either re-run `bin/drata-cli nukeAndPave` or run the following commands:

# 3. Run backfill to create indexes
./bin/drata-cli backfill search/backfill-create-opensearch-index-global --force-delete-recreate --all

# 4. Run tenant backfill to hydrate the monitoring index
./bin/drata-cli backfill search/backfill-monitoring-result-opensearch-index-tenant -ra

# 5. Run tenant backfill to hydrate the vendors index
./bin/drata-cli backfill search/backfill-vendors-opensearch-index -ra

# 6. Run tenant backfill to hydrate the vendor risks index
./bin/drata-cli backfill search/backfill-vendors-risks-opensearch-index -ra

# 7. Run tenant backfill to hydrate the risks index
./bin/drata-cli backfill search/backfill-risks-opensearch-index -ra
```

## Testing the app

For now, the team is focusing on End-to-end (e2e) testing.

```bash
# Unit tests
# Unit tests are NOT implemented at this time

# UI tests
# UI tests are NOT implemented at this time

# Run all e2e tests
$ yarn test:e2e

# Run e2e test on a suite file, for example vendors-get.e2e-spec.ts
yarn test:e2e vendors-get

# Run e2e test on one particular test
# for example:
# yarn test:e2e --testPathPattern=site-admin-framework-templates-suite-set/site-admin-framework-templates-list -t 'Anonymous users should not be able to list framework templates'
yarn test:e2e --testPathPattern=<test-suite-set-folder>/<test-suite-file> -t 'test description'
# test coverage
$ yarn test:cov
```

## Development

Please review the [Drata development playbook](https://github.com/drata/playbook) to review development guidelines.

### Debugging the app

In VSCode, under the `Run` menu item, select `Start Debugging` or hit `F5` on your keyboard.

This will start the application in debug mode where you can add breakpoints, and step over the code line by line.

You'll see the debug console via the integrated terminal (On macOS use Control + \` to open it)

### Logging

For guidance on logging HTTP Requests and Responses, see [How to: api http request and response logging](https://www.notion.so/drata/How-to-api-HTTP-Request-Logging-cd8be87dab7a45908ceff7cc1eba204f?pvs=4) and [config/default.yml](config/default.yml)

### VSCode Extensions

Make sure you have the following plugins installed

- [TypeScript Extension Pack](https://marketplace.visualstudio.com/items?itemName=loiane.ts-extension-pack)
- [Todo Tree](https://marketplace.visualstudio.com/items?itemName=Gruntfuggly.todo-tree)
- [REST Client](https://marketplace.visualstudio.com/items?itemName=humao.rest-client)
- [GitLens](https://marketplace.visualstudio.com/items?itemName=eamodio.gitlens)
- [VSCode Icons](https://marketplace.visualstudio.com/items?itemName=vscode-icons-team.vscode-icons)
- [ESLint](https://marketplace.visualstudio.com/items?itemName=dbaeumer.vscode-eslint)
- [ERD Editor](https://marketplace.visualstudio.com/items?itemName=dineug.vuerd-vscode)

### Code Formatting

We're making use of editor settings in VSCode, [Prettier](https://prettier.io), and [ESLint](https://eslint.org) to format the code on save.of the file.

- Editor configurations are set at: `.editorconfig`
- Prettier configurations are set at: `.prettierrc`
- ESLint configurations are set at: `.eslintrc`
- VSCode configurations are set at: `.vscode/settings.json`

### Generate a new migration file

Running the below scripts will look at the entities in your code, compare them to the existing migration files, and the existing migrations that have ran on your local database.

From there it will generate a new migration file for the delta. We're going to be using the [TypeORM](https://typeorm.io) generated migration files vs. making them ourselves. All that we ask, is once the file is created, open it and just save (so the prettier formatting is applied).

```bash
# Generate a new migration file for the global database, for example adding a table
$ bin/drata-cli migration:generate AddTable

# Generate a new migration file for the single-tenant databases, for example adding a field
$ bin/drata-cli migration:generate:tenant NewFieldOnTable
```

Now we need to optimize the `nukeAndPave` process.

> Global migrations are created here `src/database/migrations/global/`

> App (tenant) migrations are created here `src/database/migrations/app/`

### Running migrations

```bash
# Run global migration
$ bin/drata-cli migration:run

# Revert global migration
$ bin/drata-cli migration:revert

# Run single-tenant migration
$ bin/drata-cli migration:run:tenant

# Revert single-tenant migration
$ bin/drata-cli migration:revert:tenant
```

### Running seed scripts

```bash
# Run all seed scripts
$ bin/drata-cli seed:run

# Run seed script file, for example drata-site-admins.seed.ts
$ bin/drata-cli seed:run -s DrataSiteAdmins
```

### Starting workflows

We can start Temporal workflows with script:

```bash
# Start new workflow.
# Workflow type is: sampleWaaSWorkflowV1 rest of the parameters
# are passed from command line to the Workflow as a args
$ yarn run:workflow sampleWaaSWorkflowV1 --delay=1000 --json-route='{"name": "test"}'

# Start workflow and wait for it to be finished
$ yarn run:workflow sampleWaaSWorkflowV1 --delay=1000 --json-route='{"name": "test"}' --wait
```

### Installing packages

Make sure you use `yarn` instead of `npm` to install packages

```bash
# Add a NPM package to the dependencies section
$ yarn add <some-package>

# Add a NPM package to the devDependencies section
$ yarn add <some-package> -D
```

### Ngrok

```bash
# Ngrok is used rarely - primarily when working with typeform, pusher, or building locally for webhook tools
# Onboarding engineers can skip the Ngrok setup unless explicitly needed
```

- Download - https://ngrok.com/
- Install here
    - macOS: `/Users/<USER>/Applications`
    - Linux (Ubuntu): `/usr/bin`
- Then make this folder ~/.ngrok2
- Ask for the config file to put on: `~/.ngrok2/ngrok.yml`

```yml
# ngrok.yml looks like this
authtoken: AUTH_TOKEN_GOES_HERE

tunnels:
    drata_api:
        proto: http
        hostname: drata-api.ngrok.io
        addr: 127.0.0.1:3000
    drata_web:
        proto: http
        hostname: drata-web.ngrok.io
        addr: 127.0.0.1:5000
```

- Add the following aliases (pick or your system)

```bash
# macOS

# Local tunnels
alias ngrokapi='~/Applications/ngrok start drata_api'
alias ngrokweb='~/Applications/ngrok start drata_web'

# Linux (Ubuntu)

# Local tunnels
alias ngrokapi='ngrok start drata_api'
alias ngrokweb='ngrok start drata_web'
```

- Now you can run **Ngrok** using those aliases.

## Emails

- Build and commit html to API branch
- Testing

    - Inline CSS here
        - https://putsmail.com/inliner
    - Copy/Paste result with inlined css into an Amply template under a DEV account
        - https://sendamply.com/home/<USER>
        - If this is a new email, create a new template
        - Existing emails, overwrite template with updated html
    - Copy/Paste html into litmus to check email client formatting
        - https://litmus.com/home

- Preview alternative (just another tool)

    - http://tryhandlebarsjs.com/
    - Paste the `.hbs` content on that page and it will compile it to plain `HTML`
    - It allows context data (variables) and helper functions

- Deployment
    - Update PROD Amply account template

## GHA Workflow - Octoscan

Octoscan is a static vulnerability scanner for GitHub action workflows. The tool is implemented as a GHA workflow itself which runs against each PR.

Given a workflow has a vulnerability detected:

1. Read more about the vulnerability and try to address it.
2. Given you're confident that a line with vulnerability can stay as is, add `#ignore-octoscan-inline` next to the line that you want to be ignored.
   e.g.:

```bash
current-branch: ${{ steps.branch-name.outputs.branch }} #ignore-octoscan-inline
```

3. Given you want to ignore a whole file, add `#ignore-octoscan` at the top of the workflow file.
   e.g.:

```bash
#ignore-octoscan
name: Auto Merge (Release Train)
```

Rules that we've decided to ignore for all workflows:

The rule below will always trigger an alert since we're consciously using self-hosted runners.

1. runner-label
   The rules below apply only for public repos where there's a danger of untrusted input (PR that can be created by anyone)
2. expression-injection
3. dangerous-checkout
4. dangerous-write

## TODO

- Add list of design patterns we use
- Add relation schema

## License

Private Source Code.

**DO NOT share this or store the code on non-approved workstations without written consent from the CTO.**
