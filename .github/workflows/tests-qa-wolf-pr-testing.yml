name: Tests - Q<PERSON> Wolf PR Testing

on:
    pull_request:
        types: [labeled, synchronize]

env:
    APPLICATION: api
    RELEASE_TOKEN: ${{ secrets.RELEASE_COM_TOKEN_PROD }}
    RELEASE_ACCOUNT: ${{ vars.RELEASE_ACCOUNT }}
    RELEASE_APP: ${{ vars.RELEASE_APP }}
    RELEASE_LOGIN: ${{ vars.RELEASE_LOGIN }}
    AWS_DEFAULT_REGION: us-west-2
    RETRY_WAIT_SHORT: 10
    RETRY_WAIT_LONG: 15
    RETRY_INTERVAL_RESTORE: 180

concurrency:
    group: qawolf-pr-testing-${{ github.event.pull_request.number }}
    cancel-in-progress: true

jobs:
    check_label:
        name: Check QA Wolf Label
        runs-on: [runs-on, 'run-id=${{ github.run_id }}', runner=nonprod-small, tag=qawolf-check]
        timeout-minutes: 2
        outputs:
            should-deploy: ${{ steps.check.outputs.should-deploy }}
            deployment-reason: ${{ steps.check.outputs.deployment-reason }}
            pr-number: ${{ github.event.pull_request.number }}
        steps:
            - name: Check for QA Wolf label and determine deployment reason
              id: check
              run: |
                  # Install jq if not available
                  command -v jq >/dev/null || (apt-get update -qq && apt-get install -y -qq jq)

                  LABELS='${{ toJson(github.event.pull_request.labels.*.name) }}'
                  HAS_LABEL=$(echo "$LABELS" | jq -r 'map(select(. == "qawolf-pr-testing")) | length > 0')

                  if [ "$HAS_LABEL" = "true" ]; then
                      case "${{ github.event.action }}" in
                          "labeled") REASON="label-added" ;;
                          "synchronize") REASON="code-updated" ;;
                          *) REASON="unknown" ;;
                      esac
                      cat >> $GITHUB_OUTPUT << EOF
                  should-deploy=true
                  deployment-reason=$REASON
                  EOF
                  else
                      cat >> $GITHUB_OUTPUT << EOF
                  should-deploy=false
                  deployment-reason=no-label
                  EOF
                  fi

    build:
        name: Build API Docker Image
        needs: [check_label]
        if: needs.check_label.outputs.should-deploy == 'true'
        runs-on: [runs-on, 'run-id=${{ github.run_id }}', runner=build-api, tag=qawolf-build]
        timeout-minutes: 30
        steps:
            - name: Checkout repository
              uses: actions/checkout@v4
              with:
                  fetch-depth: 0
                  ref: ${{ github.event.pull_request.head.sha }}

            - name: Get short SHA for image tagging
              id: sha
              run: echo "short-sha=${GITHUB_SHA::7}" >> $GITHUB_OUTPUT

            - name: Assume AWS infrastructure role
              uses: drata/iam-actions/assume-role@1.4.4
              with:
                  account: infra
                  unset-current-credentials: true

            - name: Build Docker image with HUD instrumentation
              uses: drata/build-and-deployment-actions/build-ecs-image@4.8.0
              with:
                  application: ${{ env.APPLICATION }}
                  short-sha: ${{ steps.sha.outputs.short-sha }}
                  npm-token: ${{ secrets.NPM_REGISTRY_RO_PAT }}
                  hud-api-key: ${{ secrets.HUD_API_KEY }}

    deploy:
        name: Deploy to Release.com Environment
        needs: [check_label, build]
        if: needs.check_label.outputs.should-deploy == 'true'
        runs-on: [runs-on, 'run-id=${{ github.run_id }}', runner=build-api, tag=qawolf-deploy]
        timeout-minutes: 45
        environment: release-com-env
        container:
            image: public.ecr.aws/releasehub-com/release-cli
            options: --entrypoint bash
        outputs:
            environment-url: ${{ steps.deploy.outputs.environment-url }}
        steps:
            - name: Checkout repository for Release.com deployment
              uses: actions/checkout@v4
              with:
                  fetch-depth: 0
                  ref: ${{ github.event.pull_request.head.sha }}

            - name: Assume AWS infrastructure role for Release.com
              uses: drata/iam-actions/assume-role@1.4.4
              with:
                  account: infra
                  unset-current-credentials: true

            - name: Deploy to Release.com environment
              id: deploy
              shell: bash
              run: |
                  set -e

                  # Validate required environment variables
                  for var in RELEASE_ACCOUNT RELEASE_APP RELEASE_TOKEN RELEASE_LOGIN; do
                      if [ -z "${!var}" ]; then
                          echo "❌ Missing required environment variable: $var"
                          exit 1
                      fi
                  done

                  PR_NUMBER="${{ needs.check_label.outputs.pr-number }}"
                  BRANCH="${{ github.event.pull_request.head.ref }}"
                  REASON="${{ needs.check_label.outputs.deployment-reason }}"

                  # Use existing release template
                  if [ ! -f .release/application_template.yaml ]; then
                      echo "❌ Release template not found: .release/application_template.yaml"
                      exit 1
                  fi
                  echo "✅ Using existing release template"

                  # Update certificates
                  apt-get update -qq && apt-get install -y -qq ca-certificates >/dev/null 2>&1
                  update-ca-certificates >/dev/null 2>&1

                  # Find existing environment
                  if release environments list --output json > envs.json 2>/dev/null; then
                      # Check if the JSON is empty or contains no environments
                      ENV_COUNT=$(jq 'length' envs.json 2>/dev/null || echo "0")
                      if [ "$ENV_COUNT" -eq 0 ]; then
                          echo "⚠️ No environments found, creating new environment"
                          release environments create --branch "$BRANCH" --output json > result.json
                      else
                          EXISTING=$(jq -r --arg pr "$PR_NUMBER" --arg branch "$BRANCH" '
                              .[] | select(.name | contains($pr)) // select(.tracking_branch == $branch) | .handle
                          ' envs.json | head -1)

                          if [ -n "$EXISTING" ] && [ "$EXISTING" != "null" ]; then
                              if [ "$REASON" = "label-added" ]; then
                                  # Recreate for template changes
                                  release environments delete "$EXISTING" 2>/dev/null || true
                                  sleep 5
                                  release environments create --branch "$BRANCH" --output json > result.json
                              else
                                  # Reuse existing environment - extract actual environment data from list
                                  echo "♻️ Reusing existing environment: $EXISTING"
                                  jq --arg h "$EXISTING" '{environment: (.[] | select(.handle == $h))}' envs.json > result.json

                                  # Verify we got valid environment data
                                  ENV_STATE=$(jq -r '.environment.state // empty' result.json)
                                  if [ -z "$ENV_STATE" ]; then
                                      echo "❌ Could not get environment data for handle: $EXISTING"
                                      exit 1
                                  fi
                                  echo "✅ Found existing environment with state: $ENV_STATE"
                              fi
                          else
                              # Create new environment
                              release environments create --branch "$BRANCH" --output json > result.json
                          fi
                      fi
                  else
                      # Fallback create
                      release environments create --branch "$BRANCH" --output json > result.json
                  fi

                  # Parse response and construct URL
                  ENV_HANDLE=$(jq -r '.environment.handle // empty' result.json)
                  if [ -z "$ENV_HANDLE" ] || [ "$ENV_HANDLE" = "null" ]; then
                      echo "❌ Could not get environment handle from Release.com response"
                      cat result.json
                      exit 1
                  fi

                  ENV_URL="https://api-${ENV_HANDLE}.envs.drata.net"
                  echo "environment-url=$ENV_URL" >> $GITHUB_OUTPUT
                  echo "Environment ready: $ENV_URL"

    setup_environment:
        name: Setup Environment & Restore Data
        needs: [check_label, deploy]
        if: needs.check_label.outputs.should-deploy == 'true'
        runs-on: [runs-on, 'run-id=${{ github.run_id }}', runner=build-api, tag=qawolf-deploy]
        timeout-minutes: 35
        env:
            # Ensure TLS skip is applied globally for all Release CLI calls
            RELEASE_SKIP_TLS_VERIFY: 'true'
        steps:
            - name: Assume ephemeral-envs AWS role for kubectl access
              uses: drata/iam-actions/assume-role@1.4.4
              with:
                  account: ephemeral-envs
                  unset-current-credentials: true
                  role_name_environment_override: ephemerals

            - name: Setup Release CLI with kubectl and AWS
              run: |
                  echo "🔧 Setting up Release CLI via Docker with kubectl and AWS CLI support..."

                  # Install kubectl on the GitHub Actions runner
                  echo "Installing kubectl on runner..."
                  curl -LO "https://dl.k8s.io/release/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/linux/amd64/kubectl"
                  chmod +x kubectl
                  sudo mv kubectl /usr/local/bin/kubectl

                  # Verify installations (AWS CLI is already installed by the assume-role action above)
                  kubectl version --client
                  aws --version

                  # Function to retry Docker operations with exponential backoff
                  retry_docker_pull() {
                      local max_attempts=5
                      local attempt=1
                      local delay=30

                      while [ $attempt -le $max_attempts ]; do
                          echo "Attempt $attempt/$max_attempts: Pulling Release CLI Docker image..."

                          if docker pull public.ecr.aws/releasehub-com/release-cli:latest; then
                              echo "✅ Docker image pulled successfully"
                              return 0
                          else
                              echo "❌ Docker pull failed (attempt $attempt/$max_attempts)"
                              if [ $attempt -lt $max_attempts ]; then
                                  echo "⏳ Waiting ${delay}s before retry..."
                                  sleep $delay
                                  delay=$((delay * 2))  # Exponential backoff
                                  [ $delay -gt 120 ] && delay=120  # Cap at 2 minutes
                              fi
                          fi
                          attempt=$((attempt + 1))
                      done

                      echo "💥 Failed to pull Docker image after $max_attempts attempts"
                      return 1
                  }

                  # Retry the Docker pull
                  retry_docker_pull

                  # Extract AWS credentials from host and create credentials file
                  echo "Setting up AWS credentials file for Release CLI container..."

                  # Create temporary AWS credentials directory and file
                  mkdir -p /tmp/.aws
                  echo "[default]" > /tmp/.aws/credentials
                  echo "aws_access_key_id = ${AWS_ACCESS_KEY_ID}" >> /tmp/.aws/credentials
                  echo "aws_secret_access_key = ${AWS_SECRET_ACCESS_KEY}" >> /tmp/.aws/credentials
                  echo "aws_session_token = ${AWS_SESSION_TOKEN}" >> /tmp/.aws/credentials
                  chmod 600 /tmp/.aws/credentials

                  # Create AWS config file
                  echo "[default]" > /tmp/.aws/config
                  echo "region = ${{ env.AWS_DEFAULT_REGION }}" >> /tmp/.aws/config
                  echo "output = json" >> /tmp/.aws/config
                  chmod 600 /tmp/.aws/config

                  echo "✅ AWS credentials file created"

                  # Install AWS CLI v2 in a separate location to avoid conflicts
                  echo "Installing AWS CLI v2 for Release CLI container..."
                  curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
                  unzip -q awscliv2.zip
                  sudo ./aws/install --install-dir /opt/aws-cli --bin-dir /opt/aws-cli/bin

                  # Create wrapper script that mounts kubectl, AWS CLI, and AWS credentials file
                  echo '#!/bin/bash' > /tmp/release-wrapper
                  echo 'docker run --rm \' >> /tmp/release-wrapper
                  echo '  -v /usr/local/bin/kubectl:/usr/bin/kubectl \' >> /tmp/release-wrapper
                  echo '  -v /opt/aws-cli:/opt/aws-cli:ro \' >> /tmp/release-wrapper
                  echo '  -v /tmp/.aws:/root/.aws:ro \' >> /tmp/release-wrapper
                  echo '  -e PATH="/opt/aws-cli/bin:$PATH" \' >> /tmp/release-wrapper
                  echo '  -e AWS_DEFAULT_REGION="${{ env.AWS_DEFAULT_REGION }}" \' >> /tmp/release-wrapper
                  echo '  -e RELEASE_TOKEN="${RELEASE_TOKEN}" \' >> /tmp/release-wrapper
                  echo '  -e RELEASE_ACCOUNT="${RELEASE_ACCOUNT}" \' >> /tmp/release-wrapper
                  echo '  -e RELEASE_APP="${RELEASE_APP}" \' >> /tmp/release-wrapper
                  echo '  -e RELEASE_LOGIN="${RELEASE_LOGIN}" \' >> /tmp/release-wrapper
                  echo '  -e RELEASE_SKIP_TLS_VERIFY="${RELEASE_SKIP_TLS_VERIFY}" \' >> /tmp/release-wrapper
                  echo '  public.ecr.aws/releasehub-com/release-cli:latest "$@"' >> /tmp/release-wrapper

                  sudo mv /tmp/release-wrapper /usr/local/bin/release
                  sudo chmod +x /usr/local/bin/release

                  echo "✅ Release CLI Docker wrapper with kubectl and AWS CLI support installed"

                  # Test that kubectl is accessible inside the container
                  echo "Testing kubectl availability in Release CLI container..."
                  if docker run --rm -v /usr/local/bin/kubectl:/usr/bin/kubectl --entrypoint kubectl public.ecr.aws/releasehub-com/release-cli:latest version --client; then
                      echo "✅ kubectl is accessible inside Release CLI container"
                  else
                      echo "❌ kubectl is not accessible inside Release CLI container"
                      exit 1
                  fi

                  # Test AWS credentials file was created
                  echo "Verifying AWS credentials file..."
                  if [ -f /tmp/.aws/credentials ]; then
                      echo "✅ AWS credentials file created successfully"
                      echo "File location: /tmp/.aws/credentials"
                  else
                      echo "❌ Failed to create AWS credentials file"
                      exit 1
                  fi

                  # Test that AWS credentials work inside the container
                  echo "Testing AWS credentials inside Release CLI container..."
                  if docker run --rm \
                      -v /tmp/.aws:/root/.aws:ro \
                      -v /opt/aws-cli:/opt/aws-cli:ro \
                      -e PATH="/opt/aws-cli/bin:$PATH" \
                      -e AWS_DEFAULT_REGION="${{ env.AWS_DEFAULT_REGION }}" \
                      --entrypoint /opt/aws-cli/bin/aws \
                      public.ecr.aws/releasehub-com/release-cli:latest \
                      sts get-caller-identity; then
                      echo "✅ AWS credentials working inside container"
                  else
                      echo "❌ AWS credentials not working inside container"
                      exit 1
                  fi

                  # Test installation
                  release --version

                  # Verify TLS skip is working
                  echo "Verifying TLS skip configuration..."
                  echo "  RELEASE_SKIP_TLS_VERIFY: ${RELEASE_SKIP_TLS_VERIFY}"

            - name: Wait for container readiness
              run: |
                  set -e

                  ENV_HANDLE=$(echo "${{ needs.deploy.outputs.environment-url }}" | sed 's|https://api-||' | sed 's|\.envs\.drata\.net||')
                  MAX_ATTEMPTS=30
                  WAIT_SECONDS=60

                  echo "🔍 Debug info:"
                  echo "  Environment URL: ${{ needs.deploy.outputs.environment-url }}"
                  echo "  Extracted ENV_HANDLE: $ENV_HANDLE"
                  echo "  RELEASE_APP: ${{ vars.RELEASE_APP }}"
                  echo "  RELEASE_ACCOUNT: ${{ vars.RELEASE_ACCOUNT }}"
                  echo "  RELEASE_SKIP_TLS_VERIFY: ${RELEASE_SKIP_TLS_VERIFY}"
                  echo "  Max attempts: $MAX_ATTEMPTS"
                  echo "  Wait between attempts: ${WAIT_SECONDS}s"

                  # Find the actual Release environment ID (only need to do this once)
                  echo "🔍 Finding actual Release environment ID..."
                  echo "🔍 Listing environments to find match for: $ENV_HANDLE"
                  ENV_LIST_JSON=$(RELEASE_SKIP_TLS_VERIFY=true release environments list --app "${{ vars.RELEASE_APP }}" --output json 2>/dev/null || echo "")

                  if [ -z "$ENV_LIST_JSON" ]; then
                      echo "❌ Could not list environments - this is a critical error"
                      echo "Cannot proceed without proper environment ID"
                      exit 1
                  fi

                  # Parse JSON to find environment that matches our handle exactly
                  ENVIRONMENT_ID=$(echo "$ENV_LIST_JSON" | jq -r ".[] | select(.handle == \"$ENV_HANDLE\") | .id" 2>/dev/null | head -1)

                  if [ -z "$ENVIRONMENT_ID" ] || [ "$ENVIRONMENT_ID" = "null" ]; then
                      echo "❌ No environment found with handle: $ENV_HANDLE"
                      echo "Available environments:"
                      echo "$ENV_LIST_JSON" | jq -r '.[] | "  \(.handle) (\(.id)) - \(.state)"' 2>/dev/null || echo "$ENV_LIST_JSON"
                      exit 1
                  fi

                  echo "✅ Found environment ID: $ENVIRONMENT_ID"

                  # Now poll the environment state until it's DEPLOYED
                  ATTEMPT=1
                  while [ $ATTEMPT -le $MAX_ATTEMPTS ]; do
                      echo ""
                      echo "🔄 Attempt $ATTEMPT/$MAX_ATTEMPTS: Checking environment state..."

                      # Get fresh environment list to check current state
                      ENV_LIST_JSON=$(RELEASE_SKIP_TLS_VERIFY=true release environments list --app "${{ vars.RELEASE_APP }}" --output json 2>/dev/null || echo "")

                      if [ -z "$ENV_LIST_JSON" ]; then
                          echo "⚠️ Could not fetch environment list, will retry..."
                          ATTEMPT=$((ATTEMPT + 1))
                          [ $ATTEMPT -le $MAX_ATTEMPTS ] && sleep $WAIT_SECONDS
                          continue
                      fi

                      # Get current state
                      ENV_STATE=$(echo "$ENV_LIST_JSON" | jq -r ".[] | select(.id == \"$ENVIRONMENT_ID\") | .state" 2>/dev/null)
                      ENV_NAME=$(echo "$ENV_LIST_JSON" | jq -r ".[] | select(.id == \"$ENVIRONMENT_ID\") | .name" 2>/dev/null)
                      ENV_HANDLE_FOUND=$(echo "$ENV_LIST_JSON" | jq -r ".[] | select(.id == \"$ENVIRONMENT_ID\") | .handle" 2>/dev/null)

                      echo "🔍 Environment details:"
                      echo "  ID: $ENVIRONMENT_ID"
                      echo "  Handle: $ENV_HANDLE_FOUND"
                      echo "  Name: $ENV_NAME"
                      echo "  State: $ENV_STATE"

                      # Check state
                      if [ "$ENV_STATE" = "DEPLOYED" ]; then
                          echo "✅ Environment is DEPLOYED and ready!"

                          # Show available external URLs
                          echo "🔍 Available external URLs:"
                          echo "$ENV_LIST_JSON" | jq -r ".[] | select(.id == \"$ENVIRONMENT_ID\") | .hostnames[] | \"  \(.target): \(.url)\"" 2>/dev/null || echo "  Could not parse hostnames"

                          echo "ℹ️ Note: These are external URLs, not internal instance names for Release CLI"
                          echo "ENVIRONMENT_ID=$ENVIRONMENT_ID" >> $GITHUB_ENV
                          exit 0
                      elif [ "$ENV_STATE" = "DEPLOYING" ] || [ "$ENV_STATE" = "DEPLOYING_PATCH" ]; then
                          echo "⏳ Environment is still deploying (state: $ENV_STATE)"
                          if [ $ATTEMPT -lt $MAX_ATTEMPTS ]; then
                              echo "⏳ Waiting ${WAIT_SECONDS}s before next check..."
                              sleep $WAIT_SECONDS
                          fi
                      else
                          echo "❌ Environment is in unexpected state: $ENV_STATE"
                          echo "❌ Cannot proceed - environment may have failed to deploy"
                          exit 1
                      fi

                      ATTEMPT=$((ATTEMPT + 1))
                  done

                  echo "❌ Timeout: Environment did not reach DEPLOYED state after $MAX_ATTEMPTS attempts"
                  echo "❌ Last known state: $ENV_STATE"
                  exit 1

            - name: Restore QA Wolf test data
              env:
                  # Fix TERM variable globally for this step to avoid fuzzy finder issues
                  TERM: xterm
              run: |
                  set -e

                  ENV_HANDLE=$(echo "${{ needs.deploy.outputs.environment-url }}" | sed 's|https://api-||' | sed 's|\.envs\.drata\.net||')

                  echo "Restoring QA Wolf test data in environment: $ENV_HANDLE"

                  # Install AWS CLI in Release environment for EKS authentication
                  echo "🔧 Installing AWS CLI in Release environment..."
                  RELEASE_SKIP_TLS_VERIFY=true release instances exec \
                      --app "${{ vars.RELEASE_APP }}" \
                      --environment "$ENVIRONMENT_ID" \
                      --account "${{ vars.RELEASE_ACCOUNT }}" \
                      --instance "api" \
                      --if-multiple first \
                      -- bash -c "
                          if ! command -v aws &> /dev/null; then
                              echo 'Installing AWS CLI...'
                              curl 'https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip' -o 'awscliv2.zip'
                              unzip -q awscliv2.zip
                              ./aws/install
                              echo 'AWS CLI installed successfully'
                          else
                              echo 'AWS CLI already available'
                          fi
                          aws --version
                      "

                  # Use new --if-multiple flag to avoid fuzzy finder selection with retry mechanism
                  echo "Testing connection with automatic instance selection..."

                  MAX_ATTEMPTS=5
                  ATTEMPT=1
                  RETRY_INTERVAL=${{ env.RETRY_INTERVAL_RESTORE }}

                  while [ $ATTEMPT -le $MAX_ATTEMPTS ]; do
                      echo "Attempt $ATTEMPT/$MAX_ATTEMPTS: Testing instance connection..."

                      if RELEASE_SKIP_TLS_VERIFY=true release instances exec \
                          --app "${{ vars.RELEASE_APP }}" \
                          --environment "$ENVIRONMENT_ID" \
                          --account "${{ vars.RELEASE_ACCOUNT }}" \
                          --instance "api" \
                          --if-multiple first \
                          -- echo "Connection test successful"; then
                          echo "Instance connection successful!"
                          break
                      else
                          echo "Instance connection failed (attempt $ATTEMPT/$MAX_ATTEMPTS)"
                          if [ $ATTEMPT -eq $MAX_ATTEMPTS ]; then
                              echo "❌ All connection attempts failed after $MAX_ATTEMPTS tries"
                              echo "Environment: $ENVIRONMENT_ID"
                              echo "Instance: api"
                              echo "App: ${{ vars.RELEASE_APP }}"
                              echo "Account: ${{ vars.RELEASE_ACCOUNT }}"
                              exit 1
                          fi
                          echo "Waiting $RETRY_INTERVAL seconds before retry..."
                          sleep $RETRY_INTERVAL
                          ATTEMPT=$((ATTEMPT + 1))
                      fi
                  done

                  # Run the restore command - single attempt since connection test already passed
                  echo "Running QA Wolf data restore..."

                  if RELEASE_SKIP_TLS_VERIFY=true release instances exec \
                      --app "${{ vars.RELEASE_APP }}" \
                      --environment "$ENVIRONMENT_ID" \
                      --account "${{ vars.RELEASE_ACCOUNT }}" \
                      --instance "api" \
                      --if-multiple first \
                      -- ./bin/drata-cli restoreCustomBackup tdac-qawolf -s3; then
                      echo "QA Wolf test data restored successfully!"
                  else
                      echo "❌ QA Wolf data restore failed"
                      echo "Environment: $ENVIRONMENT_ID"
                      echo "Backup: tdac-qawolf"
                      echo "Command: ./bin/drata-cli restoreCustomBackup tdac-qawolf -s3"
                      exit 1
                  fi

    notify:
        name: Notify QA Wolf
        needs: [check_label, deploy, setup_environment]
        if: needs.check_label.outputs.should-deploy == 'true'
        runs-on: [runs-on, 'run-id=${{ github.run_id }}', runner=nonprod-small, tag=qawolf-notify]
        timeout-minutes: 5
        steps:
            - name: Create QA Wolf variables
              id: qawolf-vars
              run: |
                  # Extract environment handle from API URL to construct admin and web URLs
                  ENV_HANDLE=$(echo "${{ needs.deploy.outputs.environment-url }}" | sed 's|https://api-||' | sed 's|\.envs\.drata\.net||')

                  # Construct URLs for different services
                  ADMIN_URL="https://admin-${ENV_HANDLE}.envs.drata.net"
                  WEB_URL="https://app-${ENV_HANDLE}.envs.drata.net"
                  # Multiverse URL follows pattern: https://drata-app-{ENV_HANDLE}.envs.drata.net/
                  MULTIVERSE_URL="https://drata-app-${ENV_HANDLE}.envs.drata.net/"

                  VARIABLES=$(cat << EOF | tr -d '\n' | tr -s ' '
                  {
                    "ENVIRONMENT_NAME": "pr-${{ needs.check_label.outputs.pr-number }}-release",
                    "BRANCH_NAME": "${{ github.event.pull_request.head.ref }}",
                    "URL": "$WEB_URL",
                    "DEFAULT_URL": "$WEB_URL",
                    "ADMIN_SITE_URL": "$ADMIN_URL",
                    "DEFAULT_URL_MULTIVERSE": "$MULTIVERSE_URL"
                  }
                  EOF
                  )

                  echo "variables=$VARIABLES" >> $GITHUB_OUTPUT
                  echo "deployment-url=$WEB_URL" >> $GITHUB_OUTPUT

            - name: Notify QA Wolf (attempt 1)
              id: qawolf-attempt-1
              continue-on-error: true
              uses: qawolf/notify-qawolf-on-deploy-action@29f3649159b0145e2458decb2592890e30307368
              env:
                  GITHUB_TOKEN: '${{ secrets.GITHUB_TOKEN }}'
              with:
                  qawolf-api-key: '${{ secrets.QAWOLF_API_KEY }}'
                  deployment-type: 'pr-testing'
                  deployment-url: '${{ steps.qawolf-vars.outputs.deployment-url }}'
                  variables: '${{ steps.qawolf-vars.outputs.variables }}'

            - name: Wait before retry
              if: steps.qawolf-attempt-1.outcome == 'failure'
              run: sleep ${{ env.RETRY_WAIT_SHORT }}

            - name: Notify QA Wolf (attempt 2)
              id: qawolf-attempt-2
              if: steps.qawolf-attempt-1.outcome == 'failure'
              continue-on-error: true
              uses: qawolf/notify-qawolf-on-deploy-action@29f3649159b0145e2458decb2592890e30307368
              env:
                  GITHUB_TOKEN: '${{ secrets.GITHUB_TOKEN }}'
              with:
                  qawolf-api-key: '${{ secrets.QAWOLF_API_KEY }}'
                  deployment-type: 'pr-testing'
                  deployment-url: '${{ steps.qawolf-vars.outputs.deployment-url }}'
                  variables: '${{ steps.qawolf-vars.outputs.variables }}'

            - name: Wait before final retry
              if: steps.qawolf-attempt-1.outcome == 'failure' && steps.qawolf-attempt-2.outcome == 'failure'
              run: sleep ${{ env.RETRY_WAIT_LONG }}

            - name: Notify QA Wolf (final attempt)
              if: steps.qawolf-attempt-1.outcome == 'failure' && steps.qawolf-attempt-2.outcome == 'failure'
              uses: qawolf/notify-qawolf-on-deploy-action@29f3649159b0145e2458decb2592890e30307368
              env:
                  GITHUB_TOKEN: '${{ secrets.GITHUB_TOKEN }}'
              with:
                  qawolf-api-key: '${{ secrets.QAWOLF_API_KEY }}'
                  deployment-type: 'pr-testing'
                  deployment-url: '${{ steps.qawolf-vars.outputs.deployment-url }}'
                  variables: '${{ steps.qawolf-vars.outputs.variables }}'

            - name: Create GitHub Job Summary
              if: always()
              run: |
                  STATUS="${{ job.status }}"
                  ENV_NAME="pr-${{ needs.check_label.outputs.pr-number }}-release"

                  cat >> $GITHUB_STEP_SUMMARY << EOF
                  # QA Wolf PR Testing

                  $([ "$STATUS" = "success" ] && echo "✅ **SUCCESS** - Environment \`$ENV_NAME\` ready" || echo "❌ **FAILED** - Notification failed")

                  🔗 [QA Wolf Dashboard](https://app.qawolf.com/drata-876)
                  🌐 [Release Environment](${{ needs.deploy.outputs.environment-url }})
                  EOF
