name: Tests - E2E Identity Sync
on:
    pull_request:
        types: [opened, synchronize]
    workflow_dispatch:

concurrency:
    group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
    cancel-in-progress: true

env:
    NPM_TOKEN: ${{secrets.NPM_TOKEN}}

jobs:
    tests_e2e:
        name: Identity Sync E2E Tests
        runs-on:
            [
                runs-on,
                'run-id=${{ github.run_id }}',
                runner=tests-api-beacon,
                tag=tests-e2e-temporal-workflows,
            ]
        steps:
            - name: Setup MySQL
              uses: drata/tests-actions/setup-mysql@1.3.5
            - name: Show mysql version
              run: mysql --version
            - name: Clone API repository
              uses: actions/checkout@v4
              with:
                  fetch-depth: '0'
            - name: Setup Node
              id: node-install
              uses: actions/setup-node@v4
              with:
                  node-version-file: '.nvmrc'
            - name: Authenticate with private NPM package registry
              uses: drata/utils-actions/authenticate-to-github-npm@1.0.0
              with:
                  npm_pat: ${{ secrets.NPM_REGISTRY_RO_PAT }}
            - name: Restore yarn cache for api
              uses: drata/utils-actions/restore-yarn-cache@2.2.0
              with:
                  application-name: api
                  runner-os: runner.os
                  node-version: ${{ steps.node-install.outputs.node-version }}
            - name: Install and build
              run: yarn install --prefer-offline && yarn prebuild && yarn build-with-ci:worker
            - name: Get secrets
              uses: drata/tests-actions/setup-api-localdev@1.6.0
              with:
                  runson_env: infra
            - name: Set friendly branch name
              uses: drata/utils-actions/get-friendly-branch-name@1.0.2
              id: branch-name
            - name: Check for migrations
              if: ${{ !(contains(fromJSON('["main", "release"]'), steps.branch-name.outputs.branch)) }}
              id: check_migrations
              uses: drata/utils-actions/check-code-changes@1.0.3
              with:
                  current-branch: 'origin/${{ steps.branch-name.outputs.branch }}'
                  destination-branch: 'origin/release'
                  paths: 'src/database/migrations'
            - name: Run nukeAndPave
              uses: drata/utils-actions/nuke-and-pave@1.0.3
              with:
                  flavour: nukeAndPave
                  use-s3: true
                  run-migrations: ${{ steps.check_migrations.outputs.has_changes }}
            - name: Setup role profile
              uses: drata/iam-actions/create-profile@1.4.0
              with:
                  account: dev
                  region: us-west-2
                  runson_env: infra

            - name: Install 1Password CLI
              uses: 1password/install-cli-action@v1

            - name: Install Postman CLI
              run: |
                  curl -o- "https://dl-cli.pstmn.io/install/linux64.sh" | sh
                  export PATH="/github/home/<USER>"
                  echo "/github/home/<USER>" >> $GITHUB_PATH

            - name: Set up AWS profile with assumed role credentials
              run: |
                  mkdir -p ~/.aws
                  echo "[dev]" > ~/.aws/credentials
                  echo "aws_access_key_id = $AWS_ACCESS_KEY_ID" >> ~/.aws/credentials
                  echo "aws_secret_access_key = $AWS_SECRET_ACCESS_KEY" >> ~/.aws/credentials
                  echo "aws_session_token = $AWS_SESSION_TOKEN" >> ~/.aws/credentials

                  echo "[default]" >> ~/.aws/config
                  echo "region = us-west-2" >> ~/.aws/config
                  echo "output = json" >> ~/.aws/config

                  echo "[profile dev]" >> ~/.aws/config
                  echo "region = us-west-2" >> ~/.aws/config
                  echo "output = json" >> ~/.aws/config
              env:
                  AWS_ACCESS_KEY_ID: ${{ steps.configure-credentials.outputs.aws-access-key-id }}
                  AWS_SECRET_ACCESS_KEY: ${{ steps.configure-credentials.outputs.aws-secret-access-key }}
                  AWS_SESSION_TOKEN: ${{ steps.configure-credentials.outputs.aws-session-token }}

            - name: Fetch and update blueprint
              run: yarn postman:blueprint
              env:
                  OP_SERVICE_ACCOUNT_TOKEN: ${{ secrets.OP_SERVICE_ACCOUNT_TOKEN }}
            - name: Run tests
              run: yarn test:e2e src/tests/synchronizations-suite-set/
